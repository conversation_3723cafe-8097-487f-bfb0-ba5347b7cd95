import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that analyzes and visualizes where a function is increasing or decreasing.
class InteractiveIncreasingDecreasingFunctionAnalyzerWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveIncreasingDecreasingFunctionAnalyzerWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  /// Factory constructor to create the widget from JSON data
  factory InteractiveIncreasingDecreasingFunctionAnalyzerWidget.fromData(Map<String, dynamic> data) {
    return InteractiveIncreasingDecreasingFunctionAnalyzerWidget(
      data: data,
    );
  }

  @override
  State<InteractiveIncreasingDecreasingFunctionAnalyzerWidget> createState() => _InteractiveIncreasingDecreasingFunctionAnalyzerWidgetState();
}

class _InteractiveIncreasingDecreasingFunctionAnalyzerWidgetState extends State<InteractiveIncreasingDecreasingFunctionAnalyzerWidget> {
  // Function to visualize (e.g., f(x) = x^3 - 3x)
  double _function(double x) {
    return x * x * x - 3 * x;
  }

  // Derivative of the function (f'(x) = 3x^2 - 3)
  double _derivative(double x) {
    return 3 * x * x - 3;
  }

  // Colors
  late Color _primaryColor;
  late Color _secondaryColor;
  late Color _backgroundColor;
  late Color _textColor;
  late Color _increasingColor;
  late Color _decreasingColor;

  @override
  void initState() {
    super.initState();
    _primaryColor = _parseColor(widget.data['primary_color']) ?? Colors.blue;
    _secondaryColor = _parseColor(widget.data['secondary_color']) ?? Colors.orange;
    _backgroundColor = _parseColor(widget.data['background_color']) ?? Colors.white;
    _textColor = _parseColor(widget.data['text_color']) ?? Colors.black87;
    _increasingColor = _parseColor(widget.data['increasing_color']) ?? Colors.green;
    _decreasingColor = _parseColor(widget.data['decreasing_color']) ?? Colors.red;
  }

  Color? _parseColor(String? hexString) {
    if (hexString == null) return null;
    hexString = hexString.replaceAll('#', '');
    if (hexString.length == 6) {
      hexString = 'FF$hexString';
    }
    try {
      return Color(int.parse(hexString, radix: 16));
    } catch (e) {
      return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _backgroundColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.data['title'] ?? 'Increasing/Decreasing Function Analyzer',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),
          const SizedBox(height: 8),
          if (widget.data['description'] != null)
            Text(
              widget.data['description'],
              style: TextStyle(
                fontSize: 14,
                color: _textColor.withOpacity(0.8),
              ),
            ),
          const SizedBox(height: 16),

          Text(
            'Function: f(x) = x³ - 3x',
            style: TextStyle(color: _textColor, fontSize: 16, fontStyle: FontStyle.italic),
          ),
          Text(
            'Derivative: f\'(x) = 3x² - 3',
            style: TextStyle(color: _textColor, fontSize: 16, fontStyle: FontStyle.italic),
          ),
          const SizedBox(height: 16),

          Container(
            height: 250,
            child: CustomPaint(
              painter: IncreasingDecreasingPainter(
                function: _function,
                derivative: _derivative,
                primaryColor: _primaryColor,
                secondaryColor: _secondaryColor,
                increasingColor: _increasingColor,
                decreasingColor: _decreasingColor,
                textColor: _textColor,
              ),
              child: Container(),
            ),
          ),
          const SizedBox(height: 16),

          Center(
            child: Column(
              children: [
                Text(
                  'Analysis:',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: _textColor,
                  ),
                ),
                Text(
                  'f(x) is increasing when f\'(x) > 0',
                  style: TextStyle(color: _increasingColor),
                ),
                Text(
                  'f(x) is decreasing when f\'(x) < 0',
                  style: TextStyle(color: _decreasingColor),
                ),
                Text(
                  'Critical points where f\'(x) = 0: x = -1, 1',
                  style: TextStyle(color: _textColor.withOpacity(0.8)),
                ),
                Text(
                  'Increasing: (-∞, -1) U (1, ∞)',
                  style: TextStyle(color: _increasingColor, fontWeight: FontWeight.bold),
                ),
                Text(
                  'Decreasing: (-1, 1)',
                  style: TextStyle(color: _decreasingColor, fontWeight: FontWeight.bold),
                ),
              ],
            ),
          ),

          // Widget name tag
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'InteractiveIncreasingDecreasingFunctionAnalyzer',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }
}

class IncreasingDecreasingPainter extends CustomPainter {
  final Function(double) function;
  final Function(double) derivative;
  final Color primaryColor;
  final Color secondaryColor;
  final Color increasingColor;
  final Color decreasingColor;
  final Color textColor;

  IncreasingDecreasingPainter({
    required this.function,
    required this.derivative,
    required this.primaryColor,
    required this.secondaryColor,
    required this.increasingColor,
    required this.decreasingColor,
    required this.textColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint();
    final textPainter = TextPainter(textDirection: TextDirection.ltr);
    final textStyle = TextStyle(color: textColor, fontSize: 10);

    // Define plot area padding
    const double padding = 30.0;
    final double plotWidth = size.width - 2 * padding;
    final double plotHeight = size.height - 2 * padding;

    // Determine x and y ranges for plotting
    final double minPlotX = -3.0;
    final double maxPlotX = 3.0;
    final double minPlotY = -5.0; // For x^3 - 3x, min at x=1 is -2, max at x=-1 is 2
    final double maxPlotY = 5.0;

    // Scale factors
    final double xScale = plotWidth / (maxPlotX - minPlotX);
    final double yScale = plotHeight / (maxPlotY - minPlotY);

    // Helper to convert plot coordinates to canvas coordinates
    Offset toCanvas(double x, double y) {
      final canvasX = padding + (x - minPlotX) * xScale;
      final canvasY = size.height - padding - (y - minPlotY) * yScale;
      return Offset(canvasX, canvasY);
    }

    // Draw axes
    paint.color = Colors.grey;
    paint.strokeWidth = 1.0;
    // X-axis
    canvas.drawLine(toCanvas(minPlotX, 0), toCanvas(maxPlotX, 0), paint);
    // Y-axis
    canvas.drawLine(toCanvas(0, minPlotY), toCanvas(0, maxPlotY), paint);

    // Draw function curve
    paint.color = primaryColor;
    paint.style = PaintingStyle.stroke;
    paint.strokeWidth = 2.0;

    final path = Path();
    bool firstPoint = true;
    for (double x = minPlotX; x <= maxPlotX; x += (maxPlotX - minPlotX) / 200) {
      final y = function(x);
      final point = toCanvas(x, y);
      if (firstPoint) {
        path.moveTo(point.dx, point.dy);
        firstPoint = false;
      } else {
        path.lineTo(point.dx, point.dy);
      }
    }
    canvas.drawPath(path, paint);

    // Draw derivative curve
    paint.color = secondaryColor;
    paint.style = PaintingStyle.stroke;
    paint.strokeWidth = 1.5;
    final derivativePath = Path();
    firstPoint = true;
    for (double x = minPlotX; x <= maxPlotX; x += (maxPlotX - minPlotX) / 200) {
      final y = derivative(x);
      final point = toCanvas(x, y);
      if (firstPoint) {
        derivativePath.moveTo(point.dx, point.dy);
        firstPoint = false;
      } else {
        derivativePath.lineTo(point.dx, point.dy);
      }
    }
    canvas.drawPath(derivativePath, paint);

    // Highlight increasing/decreasing intervals
    // Critical points for f(x) = x^3 - 3x are x = -1 and x = 1
    final criticalPoint1 = toCanvas(-1.0, function(-1.0));
    final criticalPoint2 = toCanvas(1.0, function(1.0));

    // Draw vertical lines at critical points
    paint.color = Colors.grey.withOpacity(0.5);
    paint.strokeWidth = 1.0;
    canvas.drawLine(toCanvas(-1.0, minPlotY), toCanvas(-1.0, maxPlotY), paint);
    canvas.drawLine(toCanvas(1.0, minPlotY), toCanvas(1.0, maxPlotY), paint);

    // Draw points at critical points
    paint.color = primaryColor;
    paint.style = PaintingStyle.fill;
    canvas.drawCircle(criticalPoint1, 4, paint);
    canvas.drawCircle(criticalPoint2, 4, paint);

    // Draw labels for critical points
    textPainter.text = TextSpan(text: 'x=-1', style: textStyle);
    textPainter.layout();
    textPainter.paint(canvas, Offset(toCanvas(-1.0, 0).dx - textPainter.width / 2, size.height - padding + 5));

    textPainter.text = TextSpan(text: 'x=1', style: textStyle);
    textPainter.layout();
    textPainter.paint(canvas, Offset(toCanvas(1.0, 0).dx - textPainter.width / 2, size.height - padding + 5));
  }

  @override
  bool shouldRepaint(covariant IncreasingDecreasingPainter oldDelegate) {
    return oldDelegate.primaryColor != primaryColor ||
           oldDelegate.secondaryColor != secondaryColor ||
           oldDelegate.increasingColor != increasingColor ||
           oldDelegate.decreasingColor != decreasingColor ||
           oldDelegate.textColor != textColor;
  }
}
