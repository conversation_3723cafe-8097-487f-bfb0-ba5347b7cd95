import 'package:flutter/material.dart';
import 'package:resonance/widgets/animated_button.dart';

class InteractiveFallacyIdentifier extends StatefulWidget {
  const InteractiveFallacyIdentifier({super.key});

  @override
  State<InteractiveFallacyIdentifier> createState() => _InteractiveFallacyIdentifierState();
}

class _InteractiveFallacyIdentifierState extends State<InteractiveFallacyIdentifier> {
  String _argumentText = '';
  String _identifiedFallacy = '';
  String _explanation = '';

  final List<Map<String, String>> _fallacies = [
    {
      'name': 'Ad Hominem',
      'description': 'Attacking the person making the argument, rather than the argument itself.',
      'keywords': 'you are, your opinion, personal attack, character, motive',
      'example': 'You can\'t trust <PERSON>\'s opinion on climate change; he drives a gas-guzzling truck!',
    },
    {
      'name': 'Straw Man',
      'description': 'Misrepresenting someone\'s argument to make it easier to attack.',
      'keywords': 'misrepresent, distort, exaggerate, oversimplify',
      'example': 'Opponent: "We should put more money into health and education." Politician: "My opponent wants to bankrupt the nation by throwing money at every program imaginable!"',
    },
    {
      'name': 'Appeal to Authority',
      'description': 'Claiming something is true because an unqualified or biased authority says it is.',
      'keywords': 'expert says, doctor recommends, celebrity endorses',
      'example': 'My favorite actor says this diet pill works, so it must be effective.',
    },
    {
      'name': 'False Dilemma (Black or White)',
      'description': 'Presenting only two options or sides when there are actually more.',
      'keywords': 'either/or, only two choices, no middle ground',
      'example': 'You\'re either with us, or you\'re against us.',
    },
    {
      'name': 'Slippery Slope',
      'description': 'Asserting that a relatively small first step will inevitably lead to a chain of related events culminating in some significant (usually negative) effect.',
      'keywords': 'if A then B then C, inevitable, chain reaction',
      'example': 'If we allow students to use phones in class, soon they\'ll be cheating on every test, and then academic standards will collapse entirely!',
    },
    {
      'name': 'Hasty Generalization',
      'description': 'Making a broad claim based on insufficient evidence or a small sample size.',
      'keywords': 'all, every, small sample, few instances',
      'example': 'I met two rude people from that city, so everyone from there must be rude.',
    },
    {
      'name': 'Red Herring',
      'description': 'Introducing an irrelevant topic to divert attention from the original issue.',
      'keywords': 'distraction, irrelevant, change topic',
      'example': 'When asked about his tax evasion, the politician started talking about the need to improve the economy.',
    },
    {
      'name': 'Bandwagon (Ad Populum)',
      'description': 'Claiming something is true because many people believe it or do it.',
      'keywords': 'everyone does it, popular, majority believes',
      'example': 'Everyone is buying the new smartphone, so it must be the best one.',
    },
    {
      'name': 'Appeal to Emotion',
      'description': 'Manipulating an emotional response in place of a valid or compelling argument.',
      'keywords': 'pity, fear, anger, sympathy',
      'example': 'Please don\'t give me a failing grade; my parents will be so disappointed!',
    },
    {
      'name': 'Circular Reasoning (Begging the Question)',
      'description': 'An argument that assumes the truth of the conclusion in its premises.',
      'keywords': 'because, therefore, assumes, rephrasing',
      'example': 'The Bible is true because it says so, and what it says is true.',
    },
  ];

  void _identifyFallacy() {
    setState(() {
      if (_argumentText.isEmpty) {
        _identifiedFallacy = 'No argument entered.';
        _explanation = 'Please type an argument into the text box to identify potential fallacies.';
        return;
      }

      String lowerCaseArgument = _argumentText.toLowerCase();
      bool found = false;

      for (var fallacy in _fallacies) {
        List<String> keywords = fallacy['keywords']!.split(', ').map((e) => e.trim()).toList();
        bool matchesKeywords = keywords.any((keyword) => lowerCaseArgument.contains(keyword));

        if (matchesKeywords) {
          _identifiedFallacy = 'Potential Fallacy: ${fallacy['name']}';
          _explanation = '${fallacy['description']}\n\nExample: "${fallacy['example']}"';
          found = true;
          break;
        }
      }

      if (!found) {
        _identifiedFallacy = 'No obvious fallacy identified.';
        _explanation = 'This argument does not clearly match any common informal fallacies based on keywords. '
            'However, identifying fallacies often requires deeper contextual understanding.';
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text(
            'Fallacy Identifier',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.primary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 20),
          TextField(
            onChanged: (value) {
              setState(() {
                _argumentText = value;
              });
            },
            maxLines: 5,
            decoration: InputDecoration(
              labelText: 'Enter an argument to analyze',
              hintText: 'e.g., "You can\'t trust his economic plan; he\'s a terrible public speaker."',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              filled: true,
              fillColor: Theme.of(context).cardColor,
            ),
          ),
          const SizedBox(height: 20),
          AnimatedButton(
            onTap: _identifyFallacy,
            label: 'Identify Fallacy',
            color: Theme.of(context).colorScheme.secondary,
            labelColor: Theme.of(context).colorScheme.onSecondary,
          ),
          const SizedBox(height: 20),
          Expanded(
            child: SingleChildScrollView(
              child: Card(
                elevation: 4,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                color: Theme.of(context).colorScheme.surfaceVariant,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Identified Fallacy:',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                      const SizedBox(height: 10),
                      Text(
                        _identifiedFallacy.isEmpty ? 'Enter an argument and click "Identify Fallacy" to see the result.' : _identifiedFallacy,
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                      if (_explanation.isNotEmpty) ...[
                        const SizedBox(height: 10),
                        Text(
                          _explanation,
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Theme.of(context).colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
