import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_markdown/flutter_markdown.dart';

class InteractiveRealWorldProblemSolver extends ConsumerStatefulWidget {
  const InteractiveRealWorldProblemSolver({super.key});

  @override
  ConsumerState<InteractiveRealWorldProblemSolver> createState() => _InteractiveRealWorldProblemSolverState();
}

class _InteractiveRealWorldProblemSolverState extends ConsumerState<InteractiveRealWorldProblemSolver> {
  String _problemDescription = '';
  String _decomposition = '';
  String _abstraction = '';
  String _algorithm = '';
  String _solution = '';

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Interactive Real-World Problem Solver',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 20),
          const MarkdownBody(
            data: '''
            This interactive tool helps you practice applying computational thinking to real-world problems.
            Enter a problem description, then break it down into its components using decomposition,
            identify key aspects through abstraction, design an algorithm, and finally formulate a solution.
            ''',
          ),
          const SizedBox(height: 20),
          _buildSectionTitle('1. Problem Description'),
          TextField(
            maxLines: 3,
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
              hintText: 'Describe a real-world problem (e.g., "Optimizing delivery routes for a logistics company")',
            ),
            onChanged: (value) {
              setState(() {
                _problemDescription = value;
              });
            },
          ),
          const SizedBox(height: 20),
          _buildSectionTitle('2. Decomposition (Break down the problem)'),
          TextField(
            maxLines: 5,
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
              hintText: 'How can you break this problem into smaller, manageable parts?',
            ),
            onChanged: (value) {
              setState(() {
                _decomposition = value;
              });
            },
          ),
          const SizedBox(height: 20),
          _buildSectionTitle('3. Abstraction (Identify key concepts and ignore irrelevant details)'),
          TextField(
            maxLines: 5,
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
              hintText: 'What are the essential elements and relationships? What can be simplified or generalized?',
            ),
            onChanged: (value) {
              setState(() {
                _abstraction = value;
              });
            },
          ),
          const SizedBox(height: 20),
          _buildSectionTitle('4. Algorithm Design (Outline the steps to solve the problem)'),
          TextField(
            maxLines: 7,
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
              hintText: 'What is the step-by-step process to solve the problem using the decomposed and abstracted elements?',
            ),
            onChanged: (value) {
              setState(() {
                _algorithm = value;
              });
            },
          ),
          const SizedBox(height: 20),
          _buildSectionTitle('5. Solution (Formulate the final solution)'),
          TextField(
            maxLines: 5,
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
              hintText: 'Based on your algorithm, what is the proposed solution to the original problem?',
            ),
            onChanged: (value) {
              setState(() {
                _solution = value;
              });
            },
          ),
          const SizedBox(height: 30),
          _buildSummaryCard(context),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Text(
        title,
        style: const TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
      ),
    );
  }

  Widget _buildSummaryCard(BuildContext context) {
    return Card(
      elevation: 4,
      margin: const EdgeInsets.symmetric(vertical: 20),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Your Computational Thinking Process:',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold, color: Colors.blueAccent),
            ),
            const Divider(),
            _buildSummaryItem('Problem:', _problemDescription),
            _buildSummaryItem('Decomposition:', _decomposition),
            _buildSummaryItem('Abstraction:', _abstraction),
            _buildSummaryItem('Algorithm:', _algorithm),
            _buildSummaryItem('Solution:', _solution),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryItem(String title, String content) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 4),
          Text(
            content.isNotEmpty ? content : 'Not yet entered.',
            style: TextStyle(fontSize: 16, fontStyle: content.isEmpty ? FontStyle.italic : FontStyle.normal),
          ),
        ],
      ),
    );
  }
}
