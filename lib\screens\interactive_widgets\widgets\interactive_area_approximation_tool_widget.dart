import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that allows users to approximate the area under a curve using rectangles.
class InteractiveAreaApproximationToolWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveAreaApproximationToolWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  /// Factory constructor to create the widget from JSON data
  factory InteractiveAreaApproximationToolWidget.fromData(Map<String, dynamic> data) {
    return InteractiveAreaApproximationToolWidget(
      data: data,
    );
  }

  @override
  State<InteractiveAreaApproximationToolWidget> createState() => _InteractiveAreaApproximationToolWidgetState();
}

class _InteractiveAreaApproximationToolWidgetState extends State<InteractiveAreaApproximationToolWidget> {
  late double _minX;
  late double _maxX;
  late int _numberOfRectangles;

  // Function to approximate area for (e.g., f(x) = x^2)
  double _function(double x) {
    return x * x;
  }

  // Colors
  late Color _primaryColor;
  late Color _secondaryColor;
  late Color _backgroundColor;
  late Color _textColor;

  @override
  void initState() {
    super.initState();
    _minX = widget.data['min_x']?.toDouble() ?? 0.0;
    _maxX = widget.data['max_x']?.toDouble() ?? 5.0;
    _numberOfRectangles = widget.data['initial_rectangles'] ?? 5;

    _primaryColor = _parseColor(widget.data['primary_color']) ?? Colors.blue;
    _secondaryColor = _parseColor(widget.data['secondary_color']) ?? Colors.orange;
    _backgroundColor = _parseColor(widget.data['background_color']) ?? Colors.white;
    _textColor = _parseColor(widget.data['text_color']) ?? Colors.black87;
  }

  Color? _parseColor(String? hexString) {
    if (hexString == null) return null;
    hexString = hexString.replaceAll('#', '');
    if (hexString.length == 6) {
      hexString = 'FF$hexString';
    }
    try {
      return Color(int.parse(hexString, radix: 16));
    } catch (e) {
      return null;
    }
  }

  double _calculateApproximatedArea() {
    if (_numberOfRectangles <= 0 || _maxX <= _minX) return 0.0;

    final double deltaX = (_maxX - _minX) / _numberOfRectangles;
    double approximatedArea = 0.0;

    // Using Right Riemann Sum for simplicity
    for (int i = 1; i <= _numberOfRectangles; i++) {
      final double x = _minX + i * deltaX;
      approximatedArea += _function(x) * deltaX;
    }
    return approximatedArea;
  }

  @override
  Widget build(BuildContext context) {
    final approximatedArea = _calculateApproximatedArea();

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _backgroundColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.data['title'] ?? 'Area Approximation Tool',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),
          const SizedBox(height: 8),
          if (widget.data['description'] != null)
            Text(
              widget.data['description'],
              style: TextStyle(
                fontSize: 14,
                color: _textColor.withOpacity(0.8),
              ),
            ),
          const SizedBox(height: 16),

          Text(
            'Function: f(x) = x²',
            style: TextStyle(color: _textColor, fontSize: 16, fontStyle: FontStyle.italic),
          ),
          Text(
            'Interval: [${_minX.toStringAsFixed(0)}, ${_maxX.toStringAsFixed(0)}]',
            style: TextStyle(color: _textColor, fontSize: 16, fontStyle: FontStyle.italic),
          ),
          const SizedBox(height: 16),

          Text(
            'Number of Rectangles: ${_numberOfRectangles}',
            style: TextStyle(color: _primaryColor, fontSize: 16, fontWeight: FontWeight.bold),
          ),
          Slider(
            value: _numberOfRectangles.toDouble(),
            min: 1,
            max: 50,
            divisions: 49,
            label: _numberOfRectangles.toString(),
            activeColor: _primaryColor,
            onChanged: (value) {
              setState(() {
                _numberOfRectangles = value.toInt();
              });
            },
          ),
          const SizedBox(height: 16),

          Center(
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: _primaryColor),
              ),
              child: Text(
                'Approximated Area: ${approximatedArea.toStringAsFixed(2)}',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: _primaryColor,
                ),
              ),
            ),
          ),
          const SizedBox(height: 16),

          Container(
            height: 250,
            child: CustomPaint(
              painter: AreaApproximationPainter(
                function: _function,
                minX: _minX,
                maxX: _maxX,
                numberOfRectangles: _numberOfRectangles,
                primaryColor: _primaryColor,
                secondaryColor: _secondaryColor,
                textColor: _textColor,
              ),
              child: Container(),
            ),
          ),

          // Widget name tag
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'InteractiveAreaApproximationTool',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }
}

class AreaApproximationPainter extends CustomPainter {
  final Function(double) function;
  final double minX;
  final double maxX;
  final int numberOfRectangles;
  final Color primaryColor;
  final Color secondaryColor;
  final Color textColor;

  AreaApproximationPainter({
    required this.function,
    required this.minX,
    required this.maxX,
    required this.numberOfRectangles,
    required this.primaryColor,
    required this.secondaryColor,
    required this.textColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint();
    final textPainter = TextPainter(textDirection: TextDirection.ltr);
    final textStyle = TextStyle(color: textColor, fontSize: 10);

    // Define plot area padding
    const double padding = 30.0;
    final double plotWidth = size.width - 2 * padding;
    final double plotHeight = size.height - 2 * padding;

    // Determine y range for plotting
    final double minY = 0.0;
    final double maxY = function(maxX) * 1.2; // Max value of x^2 on [0,5] is 25, so 30

    // Scale factors
    final double xScale = plotWidth / (maxX - minX);
    final double yScale = plotHeight / (maxY - minY);

    // Helper to convert plot coordinates to canvas coordinates
    Offset toCanvas(double x, double y) {
      final canvasX = padding + (x - minX) * xScale;
      final canvasY = size.height - padding - (y - minY) * yScale;
      return Offset(canvasX, canvasY);
    }

    // Draw axes
    paint.color = Colors.grey;
    paint.strokeWidth = 1.0;
    // X-axis
    canvas.drawLine(toCanvas(minX, 0), toCanvas(maxX, 0), paint);
    // Y-axis
    canvas.drawLine(toCanvas(0, minY), toCanvas(0, maxY), paint);

    // Draw function curve
    paint.color = primaryColor;
    paint.style = PaintingStyle.stroke;
    paint.strokeWidth = 2.0;

    final path = Path();
    bool firstPoint = true;
    for (double x = minX; x <= maxX; x += (maxX - minX) / 100) {
      final y = function(x);
      final point = toCanvas(x, y);
      if (firstPoint) {
        path.moveTo(point.dx, point.dy);
        firstPoint = false;
      } else {
        path.lineTo(point.dx, point.dy);
      }
    }
    canvas.drawPath(path, paint);

    // Draw rectangles
    final double deltaX = (maxX - minX) / numberOfRectangles;
    for (int i = 0; i < numberOfRectangles; i++) {
      final double x = minX + (i + 1) * deltaX; // Right Riemann Sum
      final double rectHeight = function(x);
      
      final rectLeft = toCanvas(minX + i * deltaX, 0).dx;
      final rectRight = toCanvas(minX + (i + 1) * deltaX, 0).dx;
      final rectTop = toCanvas(0, rectHeight).dy;
      final rectBottom = toCanvas(0, 0).dy;

      paint.color = secondaryColor.withOpacity(0.4);
      paint.style = PaintingStyle.fill;
      canvas.drawRect(Rect.fromLTRB(rectLeft, rectTop, rectRight, rectBottom), paint);

      paint.color = secondaryColor;
      paint.style = PaintingStyle.stroke;
      paint.strokeWidth = 1.0;
      canvas.drawRect(Rect.fromLTRB(rectLeft, rectTop, rectRight, rectBottom), paint);
    }
  }

  @override
  bool shouldRepaint(covariant AreaApproximationPainter oldDelegate) {
    return oldDelegate.minX != minX ||
           oldDelegate.maxX != maxX ||
           oldDelegate.numberOfRectangles != numberOfRectangles ||
           oldDelegate.primaryColor != primaryColor ||
           oldDelegate.secondaryColor != secondaryColor ||
           oldDelegate.textColor != textColor;
  }
}
