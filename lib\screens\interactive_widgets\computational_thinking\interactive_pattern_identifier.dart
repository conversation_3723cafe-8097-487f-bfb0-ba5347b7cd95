import 'package:flutter/material.dart';
import 'dart:math';

class InteractivePatternIdentifier extends StatefulWidget {
  const InteractivePatternIdentifier({super.key});

  @override
  State<InteractivePatternIdentifier> createState() => _InteractivePatternIdentifierState();
}

class _InteractivePatternIdentifierState extends State<InteractivePatternIdentifier> {
  List<dynamic> _currentSequence = [];
  String _correctRule = '';
  final TextEditingController _ruleInputController = TextEditingController();
  String? _feedbackMessage;
  bool _isCorrect = false;

  final List<Map<String, dynamic>> _patterns = [
    {
      'sequence': [2, 4, 6, 8, 10],
      'rule': 'Arithmetic: Add 2',
    },
    {
      'sequence': [1, 3, 9, 27, 81],
      'rule': 'Geometric: Multiply by 3',
    },
    {
      'sequence': [1, 4, 9, 16, 25],
      'rule': 'Squares: n^2',
    },
    {
      'sequence': ['A', 'B', 'C', 'D', 'E'],
      'rule': 'Alphabetical order',
    },
    {
      'sequence': [1, 1, 2, 3, 5, 8],
      'rule': 'Fibonacci sequence',
    },
  ];

  @override
  void initState() {
    super.initState();
    _generateNewPattern();
  }

  void _generateNewPattern() {
    final random = Random();
    final patternData = _patterns[random.nextInt(_patterns.length)];
    setState(() {
      _currentSequence = List<dynamic>.from(patternData['sequence']);
      _correctRule = patternData['rule'];
      _ruleInputController.clear();
      _feedbackMessage = null;
      _isCorrect = false;
    });
  }

  void _checkAnswer() {
    setState(() {
      if (_ruleInputController.text.trim().toLowerCase() == _correctRule.toLowerCase()) {
        _feedbackMessage = 'Correct! You identified the pattern.';
        _isCorrect = true;
      } else {
        _feedbackMessage = 'Incorrect. The correct rule is: $_correctRule';
        _isCorrect = false;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Interactive Pattern Identifier',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            const Text(
              'Identify the rule for the following sequence:',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 10),
            Text(
              _currentSequence.map((e) => e.toString()).join(', '),
              style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold, color: Colors.blue),
            ),
            const SizedBox(height: 20),
            TextField(
              controller: _ruleInputController,
              decoration: InputDecoration(
                labelText: 'Enter the pattern rule (e.g., "Add 2", "Multiply by 3")',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 10),
            ElevatedButton(
              onPressed: _checkAnswer,
              child: const Text('Check Rule'),
            ),
            const SizedBox(height: 10),
            if (_feedbackMessage != null)
              Text(
                _feedbackMessage!,
                style: TextStyle(
                  color: _isCorrect ? Colors.green : Colors.red,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: _generateNewPattern,
              child: const Text('New Pattern'),
            ),
          ],
        ),
      ),
    );
  }
}
