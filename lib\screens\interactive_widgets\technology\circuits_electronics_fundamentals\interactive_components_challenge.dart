import 'package:flutter/material.dart';
import 'package:resonance_app/widgets/interactive_widget_scaffold.dart';
import 'dart:math';

class InteractiveComponentsChallenge extends StatefulWidget {
  const InteractiveComponentsChallenge({super.key});

  @override
  State<InteractiveComponentsChallenge> createState() => _InteractiveComponentsChallengeState();
}

class _InteractiveComponentsChallengeState extends State<InteractiveComponentsChallenge> {
  final Random _random = Random();
  String _componentType = '';
  String _question = '';
  String _correctAnswer = '';
  TextEditingController _answerController = TextEditingController();
  String _feedback = '';
  int _score = 0;
  int _questionCount = 0;

  @override
  void initState() {
    super.initState();
    _generateQuestion();
  }

  void _generateQuestion() {
    _questionCount++;
    _answerController.clear();
    _feedback = '';

    final componentTypes = ['Resistor', 'Capacitor', 'Inductor', 'Diode', 'Transistor'];
    _componentType = componentTypes[_random.nextInt(componentTypes.length)];

    switch (_componentType) {
      case 'Resistor':
        double voltage = _random.nextDouble() * 10 + 1; // 1 to 11V
        double current = _random.nextDouble() * 0.1 + 0.01; // 0.01 to 0.11A
        _question = 'If Voltage (V) = ${voltage.toStringAsFixed(2)}V and Current (I) = ${current.toStringAsFixed(2)}A, what is the Resistance (R)? (Ohms)';
        _correctAnswer = (voltage / current).toStringAsFixed(2);
        break;
      case 'Capacitor':
        double capacitance = _random.nextDouble() * 5 + 0.5; // 0.5 to 5.5F
        double voltage = _random.nextDouble() * 10 + 1; // 1 to 11V
        _question = 'If Capacitance (C) = ${capacitance.toStringAsFixed(2)}F and Voltage (V) = ${voltage.toStringAsFixed(2)}V, what is the Charge (Q)? (Coulombs)';
        _correctAnswer = (capacitance * voltage).toStringAsFixed(2);
        break;
      case 'Inductor':
        double inductance = _random.nextDouble() * 5 + 0.5; // 0.5 to 5.5H
        double currentChangeRate = _random.nextDouble() * 2 - 1; // -1 to 1 A/s
        _question = 'If Inductance (L) = ${inductance.toStringAsFixed(2)}H and Rate of Current Change (dI/dt) = ${currentChangeRate.toStringAsFixed(2)} A/s, what is the Induced Voltage (V)? (Volts)';
        _correctAnswer = (-inductance * currentChangeRate).toStringAsFixed(2);
        break;
      case 'Diode':
        _question = 'Which component allows current to flow primarily in one direction?';
        _correctAnswer = 'Diode';
        break;
      case 'Transistor':
        _question = 'Which component can act as an electronic switch or amplifier?';
        _correctAnswer = 'Transistor';
        break;
    }
  }

  void _checkAnswer() {
    String userAnswer = _answerController.text.trim();
    if (_componentType == 'Diode' || _componentType == 'Transistor') {
      if (userAnswer.toLowerCase() == _correctAnswer.toLowerCase()) {
        setState(() {
          _feedback = 'Correct!';
          _score++;
        });
      } else {
        setState(() {
          _feedback = 'Incorrect. The correct answer was $_correctAnswer.';
        });
      }
    } else {
      // For numerical answers, allow for some tolerance
      double? userNum = double.tryParse(userAnswer);
      double correctNum = double.parse(_correctAnswer);
      if (userNum != null && (userNum - correctNum).abs() < 0.01) {
        setState(() {
          _feedback = 'Correct!';
          _score++;
        });
      } else {
        setState(() {
          _feedback = 'Incorrect. The correct answer was $_correctAnswer.';
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return InteractiveWidgetScaffold(
      title: 'Interactive Components Challenge',
      description: 'Test your knowledge of core electrical components.',
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text(
              'Question $_questionCount:',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 16.0),
            Text(
              _question,
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 24.0),
            TextField(
              controller: _answerController,
              keyboardType: TextInputType.text,
              decoration: const InputDecoration(
                labelText: 'Your Answer',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 24.0),
            ElevatedButton(
              onPressed: _checkAnswer,
              child: const Text('Submit Answer'),
            ),
            const SizedBox(height: 16.0),
            Text(
              _feedback,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: _feedback.startsWith('Correct') ? Colors.green : Colors.red,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24.0),
            ElevatedButton(
              onPressed: _generateQuestion,
              child: const Text('Next Question'),
            ),
            const SizedBox(height: 16.0),
            Text(
              'Score: $_score / $_questionCount',
              style: Theme.of(context).textTheme.headlineSmall,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
