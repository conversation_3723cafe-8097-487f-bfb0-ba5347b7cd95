import 'package:flutter/material.dart';
import '../models/course_models.dart';
import 'json_course_content_service.dart';

/// Service for managing courses and user data
class CourseService {
  static final CourseService _instance = CourseService._internal();

  // Singleton instance
  factory CourseService() {
    return _instance;
  }

  CourseService._internal();

  // Current user
  User? _currentUser;

  // Course data
  List<CourseCategory> _categories = [];
  List<Course> _courses = [];

  // Course content service
  final JsonCourseContentService _contentService = JsonCourseContentService();

  // Getters
  User? get currentUser => _currentUser;
  List<CourseCategory> get categories => _categories;
  List<Course> get courses => _courses;

  // Initialize the service with sample data
  Future<void> initialize() async {
    await _loadSampleData();

    // Create a guest user if no user exists
    _currentUser ??= User.guest();

    // Special handling for Mathematical Thinking course
    Course? mathThinkingCourseMetadata;
    try {
      mathThinkingCourseMetadata = _courses.firstWhere(
        (c) => c.id == 'mathematical_thinking',
      );

      // Load modules for Mathematical Thinking course
      final List<Module> loadedModules = await _contentService
          .loadModulesByName('maths', 'mathematical_thinking');

      if (loadedModules.isNotEmpty) {
        final fullCourse = Course(
          id: mathThinkingCourseMetadata.id,
          title: mathThinkingCourseMetadata.title,
          description: mathThinkingCourseMetadata.description,
          categoryId: mathThinkingCourseMetadata.categoryId,
          thumbnailPath: mathThinkingCourseMetadata.thumbnailPath,
          difficulty: mathThinkingCourseMetadata.difficulty,
          prerequisites: mathThinkingCourseMetadata.prerequisites,
          modules: loadedModules, // Use the modules from loadModulesByName
          isEncrypted: mathThinkingCourseMetadata.isEncrypted,
          encryptionKey: mathThinkingCourseMetadata.encryptionKey,
        );

        final index = _courses.indexWhere(
          (c) => c.id == 'mathematical_thinking',
        );
        if (index >= 0) {
          _courses[index] = fullCourse;
          debugPrint(
            'Replaced Mathematical Thinking course with fully loaded version',
          );
        }
      }
    } catch (e) {
      debugPrint('Error loading Mathematical Thinking course: $e');
    }
    // End of special handling for Mathematical Thinking

    // Special handling for Equations and Algebra course
    Course? algebraCourseMetadata;
    try {
      // Try to find the course in the loaded courses
      try {
        algebraCourseMetadata = _courses.firstWhere(
          (c) => c.id == 'equations-and-algebra',
        );
        debugPrint('Found Equations and Algebra course in loaded courses');
      } catch (e) {
        // If not found, try to load it directly
        algebraCourseMetadata = await _contentService.loadCourse(
          'maths',
          'equations-and-algebra',
        );
        debugPrint('Loaded Equations and Algebra course directly from JSON');

        // Add it to the courses list if it was loaded successfully
        if (algebraCourseMetadata != null) {
          _courses.add(algebraCourseMetadata);
        }
      }

      if (algebraCourseMetadata != null) {
        // Load modules for Equations and Algebra course
        final List<Module> loadedModules = [];

        // Load each module directly
        final moduleIds = [
          'language-of-variables',
          'solving-one-step-equations',
          'tackling-two-step-equations',
          'introduction-to-inequalities',
          'exploring-algebraic-relationships',
        ];

        for (final moduleId in moduleIds) {
          final module = await _contentService.loadModule(
            'maths',
            'equations-and-algebra',
            moduleId,
          );

          if (module != null) {
            loadedModules.add(module);
            debugPrint('Loaded module: ${module.title}');
          }
        }

        if (loadedModules.isNotEmpty) {
          final fullCourse = Course(
            id: algebraCourseMetadata.id,
            title: algebraCourseMetadata.title,
            description: algebraCourseMetadata.description,
            categoryId: algebraCourseMetadata.categoryId,
            thumbnailPath: algebraCourseMetadata.thumbnailPath,
            difficulty: algebraCourseMetadata.difficulty,
            prerequisites: algebraCourseMetadata.prerequisites,
            modules: loadedModules,
            isEncrypted: algebraCourseMetadata.isEncrypted,
            encryptionKey: algebraCourseMetadata.encryptionKey,
          );

          // Replace or add the course
          final index = _courses.indexWhere(
            (c) => c.id == 'equations-and-algebra',
          );

          if (index >= 0) {
            _courses[index] = fullCourse;
            debugPrint(
              'Replaced Equations and Algebra course with fully loaded version',
            );
          } else {
            _courses.add(fullCourse);
            debugPrint('Added fully loaded Equations and Algebra course');
          }
        }
      }
    } catch (e) {
      debugPrint('Error loading Equations and Algebra course: $e');
      debugPrint('Error details: ${e.toString()}');
    }
    // End of special handling for Equations and Algebra

    // Special handling for Scientific Thinking course
    Course? scientificThinkingCourseMetadata;
    try {
      // Try to find the course in the loaded courses
      try {
        scientificThinkingCourseMetadata = _courses.firstWhere(
          (c) => c.id == 'scientific_thinking',
        );
        debugPrint('Found Scientific Thinking course in loaded courses');
      } catch (e) {
        // If not found, try to load it directly
        scientificThinkingCourseMetadata = await _contentService.loadCourse(
          'science',
          'scientific_thinking',
        );
        debugPrint('Loaded Scientific Thinking course directly from JSON');

        // Add it to the courses list if it was loaded successfully
        if (scientificThinkingCourseMetadata != null) {
          _courses.add(scientificThinkingCourseMetadata);
        }
      }

      if (scientificThinkingCourseMetadata != null) {
        // Load modules for Scientific Thinking course
        final List<Module> loadedModules = [];

        // Load each module directly
        final moduleIds = [
          'foundation-of-inquiry',
          'observing-recording-interpreting-data',
          'scientific-models-and-theories',
          'scientific-reasoning-argumentation',
          'frontiers-of-scientific-inquiry',
        ];

        for (final moduleId in moduleIds) {
          final module = await _contentService.loadModule(
            'science',
            'scientific_thinking',
            moduleId,
          );

          if (module != null) {
            loadedModules.add(module);
            debugPrint('Loaded module: ${module.title}');
          }
        }

        if (loadedModules.isNotEmpty) {
          final fullCourse = Course(
            id: scientificThinkingCourseMetadata.id,
            title: scientificThinkingCourseMetadata.title,
            description: scientificThinkingCourseMetadata.description,
            categoryId: scientificThinkingCourseMetadata.categoryId,
            thumbnailPath: scientificThinkingCourseMetadata.thumbnailPath,
            difficulty: scientificThinkingCourseMetadata.difficulty,
            prerequisites: scientificThinkingCourseMetadata.prerequisites,
            modules: loadedModules,
            isEncrypted: scientificThinkingCourseMetadata.isEncrypted,
            encryptionKey: scientificThinkingCourseMetadata.encryptionKey,
          );

          // Replace or add the course
          final index = _courses.indexWhere(
            (c) => c.id == 'scientific_thinking',
          );

          if (index >= 0) {
            _courses[index] = fullCourse;
            debugPrint(
              'Replaced Scientific Thinking course with fully loaded version',
            );
          } else {
            _courses.add(fullCourse);
            debugPrint('Added fully loaded Scientific Thinking course');
          }
        }
      }
    } catch (e) {
      debugPrint('Error loading Scientific Thinking course: $e');
      debugPrint('Error details: ${e.toString()}');
    }
    // End of special handling for Scientific Thinking
  }

  // Load sample course data
  Future<void> _loadSampleData() async {
    // Clear any existing cache to ensure fresh data
    _contentService.clearCache();

    // Load categories
    _categories = [
      CourseCategory(
        id: "maths",
        name: "Maths",
        description: "Explore the world of numbers and patterns",
        iconPath: "assets/images/maths_icon.svg",
        color: "#4285F4", // Blue
      ),
      CourseCategory(
        id: "science",
        name: "Science",
        description: "Discover how the world works",
        iconPath: "assets/images/science_icon.svg",
        color: "#FFC107", // Amber
      ),
      CourseCategory(
        id: "computer_science",
        name: "Computer Science",
        description: "Learn programming and computational thinking",
        iconPath: "assets/images/cs_icon.svg",
        color: "#9C27B0", // Purple
      ),
      CourseCategory(
        id: "reasoning",
        name: "Reasoning",
        description: "Develop critical thinking and problem-solving skills",
        iconPath: "assets/images/reasoning_icon.svg",
        color: "#FF5722", // Deep Orange
      ),
      CourseCategory(
        id: "technology",
        name: "Technology",
        description: "Explore modern technologies and their applications",
        iconPath: "assets/images/technology_icon.svg",
        color: "#00BCD4", // Cyan
      ),
      CourseCategory(
        id: "puzzles",
        name: "Puzzles",
        description: "Challenge your mind with engaging puzzles",
        iconPath: "assets/images/puzzles_icon.svg",
        color: "#8BC34A", // Light Green
      ),
      CourseCategory(
        id: "curiosity_corner",
        name: "Curiosity Corner",
        description: "Explore fascinating topics across various disciplines",
        iconPath: "assets/images/curiosity_icon.svg",
        color: "#FF9800", // Orange
      ),
      CourseCategory(
        id: "coming_soon",
        name: "Coming Soon",
        description:
            "More categories like Music, Business, Psychology coming soon",
        iconPath: "assets/images/coming_soon_icon.svg",
        color: "#9E9E9E", // Grey
      ),
    ];

    // We'll load courses from each category directory
    try {
      // Load courses from each category
      for (final category in _categories) {
        debugPrint('Loading courses for category: ${category.id}');
        final categoryCourses = await _contentService.getCoursesByCategory(
          category.id,
        );
        debugPrint(
          'Loaded ${categoryCourses.length} courses for category ${category.id}:',
        );
        for (final course in categoryCourses) {
          debugPrint('  - ${course.title} (${course.id})');
        }
        _courses.addAll(categoryCourses);
      }

      // Debug: Print all loaded courses
      debugPrint('Total loaded courses: ${_courses.length}');
      for (final course in _courses) {
        debugPrint(
          'Loaded course: ${course.title} (${course.id}) in category ${course.categoryId}',
        );
      }
    } catch (e) {
      debugPrint('Error loading courses from JSON: $e');
    }

    // If no courses were loaded from JSON, create placeholder courses
    if (_courses.isEmpty) {
      _createPlaceholderCourses();
    }

    // Pre-load all modules for all courses to ensure they're available
    for (final course in List<Course>.from(_courses)) {
      try {
        final fullCourse = await _contentService.getFullCourse(
          course.categoryId,
          course.id,
        );
        if (fullCourse != null) {
          // Replace the course with the fully loaded version
          final index = _courses.indexWhere((c) => c.id == course.id);
          if (index >= 0) {
            _courses[index] = fullCourse;
          }
        }
      } catch (e) {
        debugPrint('Error pre-loading modules for course ${course.id}: $e');
      }
    }
  }

  // Create placeholder courses if no JSON courses are found
  void _createPlaceholderCourses() {
    _courses = [
      // Math placeholder
      Course(
        id: 'mathematical_thinking',
        title: 'Mathematical Thinking',
        description:
            'Develop core logic and robust problem-solving approaches for mathematical challenges.',
        categoryId: 'maths',
        thumbnailPath: 'assets/images/math_thinking.png',
        difficulty: 'Beginner',
        modules: [],
      ),
      // Science placeholder
      Course(
        id: 'scientific_thinking',
        title: 'Scientific Thinking',
        description:
            'Learn the powerful methodology scientists use to explore and understand reality.',
        categoryId: 'science',
        thumbnailPath: 'assets/images/science_thinking.png',
        difficulty: 'Beginner',
        modules: [],
      ),
    ];
  }

  // Get courses by category
  List<Course> getCoursesByCategory(String categoryId) {
    return _courses.where((course) => course.categoryId == categoryId).toList();
  }

  // Get course by ID
  Future<Course?> getCourseById(String courseId) async {
    debugPrint('[CourseService.getCourseById] Getting course: $courseId');

    // First check if the course is already loaded
    try {
      final existingCourse = _courses.firstWhere(
        (course) => course.id == courseId,
      );

      debugPrint(
        '[CourseService.getCourseById] Found existing course: ${existingCourse.title} with ${existingCourse.modules.length} modules',
      );

      // Check if this course already has fully loaded modules with lessons
      int totalLessons = existingCourse.modules.fold(
        0,
        (sum, module) => sum + module.lessons.length,
      );

      debugPrint(
        '[CourseService.getCourseById] Existing course has $totalLessons total lessons',
      );

      if (totalLessons > 0) {
        debugPrint(
          '[CourseService.getCourseById] Returning existing course with lessons',
        );
        return existingCourse;
      }

      // If no lessons are loaded, try to get the full course
      // First try clearing the cache
      debugPrint(
        '[CourseService.getCourseById] Clearing cache and trying to get full course',
      );
      _contentService.clearCache();

      final fullCourse = await _contentService.getFullCourse(
        existingCourse.categoryId,
        courseId,
      );
      if (fullCourse != null) {
        debugPrint(
          '[CourseService.getCourseById] Successfully loaded full course: ${fullCourse.title} with ${fullCourse.modules.length} modules',
        );

        int fullCourseLessons = fullCourse.modules.fold(
          0,
          (sum, module) => sum + module.lessons.length,
        );
        debugPrint(
          '[CourseService.getCourseById] Full course has $fullCourseLessons total lessons',
        );

        // Replace the existing course with the full one
        final index = _courses.indexWhere((course) => course.id == courseId);
        _courses[index] = fullCourse;
        return fullCourse;
      }

      // If still no lessons, try direct module loading
      debugPrint('[CourseService.getCourseById] Trying direct module loading');
      final directModules = await _contentService.loadModulesDirect(
        existingCourse.categoryId,
        courseId,
      );

      debugPrint(
        '[CourseService.getCourseById] Direct module loading returned ${directModules.length} modules',
      );
      for (var module in directModules) {
        debugPrint(
          '[CourseService.getCourseById] Direct loaded module: ${module.title} with ${module.lessons.length} lessons',
        );
      }

      if (directModules.isNotEmpty) {
        // Create a new course with the directly loaded modules
        final directCourse = Course(
          id: existingCourse.id,
          title: existingCourse.title,
          description: existingCourse.description,
          categoryId: existingCourse.categoryId,
          thumbnailPath: existingCourse.thumbnailPath,
          difficulty: existingCourse.difficulty,
          prerequisites: existingCourse.prerequisites,
          modules: directModules,
          isEncrypted: existingCourse.isEncrypted,
          encryptionKey: existingCourse.encryptionKey,
        );

        int directCourseLessons = directCourse.modules.fold(
          0,
          (sum, module) => sum + module.lessons.length,
        );
        debugPrint(
          '[CourseService.getCourseById] Direct course has $directCourseLessons total lessons',
        );

        // Replace the existing course
        final index = _courses.indexWhere((course) => course.id == courseId);
        _courses[index] = directCourse;
        return directCourse;
      }

      return existingCourse;
    } catch (e) {
      // If not found, try to load it from JSON
      // We need to determine the category ID from the course ID
      for (final category in _categories) {
        // Try to load the full course directly
        final fullCourse = await _contentService.getFullCourse(
          category.id,
          courseId,
        );
        if (fullCourse != null) {
          // Add to our list if it was loaded successfully
          _courses.add(fullCourse);
          return fullCourse;
        }

        // Try direct module loading
        final directModules = await _contentService.loadModulesDirect(
          category.id,
          courseId,
        );

        if (directModules.isNotEmpty) {
          // First load just the course metadata
          final courseMetadata = await _contentService.loadCourse(
            category.id,
            courseId,
          );
          if (courseMetadata != null) {
            // Create a new course with the directly loaded modules
            final directCourse = Course(
              id: courseMetadata.id,
              title: courseMetadata.title,
              description: courseMetadata.description,
              categoryId: courseMetadata.categoryId,
              thumbnailPath: courseMetadata.thumbnailPath,
              difficulty: courseMetadata.difficulty,
              prerequisites: courseMetadata.prerequisites,
              modules: directModules,
              isEncrypted: courseMetadata.isEncrypted,
              encryptionKey: courseMetadata.encryptionKey,
            );

            // Add to our list
            _courses.add(directCourse);
            return directCourse;
          }
        }

        // If full course loading fails, try just the course metadata
        final course = await _contentService.loadCourse(category.id, courseId);
        if (course != null) {
          // Add to our list if it was loaded successfully
          _courses.add(course);
          return course;
        }
      }

      return null;
    }
  }

  // Get module by ID from a course
  Future<Module?> getModuleById(Course course, String moduleId) async {
    // First check if the module is already loaded in the course
    try {
      return course.modules.firstWhere((module) => module.id == moduleId);
    } catch (e) {
      // If not found, try to load it from JSON
      return await _contentService.loadModule(
        course.categoryId,
        course.id,
        moduleId,
      );
    }
  }

  // Get category by ID
  CourseCategory? getCategoryById(String categoryId) {
    try {
      return _categories.firstWhere((category) => category.id == categoryId);
    } catch (e) {
      return null;
    }
  }

  // Enroll user in a course
  void enrollUserInCourse(String courseId) {
    if (_currentUser == null) return;

    // Create a new user with the updated enrolled courses
    final updatedEnrolledCourseIds = List<String>.from(
      _currentUser!.enrolledCourseIds,
    );
    if (!updatedEnrolledCourseIds.contains(courseId)) {
      updatedEnrolledCourseIds.add(courseId);
    }

    // Create a new course progress entry if it doesn't exist
    final updatedCourseProgress = Map<String, CourseProgress>.from(
      _currentUser!.courseProgress,
    );
    if (!updatedCourseProgress.containsKey(courseId)) {
      updatedCourseProgress[courseId] = CourseProgress(courseId: courseId);
    }

    // Update the current user
    _currentUser = User(
      id: _currentUser!.id,
      name: _currentUser!.name,
      email: _currentUser!.email,
      isGuest: _currentUser!.isGuest,
      enrolledCourseIds: updatedEnrolledCourseIds,
      courseProgress: updatedCourseProgress,
      dailyGoalMinutes: _currentUser!.dailyGoalMinutes,
    );
  }

  // Get enrolled courses for the current user
  Future<List<Course>> getEnrolledCourses() async {
    if (_currentUser == null) return [];

    final enrolledCourses = <Course>[];

    for (final courseId in _currentUser!.enrolledCourseIds) {
      final course = await getCourseById(courseId);
      if (course != null) {
        enrolledCourses.add(course);
      }
    }

    return enrolledCourses;
  }

  // Get recommended courses based on user preferences
  List<Course> getRecommendedCourses() {
    // For now, just return all courses that the user is not enrolled in
    if (_currentUser == null) return [];

    return _courses
        .where((course) => !_currentUser!.enrolledCourseIds.contains(course.id))
        .toList();
  }

  // Clear the course cache and reload courses
  Future<void> clearCacheAndReloadCourses() async {
    _contentService.clearCache();
    _courses.clear();

    // Reload courses from each category
    for (final category in _categories) {
      final categoryCourses = await _contentService.getCoursesByCategory(
        category.id,
      );
      _courses.addAll(categoryCourses);
    }

    // Special handling for Mathematical Thinking course
    try {
      // First get the course metadata
      final mathThinkingCourse = _courses.firstWhere(
        (course) => course.id == 'mathematical_thinking',
      );

      // Load modules directly
      final modules = await _contentService.loadModulesByName(
        'maths',
        'mathematical_thinking',
      );

      if (modules.isNotEmpty) {
        // Create a new course with the loaded modules
        final fullCourse = Course(
          id: mathThinkingCourse.id,
          title: mathThinkingCourse.title,
          description: mathThinkingCourse.description,
          categoryId: mathThinkingCourse.categoryId,
          thumbnailPath: mathThinkingCourse.thumbnailPath,
          difficulty: mathThinkingCourse.difficulty,
          prerequisites: mathThinkingCourse.prerequisites,
          modules: modules,
          isEncrypted: mathThinkingCourse.isEncrypted,
          encryptionKey: mathThinkingCourse.encryptionKey,
        );

        // Replace the existing course
        final index = _courses.indexWhere(
          (c) => c.id == 'mathematical_thinking',
        );
        if (index >= 0) {
          _courses[index] = fullCourse;
        }
      }
    } catch (e) {
      debugPrint('Error during special handling for Mathematical Thinking: $e');
    }

    // Special handling for Equations and Algebra course
    try {
      // Try to find the course in the loaded courses
      Course? algebraCourse;
      try {
        algebraCourse = _courses.firstWhere(
          (c) => c.id == 'equations-and-algebra',
        );
        debugPrint('Found Equations and Algebra course in loaded courses');
      } catch (e) {
        // If not found, try to load it directly
        algebraCourse = await _contentService.loadCourse(
          'maths',
          'equations-and-algebra',
        );
        debugPrint('Loaded Equations and Algebra course directly from JSON');

        // Add it to the courses list if it was loaded successfully
        if (algebraCourse != null) {
          _courses.add(algebraCourse);
        }
      }

      if (algebraCourse != null) {
        // Load modules for Equations and Algebra course
        final List<Module> loadedModules = [];

        // Load each module directly
        final moduleIds = [
          'language-of-variables',
          'solving-one-step-equations',
          'tackling-two-step-equations',
          'introduction-to-inequalities',
          'exploring-algebraic-relationships',
        ];

        for (final moduleId in moduleIds) {
          final module = await _contentService.loadModule(
            'maths',
            'equations-and-algebra',
            moduleId,
          );

          if (module != null) {
            loadedModules.add(module);
            debugPrint('Loaded module: ${module.title}');
          }
        }

        if (loadedModules.isNotEmpty) {
          final fullCourse = Course(
            id: algebraCourse.id,
            title: algebraCourse.title,
            description: algebraCourse.description,
            categoryId: algebraCourse.categoryId,
            thumbnailPath: algebraCourse.thumbnailPath,
            difficulty: algebraCourse.difficulty,
            prerequisites: algebraCourse.prerequisites,
            modules: loadedModules,
            isEncrypted: algebraCourse.isEncrypted,
            encryptionKey: algebraCourse.encryptionKey,
          );

          // Replace or add the course
          final index = _courses.indexWhere(
            (c) => c.id == 'equations-and-algebra',
          );

          if (index >= 0) {
            _courses[index] = fullCourse;
            debugPrint(
              'Replaced Equations and Algebra course with fully loaded version',
            );
          } else {
            _courses.add(fullCourse);
            debugPrint('Added fully loaded Equations and Algebra course');
          }
        }
      }
    } catch (e) {
      debugPrint('Error during special handling for Equations and Algebra: $e');
      debugPrint('Error details: ${e.toString()}');
    }

    // Special handling for Scientific Thinking course
    try {
      // Try to find the course in the loaded courses
      Course? scientificThinkingCourse;
      try {
        scientificThinkingCourse = _courses.firstWhere(
          (c) => c.id == 'scientific_thinking',
        );
        debugPrint('Found Scientific Thinking course in loaded courses');
      } catch (e) {
        // If not found, try to load it directly
        scientificThinkingCourse = await _contentService.loadCourse(
          'science',
          'scientific_thinking',
        );
        debugPrint('Loaded Scientific Thinking course directly from JSON');

        // Add it to the courses list if it was loaded successfully
        if (scientificThinkingCourse != null) {
          _courses.add(scientificThinkingCourse);
        }
      }

      if (scientificThinkingCourse != null) {
        // Load modules for Scientific Thinking course
        final List<Module> loadedModules = [];

        // Load each module directly
        final moduleIds = [
          'foundation-of-inquiry',
          'observing-recording-interpreting-data',
          'scientific-models-and-theories',
          'scientific-reasoning-argumentation',
          'frontiers-of-scientific-inquiry',
        ];

        for (final moduleId in moduleIds) {
          final module = await _contentService.loadModule(
            'science',
            'scientific_thinking',
            moduleId,
          );

          if (module != null) {
            loadedModules.add(module);
            debugPrint('Loaded module: ${module.title}');
          }
        }

        if (loadedModules.isNotEmpty) {
          final fullCourse = Course(
            id: scientificThinkingCourse.id,
            title: scientificThinkingCourse.title,
            description: scientificThinkingCourse.description,
            categoryId: scientificThinkingCourse.categoryId,
            thumbnailPath: scientificThinkingCourse.thumbnailPath,
            difficulty: scientificThinkingCourse.difficulty,
            prerequisites: scientificThinkingCourse.prerequisites,
            modules: loadedModules,
            isEncrypted: scientificThinkingCourse.isEncrypted,
            encryptionKey: scientificThinkingCourse.encryptionKey,
          );

          // Replace or add the course
          final index = _courses.indexWhere(
            (c) => c.id == 'scientific_thinking',
          );

          if (index >= 0) {
            _courses[index] = fullCourse;
            debugPrint(
              'Replaced Scientific Thinking course with fully loaded version',
            );
          } else {
            _courses.add(fullCourse);
            debugPrint('Added fully loaded Scientific Thinking course');
          }
        }
      }
    } catch (e) {
      debugPrint('Error during special handling for Scientific Thinking: $e');
      debugPrint('Error details: ${e.toString()}');
    }
  }

  // Get lesson by ID from a module
  LessonDefinition? getLessonById(Module module, String lessonId) {
    return _contentService.getLessonById(module, lessonId);
  }

  // Get the next lesson in a module
  LessonDefinition? getNextLesson(Module module, String currentLessonId) {
    return _contentService.getNextLesson(module, currentLessonId);
  }

  // Get the previous lesson in a module
  LessonDefinition? getPreviousLesson(Module module, String currentLessonId) {
    return _contentService.getPreviousLesson(module, currentLessonId);
  }

  // Update user's daily goal minutes
  void updateDailyGoalMinutes(int minutes) {
    if (_currentUser == null) return;

    // Update the current user with new daily goal
    _currentUser = User(
      id: _currentUser!.id,
      name: _currentUser!.name,
      email: _currentUser!.email,
      isGuest: _currentUser!.isGuest,
      enrolledCourseIds: _currentUser!.enrolledCourseIds,
      courseProgress: _currentUser!.courseProgress,
      dailyGoalMinutes: minutes,
    );
  }

  // Update user's course progress
  void updateUserCourseProgress(Map<String, CourseProgress> updatedProgress) {
    if (_currentUser == null) return;

    // Update the current user with new course progress
    _currentUser = User(
      id: _currentUser!.id,
      name: _currentUser!.name,
      email: _currentUser!.email,
      isGuest: _currentUser!.isGuest,
      enrolledCourseIds: _currentUser!.enrolledCourseIds,
      courseProgress: updatedProgress,
      dailyGoalMinutes: _currentUser!.dailyGoalMinutes,
    );
  }

  // Mark a course as completed
  void completeCourse(String courseId) {
    if (_currentUser == null) return;

    final updatedCourseProgress = Map<String, CourseProgress>.from(
      _currentUser!.courseProgress,
    );

    // Get the current progress or create a new one if it doesn't exist
    final currentProgress =
        updatedCourseProgress[courseId] ?? CourseProgress(courseId: courseId);

    // Create a new CourseProgress object with isCompleted set to true
    updatedCourseProgress[courseId] = CourseProgress(
      courseId: courseId,
      lessonProgress: currentProgress.lessonProgress,
      lastAccessed: DateTime.now(), // Update lastAccessed time
      isCompleted: true,
    );

    // Update the current user
    _currentUser = User(
      id: _currentUser!.id,
      name: _currentUser!.name,
      email: _currentUser!.email,
      isGuest: _currentUser!.isGuest,
      enrolledCourseIds: _currentUser!.enrolledCourseIds,
      courseProgress: updatedCourseProgress,
      dailyGoalMinutes: _currentUser!.dailyGoalMinutes,
    );
  }
}

// Helper functions for category display
Color getCategoryColor(String categoryId) {
  switch (categoryId.toLowerCase()) {
    case 'maths':
      return Color(0xFF4285F4); // Blue
    case 'science':
      return Color(0xFFFFB300); // Amber
    case 'computer_science':
      return Color(0xFF9C27B0); // Purple
    case 'reasoning':
      return Color(0xFFFF5722); // Deep Orange
    case 'technology':
      return Color(0xFF00BCD4); // Cyan
    case 'puzzles':
      return Color(0xFF8BC34A); // Light Green
    case 'curiosity_corner':
      return Color(0xFFFF9800); // Orange
    case 'coming_soon':
      return Color(0xFF9E9E9E); // Grey
    // Legacy categories
    case 'math':
      return Color(0xFF4285F4); // Blue
    case 'cs':
      return Color(0xFF9C27B0); // Purple
    case 'data':
      return Color(0xFFFF5722); // Orange
    default:
      return Color(0xFF4285F4); // Default blue
  }
}

// Get short name for category
String getCategoryShortName(String categoryId) {
  switch (categoryId.toLowerCase()) {
    case 'maths':
      return 'Maths';
    case 'science':
      return 'Science';
    case 'computer_science':
      return 'CS';
    case 'reasoning':
      return 'Reasoning';
    case 'technology':
      return 'Tech';
    case 'puzzles':
      return 'Puzzles';
    case 'curiosity_corner':
      return 'Curiosity';
    case 'coming_soon':
      return 'Coming Soon';
    // Legacy categories
    case 'math':
      return 'Math';
    case 'data':
      return 'Data';
    default:
      return 'Category';
  }
}
