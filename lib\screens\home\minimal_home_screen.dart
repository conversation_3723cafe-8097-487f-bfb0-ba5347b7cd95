import 'package:flutter/material.dart';
import '../../models/course_models.dart';
import '../../services/service_provider.dart';
import '../courses/course_detail_screen.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';

class MinimalHomeScreen extends StatefulWidget {
  const MinimalHomeScreen({super.key});

  @override
  State<MinimalHomeScreen> createState() => _MinimalHomeScreenState();
}

class _MinimalHomeScreenState extends State<MinimalHomeScreen> {
  final PageController _pageController = PageController();

  // Purple color used throughout the app
  static const Color resonancePurple = Color.fromRGBO(124, 66, 210, 1);

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final courseService = ServiceProvider.of(context).courseService;
    final recommendedCourses = courseService.getRecommendedCourses();

    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Column(
          children: [
            // Rank badge at the top
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [_buildRankBadge()],
              ),
            ),
            Expanded(
              child:
                  recommendedCourses.isEmpty
                      ? _buildEmptyState()
                      : _buildCoursePageView(recommendedCourses),
            ),
          ],
        ),
      ),
    );
  }

  // Build rank badge widget
  Widget _buildRankBadge() {
    return GestureDetector(
      onTap: () {
        // Use Navigator to push to the leagues tab
        Navigator.of(context).pushNamed('/leagues');
      },
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(26),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Rank icon
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: const Color.fromRGBO(124, 66, 210, 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: const Color.fromRGBO(124, 66, 210, 0.5),
                  width: 2,
                ),
              ),
              child: const Center(
                child: Icon(
                  Icons.shield,
                  color: Color.fromRGBO(124, 66, 210, 1),
                  size: 24,
                ),
              ),
            ),
            const SizedBox(width: 8),
            // Rank info
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text(
                  'Vibration',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                    fontFamily: 'WorkSans',
                  ),
                ),
                Text(
                  '6 days left',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                    fontFamily: 'WorkSans',
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.school_outlined, size: 80, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            'No courses available yet',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.grey[600],
              fontFamily: 'WorkSans',
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Check back soon for new content',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[500],
              fontFamily: 'WorkSans',
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCoursePageView(List<Course> courses) {
    return Column(
      children: [
        // Course cards
        Expanded(
          child: PageView.builder(
            controller: _pageController,
            itemCount: courses.length,
            onPageChanged: (index) {
              // Page changed, no need to track the index
            },
            itemBuilder: (context, index) {
              return _buildCourseCard(courses[index]);
            },
          ),
        ),

        // Page indicator
        Padding(
          padding: const EdgeInsets.only(bottom: 24.0),
          child: SmoothPageIndicator(
            controller: _pageController,
            count: courses.length,
            effect: ExpandingDotsEffect(
              activeDotColor: resonancePurple,
              dotColor: Colors.grey[300]!,
              dotHeight: 8,
              dotWidth: 8,
              spacing: 4,
              expansionFactor: 2,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildCourseCard(Course course) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Course title
          Text(
            course.title,
            style: const TextStyle(
              fontSize: 28,
              fontWeight: FontWeight.bold,
              fontFamily: 'WorkSans',
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 16),

          // Level indicator
          Text(
            'LEVEL ${_getLevelNumber(course.difficulty)}',
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.blue,
              fontFamily: 'WorkSans',
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 40),

          // Course illustration
          _buildCourseIllustration(course),

          const SizedBox(height: 40),

          // Practice time
          Text(
            'Practice · ${_getEstimatedTime(course)} min',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[700],
              fontFamily: 'WorkSans',
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 24),

          // Start button
          SizedBox(
            width: 240,
            height: 56,
            child: ElevatedButton(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder:
                        (context) => CourseDetailScreen(courseId: course.id),
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: resonancePurple,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(28),
                ),
                elevation: 0,
              ),
              child: const Text(
                'Start',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  fontFamily: 'WorkSans',
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCourseIllustration(Course course) {
    // Map course categories to appropriate illustrations
    Widget illustration;

    switch (course.categoryId.toLowerCase()) {
      case 'math':
        illustration = _buildBalanceScale();
        break;
      case 'cs':
        illustration = _buildComputerIllustration();
        break;
      case 'data':
        illustration = _buildDataIllustration();
        break;
      case 'science':
        illustration = _buildScienceIllustration();
        break;
      default:
        illustration = _buildBalanceScale();
    }

    return SizedBox(height: 160, child: illustration);
  }

  // Balance scale illustration for math courses
  Widget _buildBalanceScale() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        SizedBox(
          width: 240,
          height: 160,
          child: CustomPaint(painter: BalanceScalePainter()),
        ),
      ],
    );
  }

  // Computer illustration for CS courses
  Widget _buildComputerIllustration() {
    return Icon(Icons.computer, size: 120, color: Colors.blue[400]);
  }

  // Data illustration
  Widget _buildDataIllustration() {
    return Icon(Icons.bar_chart, size: 120, color: Colors.orange[400]);
  }

  // Science illustration
  Widget _buildScienceIllustration() {
    return Icon(Icons.science, size: 120, color: Colors.amber[400]);
  }

  // Helper method to get level number from difficulty
  String _getLevelNumber(String difficulty) {
    switch (difficulty.toLowerCase()) {
      case 'beginner':
        return '1';
      case 'intermediate':
        return '2';
      case 'advanced':
        return '3';
      case 'expert':
        return '4';
      default:
        return '1';
    }
  }

  // Helper method to get estimated time
  int _getEstimatedTime(Course course) {
    // Calculate total estimated time from lessons
    int totalMinutes = 0;
    final allLessons =
        course.modules.expand((module) => module.lessons).toList();
    for (final lesson in allLessons) {
      totalMinutes += lesson.estimatedTimeMinutes;
    }

    // If no lessons or time is 0, return a default value
    return totalMinutes > 0 ? totalMinutes : 2;
  }
}

// Custom painter for the balance scale
class BalanceScalePainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..color = Colors.blue[400]!
          ..style = PaintingStyle.fill;

    // Draw the base
    final basePath =
        Path()
          ..moveTo(size.width * 0.5, size.height * 0.9)
          ..lineTo(size.width * 0.4, size.height)
          ..lineTo(size.width * 0.6, size.height)
          ..close();
    canvas.drawPath(basePath, paint);

    // Draw the pole
    canvas.drawRect(
      Rect.fromLTWH(
        size.width * 0.48,
        size.height * 0.3,
        size.width * 0.04,
        size.height * 0.6,
      ),
      paint,
    );

    // Draw the beam
    canvas.drawRect(
      Rect.fromLTWH(
        size.width * 0.2,
        size.height * 0.3,
        size.width * 0.6,
        size.height * 0.04,
      ),
      paint,
    );

    // Draw left pan
    final leftPanPaint =
        Paint()
          ..color = Colors.blue[400]!
          ..style = PaintingStyle.fill;
    canvas.drawOval(
      Rect.fromLTWH(
        size.width * 0.15,
        size.height * 0.4,
        size.width * 0.2,
        size.height * 0.1,
      ),
      leftPanPaint,
    );

    // Draw right pan
    final rightPanPaint =
        Paint()
          ..color = Colors.blue[400]!
          ..style = PaintingStyle.fill;
    canvas.drawOval(
      Rect.fromLTWH(
        size.width * 0.65,
        size.height * 0.4,
        size.width * 0.2,
        size.height * 0.1,
      ),
      rightPanPaint,
    );

    // Draw weight on left pan
    final weightPaint =
        Paint()
          ..color = Colors.grey[600]!
          ..style = PaintingStyle.fill;
    canvas.drawCircle(
      Offset(size.width * 0.25, size.height * 0.38),
      size.width * 0.06,
      weightPaint,
    );

    // Draw spheres on right pan
    final sphere1Paint =
        Paint()
          ..color = Colors.amber[400]!
          ..style = PaintingStyle.fill;
    canvas.drawCircle(
      Offset(size.width * 0.7, size.height * 0.35),
      size.width * 0.04,
      sphere1Paint,
    );

    final sphere2Paint =
        Paint()
          ..color = Colors.blue[300]!
          ..style = PaintingStyle.fill;
    canvas.drawCircle(
      Offset(size.width * 0.78, size.height * 0.38),
      size.width * 0.03,
      sphere2Paint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return false;
  }
}
