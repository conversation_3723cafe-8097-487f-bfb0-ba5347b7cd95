import 'package:flutter/material.dart';
import 'package:resonance/widgets/interactive_widget_scaffold.dart';

class InteractiveParallelUniverseExplorer extends StatefulWidget {
  const InteractiveParallelUniverseExplorer({super.key});

  @override
  State<InteractiveParallelUniverseExplorer> createState() => _InteractiveParallelUniverseExplorerState();
}

class _InteractiveParallelUniverseExplorerState extends State<InteractiveParallelUniverseExplorer> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  double _universeDivergence = 0.0; // 0.0 to 1.0, representing divergence from original universe
  String _universeDescription = 'No divergence. This is the original universe.';

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 5),
    )..repeat(reverse: true);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  String _getUniverseDescription() {
    if (_universeDivergence < 0.2) {
      return 'No divergence. This is the original universe. Any time travel events either self-corrected or created a new, unobservable branch.';
    } else if (_universeDivergence < 0.5) {
      return 'Slight divergence: A parallel universe exists with minor historical differences, perhaps a different election outcome or a slightly altered technological path.';
    } else if (_universeDivergence < 0.8) {
      return 'Moderate divergence: A parallel universe with significant historical changes, such as a different major war outcome, a different dominant culture, or a major scientific discovery occurring earlier/later.';
    } else {
      return 'Extreme divergence: A completely different parallel universe where fundamental laws of physics might be altered, or humanity never evolved, or an entirely different species dominates.';
    }
  }

  @override
  Widget build(BuildContext context) {
    return InteractiveWidgetScaffold(
      title: 'Parallel Universe Explorer',
      description: 'Explore parallel universes created by time travel.',
      interactiveContent: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            AnimatedBuilder(
              animation: _controller,
              builder: (context, child) {
                return Container(
                  width: 150,
                  height: 150,
                  decoration: BoxDecoration(
                    color: Colors.deepPurple.withOpacity(0.3 + (_controller.value * 0.7 * _universeDivergence)),
                    shape: BoxShape.circle,
                    border: Border.all(color: Colors.deepPurple, width: 5.0 * (1 + _universeDivergence)),
                  ),
                  child: Icon(Icons.blur_on, size: 80, color: Colors.white.withOpacity(0.8)),
                );
              },
            ),
            const SizedBox(height: 20),
            Text(
              'Universe Divergence: ${(_universeDivergence * 100).toStringAsFixed(0)}%',
              style: const TextStyle(fontSize: 18),
            ),
            Slider(
              value: _universeDivergence,
              min: 0.0,
              max: 1.0,
              divisions: 10,
              label: (_universeDivergence * 100).toStringAsFixed(0),
              onChanged: (value) {
                setState(() {
                  _universeDivergence = value;
                });
              },
            ),
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: Text(
                'Adjust the slider to simulate the hypothetical divergence of a parallel universe due to time travel. Observe the visual changes and read the description to understand the potential differences in these alternate realities.',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 16),
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Text(
                _getUniverseDescription(),
                textAlign: TextAlign.center,
                style: const TextStyle(fontSize: 16, fontStyle: FontStyle.italic),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
