import 'package:flutter/material.dart';

class LeaguesScreen extends StatefulWidget {
  const LeaguesScreen({super.key});

  @override
  State<LeaguesScreen> createState() => _LeaguesScreenState();
}

class _LeaguesScreenState extends State<LeaguesScreen> {
  // List of league ranks with musical theme
  final List<Map<String, dynamic>> leagueRanks = [
    {
      'name': 'Echo',
      'color': Colors.blue[300],
      'isUnlocked': true,
      'isActive': false,
      'description': 'The initial stage, a faint reflection of learning.',
    },
    {
      'name': 'Harmonic',
      'color': Colors.green[400],
      'isUnlocked': true,
      'isActive': false,
      'description': 'Building towards a richer sound.',
    },
    {
      'name': 'Vibration',
      'color': Colors.teal,
      'isUnlocked': true,
      'isActive': true,
      'description': 'A stronger, more noticeable level of understanding.',
    },
    {
      'name': 'Frequency',
      'color': Colors.amber[600],
      'isUnlocked': false,
      'isActive': false,
      'description': 'A specific level of expertise.',
    },
    {
      'name': 'Amplitude',
      'color': Colors.orange[600],
      'isUnlocked': false,
      'isActive': false,
      'description': 'Significant depth and breadth of knowledge.',
    },
    {
      'name': 'Sustain',
      'color': Colors.red[400],
      'isUnlocked': false,
      'isActive': false,
      'description': 'Maintaining a high level of mastery.',
    },
    {
      'name': 'Chord',
      'color': Colors.purple[400],
      'isUnlocked': false,
      'isActive': false,
      'description':
          'Multiple elements of learning coming together beautifully.',
    },
    {
      'name': 'Resonant',
      'color': Colors.deepPurple[600],
      'isUnlocked': false,
      'isActive': false,
      'description': 'The ultimate level of deep understanding and mastery.',
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Column(
          children: [
            // League ranks at the top
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 24, 16, 24),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children:
                    leagueRanks.map((rank) => _buildRankBadge(rank)).toList(),
              ),
            ),

            // Current rank title
            const Text(
              'Vibration',
              style: TextStyle(
                fontSize: 28,
                fontWeight: FontWeight.bold,
                fontFamily: 'WorkSans',
              ),
              textAlign: TextAlign.center,
            ),

            // Time left
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8),
              child: Text(
                'Time left to join a league',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey[600],
                  fontFamily: 'WorkSans',
                ),
                textAlign: TextAlign.center,
              ),
            ),

            // Days left
            Text(
              '6 days',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.grey[800],
                fontFamily: 'WorkSans',
              ),
              textAlign: TextAlign.center,
            ),

            const Divider(height: 40),

            // Trophy illustration
            Expanded(
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    _buildTrophyIllustration(),
                    const SizedBox(height: 32),
                    Text(
                      'Earn XP to join the next leaderboard',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: Colors.grey[800],
                        fontFamily: 'WorkSans',
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Build rank badge widget
  Widget _buildRankBadge(Map<String, dynamic> rank) {
    final bool isUnlocked = rank['isUnlocked'] as bool;
    final bool isActive = rank['isActive'] as bool;
    final Color? badgeColor = rank['color'] as Color?;

    return Container(
      width: 60,
      height: 60,
      decoration: BoxDecoration(
        color:
            isUnlocked
                ? (isActive ? badgeColor : Colors.grey[300])
                : Colors.grey[300],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.black.withAlpha(20), width: 2),
        boxShadow: [
          if (isActive)
            BoxShadow(
              color: (badgeColor ?? Colors.grey).withAlpha(50),
              blurRadius: 8,
              spreadRadius: 2,
            ),
        ],
      ),
      child: Center(
        child: Icon(
          isUnlocked
              ? (isActive ? Icons.play_arrow : Icons.emoji_events)
              : Icons.lock,
          color:
              isUnlocked
                  ? (isActive ? Colors.white : Colors.grey[700])
                  : Colors.grey[500],
          size: 28,
        ),
      ),
    );
  }

  // Build trophy illustration
  Widget _buildTrophyIllustration() {
    return SizedBox(
      height: 160,
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Trophy base
          Positioned(
            bottom: 0,
            child: Container(
              width: 80,
              height: 20,
              decoration: BoxDecoration(
                color: Colors.grey[800],
                borderRadius: BorderRadius.circular(4),
              ),
            ),
          ),

          // Trophy stand
          Positioned(
            bottom: 20,
            child: Container(
              width: 40,
              height: 30,
              decoration: BoxDecoration(
                color: Colors.grey[700],
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(4),
                  topRight: Radius.circular(4),
                ),
              ),
            ),
          ),

          // Trophy cup
          Positioned(
            bottom: 50,
            child: Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: Colors.amber[400],
                shape: BoxShape.rectangle,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.amber.withAlpha(100),
                    blurRadius: 10,
                    spreadRadius: 2,
                  ),
                ],
              ),
              child: const Center(
                child: Icon(Icons.star, color: Colors.white, size: 40),
              ),
            ),
          ),

          // Green badge
          Positioned(
            right: 100,
            bottom: 70,
            child: Transform.rotate(
              angle: -0.3,
              child: Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: Colors.green[400],
                  borderRadius: BorderRadius.circular(8),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withAlpha(50),
                      blurRadius: 8,
                      offset: const Offset(2, 2),
                    ),
                  ],
                ),
                child: const Center(
                  child: Icon(Icons.check, color: Colors.white, size: 30),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
