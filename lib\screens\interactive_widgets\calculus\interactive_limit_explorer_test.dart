import 'package:flutter/material.dart';
import 'dart:math';

class InteractiveLimitExplorerTest extends StatefulWidget {
  const InteractiveLimitExplorerTest({super.key});

  @override
  State<InteractiveLimitExplorerTest> createState() => _InteractiveLimitExplorerTestState();
}

class _InteractiveLimitExplorerTestState extends State<InteractiveLimitExplorerTest> {
  int _currentQuestionIndex = 0;
  String? _selectedAnswer;
  String? _feedbackMessage;
  bool _isCorrect = false;

  final List<Map<String, dynamic>> _questions = [
    {
      'type': 'graph',
      'question': 'What is the limit of f(x) as x approaches 2?',
      'function': (double x) => (x * x - 4) / (x - 2), // x + 2 with a hole at x=2
      'targetX': 2.0,
      'correctAnswer': '4.0',
      'options': ['2.0', 'Undefined', '4.0', '0.0'],
    },
    {
      'type': 'table',
      'question': 'Based on the table, what is the limit of f(x) as x approaches 3?',
      'function': (double x) => x * x, // f(x) = x^2
      'targetX': 3.0,
      'correctAnswer': '9.0',
      'options': ['6.0', 'Undefined', '9.0', '3.0'],
    },
    {
      'type': 'piecewise',
      'question': 'What is the limit of f(x) as x approaches 0?',
      'function': (double x) {
        if (x < 0) return x + 2;
        return x - 2;
      },
      'targetX': 0.0,
      'correctAnswer': 'Does Not Exist',
      'options': ['2.0', '-2.0', '0.0', 'Does Not Exist'],
    },
  ];

  void _checkAnswer() {
    final currentQuestion = _questions[_currentQuestionIndex];
    setState(() {
      if (_selectedAnswer == currentQuestion['correctAnswer']) {
        _feedbackMessage = 'Correct!';
        _isCorrect = true;
      } else {
        _feedbackMessage = 'Incorrect. The correct answer is ${currentQuestion['correctAnswer']}.';
        _isCorrect = false;
      }
    });
  }

  void _nextQuestion() {
    setState(() {
      if (_currentQuestionIndex < _questions.length - 1) {
        _currentQuestionIndex++;
        _selectedAnswer = null;
        _feedbackMessage = null;
        _isCorrect = false;
      } else {
        // End of test
        _feedbackMessage = 'Test Completed! Review your answers.';
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final currentQuestion = _questions[_currentQuestionIndex];

    Widget questionWidget;
    if (currentQuestion['type'] == 'graph') {
      questionWidget = Column(
        children: [
          Text(currentQuestion['question'], style: TextStyle(fontSize: 16)),
          SizedBox(height: 10),
          CustomPaint(
            size: Size(300, 200),
            painter: _GraphPainter(currentQuestion['function'], currentQuestion['targetX']),
          ),
        ],
      );
    } else if (currentQuestion['type'] == 'table') {
      List<Map<String, String>> tableData = _generateTableData(currentQuestion['function'], currentQuestion['targetX']);
      questionWidget = Column(
        children: [
          Text(currentQuestion['question'], style: TextStyle(fontSize: 16)),
          SizedBox(height: 10),
          Table(
            border: TableBorder.all(color: Colors.grey),
            columnWidths: const {
              0: FlexColumnWidth(1),
              1: FlexColumnWidth(1),
            },
            children: [
              TableRow(
                decoration: BoxDecoration(color: Colors.blueGrey[100]),
                children: const [
                  Padding(
                    padding: EdgeInsets.all(8.0),
                    child: Text('x', style: TextStyle(fontWeight: FontWeight.bold)),
                  ),
                  Padding(
                    padding: EdgeInsets.all(8.0),
                    child: Text('f(x)', style: TextStyle(fontWeight: FontWeight.bold)),
                  ),
                ],
              ),
              ...tableData.map((row) {
                return TableRow(
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Text(row['x']!),
                    ),
                    Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Text(row['f(x)']!),
                    ),
                  ],
                );
              }).toList(),
            ],
          ),
        ],
      );
    } else if (currentQuestion['type'] == 'piecewise') {
      questionWidget = Column(
        children: [
          Text(currentQuestion['question'], style: TextStyle(fontSize: 16)),
          SizedBox(height: 10),
          Text(
            'f(x) = { x + 2 if x < 0, x - 2 if x ≥ 0 }',
            style: TextStyle(fontSize: 14),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 10),
          CustomPaint(
            size: Size(300, 200),
            painter: _PiecewiseGraphPainter(currentQuestion['function'], currentQuestion['targetX']),
          ),
        ],
      );
    } else {
      questionWidget = Text('Unknown question type');
    }

    return Card(
      margin: const EdgeInsets.all(16.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Interactive Limit Explorer (Module Test)',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 20),
            questionWidget,
            const SizedBox(height: 20),
            ...currentQuestion['options'].map<Widget>((option) {
              return RadioListTile<String>(
                title: Text(option),
                value: option,
                groupValue: _selectedAnswer,
                onChanged: (value) {
                  setState(() {
                    _selectedAnswer = value;
                    _feedbackMessage = null; // Clear feedback when answer changes
                  });
                },
              );
            }).toList(),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: _selectedAnswer == null ? null : _checkAnswer,
              child: const Text('Check Answer'),
            ),
            if (_feedbackMessage != null)
              Padding(
                padding: const EdgeInsets.all(10.0),
                child: Text(
                  _feedbackMessage!,
                  style: TextStyle(
                    color: _isCorrect ? Colors.green : Colors.red,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            const SizedBox(height: 10),
            ElevatedButton(
              onPressed: _nextQuestion,
              child: Text(_currentQuestionIndex < _questions.length - 1 ? 'Next Question' : 'Finish Test'),
            ),
          ],
        ),
      ),
    );
  }

  List<Map<String, String>> _generateTableData(Function(double) func, double targetX) {
    List<Map<String, String>> tableData = [];
    // Values approaching from the left
    for (int i = 3; i >= 0; i--) {
      double x = targetX - pow(10, -i).toDouble();
      double y = func(x);
      tableData.add({
        'x': x.toStringAsFixed(i + 2),
        'f(x)': y.isNaN ? 'Undefined' : y.toStringAsFixed(i + 2),
      });
    }
    // Target point
    double yTarget = func(targetX);
    tableData.add({
      'x': targetX.toStringAsFixed(1),
      'f(x)': yTarget.isNaN ? 'Undefined' : yTarget.toStringAsFixed(1),
    });
    // Values approaching from the right
    for (int i = 0; i <= 3; i++) {
      double x = targetX + pow(10, -i).toDouble();
      double y = func(x);
      tableData.add({
        'x': x.toStringAsFixed(i + 2),
        'f(x)': y.isNaN ? 'Undefined' : y.toStringAsFixed(i + 2),
      });
    }
    return tableData;
  }
}

class _GraphPainter extends CustomPainter {
  final Function(double) _function;
  final double _targetX;

  _GraphPainter(this._function, this._targetX);

  @override
  void paint(Canvas canvas, Size size) {
    final Paint paint = Paint()
      ..color = Colors.blue
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    final Paint targetLinePaint = Paint()
      ..color = Colors.green
      ..strokeWidth = 1
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    final double minX = _targetX - 3.0;
    final double maxX = _targetX + 3.0;
    final double minY = _function(_targetX + 0.001) - 3.0;
    final double maxY = _function(_targetX + 0.001) + 3.0;

    final double rangeX = maxX - minX;
    final double rangeY = maxY - minY;

    final double scaleX = size.width / rangeX;
    final double scaleY = size.height / rangeY;

    Offset toCanvas(double x, double y) {
      final double canvasX = (x - minX) * scaleX;
      final double canvasY = size.height - (y - minY) * scaleY;
      return Offset(canvasX, canvasY);
    }

    canvas.drawLine(toCanvas(minX, 0), toCanvas(maxX, 0), paint..color = Colors.grey);
    canvas.drawLine(toCanvas(0, minY), toCanvas(0, maxY), paint..color = Colors.grey);

    final Path path = Path();
    bool firstPoint = true;
    for (double i = minX; i <= maxX; i += 0.05) {
      double x = i;
      double y = _function(x);
      if (!y.isNaN && y.isFinite) {
        if (firstPoint) {
          path.moveTo(toCanvas(x, y).dx, toCanvas(x, y).dy);
          firstPoint = false;
        } else {
          path.lineTo(toCanvas(x, y).dx, toCanvas(x, y).dy);
        }
      } else {
        firstPoint = true;
      }
    }
    canvas.drawPath(path, paint);

    if (_function(_targetX).isNaN) {
      canvas.drawCircle(toCanvas(_targetX, _targetX + 2), 4, Paint()..color = Colors.orange..style = PaintingStyle.stroke..strokeWidth = 2);
    }

    canvas.drawLine(toCanvas(_targetX, minY), toCanvas(_targetX, maxY), targetLinePaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return false; // Graph is static for the test question
  }
}

class _PiecewiseGraphPainter extends CustomPainter {
  final Function(double) _function;
  final double _targetX;

  _PiecewiseGraphPainter(this._function, this._targetX);

  @override
  void paint(Canvas canvas, Size size) {
    final Paint paint = Paint()
      ..color = Colors.blue
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    final Paint targetLinePaint = Paint()
      ..color = Colors.green
      ..strokeWidth = 1
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    final double minX = _targetX - 3.0;
    final double maxX = _targetX + 3.0;
    final double minY = -3.0;
    final double maxY = 3.0;

    final double rangeX = maxX - minX;
    final double rangeY = maxY - minY;

    final double scaleX = size.width / rangeX;
    final double scaleY = size.height / rangeY;

    Offset toCanvas(double x, double y) {
      final double canvasX = (x - minX) * scaleX;
      final double canvasY = size.height - (y - minY) * scaleY;
      return Offset(canvasX, canvasY);
    }

    canvas.drawLine(toCanvas(minX, 0), toCanvas(maxX, 0), paint..color = Colors.grey);
    canvas.drawLine(toCanvas(0, minY), toCanvas(0, maxY), paint..color = Colors.grey);

    final Path pathLeft = Path();
    for (double i = minX; i < _targetX; i += 0.05) {
      double x = i;
      double y = _function(x);
      if (!y.isNaN && y.isFinite) {
        if (pathLeft.isEmpty) {
          pathLeft.moveTo(toCanvas(x, y).dx, toCanvas(x, y).dy);
        } else {
          pathLeft.lineTo(toCanvas(x, y).dx, toCanvas(x, y).dy);
        }
      }
    }
    canvas.drawPath(pathLeft, paint);

    final Path pathRight = Path();
    for (double i = _targetX; i <= maxX; i += 0.05) {
      double x = i;
      double y = _function(x);
      if (!y.isNaN && y.isFinite) {
        if (pathRight.isEmpty) {
          pathRight.moveTo(toCanvas(x, y).dx, toCanvas(x, y).dy);
        } else {
          pathRight.lineTo(toCanvas(x, y).dx, toCanvas(x, y).dy);
        }
      }
    }
    canvas.drawPath(pathRight, paint);

    canvas.drawCircle(toCanvas(_targetX, _function(_targetX - 0.001)), 4, Paint()..color = Colors.blue..style = PaintingStyle.stroke..strokeWidth = 2);
    canvas.drawCircle(toCanvas(_targetX, _function(_targetX)), 4, Paint()..color = Colors.blue..style = PaintingStyle.fill);

    canvas.drawLine(toCanvas(_targetX, minY), toCanvas(_targetX, maxY), targetLinePaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return false; // Graph is static for the test question
  }
}
