import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:resonance_app/services/interactive_widget_service.dart';
import 'package:resonance_app/widgets/animated_button.dart';
import 'dart:collection';

class InteractiveGraphTraversalDemonstrator extends StatefulWidget {
  final String widgetId;

  const InteractiveGraphTraversalDemonstrator({Key? key, required this.widgetId}) : super(key: key);

  @override
  _InteractiveGraphTraversalDemonstratorState createState() => _InteractiveGraphTraversalDemonstratorState();
}

class _InteractiveGraphTraversalDemonstratorState extends State<InteractiveGraphTraversalDemonstrator> {
  Map<String, List<String>> _adjacencyList = {
    'A': ['B', 'C'],
    'B': ['A', 'D', 'E'],
    'C': ['A', 'F'],
    'D': ['B'],
    'E': ['B', 'F'],
    'F': ['C', 'E'],
  };
  List<String> _nodes = ['A', 'B', 'C', 'D', 'E', 'F'];
  Set<String> _visitedNodes = {};
  List<String> _traversalOrder = [];
  String _message = '';
  bool _isTraversing = false;
  double _animationSpeed = 500; // milliseconds

  @override
  void initState() {
    super.initState();
    _loadState();
  }

  void _loadState() async {
    final service = Provider.of<InteractiveWidgetService>(context, listen: false);
    final savedState = await service.getWidgetState(widget.widgetId);
    if (savedState != null) {
      setState(() {
        _adjacencyList = Map<String, List<String>>.from(savedState['adjacencyList']?.map((key, value) => MapEntry(key, List<String>.from(value))) ?? {});
        _nodes = List<String>.from(savedState['nodes'] ?? []);
        _visitedNodes = Set<String>.from(savedState['visitedNodes'] ?? {});
        _traversalOrder = List<String>.from(savedState['traversalOrder'] ?? []);
        _message = savedState['message'] ?? '';
        _isTraversing = savedState['isTraversing'] ?? false;
        _animationSpeed = savedState['animationSpeed'] ?? 500;
      });
    }
  }

  void _saveState() {
    final service = Provider.of<InteractiveWidgetService>(context, listen: false);
    service.saveWidgetState(widget.widgetId, {
      'adjacencyList': _adjacencyList,
      'nodes': _nodes,
      'visitedNodes': _visitedNodes.toList(),
      'traversalOrder': _traversalOrder,
      'message': _message,
      'isTraversing': _isTraversing,
      'animationSpeed': _animationSpeed,
    });
  }

  void _resetTraversal() {
    setState(() {
      _visitedNodes.clear();
      _traversalOrder.clear();
      _message = 'Traversal reset.';
      _isTraversing = false;
    });
    _saveState();
  }

  Future<void> _bfs(String startNode) async {
    _resetTraversal();
    if (!_nodes.contains(startNode)) {
      setState(() {
        _message = 'Start node "$startNode" not found.';
      });
      return;
    }

    setState(() {
      _isTraversing = true;
      _message = 'Starting BFS from $startNode...';
    });
    _saveState();
    await Future.delayed(Duration(milliseconds: _animationSpeed.toInt()));

    Queue<String> queue = Queue();
    queue.add(startNode);
    _visitedNodes.add(startNode);
    _traversalOrder.add(startNode);

    while (queue.isNotEmpty) {
      String currentNode = queue.removeFirst();
      setState(() {
        _message = 'Visiting node: $currentNode';
      });
      _saveState();
      await Future.delayed(Duration(milliseconds: _animationSpeed.toInt()));

      for (String neighbor in _adjacencyList[currentNode]!) {
        if (!_visitedNodes.contains(neighbor)) {
          _visitedNodes.add(neighbor);
          queue.add(neighbor);
          setState(() {
            _traversalOrder.add(neighbor);
            _message = 'Adding neighbor $neighbor to queue.';
          });
          _saveState();
          await Future.delayed(Duration(milliseconds: _animationSpeed.toInt()));
        }
      }
    }

    setState(() {
      _isTraversing = false;
      _message = 'BFS completed. Traversal order: ${_traversalOrder.join(' -> ')}';
    });
    _saveState();
  }

  Future<void> _dfs(String startNode) async {
    _resetTraversal();
    if (!_nodes.contains(startNode)) {
      setState(() {
        _message = 'Start node "$startNode" not found.';
      });
      return;
    }

    setState(() {
      _isTraversing = true;
      _message = 'Starting DFS from $startNode...';
    });
    _saveState();
    await Future.delayed(Duration(milliseconds: _animationSpeed.toInt()));

    List<String> stack = []; // Using a List as a stack
    stack.add(startNode);
    _visitedNodes.add(startNode);
    _traversalOrder.add(startNode);

    while (stack.isNotEmpty) {
      String currentNode = stack.last; // Peek at the top element
      bool foundUnvisitedNeighbor = false;

      setState(() {
        _message = 'Visiting node: $currentNode';
      });
      _saveState();
      await Future.delayed(Duration(milliseconds: _animationSpeed.toInt()));

      for (String neighbor in _adjacencyList[currentNode]!) {
        if (!_visitedNodes.contains(neighbor)) {
          _visitedNodes.add(neighbor);
          stack.add(neighbor);
          setState(() {
            _traversalOrder.add(neighbor);
            _message = 'Moving to unvisited neighbor $neighbor.';
          });
          _saveState();
          await Future.delayed(Duration(milliseconds: _animationSpeed.toInt()));
          foundUnvisitedNeighbor = true;
          break; // Move to the new node
        }
      }

      if (!foundUnvisitedNeighbor) {
        stack.removeLast(); // Backtrack
        setState(() {
          _message = 'No unvisited neighbors from $currentNode. Backtracking.';
        });
        _saveState();
        await Future.delayed(Duration(milliseconds: _animationSpeed.toInt()));
      }
    }

    setState(() {
      _isTraversing = false;
      _message = 'DFS completed. Traversal order: ${_traversalOrder.join(' -> ')}';
    });
    _saveState();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Interactive Graph Traversal Demonstrator',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                child: Slider(
                  value: _animationSpeed,
                  min: 50,
                  max: 1000,
                  divisions: 19,
                  label: _animationSpeed.round().toString(),
                  onChanged: (double value) {
                    setState(() {
                      _animationSpeed = value;
                    });
                  },
                ),
              ),
              Text('Speed: ${_animationSpeed.round()}ms'),
            ],
          ),
          const SizedBox(height: 10),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              AnimatedButton(
                onTap: _isTraversing ? null : () => _bfs('A'), // Start BFS from 'A'
                text: 'Start BFS (from A)',
                color: _isTraversing ? Colors.grey : Colors.blue,
              ),
              AnimatedButton(
                onTap: _isTraversing ? null : () => _dfs('A'), // Start DFS from 'A'
                text: 'Start DFS (from A)',
                color: _isTraversing ? Colors.grey : Colors.green,
              ),
            ],
          ),
          const SizedBox(height: 10),
          AnimatedButton(
            onTap: _resetTraversal,
            text: 'Reset Traversal',
            color: Colors.red,
          ),
          const SizedBox(height: 20),
          Text(
            'Graph Nodes:',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 10),
          Wrap(
            spacing: 8.0,
            runSpacing: 4.0,
            children: _nodes.map((node) {
              Color color = Colors.grey;
              if (_visitedNodes.contains(node)) {
                color = Colors.green;
              }
              return Chip(
                label: Text(node),
                backgroundColor: color,
                labelStyle: const TextStyle(color: Colors.white),
              );
            }).toList(),
          ),
          const SizedBox(height: 20),
          Text(
            'Traversal Order: ${_traversalOrder.join(' -> ')}',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 20),
          Text(
            'Message: $_message',
            style: Theme.of(context).textTheme.bodyLarge,
          ),
        ],
      ),
    );
  }
}
