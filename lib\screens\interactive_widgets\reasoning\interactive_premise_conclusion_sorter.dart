import 'package:flutter/material.dart';

class InteractivePremiseConclusionSorter extends StatefulWidget {
  const InteractivePremiseConclusionSorter({super.key});

  @override
  State<InteractivePremiseConclusionSorter> createState() => _InteractivePremiseConclusionSorterState();
}

class _InteractivePremiseConclusionSorterState extends State<InteractivePremiseConclusionSorter> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Premise-Conclusion Sorter'),
        backgroundColor: Colors.blueAccent,
      ),
      body: const Center(
        child: Text(
          'Interactive Premise-Conclusion Sorter Widget',
          style: TextStyle(fontSize: 24),
        ),
      ),
    );
  }
}
