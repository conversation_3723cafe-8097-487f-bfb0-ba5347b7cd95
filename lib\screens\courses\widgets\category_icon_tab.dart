import 'package:flutter/material.dart';

class CategoryIconTab extends StatelessWidget {
  final String name;
  final Widget icon;
  final bool isSelected;
  final VoidCallback onTap;

  const CategoryIconTab({
    Key? key,
    required this.name,
    required this.icon,
    required this.isSelected,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Icon container
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: isSelected ? Colors.blue[50] : Colors.white,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: isSelected ? Colors.blue : Colors.grey[300]!,
                width: 1.5,
              ),
            ),
            child: Center(
              child: icon,
            ),
          ),
          const SizedBox(height: 8),
          // Category name
          Text(
            name,
            style: TextStyle(
              color: isSelected ? Colors.blue[700] : Colors.grey[700],
              fontSize: 12,
              fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
              fontFamily: 'WorkSans',
            ),
          ),
        ],
      ),
    );
  }
}
