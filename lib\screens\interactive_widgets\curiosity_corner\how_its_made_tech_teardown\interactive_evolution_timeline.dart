import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:resonance_app/services/service_provider.dart';
import 'package:resonance_app/utils/page_transitions.dart';

class InteractiveEvolutionTimeline extends StatefulWidget {
  const InteractiveEvolutionTimeline({super.key});

  @override
  State<InteractiveEvolutionTimeline> createState() => _InteractiveEvolutionTimelineState();
}

class _InteractiveEvolutionTimelineState extends State<InteractiveEvolutionTimeline> with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 1),
    )..repeat(reverse: true);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Interactive Evolution Timeline'),
        backgroundColor: Theme.of(context).primaryColor,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Explore the evolution of smartphones over time!',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 20),
            Expanded(
              child: Center(
                child: FadeTransition(
                  opacity: _controller,
                  child: const Text(
                    'Placeholder for interactive evolution timeline.',
                    textAlign: TextAlign.center,
                    style: TextStyle(fontSize: 18, fontStyle: FontStyle.italic),
                  ),
                ),
              ),
            ),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('Go Back'),
            ),
          ],
        ),
      ),
    );
  }
}
