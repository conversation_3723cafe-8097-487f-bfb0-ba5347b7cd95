import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:resonance_app/services/interactive_widget_service.dart';
import 'package:resonance_app/widgets/animated_button.dart';
import 'dart:math';

class InteractiveGraphRepresentationTool extends StatefulWidget {
  final String widgetId;

  const InteractiveGraphRepresentationTool({Key? key, required this.widgetId}) : super(key: key);

  @override
  _InteractiveGraphRepresentationToolState createState() => _InteractiveGraphRepresentationToolState();
}

class _InteractiveGraphRepresentationToolState extends State<InteractiveGraphRepresentationTool> {
  Map<String, List<String>> _adjacencyList = {};
  List<List<int>> _adjacencyMatrix = [];
  List<String> _nodes = [];
  TextEditingController _nodeController = TextEditingController();
  TextEditingController _edgeFromController = TextEditingController();
  TextEditingController _edgeToController = TextEditingController();
  String _message = '';
  bool _isDirected = false;

  @override
  void initState() {
    super.initState();
    _loadState();
  }

  void _loadState() async {
    final service = Provider.of<InteractiveWidgetService>(context, listen: false);
    final savedState = await service.getWidgetState(widget.widgetId);
    if (savedState != null) {
      setState(() {
        _adjacencyList = Map<String, List<String>>.from(savedState['adjacencyList']?.map((key, value) => MapEntry(key, List<String>.from(value))) ?? {});
        _adjacencyMatrix = List<List<int>>.from(savedState['adjacencyMatrix']?.map((list) => List<int>.from(list)) ?? []);
        _nodes = List<String>.from(savedState['nodes'] ?? []);
        _isDirected = savedState['isDirected'] ?? false;
        _message = savedState['message'] ?? '';
      });
    }
  }

  void _saveState() {
    final service = Provider.of<InteractiveWidgetService>(context, listen: false);
    service.saveWidgetState(widget.widgetId, {
      'adjacencyList': _adjacencyList,
      'adjacencyMatrix': _adjacencyMatrix,
      'nodes': _nodes,
      'isDirected': _isDirected,
      'message': _message,
    });
  }

  void _addNode() {
    final node = _nodeController.text.trim();
    if (node.isNotEmpty && !_nodes.contains(node)) {
      setState(() {
        _nodes.add(node);
        _adjacencyList[node] = [];
        _updateAdjacencyMatrix();
        _message = 'Node "$node" added.';
      });
      _nodeController.clear();
      _saveState();
    } else {
      setState(() {
        _message = 'Please enter a unique node name.';
      });
    }
  }

  void _addEdge() {
    final fromNode = _edgeFromController.text.trim();
    final toNode = _edgeToController.text.trim();

    if (_nodes.contains(fromNode) && _nodes.contains(toNode)) {
      setState(() {
        if (!_adjacencyList[fromNode]!.contains(toNode)) {
          _adjacencyList[fromNode]!.add(toNode);
          _updateAdjacencyMatrix();
          _message = 'Edge from "$fromNode" to "$toNode" added.';
        } else {
          _message = 'Edge already exists.';
        }
        if (!_isDirected && !_adjacencyList[toNode]!.contains(fromNode)) {
          _adjacencyList[toNode]!.add(fromNode);
          _updateAdjacencyMatrix();
        }
      });
      _edgeFromController.clear();
      _edgeToController.clear();
      _saveState();
    } else {
      setState(() {
        _message = 'Please enter valid existing nodes for the edge.';
      });
    }
  }

  void _updateAdjacencyMatrix() {
    int n = _nodes.length;
    _adjacencyMatrix = List.generate(n, (_) => List.filled(n, 0));

    for (int i = 0; i < n; i++) {
      String node1 = _nodes[i];
      for (int j = 0; j < n; j++) {
        String node2 = _nodes[j];
        if (_adjacencyList[node1]!.contains(node2)) {
          _adjacencyMatrix[i][j] = 1;
        }
      }
    }
  }

  void _toggleDirected() {
    setState(() {
      _isDirected = !_isDirected;
      _message = _isDirected ? 'Graph is now Directed.' : 'Graph is now Undirected.';
      // If switching to undirected, ensure symmetric edges
      if (!_isDirected) {
        Map<String, List<String>> newAdjacencyList = {};
        for (var node in _nodes) {
          newAdjacencyList[node] = List.from(_adjacencyList[node] ?? []);
        }
        _adjacencyList.forEach((node, neighbors) {
          for (var neighbor in neighbors) {
            if (!newAdjacencyList[neighbor]!.contains(node)) {
              newAdjacencyList[neighbor]!.add(node);
            }
          }
        });
        _adjacencyList = newAdjacencyList;
      }
      _updateAdjacencyMatrix();
    });
    _saveState();
  }

  void _resetGraph() {
    setState(() {
      _adjacencyList = {};
      _adjacencyMatrix = [];
      _nodes = [];
      _message = 'Graph reset.';
      _isDirected = false;
    });
    _saveState();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Interactive Graph Representation Tool',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 20),
          TextField(
            controller: _nodeController,
            decoration: const InputDecoration(
              labelText: 'Add Node (e.g., A, B)',
              border: OutlineInputBorder(),
            ),
          ),
          const SizedBox(height: 10),
          AnimatedButton(
            onTap: _addNode,
            text: 'Add Node',
            color: Colors.blue,
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _edgeFromController,
                  decoration: const InputDecoration(
                    labelText: 'From Node',
                    border: OutlineInputBorder(),
                  ),
                ),
              ),
              const SizedBox(width: 10),
              Expanded(
                child: TextField(
                  controller: _edgeToController,
                  decoration: const InputDecoration(
                    labelText: 'To Node',
                    border: OutlineInputBorder(),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 10),
          AnimatedButton(
            onTap: _addEdge,
            text: 'Add Edge',
            color: Colors.green,
          ),
          const SizedBox(height: 20),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              AnimatedButton(
                onTap: _toggleDirected,
                text: _isDirected ? 'Switch to Undirected' : 'Switch to Directed',
                color: Colors.orange,
              ),
              AnimatedButton(
                onTap: _resetGraph,
                text: 'Reset Graph',
                color: Colors.red,
              ),
            ],
          ),
          const SizedBox(height: 20),
          Text(
            'Nodes: ${_nodes.join(', ')}',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 10),
          Text(
            'Adjacency List:',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          ..._adjacencyList.entries.map((entry) => Text('${entry.key}: ${entry.value.join(', ')}')).toList(),
          const SizedBox(height: 10),
          Text(
            'Adjacency Matrix:',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          if (_nodes.isNotEmpty)
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const SizedBox(width: 24), // For row headers
                    ..._nodes.map((node) => Container(width: 24, alignment: Alignment.center, child: Text(node))).toList(),
                  ],
                ),
                ..._adjacencyMatrix.asMap().entries.map((rowEntry) {
                  int rowIndex = rowEntry.key;
                  List<int> row = rowEntry.value;
                  return Row(
                    children: [
                      Container(width: 24, alignment: Alignment.center, child: Text(_nodes[rowIndex])),
                      ...row.map((cell) => Container(width: 24, alignment: Alignment.center, child: Text(cell.toString()))).toList(),
                    ],
                  );
                }).toList(),
              ],
            ),
          const SizedBox(height: 20),
          Text(
            'Message: $_message',
            style: Theme.of(context).textTheme.bodyLarge,
          ),
        ],
      ),
    );
  }
}
