import 'package:flutter/material.dart';
import 'package:resonance/widgets/animated_button.dart';

class InteractiveFallacyAvoidanceChallenge extends StatefulWidget {
  const InteractiveFallacyAvoidanceChallenge({super.key});

  @override
  State<InteractiveFallacyAvoidanceChallenge> createState() => _InteractiveFallacyAvoidanceChallengeState();
}

class _InteractiveFallacyAvoidanceChallengeState extends State<InteractiveFallacyAvoidanceChallenge> {
  int _currentQuestionIndex = 0;
  String? _selectedOption;
  String _feedback = '';
  int _score = 0;

  final List<Map<String, dynamic>> _questions = [
    {
      'scenario': 'Your friend argues that we should ban all sugary drinks because one study showed a link between sugar and hyperactivity in children.',
      'question': 'Which fallacy is your friend likely committing, and how can you help them avoid it?',
      'options': [
        'Hasty Generalization; point out that one study is not enough evidence to make a universal claim.',
        'Ad Hominem; tell them not to attack the study\'s authors.',
        '<PERSON>lipper<PERSON> Slope; warn them about the inevitable collapse of society if sugary drinks are banned.',
        'Appeal to Emotion; tell them not to use emotional language.',
      ],
      'correctAnswer': 'Hasty Generalization; point out that one study is not enough evidence to make a universal claim.',
      'explanation': 'Basing a broad conclusion on a single study is a Hasty Generalization. To avoid it, one needs more comprehensive and replicated evidence.',
    },
    {
      'scenario': 'A politician says, "My opponent wants to raise taxes, which means he hates the poor and wants them to suffer!"',
      'question': 'What fallacy is the politician using, and what\'s a better way to address the tax issue?',
      'options': [
        'Red Herring; change the topic to something else.',
        'Straw Man; accurately represent the opponent\'s tax plan and discuss its potential economic impacts.',
        'Bandwagon; point out that not everyone agrees with the tax hike.',
        'False Dilemma; suggest there are only two ways to handle taxes.',
      ],
      'correctAnswer': 'Straw Man; accurately represent the opponent\'s tax plan and discuss its potential economic impacts.',
      'explanation': 'The politician is creating a Straw Man by misrepresenting the opponent\'s position. A better approach is to address the actual tax plan and its likely effects.',
    },
    {
      'scenario': 'Your teacher tells you that you must study hard for the exam, otherwise you will fail the course and never get into college.',
      'question': 'Which fallacy is the teacher using, and how can you reframe their advice?',
      'options': [
        'Appeal to Authority; remind them they are just a teacher.',
        'Circular Reasoning; ask them to explain why studying hard leads to good grades.',
        'Slippery Slope; reframe the advice to focus on the direct benefits of studying for the exam, like better understanding and a higher grade.',
        'Appeal to Emotion; tell them not to make you feel guilty.',
      ],
      'correctAnswer': 'Slippery Slope; reframe the advice to focus on the direct benefits of studying for the exam, like better understanding and a higher grade.',
      'explanation': 'The teacher is using a Slippery Slope fallacy by presenting an exaggerated chain of negative consequences. Reframing focuses on direct, probable outcomes.',
    },
  ];

  void _checkAnswer() {
    if (_selectedOption == null) {
      setState(() {
        _feedback = 'Please select an option.';
      });
      return;
    }

    if (_selectedOption == _questions[_currentQuestionIndex]['correctAnswer']) {
      setState(() {
        _feedback = 'Correct! ${_questions[_currentQuestionIndex]['explanation']}';
        _score++;
      });
    } else {
      setState(() {
        _feedback = 'Incorrect. The correct answer was: "${_questions[_currentQuestionIndex]['correctAnswer']}". ${_questions[_currentQuestionIndex]['explanation']}';
      });
    }
  }

  void _nextQuestion() {
    setState(() {
      if (_currentQuestionIndex < _questions.length - 1) {
        _currentQuestionIndex++;
        _selectedOption = null;
        _feedback = '';
      } else {
        _feedback = 'Challenge Completed! Your score: $_score / ${_questions.length}';
      }
    });
  }

  void _resetChallenge() {
    setState(() {
      _currentQuestionIndex = 0;
      _selectedOption = null;
      _feedback = '';
      _score = 0;
    });
  }

  @override
  Widget build(BuildContext context) {
    final currentQuestion = _questions[_currentQuestionIndex];

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text(
            'Fallacy Avoidance Challenge',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.primary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 20),
          Card(
            elevation: 4,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            color: Theme.of(context).colorScheme.surfaceVariant,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Scenario:',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                  const SizedBox(height: 10),
                  Text(
                    currentQuestion['scenario'],
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                  const SizedBox(height: 15),
                  Text(
                    currentQuestion['question'],
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                  ... (currentQuestion['options'] as List<String>).map((option) {
                    return RadioListTile<String>(
                      title: Text(
                        option,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                      value: option,
                      groupValue: _selectedOption,
                      onChanged: (value) {
                        setState(() {
                          _selectedOption = value;
                          _feedback = ''; // Clear feedback on new selection
                        });
                      },
                      activeColor: Theme.of(context).colorScheme.secondary,
                    );
                  }).toList(),
                ],
              ),
            ),
          ),
          const SizedBox(height: 20),
          AnimatedButton(
            onTap: _checkAnswer,
            label: 'Check Answer',
            color: Theme.of(context).colorScheme.secondary,
            labelColor: Theme.of(context).colorScheme.onSecondary,
          ),
          if (_feedback.isNotEmpty) ...[
            const SizedBox(height: 10),
            Text(
              _feedback,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: _feedback.startsWith('Correct') ? Colors.green : Colors.red,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
          ],
          const SizedBox(height: 20),
          AnimatedButton(
            onTap: _nextQuestion,
            label: _currentQuestionIndex < _questions.length - 1 ? 'Next Question' : 'Finish Challenge',
            color: Theme.of(context).colorScheme.tertiary,
            labelColor: Theme.of(context).colorScheme.onTertiary,
          ),
          if (_currentQuestionIndex == _questions.length - 1 && _feedback.contains('Completed'))
            Padding(
              padding: const EdgeInsets.only(top: 20.0),
              child: AnimatedButton(
                onTap: _resetChallenge,
                label: 'Restart Challenge',
                color: Theme.of(context).colorScheme.primary,
                labelColor: Theme.of(context).colorScheme.onPrimary,
              ),
            ),
        ],
      ),
    );
  }
}
