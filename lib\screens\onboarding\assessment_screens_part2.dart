import 'package:flutter/material.dart';
import '../../models/user_preferences.dart';
import '../../widgets/animated_selection_option.dart';
import '../../widgets/animated_text.dart';
import '../../widgets/animated_icon_widget.dart';
import '../../widgets/animated_button.dart';

// Screen 5: Topic selection
class TopicSelectionScreen extends StatefulWidget {
  final UserPreferences userPreferences;
  final VoidCallback onContinue;

  const TopicSelectionScreen({
    Key? key,
    required this.userPreferences,
    required this.onContinue,
  }) : super(key: key);

  @override
  _TopicSelectionScreenState createState() => _TopicSelectionScreenState();
}

class _TopicSelectionScreenState extends State<TopicSelectionScreen> {
  final List<String> _topics = [
    'Mathematics',
    'Science and Engineering',
    'Computer Science',
    'Data Analysis',
    'Languages',
    'Arts and Humanities',
  ];

  final List<String> _levels = [
    'Beginner',
    'Intermediate',
    'Advanced',
    'Expert',
  ];

  String? _selectedTopic;
  String? _selectedLevel;

  @override
  void initState() {
    super.initState();
    _selectedTopic = widget.userPreferences.topicToExplore;
    _selectedLevel = widget.userPreferences.userLevel;
  }

  void _selectTopic(String topic) {
    setState(() {
      _selectedTopic = topic;
      widget.userPreferences.topicToExplore = topic;
    });
  }

  void _selectLevel(String level) {
    setState(() {
      _selectedLevel = level;
      widget.userPreferences.userLevel = level;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          AnimatedHeadingText(
            text: 'Which topic do you want to explore first?',
            style: TextStyle(
              color: Color.fromRGBO(16, 17, 20, 1),
              fontFamily: 'WorkSans',
              fontSize: 30,
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 24),

          // Topic options with animation
          SizedBox(
            height: 200,
            child: ListView.builder(
              itemCount: _topics.length,
              itemBuilder: (context, index) {
                return AnimatedSelectionOption(
                  text: _topics[index],
                  isSelected: _selectedTopic == _topics[index],
                  onTap: () => _selectTopic(_topics[index]),
                  icon: _getIconForTopic(_topics[index]),
                  index: index,
                );
              },
            ),
          ),

          SizedBox(height: 24),

          AnimatedHeadingText(
            text: 'What\'s your level?',
            style: TextStyle(
              color: Color.fromRGBO(16, 17, 20, 1),
              fontFamily: 'WorkSans',
              fontSize: 24,
              fontWeight: FontWeight.w600,
            ),
            delay: 200,
          ),
          SizedBox(height: 16),

          // Level options with animation
          SizedBox(
            height: 120,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _levels.length,
              itemBuilder: (context, index) {
                final level = _levels[index];
                return Padding(
                  padding: EdgeInsets.only(right: 12),
                  child: AnimatedScale(
                    scale: _selectedLevel == level ? 1.05 : 1.0,
                    duration: Duration(milliseconds: 200),
                    child: AnimatedContainer(
                      duration: Duration(milliseconds: 300),
                      curve: Curves.easeOutCubic,
                      width: 120,
                      margin: EdgeInsets.symmetric(vertical: 8),
                      padding: EdgeInsets.symmetric(
                        vertical: 16,
                        horizontal: 12,
                      ),
                      decoration: BoxDecoration(
                        color:
                            _selectedLevel == level
                                ? Color.fromRGBO(100, 45, 176, 1)
                                : Color.fromRGBO(243, 243, 243, 1),
                        borderRadius: BorderRadius.circular(16),
                        boxShadow:
                            _selectedLevel == level
                                ? [
                                  BoxShadow(
                                    color: Color.fromRGBO(100, 45, 176, 0.3),
                                    blurRadius: 8,
                                    offset: Offset(0, 3),
                                  ),
                                ]
                                : null,
                      ),
                      child: InkWell(
                        onTap: () => _selectLevel(level),
                        borderRadius: BorderRadius.circular(16),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            // Level icon
                            Icon(
                              _getIconForLevel(level),
                              color:
                                  _selectedLevel == level
                                      ? Colors.white
                                      : Color.fromRGBO(100, 45, 176, 1),
                              size: 24,
                            ),
                            SizedBox(height: 8),
                            // Level text
                            Text(
                              level,
                              textAlign: TextAlign.center,
                              overflow: TextOverflow.ellipsis,
                              style: TextStyle(
                                color:
                                    _selectedLevel == level
                                        ? Colors.white
                                        : Color.fromRGBO(16, 17, 20, 1),
                                fontFamily: 'WorkSans',
                                fontSize: 13,
                                fontWeight:
                                    _selectedLevel == level
                                        ? FontWeight.w600
                                        : FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  IconData? _getIconForTopic(String topic) {
    switch (topic) {
      case 'Mathematics':
        return Icons.calculate;
      case 'Science and Engineering':
        return Icons.science;
      case 'Computer Science':
        return Icons.computer;
      case 'Data Analysis':
        return Icons.bar_chart;
      case 'Languages':
        return Icons.translate;
      case 'Arts and Humanities':
        return Icons.palette;
      default:
        return null;
    }
  }

  IconData _getIconForLevel(String level) {
    switch (level) {
      case 'Beginner':
        return Icons.emoji_people;
      case 'Intermediate':
        return Icons.directions_run;
      case 'Advanced':
        return Icons.rocket_launch;
      case 'Expert':
        return Icons.military_tech;
      default:
        return Icons.school;
    }
  }
}

// Screen 6: Effectiveness message
class EffectivenessScreen extends StatelessWidget {
  final UserPreferences userPreferences;
  final VoidCallback onContinue;

  const EffectivenessScreen({
    Key? key,
    required this.userPreferences,
    required this.onContinue,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Effectiveness icon with animation
          AnimatedIconWidget(
            icon: Icons.speed,
            size: 120,
            color: Color.fromRGBO(100, 45, 176, 1),
          ),
          SizedBox(height: 32),
          AnimatedHeadingText(
            text: 'Learn 6x More Effectively',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Color.fromRGBO(16, 17, 20, 1),
              fontFamily: 'WorkSans',
              fontSize: 30,
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 16),
          AnimatedBodyText(
            text:
                'Our adaptive learning system personalizes your experience to maximize retention and understanding.',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Color.fromRGBO(57, 59, 66, 1),
              fontFamily: 'WorkSans',
              fontSize: 18,
              fontWeight: FontWeight.w400,
            ),
            delay: 200,
          ),
          SizedBox(height: 16),
          AnimatedBodyText(
            text: 'Let\'s set up your daily learning routine.',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Color.fromRGBO(100, 45, 176, 1),
              fontFamily: 'WorkSans',
              fontSize: 18,
              fontWeight: FontWeight.w500,
            ),
            delay: 300,
          ),
          SizedBox(height: 48),
          AnimatedButton(
            onPressed: onContinue,
            text: 'Continue',
            icon: Icons.arrow_forward,
            backgroundColor: Color.fromRGBO(16, 17, 20, 1),
            textColor: Colors.white,
            delay: 400,
          ),
        ],
      ),
    );
  }
}
