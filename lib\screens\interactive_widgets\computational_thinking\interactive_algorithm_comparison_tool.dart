import 'package:flutter/material.dart';
import 'dart:math';

class InteractiveAlgorithmComparisonTool extends StatefulWidget {
  const InteractiveAlgorithmComparisonTool({super.key});

  @override
  State<InteractiveAlgorithmComparisonTool> createState() => _InteractiveAlgorithmComparisonToolState();
}

class _InteractiveAlgorithmComparisonToolState extends State<InteractiveAlgorithmComparisonTool> {
  String _selectedAlgorithm1 = 'Bubble Sort';
  String _selectedAlgorithm2 = 'Selection Sort';
  int _dataSize = 100;
  String _comparisonResult = '';

  final Map<String, Function> _algorithmPerformance = {
    'Bubble Sort': (int n) => n * n, // O(n^2)
    'Selection Sort': (int n) => n * n, // O(n^2)
    'Insertion Sort': (int n) => n * n, // O(n^2)
    'Merge Sort': (int n) => n * log(n) / ln(2), // O(n log n)
    'Quick Sort': (int n) => n * log(n) / ln(2), // O(n log n) (average)
  };

  @override
  void initState() {
    super.initState();
    _compareAlgorithms();
  }

  void _compareAlgorithms() {
    setState(() {
      double performance1 = _algorithmPerformance[_selectedAlgorithm1]!(_dataSize);
      double performance2 = _algorithmPerformance[_selectedAlgorithm2]!(_dataSize);

      _comparisonResult = '''
Performance for Data Size = $_dataSize:
$_selectedAlgorithm1: ${performance1.toStringAsFixed(0)} operations (approx.)
$_selectedAlgorithm2: ${performance2.toStringAsFixed(0)} operations (approx.)

Interpretation:
${_getInterpretation(performance1, performance2)}
''';
    });
  }

  String _getInterpretation(double p1, double p2) {
    if (p1 < p2) {
      return '$_selectedAlgorithm1 is generally more efficient for this data size.';
    } else if (p2 < p1) {
      return '$_selectedAlgorithm2 is generally more efficient for this data size.';
    } else {
      return 'Both algorithms have similar efficiency for this data size.';
    }
  }

  // Helper for natural logarithm
  static const double ln2 = 0.69314718056;
  static double ln(double x) => log(x) / ln2;

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Interactive Algorithm Comparison Tool',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                Column(
                  children: [
                    const Text('Algorithm 1:'),
                    DropdownButton<String>(
                      value: _selectedAlgorithm1,
                      onChanged: (String? newValue) {
                        setState(() {
                          _selectedAlgorithm1 = newValue!;
                          _compareAlgorithms();
                        });
                      },
                      items: _algorithmPerformance.keys
                          .map<DropdownMenuItem<String>>((String value) {
                        return DropdownMenuItem<String>(
                          value: value,
                          child: Text(value),
                        );
                      }).toList(),
                    ),
                  ],
                ),
                Column(
                  children: [
                    const Text('Algorithm 2:'),
                    DropdownButton<String>(
                      value: _selectedAlgorithm2,
                      onChanged: (String? newValue) {
                        setState(() {
                          _selectedAlgorithm2 = newValue!;
                          _compareAlgorithms();
                        });
                      },
                      items: _algorithmPerformance.keys
                          .map<DropdownMenuItem<String>>((String value) {
                        return DropdownMenuItem<String>(
                          value: value,
                          child: Text(value),
                        );
                      }).toList(),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 20),
            Text('Data Size: $_dataSize'),
            Slider(
              value: _dataSize.toDouble(),
              min: 10.0,
              max: 1000.0,
              divisions: 99,
              onChanged: (newValue) {
                setState(() {
                  _dataSize = newValue.toInt();
                  _compareAlgorithms();
                });
              },
            ),
            const SizedBox(height: 20),
            Container(
              padding: const EdgeInsets.all(12.0),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(8.0),
              ),
              width: double.infinity,
              child: Text(
                _comparisonResult,
                style: TextStyle(fontSize: 14, color: Colors.black87),
              ),
            ),
            const SizedBox(height: 20),
            const Text(
              'Compare the theoretical performance of different algorithms based on data size.',
              style: TextStyle(fontStyle: FontStyle.italic, color: Colors.grey),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
