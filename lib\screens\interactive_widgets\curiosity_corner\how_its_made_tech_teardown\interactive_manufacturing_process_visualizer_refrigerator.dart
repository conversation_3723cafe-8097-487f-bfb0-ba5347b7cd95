import 'package:flutter/material.dart';

class InteractiveManufacturingProcessVisualizerRefrigerator extends StatefulWidget {
  const InteractiveManufacturingProcessVisualizerRefrigerator({super.key});

  @override
  InteractiveManufacturingProcessVisualizerRefrigeratorState createState() => InteractiveManufacturingProcessVisualizerRefrigeratorState();
}

class InteractiveManufacturingProcessVisualizerRefrigeratorState extends State<InteractiveManufacturingProcessVisualizerRefrigerator> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  int _currentStep = 0;
  final List<String> _manufacturingSteps = [
    '1. Metal Sheet Forming (Cabinet & Door)',
    '2. Insulation Injection (Foaming)',
    '3. Refrigeration System Assembly (Compressor, Coils, etc.)',
    '4. Interior Component Installation (Shelves, Drawers, Lighting)',
    '5. Door Assembly & Hinging',
    '6. Electrical Wiring & Control Panel Installation',
    '7. Vacuum & Refrigerant Charging',
    '8. Leak Testing & Performance Testing',
    '9. Final Assembly & Packaging',
  ];

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _nextStep() {
    setState(() {
      if (_currentStep < _manufacturingSteps.length - 1) {
        _currentStep++;
        _controller.forward(from: 0.0);
      }
    });
  }

  void _previousStep() {
    setState(() {
      if (_currentStep > 0) {
        _currentStep--;
        _controller.forward(from: 0.0);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Refrigerator Manufacturing Process Visualizer',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            Container(
              height: 150,
              width: double.infinity,
              color: Colors.blueGrey[50],
              child: Center(
                child: FadeTransition(
                  opacity: _controller,
                  child: Text(
                    _manufacturingSteps[_currentStep],
                    textAlign: TextAlign.center,
                    style: const TextStyle(fontSize: 18, fontWeight: FontWeight.w500),
                  ),
                ),
              ),
            ),
            const SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ElevatedButton(
                  onPressed: _currentStep > 0 ? _previousStep : null,
                  child: const Text('Previous Step'),
                ),
                ElevatedButton(
                  onPressed: _currentStep < _manufacturingSteps.length - 1 ? _nextStep : null,
                  child: const Text('Next Step'),
                ),
              ],
            ),
            const SizedBox(height: 10),
            Text(
              'Step ${_currentStep + 1} of ${_manufacturingSteps.length}',
              style: const TextStyle(fontSize: 14, color: Colors.grey),
            ),
          ],
        ),
      ),
    );
  }
}
