import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class InteractiveRotationalKineticEnergyCalculator extends StatefulWidget {
  const InteractiveRotationalKineticEnergyCalculator({super.key});

  @override
  State<InteractiveRotationalKineticEnergyCalculator> createState() => _InteractiveRotationalKineticEnergyCalculatorState();
}

class _InteractiveRotationalKineticEnergyCalculatorState extends State<InteractiveRotationalKineticEnergyCalculator> {
  final TextEditingController _momentOfInertiaController = TextEditingController();
  final TextEditingController _angularVelocityController = TextEditingController();
  String _result = '';

  @override
  void dispose() {
    _momentOfInertiaController.dispose();
    _angularVelocityController.dispose();
    super.dispose();
  }

  void _calculateRotationalKineticEnergy() {
    final double? i = double.tryParse(_momentOfInertiaController.text);
    final double? w = double.tryParse(_angularVelocityController.text);

    if (i == null || w == null || i <= 0) {
      setState(() {
        _result = 'Please enter valid positive numbers for moment of inertia and angular velocity.';
      });
      return;
    }

    // Rotational Kinetic Energy: KE_rot = 0.5 * I * ω²
    final double keRot = 0.5 * i * w * w;

    setState(() {
      _result = 'Rotational Kinetic Energy (KE_rot): ${keRot.toStringAsFixed(4)} Joules';
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Rotational Kinetic Energy Calculator'),
        backgroundColor: Colors.deepPurple,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'Calculate the rotational kinetic energy of an object.',
              style: TextStyle(fontSize: 16.0, fontStyle: FontStyle.italic),
            ),
            const SizedBox(height: 20),
            TextField(
              controller: _momentOfInertiaController,
              keyboardType: const TextInputType.numberWithOptions(decimal: true),
              inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*'))],
              decoration: const InputDecoration(
                labelText: 'Moment of Inertia (I) in kg·m²',
                border: OutlineInputBorder(),
                hintText: 'Enter I',
              ),
            ),
            const SizedBox(height: 15),
            TextField(
              controller: _angularVelocityController,
              keyboardType: const TextInputType.numberWithOptions(decimal: true),
              inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*'))],
              decoration: const InputDecoration(
                labelText: 'Angular Velocity (ω) in rad/s',
                border: OutlineInputBorder(),
                hintText: 'Enter ω',
              ),
            ),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: _calculateRotationalKineticEnergy,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.deepPurple,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 15),
                textStyle: const TextStyle(fontSize: 18),
              ),
              child: const Text('Calculate Rotational Kinetic Energy'),
            ),
            const SizedBox(height: 20),
            Card(
              elevation: 4,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Result:',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 10),
                    Text(
                      _result,
                      style: const TextStyle(fontSize: 20, color: Colors.blueAccent),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 20),
            const Text(
              'Formula used: KE_rot = 0.5 * I * ω²',
              style: TextStyle(fontSize: 14, fontStyle: FontStyle.italic, color: Colors.grey),
            ),
          ],
        ),
      ),
    );
  }
}
