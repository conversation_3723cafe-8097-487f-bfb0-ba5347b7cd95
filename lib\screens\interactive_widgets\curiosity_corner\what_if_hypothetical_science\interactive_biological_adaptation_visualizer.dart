import 'package:flutter/material.dart';
import 'package:resonance/widgets/interactive_widget_scaffold.dart';

class InteractiveBiologicalAdaptationVisualizer extends StatefulWidget {
  const InteractiveBiologicalAdaptationVisualizer({super.key});

  @override
  State<InteractiveBiologicalAdaptationVisualizer> createState() => _InteractiveBiologicalAdaptationVisualizerState();
}

class _InteractiveBiologicalAdaptationVisualizerState extends State<InteractiveBiologicalAdaptationVisualizer> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  double _adaptationLevel = 0.0; // 0.0 to 1.0, representing level of adaptation

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 3),
    )..repeat(reverse: true);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  String _getAdaptationDescription() {
    if (_adaptationLevel < 0.2) {
      return 'Early stages of adaptation: Subtle skin pigmentation changes, increased efficiency in light absorption at a cellular level.';
    } else if (_adaptationLevel < 0.5) {
      return 'Moderate adaptation: Visible skin color shifts (greenish tint), specialized light-harvesting organs (e.g., broader, thinner skin areas), and reduced need for external food sources.';
    } else if (_adaptationLevel < 0.8) {
      return 'Advanced adaptation: Pronounced green skin, highly efficient light-absorbing structures (e.g., leaf-like appendages), and significant reduction in digestive system complexity.';
    } else {
      return 'Full adaptation: Human-plant hybrids with extensive chlorophyll integration, minimal digestive needs, and reliance almost entirely on sunlight for energy.';
    }
  }

  IconData _getAdaptationIcon() {
    if (_adaptationLevel < 0.2) {
      return Icons.person;
    } else if (_adaptationLevel < 0.5) {
      return Icons.person_outline; // Placeholder for a slightly adapted person icon
    } else if (_adaptationLevel < 0.8) {
      return Icons.nature_people;
    } else {
      return Icons.local_florist;
    }
  }

  Color _getAdaptationColor() {
    if (_adaptationLevel < 0.2) {
      return Colors.brown;
    } else if (_adaptationLevel < 0.5) {
      return Colors.lightGreen;
    } else if (_adaptationLevel < 0.8) {
      return Colors.green;
    } else {
      return Colors.green.shade900;
    }
  }

  @override
  Widget build(BuildContext context) {
    return InteractiveWidgetScaffold(
      title: 'Biological Adaptation Visualizer',
      description: 'Visualize biological adaptations for photosynthesis in humans.',
      interactiveContent: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            AnimatedBuilder(
              animation: _controller,
              builder: (context, child) {
                return Transform.scale(
                  scale: 1.0 + (_controller.value * 0.1 * _adaptationLevel),
                  child: Icon(
                    _getAdaptationIcon(),
                    size: 100,
                    color: _getAdaptationColor(),
                  ),
                );
              },
            ),
            const SizedBox(height: 20),
            Text(
              'Adaptation Level: ${(_adaptationLevel * 100).toStringAsFixed(0)}%',
              style: const TextStyle(fontSize: 18),
            ),
            Slider(
              value: _adaptationLevel,
              min: 0.0,
              max: 1.0,
              divisions: 10,
              label: (_adaptationLevel * 100).toStringAsFixed(0),
              onChanged: (value) {
                setState(() {
                  _adaptationLevel = value;
                });
              },
            ),
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: Text(
                'Adjust the slider to visualize hypothetical biological adaptations in humans if they could photosynthesize. Observe the icon and color changes, and read the description to understand the evolutionary pathway.',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 16),
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Text(
                _getAdaptationDescription(),
                textAlign: TextAlign.center,
                style: const TextStyle(fontSize: 16, fontStyle: FontStyle.italic),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
