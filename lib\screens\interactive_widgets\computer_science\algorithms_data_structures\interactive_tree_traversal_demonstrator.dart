import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:resonance_app/services/interactive_widget_service.dart';
import 'package:resonance_app/widgets/animated_button.dart';
import 'dart:math';

class InteractiveTreeTraversalDemonstrator extends StatefulWidget {
  final String widgetId;

  const InteractiveTreeTraversalDemonstrator({Key? key, required this.widgetId}) : super(key: key);

  @override
  _InteractiveTreeTraversalDemonstratorState createState() => _InteractiveTreeTraversalDemonstratorState();
}

class _InteractiveTreeTraversalDemonstratorState extends State<InteractiveTreeTraversalDemonstrator> {
  TreeNode? _root;
  List<int> _traversalOrder = [];
  String _message = '';
  bool _isTraversing = false;
  double _animationSpeed = 500; // milliseconds

  @override
  void initState() {
    super.initState();
    _loadState();
    _buildInitialTree();
  }

  void _loadState() async {
    final service = Provider.of<InteractiveWidgetService>(context, listen: false);
    final savedState = await service.getWidgetState(widget.widgetId);
    if (savedState != null) {
      setState(() {
        _root = _deserializeTree(savedState['tree']);
        _traversalOrder = List<int>.from(savedState['traversalOrder'] ?? []);
        _message = savedState['message'] ?? '';
        _isTraversing = savedState['isTraversing'] ?? false;
        _animationSpeed = savedState['animationSpeed'] ?? 500;
      });
    }
  }

  void _saveState() {
    final service = Provider.of<InteractiveWidgetService>(context, listen: false);
    service.saveWidgetState(widget.widgetId, {
      'tree': _serializeTree(_root),
      'traversalOrder': _traversalOrder,
      'message': _message,
      'isTraversing': _isTraversing,
      'animationSpeed': _animationSpeed,
    });
  }

  Map<String, dynamic>? _serializeTree(TreeNode? node) {
    if (node == null) return null;
    return {
      'value': node.value,
      'left': _serializeTree(node.left),
      'right': _serializeTree(node.right),
    };
  }

  TreeNode? _deserializeTree(Map<String, dynamic>? data) {
    if (data == null) return null;
    final node = TreeNode(data['value']);
    node.left = _deserializeTree(data['left']);
    node.right = _deserializeTree(data['right']);
    return node;
  }

  void _buildInitialTree() {
    setState(() {
      _root = null;
      _insertNode(50);
      _insertNode(30);
      _insertNode(70);
      _insertNode(20);
      _insertNode(40);
      _insertNode(60);
      _insertNode(80);
      _message = 'Initial tree built.';
      _traversalOrder.clear();
    });
    _saveState();
  }

  void _insertNode(int value) {
    if (_root == null) {
      _root = TreeNode(value);
      return;
    }
    TreeNode? current = _root;
    while (current != null) {
      if (value < current.value) {
        if (current.left == null) {
          current.left = TreeNode(value);
          return;
        }
        current = current.left;
      } else if (value > current.value) {
        if (current.right == null) {
          current.right = TreeNode(value);
          return;
        }
        current = current.right;
      } else {
        return;
      }
    }
  }

  void _resetTraversal() {
    setState(() {
      _traversalOrder.clear();
      _message = 'Traversal reset.';
      _isTraversing = false;
    });
    _saveState();
  }

  Future<void> _inorderTraversal(TreeNode? node) async {
    if (node == null) return;

    setState(() {
      _message = 'Traversing left subtree of ${node.value}';
    });
    _saveState();
    await Future.delayed(Duration(milliseconds: _animationSpeed.toInt()));
    await _inorderTraversal(node.left);

    setState(() {
      _traversalOrder.add(node.value);
      _message = 'Visiting node: ${node.value}';
    });
    _saveState();
    await Future.delayed(Duration(milliseconds: _animationSpeed.toInt()));

    setState(() {
      _message = 'Traversing right subtree of ${node.value}';
    });
    _saveState();
    await Future.delayed(Duration(milliseconds: _animationSpeed.toInt()));
    await _inorderTraversal(node.right);
  }

  Future<void> _preorderTraversal(TreeNode? node) async {
    if (node == null) return;

    setState(() {
      _traversalOrder.add(node.value);
      _message = 'Visiting node: ${node.value}';
    });
    _saveState();
    await Future.delayed(Duration(milliseconds: _animationSpeed.toInt()));

    setState(() {
      _message = 'Traversing left subtree of ${node.value}';
    });
    _saveState();
    await Future.delayed(Duration(milliseconds: _animationSpeed.toInt()));
    await _preorderTraversal(node.left);

    setState(() {
      _message = 'Traversing right subtree of ${node.value}';
    });
    _saveState();
    await Future.delayed(Duration(milliseconds: _animationSpeed.toInt()));
    await _preorderTraversal(node.right);
  }

  Future<void> _postorderTraversal(TreeNode? node) async {
    if (node == null) return;

    setState(() {
      _message = 'Traversing left subtree of ${node.value}';
    });
    _saveState();
    await Future.delayed(Duration(milliseconds: _animationSpeed.toInt()));
    await _postorderTraversal(node.left);

    setState(() {
      _message = 'Traversing right subtree of ${node.value}';
    });
    _saveState();
    await Future.delayed(Duration(milliseconds: _animationSpeed.toInt()));
    await _postorderTraversal(node.right);

    setState(() {
      _traversalOrder.add(node.value);
      _message = 'Visiting node: ${node.value}';
    });
    _saveState();
    await Future.delayed(Duration(milliseconds: _animationSpeed.toInt()));
  }

  Future<void> _startTraversal(String type) async {
    _resetTraversal();
    if (_root == null) {
      setState(() {
        _message = 'Tree is empty. Cannot traverse.';
      });
      return;
    }

    setState(() {
      _isTraversing = true;
      _message = 'Starting $type traversal...';
    });
    _saveState();
    await Future.delayed(Duration(milliseconds: _animationSpeed.toInt()));

    switch (type) {
      case 'Inorder':
        await _inorderTraversal(_root);
        break;
      case 'Preorder':
        await _preorderTraversal(_root);
        break;
      case 'Postorder':
        await _postorderTraversal(_root);
        break;
    }

    setState(() {
      _isTraversing = false;
      _message = '$type traversal completed. Order: ${_traversalOrder.join(' -> ')}';
    });
    _saveState();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Interactive Tree Traversal Demonstrator',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                child: Slider(
                  value: _animationSpeed,
                  min: 50,
                  max: 1000,
                  divisions: 19,
                  label: _animationSpeed.round().toString(),
                  onChanged: (double value) {
                    setState(() {
                      _animationSpeed = value;
                    });
                  },
                ),
              ),
              Text('Speed: ${_animationSpeed.round()}ms'),
            ],
          ),
          const SizedBox(height: 10),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              AnimatedButton(
                onTap: _isTraversing ? null : () => _startTraversal('Inorder'),
                text: 'Inorder',
                color: _isTraversing ? Colors.grey : Colors.blue,
              ),
              AnimatedButton(
                onTap: _isTraversing ? null : () => _startTraversal('Preorder'),
                text: 'Preorder',
                color: _isTraversing ? Colors.grey : Colors.green,
              ),
              AnimatedButton(
                onTap: _isTraversing ? null : () => _startTraversal('Postorder'),
                text: 'Postorder',
                color: _isTraversing ? Colors.grey : Colors.purple,
              ),
            ],
          ),
          const SizedBox(height: 10),
          AnimatedButton(
            onTap: _buildInitialTree,
            text: 'Reset Tree',
            color: Colors.orange,
          ),
          const SizedBox(height: 20),
          Text(
            'Traversal Order: ${_traversalOrder.join(' -> ')}',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 20),
          Text(
            'Message: $_message',
            style: Theme.of(context).textTheme.bodyLarge,
          ),
          const SizedBox(height: 20),
          Expanded(
            child: SingleChildScrollView(
              child: _root == null
                  ? const Center(child: Text('Tree is empty.'))
                  : TreeView(root: _root!),
            ),
          ),
        ],
      ),
    );
  }
}

class TreeNode {
  int value;
  TreeNode? left;
  TreeNode? right;

  TreeNode(this.value);
}

class TreeView extends StatelessWidget {
  final TreeNode root;

  const TreeView({Key? key, required this.root}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        return CustomPaint(
          painter: TreePainter(root),
          size: Size(constraints.maxWidth, _calculateTreeHeight(root) * 50.0 + 50), // Adjust height dynamically
        );
      },
    );
  }

  double _calculateTreeHeight(TreeNode? node) {
    if (node == null) return 0;
    return 1 + max(_calculateTreeHeight(node.left), _calculateTreeHeight(node.right));
  }
}

class TreePainter extends CustomPainter {
  final TreeNode root;
  final double nodeRadius = 15;
  final double horizontalSpacing = 40;
  final double verticalSpacing = 50;

  TreePainter(this.root);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.black
      ..strokeWidth = 2;

    _drawNode(canvas, root, size.width / 2, nodeRadius + 10, size.width / 4);
  }

  void _drawNode(Canvas canvas, TreeNode node, double x, double y, double horizontalOffset) {
    // Draw node circle
    canvas.drawCircle(Offset(x, y), nodeRadius, Paint()..color = Colors.blue);
    // Draw node text
    TextPainter textPainter = TextPainter(
      text: TextSpan(
        text: node.value.toString(),
        style: const TextStyle(color: Colors.white, fontSize: 12, fontWeight: FontWeight.bold),
      ),
      textDirection: TextDirection.ltr,
    );
    textPainter.layout();
    textPainter.paint(canvas, Offset(x - textPainter.width / 2, y - textPainter.height / 2));

    // Draw lines to children
    if (node.left != null) {
      canvas.drawLine(Offset(x, y + nodeRadius), Offset(x - horizontalOffset, y + verticalSpacing - nodeRadius), Paint()..color = Colors.black..strokeWidth = 2);
      _drawNode(canvas, node.left!, x - horizontalOffset, y + verticalSpacing, horizontalOffset / 2);
    }
    if (node.right != null) {
      canvas.drawLine(Offset(x, y + nodeRadius), Offset(x + horizontalOffset, y + verticalSpacing - nodeRadius), Paint()..color = Colors.black..strokeWidth = 2);
      _drawNode(canvas, node.right!, x + horizontalOffset, y + verticalSpacing, horizontalOffset / 2);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}
