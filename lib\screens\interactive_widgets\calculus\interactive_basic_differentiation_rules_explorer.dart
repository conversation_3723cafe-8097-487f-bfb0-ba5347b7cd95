import 'package:flutter/material.dart';
import 'dart:math';

class InteractiveBasicDifferentiationRulesExplorer extends StatefulWidget {
  const InteractiveBasicDifferentiationRulesExplorer({super.key});

  @override
  State<InteractiveBasicDifferentiationRulesExplorer> createState() => _InteractiveBasicDifferentiationRulesExplorerState();
}

class _InteractiveBasicDifferentiationRulesExplorerState extends State<InteractiveBasicDifferentiationRulesExplorer> {
  String _selectedRule = 'Power Rule';
  double _power = 2.0; // For Power Rule: x^n
  double _constant = 5.0; // For Constant Rule: c, and Constant Multiple Rule: c*f(x)

  String _functionDisplay = '';
  String _derivativeDisplay = '';

  @override
  void initState() {
    super.initState();
    _updateDisplays();
  }

  void _updateDisplays() {
    setState(() {
      switch (_selectedRule) {
        case 'Power Rule':
          _functionDisplay = 'f(x) = x^${_power.toStringAsFixed(0)}';
          _derivativeDisplay = 'f\'(x) = ${_power.toStringAsFixed(0)}x^${(_power - 1).toStringAsFixed(0)}';
          break;
        case 'Constant Rule':
          _functionDisplay = 'f(x) = ${_constant.toStringAsFixed(0)}';
          _derivativeDisplay = 'f\'(x) = 0';
          break;
        case 'Constant Multiple Rule':
          _functionDisplay = 'f(x) = ${_constant.toStringAsFixed(0)}x^2';
          _derivativeDisplay = 'f\'(x) = ${(_constant * 2).toStringAsFixed(0)}x';
          break;
        case 'Sum/Difference Rule':
          _functionDisplay = 'f(x) = x^2 + x';
          _derivativeDisplay = 'f\'(x) = 2x + 1';
          break;
        case 'Product Rule':
          _functionDisplay = 'f(x) = (x^2)(sin(x))';
          _derivativeDisplay = 'f\'(x) = 2x sin(x) + x^2 cos(x)';
          break;
        case 'Quotient Rule':
          _functionDisplay = 'f(x) = sin(x) / x';
          _derivativeDisplay = 'f\'(x) = (x cos(x) - sin(x)) / x^2';
          break;
        case 'Chain Rule':
          _functionDisplay = 'f(x) = (x^2 + 1)^3';
          _derivativeDisplay = 'f\'(x) = 3(x^2 + 1)^2 (2x)';
          break;
        default:
          _functionDisplay = '';
          _derivativeDisplay = '';
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Interactive Basic Differentiation Rules Explorer',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            DropdownButton<String>(
              value: _selectedRule,
              onChanged: (String? newValue) {
                setState(() {
                  _selectedRule = newValue!;
                  _updateDisplays();
                });
              },
              items: <String>[
                'Power Rule',
                'Constant Rule',
                'Constant Multiple Rule',
                'Sum/Difference Rule',
                'Product Rule',
                'Quotient Rule',
                'Chain Rule',
              ].map<DropdownMenuItem<String>>((String value) {
                return DropdownMenuItem<String>(
                  value: value,
                  child: Text(value),
                );
              }).toList(),
            ),
            const SizedBox(height: 20),
            if (_selectedRule == 'Power Rule')
              Column(
                children: [
                  Text('Power (n): ${_power.toStringAsFixed(0)}'),
                  Slider(
                    value: _power,
                    min: 0.0,
                    max: 5.0,
                    divisions: 5,
                    onChanged: (newValue) {
                      setState(() {
                        _power = newValue;
                        _updateDisplays();
                      });
                    },
                  ),
                ],
              ),
            if (_selectedRule == 'Constant Rule' || _selectedRule == 'Constant Multiple Rule')
              Column(
                children: [
                  Text('Constant (c): ${_constant.toStringAsFixed(0)}'),
                  Slider(
                    value: _constant,
                    min: 1.0,
                    max: 10.0,
                    divisions: 9,
                    onChanged: (newValue) {
                      setState(() {
                        _constant = newValue;
                        _updateDisplays();
                      });
                    },
                  ),
                ],
              ),
            const SizedBox(height: 20),
            Text(
              'Function: $_functionDisplay',
              style: TextStyle(fontSize: 16, color: Colors.blue),
            ),
            const SizedBox(height: 10),
            Text(
              'Derivative: $_derivativeDisplay',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: Colors.green),
            ),
            const SizedBox(height: 20),
            // Optional: Add a simple graph or visual representation for some rules
            // For now, just text display
          ],
        ),
      ),
    );
  }
}
