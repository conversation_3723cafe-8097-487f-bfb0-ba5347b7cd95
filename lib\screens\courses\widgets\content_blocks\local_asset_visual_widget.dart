import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class LocalAssetVisualWidget extends StatelessWidget {
  final String assetPath;
  final double? height;
  final double? width;

  const LocalAssetVisualWidget({
    super.key,
    required this.assetPath,
    this.height,
    this.width,
  });

  @override
  Widget build(BuildContext context) {
    Widget imageWidget;
    bool isSvg = assetPath.toLowerCase().endsWith('.svg');

    try {
      if (isSvg) {
        imageWidget = SvgPicture.asset(
          assetPath,
          fit: BoxFit.contain,
          placeholderBuilder:
              (BuildContext context) => Container(
                padding: const EdgeInsets.all(8.0),
                child: const Center(
                  child: Icon(Icons.broken_image, size: 40, color: Colors.grey),
                ),
              ),
        );
      } else {
        imageWidget = Image.asset(
          assetPath,
          fit: BoxFit.contain,
          errorBuilder: (
            BuildContext context,
            Object exception,
            StackTrace? stackTrace,
          ) {
            return Container(
              padding: const EdgeInsets.all(8.0),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.error_outline,
                      size: 40,
                      color: Colors.redAccent,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Error loading: ${assetPath.split('/').last}',
                      textAlign: TextAlign.center,
                      style: const TextStyle(
                        fontSize: 12,
                        color: Colors.redAccent,
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        );
      }
    } catch (e) {
      // Fallback for any other error during asset loading attempt
      imageWidget = Container(
        padding: const EdgeInsets.all(8.0),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.hide_image_outlined,
                size: 40,
                color: Colors.grey,
              ),
              const SizedBox(height: 8),
              Text(
                'Asset not found: ${assetPath.split('/').last}',
                textAlign: TextAlign.center,
                style: const TextStyle(fontSize: 12, color: Colors.grey),
              ),
            ],
          ),
        ),
      );
    }

    return Container(
      width: double.infinity, // Try to take full width
      constraints: BoxConstraints(
        // Reduced max height to 25% of screen height (was 40%)
        maxHeight: height ?? MediaQuery.of(context).size.height * 0.25,
        // Add a max width constraint to prevent images from being too wide
        maxWidth: width ?? MediaQuery.of(context).size.width * 0.8,
      ),
      padding: const EdgeInsets.symmetric(
        vertical: 8.0, // Reduced vertical padding
        horizontal: 8.0,
      ),
      child: Center(child: imageWidget),
    );
  }
}
