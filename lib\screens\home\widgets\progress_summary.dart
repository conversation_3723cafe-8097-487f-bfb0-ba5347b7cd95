import 'package:flutter/material.dart';
import '../../../models/course_models.dart';

class ProgressSummary extends StatelessWidget {
  final List<Course> enrolledCourses;
  final User user;

  const ProgressSummary({
    Key? key,
    required this.enrolledCourses,
    required this.user,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Calculate overall progress
    double overallProgress = 0;
    if (enrolledCourses.isNotEmpty) {
      double totalProgress = 0;
      for (final course in enrolledCourses) {
        final progress =
            user.courseProgress[course.id]?.progressPercentage ?? 0;
        totalProgress += progress;
      }
      overallProgress = totalProgress / enrolledCourses.length;
    }

    // Calculate streak (placeholder for now)
    const streak = 3;

    return Container(
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [
            Color.fromRGBO(124, 66, 210, 1),
            Color.fromRGBO(100, 45, 176, 1),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: const Color.fromRGBO(
              124,
              66,
              210,
              1,
            ).withAlpha(76), // 0.3 opacity is approximately 76 in alpha
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          const Text(
            'Your Progress',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.w600,
              fontFamily: 'WorkSans',
            ),
          ),

          const SizedBox(height: 16),

          // Progress Stats
          Row(
            children: [
              // Overall Progress
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Overall Progress',
                      style: TextStyle(
                        color: Colors.white70,
                        fontSize: 14,
                        fontFamily: 'WorkSans',
                      ),
                    ),
                    const SizedBox(height: 8),
                    Stack(
                      children: [
                        // Background
                        Container(
                          height: 8,
                          decoration: BoxDecoration(
                            color: Colors.white.withAlpha(
                              51,
                            ), // 0.2 opacity is approximately 51 in alpha
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),
                        // Progress
                        FractionallySizedBox(
                          widthFactor: overallProgress / 100,
                          child: Container(
                            height: 8,
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(4),
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '${overallProgress.toInt()}%',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        fontFamily: 'WorkSans',
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(width: 24),

              // Streak
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Current Streak',
                    style: TextStyle(
                      color: Colors.white70,
                      fontSize: 14,
                      fontFamily: 'WorkSans',
                    ),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      const Icon(
                        Icons.local_fire_department,
                        color: Colors.orange,
                        size: 24,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        '$streak days',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          fontFamily: 'WorkSans',
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Daily Goal
          Row(
            children: [
              const Icon(Icons.timer_outlined, color: Colors.white70, size: 16),
              const SizedBox(width: 8),
              Text(
                'Daily Goal: ${user.dailyGoalMinutes ?? 15} minutes',
                style: const TextStyle(
                  color: Colors.white70,
                  fontSize: 14,
                  fontFamily: 'WorkSans',
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
