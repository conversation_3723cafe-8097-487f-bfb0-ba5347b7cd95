import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../../../models/course_models.dart';
import '../../../services/service_provider.dart';
import '../continuous_lesson_screen.dart';

class RoadmapScreen extends StatefulWidget {
  final String courseId;

  const RoadmapScreen({super.key, required this.courseId});

  @override
  State<RoadmapScreen> createState() => RoadmapScreenState();
}

class RoadmapScreenState extends State<RoadmapScreen> {
  late Course _course;
  late List<LessonDefinition> _lessons = []; // Changed to LessonDefinition
  // Track which module each lesson belongs to
  final Map<String, Module> _lessonModuleMap = {};
  // Track module start indices in the flattened lesson list
  final Map<int, Module> _moduleStartIndices = {};
  String _categoryName = 'COURSE';
  Color _categoryColor = Colors.blue; // Default accent color
  final Color _pathLineColor = Colors.grey[300]!;
  final Color _inactiveNodeBgColor = Colors.grey[200]!;
  final Color _darkTextColor = Color.fromRGBO(0, 0, 0, 0.85);
  final Color _lightTextColor = Colors.grey[600]!;
  final Color _completedNodeColor =
      Colors.blueGrey[300]!; // Color for completed nodes
  final Color _completedNodeContentColor = Colors.white;

  int _selectedLessonIndex = 0;
  bool _isLoading = true;
  final Map<String, bool> _lessonCompletionStatus = {};

  // Layout constants - refined based on image
  final double _nodeSize = 50.0;
  final double _selectedNodeSize =
      54.0; // Slightly larger when card is shown for it
  final double _verticalSpacingBetweenNodes =
      100.0; // Vertical distance between node CENTERS
  final double _horizontalNodeOffset =
      85.0; // Horizontal offset from centerline to node CENTER for side nodes
  final double _roadmapTopPadding = 35.0;
  final double _nodeTitleSpacing = 8.0;
  final double _levelTagHeight = 19.0;
  final double _levelTagHorizontalPadding = 7.0;
  final double _levelTagVerticalPadding = 3.0;
  final double _levelTagFontSize = 8.0;
  final double _nodeTitleFontSize = 10.0;
  final double _pathStrokeWidth = 1.3;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _loadCourseData();
      }
    });
  }

  Future<void> _loadCourseData() async {
    if (!mounted) return;
    setState(() => _isLoading = true);

    final courseService = ServiceProvider.of(context).courseService;
    debugPrint('RoadmapScreen: Loading course data for ID: ${widget.courseId}');

    // First try to get the course
    var course = await courseService.getCourseById(widget.courseId);

    if (course == null) {
      debugPrint('RoadmapScreen: Course not found');
      if (mounted) {
        setState(() => _isLoading = false);
        if (Navigator.canPop(context)) Navigator.of(context).pop();
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text("Course not found.")));
      }
      return;
    }

    debugPrint('RoadmapScreen: Course loaded: ${course.title}');
    debugPrint('RoadmapScreen: Course has ${course.modules.length} modules');

    // Check if modules have lessons
    int totalLessons = course.modules.fold(
      0,
      (sum, module) => sum + module.lessons.length,
    );
    debugPrint('RoadmapScreen: Course has $totalLessons total lessons');

    // If no lessons are loaded, try to reload with a cache clear
    if (totalLessons == 0) {
      debugPrint(
        'RoadmapScreen: No lessons found, clearing cache and reloading',
      );
      await courseService.clearCacheAndReloadCourses();

      // Try to get the course again
      course = await courseService.getCourseById(widget.courseId);

      if (course == null) {
        debugPrint('RoadmapScreen: Course still not found after cache clear');
        if (mounted) {
          setState(() => _isLoading = false);
        }
        return;
      }

      // Check lessons again
      totalLessons = course.modules.fold(
        0,
        (sum, module) => sum + module.lessons.length,
      );
      debugPrint(
        'RoadmapScreen: After reload, course has $totalLessons total lessons',
      );

      // If still no lessons, try to load each module individually
      if (totalLessons == 0) {
        debugPrint(
          'RoadmapScreen: Still no lessons, trying to load modules individually',
        );
        for (var moduleMetadata in course.modules) {
          final module = await courseService.getModuleById(
            course,
            moduleMetadata.id,
          );
          if (module != null) {
            debugPrint(
              'RoadmapScreen: Loaded module ${module.title} with ${module.lessons.length} lessons',
            );
          } else {
            debugPrint(
              'RoadmapScreen: Failed to load module ${moduleMetadata.id}',
            );
          }
        }

        // Try to get the course one more time
        course = await courseService.getCourseById(widget.courseId);
        if (course != null) {
          totalLessons = course.modules.fold(
            0,
            (sum, module) => sum + module.lessons.length,
          );
          debugPrint(
            'RoadmapScreen: After module loading, course has $totalLessons total lessons',
          );
        }
      }
    }

    _course = course!; // We know course is not null here

    // Clear previous mappings
    _lessonModuleMap.clear();
    _moduleStartIndices.clear();

    // Create a list of all lessons and track which module they belong to
    _lessons = [];
    int lessonIndex = 0;

    // Sort modules by order first
    final sortedModules = List<Module>.from(_course.modules)
      ..sort((a, b) => a.order.compareTo(b.order));

    for (final module in sortedModules) {
      // Sort lessons within this module
      final moduleLessons = List<LessonDefinition>.from(
        module.lessons,
      ) // Changed to LessonDefinition
      ..sort((a, b) => a.order.compareTo(b.order));

      if (moduleLessons.isNotEmpty) {
        // Mark the start of this module in the flattened list
        _moduleStartIndices[lessonIndex] = module;

        // Add lessons to the flattened list and track their module
        for (final lesson in moduleLessons) {
          _lessons.add(lesson);
          _lessonModuleMap[lesson.id] = module;
          lessonIndex++;
        }
      }
    }

    debugPrint(
      'RoadmapScreen: Flattened and sorted ${_lessons.length} lessons with ${_moduleStartIndices.length} modules',
    );

    final category = courseService.getCategoryById(_course.categoryId);
    _categoryName = category?.name.toUpperCase() ?? 'COURSE';
    _categoryColor = _parseColor(
      category?.color ?? '#007AFF',
    ); // Use category color as accent

    final user = courseService.currentUser;
    _selectedLessonIndex = 0;
    _lessonCompletionStatus.clear();

    if (user != null && user.courseProgress.containsKey(widget.courseId)) {
      final progress = user.courseProgress[widget.courseId]!;
      int highestCompletedLessonOrder = -1;
      for (int i = 0; i < _lessons.length; i++) {
        final lesson = _lessons[i];
        final lessonProgress = progress.lessonProgress[lesson.id];
        final isCompleted = lessonProgress?.isCompleted ?? false;
        _lessonCompletionStatus[lesson.id] = isCompleted;
        if (isCompleted) highestCompletedLessonOrder = i;
      }
      if (highestCompletedLessonOrder >= 0 &&
          highestCompletedLessonOrder < _lessons.length - 1) {
        _selectedLessonIndex = highestCompletedLessonOrder + 1;
      } else if (highestCompletedLessonOrder == _lessons.length - 1 &&
          _lessons.isNotEmpty) {
        _selectedLessonIndex = highestCompletedLessonOrder;
      }
    } else {
      for (final lesson in _lessons) {
        _lessonCompletionStatus[lesson.id] = false;
      }
    }

    if (mounted) setState(() => _isLoading = false);
  }

  Color _parseColor(String hexColor) {
    try {
      hexColor = hexColor.replaceAll('#', '');
      if (hexColor.length == 6) hexColor = 'FF$hexColor';
      return Color(int.parse(hexColor, radix: 16));
    } catch (e) {
      return Colors.blue; // Fallback
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenBackgroundColor = Colors.white;

    if (_isLoading) {
      return Scaffold(
        backgroundColor: screenBackgroundColor,
        appBar: AppBar(
          backgroundColor: screenBackgroundColor,
          elevation: 0,
          leading: IconButton(
            icon: Icon(
              Icons.arrow_back_ios_new_rounded,
              color: _darkTextColor,
              size: 20,
            ),
            onPressed: () => Navigator.pop(context),
          ),
        ),
        body: Center(
          // Removed const
          child: CircularProgressIndicator(color: _categoryColor),
        ),
      );
    }
    if (_lessons.isEmpty && !_isLoading) {
      debugPrint('No lessons found for course: ${_course.id}');
      debugPrint('Course has ${_course.modules.length} modules');
      for (var module in _course.modules) {
        debugPrint('Module ${module.id} has ${module.lessons.length} lessons');
      }

      return Scaffold(
        backgroundColor: screenBackgroundColor,
        appBar: AppBar(
          title: Text(
            _course.title,
            style: TextStyle(
              color: _darkTextColor,
              fontFamily: 'WorkSans',
              fontWeight: FontWeight.bold,
              fontSize: 18,
            ),
          ),
          backgroundColor: screenBackgroundColor,
          elevation: 0,
          leading: IconButton(
            icon: Icon(
              Icons.arrow_back_ios_new_rounded,
              color: _darkTextColor,
              size: 20,
            ),
            onPressed: () => Navigator.pop(context),
          ),
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                "No lessons available in this course.",
                style: TextStyle(
                  color: _lightTextColor,
                  fontSize: 16,
                  fontFamily: 'WorkSans',
                ),
              ),
              const SizedBox(height: 8),
              Text(
                "Please check the module files.",
                style: TextStyle(
                  color: _lightTextColor,
                  fontSize: 14,
                  fontFamily: 'WorkSans',
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      backgroundColor: screenBackgroundColor,
      body: SafeArea(
        bottom: false,
        child: Column(
          children: [
            _buildHeader(),
            Expanded(
              child: Stack(
                alignment: Alignment.bottomCenter,
                children: [
                  LayoutBuilder(
                    builder: (context, constraints) {
                      final roadmapHeight = _calculateRoadmapHeight();
                      return SingleChildScrollView(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 20.0,
                        ).copyWith(bottom: 230, top: 10),
                        child: SizedBox(
                          height: math.max(
                            roadmapHeight,
                            constraints.maxHeight -
                                (MediaQuery.of(context).padding.top +
                                    kToolbarHeight),
                          ),
                          width: constraints.maxWidth,
                          child: Stack(
                            children: [
                              CustomPaint(
                                size: Size(constraints.maxWidth, roadmapHeight),
                                painter: RoadmapPainter(
                                  lessonsCount: _lessons.length,
                                  nodeSize: _nodeSize,
                                  verticalSpacing: _verticalSpacingBetweenNodes,
                                  horizontalNodeOffset: _horizontalNodeOffset,
                                  pathColor: _pathLineColor,
                                  roadmapTopPadding: _roadmapTopPadding,
                                  pathStrokeWidth: _pathStrokeWidth,
                                  lessonCompletionStatus:
                                      _lessonCompletionStatus,
                                  lessons: _lessons,
                                  completedPathColor: _categoryColor,
                                ),
                              ),
                              _buildNodesList(constraints.maxWidth),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                  _buildAnimatedSelectedLessonCard(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  double _calculateRoadmapHeight() {
    if (_lessons.isEmpty) return 300;
    return _roadmapTopPadding +
        (_lessons.length * (_nodeSize + _verticalSpacingBetweenNodes)) +
        50; // Buffer at bottom
  }

  Widget _buildHeader() {
    double overallProgress = 0.0;
    if (_lessons.isNotEmpty) {
      int completedCount =
          _lessonCompletionStatus.values.where((s) => s == true).length;
      overallProgress = completedCount / _lessons.length;
    }

    return Container(
      padding: const EdgeInsets.fromLTRB(16, 8, 16, 16),
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              IconButton(
                icon: Icon(
                  Icons.arrow_back_ios_new_rounded,
                  size: 20,
                  color: _darkTextColor,
                ),
                onPressed: () => Navigator.pop(context),
                padding: const EdgeInsets.only(right: 8), // Reduced padding
                constraints: const BoxConstraints(),
                tooltip: "Back",
              ),
              const Spacer(),
              // Optional: Placeholder for the orange "O+O+" icon if needed
              // Container(
              //   padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              //   decoration: BoxDecoration(color: Colors.orange[100], borderRadius: BorderRadius.circular(10)),
              //   child: Text("O+O+", style: TextStyle(color: Colors.orange[700], fontSize: 9, fontWeight: FontWeight.bold)),
              // )
            ],
          ),
          const SizedBox(height: 10),
          Text(
            _categoryName,
            style: TextStyle(
              color: _categoryColor,
              fontSize: 11.5,
              fontWeight: FontWeight.w700,
              fontFamily: 'WorkSans',
              letterSpacing: 0.5,
            ),
          ),
          const SizedBox(height: 3),
          Text(
            _course.title,
            style: TextStyle(
              color: _darkTextColor,
              fontSize: 19,
              fontWeight: FontWeight.bold,
              fontFamily: 'WorkSans',
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Icon(Icons.star_rate_rounded, color: Colors.green[600], size: 15),
              const SizedBox(width: 5),
              Expanded(
                child: Container(
                  height: 3, // Thinner line
                  decoration: BoxDecoration(
                    color: _inactiveNodeBgColor,
                    borderRadius: BorderRadius.circular(1.5),
                  ),
                  child: Align(
                    alignment: Alignment.centerLeft,
                    child: FractionallySizedBox(
                      widthFactor: overallProgress,
                      child: Container(
                        decoration: BoxDecoration(
                          color: _categoryColor,
                          borderRadius: BorderRadius.circular(1.5),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Icon(
                Icons.info_outline_rounded,
                color: Colors.grey[400],
                size: 17,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildNodesList(double availableWidth) {
    List<Widget> nodeWidgets = [];
    final double contentWidthForNodes = availableWidth;

    for (int i = 0; i < _lessons.length; i++) {
      final lesson = _lessons[i];
      final isCompleted = _lessonCompletionStatus[lesson.id] ?? false;
      // Allow starting any lesson, so only lock if explicitly set to locked
      final bool isCurrentActive = i == _selectedLessonIndex;
      final bool isLocked =
          false; // Remove locking to allow starting any lesson

      // Check if this is the start of a module
      if (_moduleStartIndices.containsKey(i)) {
        final module = _moduleStartIndices[i]!;
        final moduleHeaderYPos =
            _roadmapTopPadding +
            (i * (_nodeSize + _verticalSpacingBetweenNodes)) -
            _verticalSpacingBetweenNodes / 2;

        // Add module header
        nodeWidgets.add(
          Positioned(
            top: moduleHeaderYPos,
            left: 0,
            right: 0,
            child: _buildModuleHeader(module),
          ),
        );
      }

      double xNodeCenter;
      if (i == 0) {
        // Node 1 (index 0) is center
        xNodeCenter = contentWidthForNodes / 2;
      } else if (i % 2 == 1) {
        // Nodes 2, 4... (indices 1, 3...) are to the right
        xNodeCenter = contentWidthForNodes / 2 + _horizontalNodeOffset;
      } else {
        // Nodes 3, 5... (indices 2, 4...) are to the left
        xNodeCenter = contentWidthForNodes / 2 - _horizontalNodeOffset;
      }
      // Clamp to prevent nodes from going too far off-screen if canvas is narrow
      xNodeCenter = xNodeCenter.clamp(
        _nodeSize / 2 + 5,
        contentWidthForNodes - _nodeSize / 2 - 5,
      );

      final yPos =
          _roadmapTopPadding + (i * (_nodeSize + _verticalSpacingBetweenNodes));
      final currentActualNodeSize =
          isSelectedOnScreen(i) ? _selectedNodeSize : _nodeSize;

      nodeWidgets.add(
        Positioned(
          top: yPos,
          left: xNodeCenter - (currentActualNodeSize / 2),
          child: _buildRoadmapNode(
            index: i,
            lesson: lesson,
            isCompleted: isCompleted,
            isCurrentActive: isCurrentActive,
            isLocked: isLocked,
          ),
        ),
      );
    }
    return Stack(children: nodeWidgets);
  }

  // Build a module header widget
  Widget _buildModuleHeader(Module module) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13), // 0.05 * 255 ≈ 13
            blurRadius: 8,
            spreadRadius: 1,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            module.title.toUpperCase(),
            style: TextStyle(
              color: _categoryColor,
              fontSize: 14,
              fontWeight: FontWeight.bold,
              fontFamily: 'WorkSans',
              letterSpacing: 0.5,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            module.description,
            style: TextStyle(
              color: _lightTextColor,
              fontSize: 12,
              fontFamily: 'WorkSans',
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  bool isSelectedOnScreen(int index) => index == _selectedLessonIndex;

  Widget _buildRoadmapNode({
    required int index,
    required LessonDefinition lesson, // Changed to LessonDefinition
    required bool isCompleted,
    required bool isCurrentActive,
    required bool isLocked,
  }) {
    final bool currentIsSelectedOnScreen = isSelectedOnScreen(index);
    final double currentActualNodeSize =
        currentIsSelectedOnScreen ? _selectedNodeSize : _nodeSize;
    Color nodeBgColor;
    Color nodeContentColor;
    Widget? nodeChild;
    List<BoxShadow> shadows = [];

    if (isCompleted) {
      nodeBgColor = _completedNodeColor;
      nodeContentColor = _completedNodeContentColor;
      nodeChild = Icon(
        Icons.check_rounded,
        color: nodeContentColor,
        size: currentActualNodeSize * 0.5,
      );
    } else if (isCurrentActive) {
      nodeBgColor = _categoryColor;
      nodeContentColor = Colors.white;
      nodeChild = Text(
        (index + 1).toString(),
        style: TextStyle(
          color: nodeContentColor,
          fontWeight: FontWeight.bold,
          fontSize: currentActualNodeSize * 0.4,
          fontFamily: 'WorkSans',
        ),
      );
      shadows = [
        BoxShadow(
          color: Colors.grey[300]!,
          blurRadius: 6,
          spreadRadius: 0,
          offset: Offset(0, 2.5),
        ),
      ];
    } else {
      nodeBgColor = _inactiveNodeBgColor;
      nodeContentColor = isLocked ? Colors.grey[400]! : _categoryColor;
      nodeChild = Text(
        (index + 1).toString(),
        style: TextStyle(
          color: nodeContentColor,
          fontWeight: FontWeight.w600,
          fontSize: currentActualNodeSize * 0.4,
          fontFamily: 'WorkSans',
        ),
      );
    }

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Stack(
          clipBehavior: Clip.none,
          alignment: Alignment.center,
          children: [
            GestureDetector(
              onTap:
                  (!isLocked)
                      ? () {
                        if (mounted) {
                          setState(() => _selectedLessonIndex = index);
                        }
                      }
                      : null,
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 150),
                width: currentActualNodeSize,
                height: currentActualNodeSize,
                decoration: BoxDecoration(
                  color: nodeBgColor,
                  borderRadius: BorderRadius.circular(10), // Image-like radius
                  boxShadow: shadows,
                ),
                child: Center(child: nodeChild),
              ),
            ),
            if (index == 0)
              Positioned(
                top: -_levelTagHeight * 0.4,
                left:
                    -_levelTagHorizontalPadding *
                    0.8, // Adjusted for tighter fit
                child: Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: _levelTagHorizontalPadding,
                    vertical: _levelTagVerticalPadding,
                  ),
                  decoration: BoxDecoration(
                    color: _categoryColor,
                    borderRadius: BorderRadius.circular(_levelTagHeight / 2),
                    boxShadow: [
                      BoxShadow(
                        color: Color.fromRGBO(0, 0, 0, 0.1),
                        blurRadius: 2,
                        offset: Offset(0, 1),
                      ),
                    ],
                  ),
                  child: Text(
                    "LEVEL 1",
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: _levelTagFontSize,
                      fontWeight: FontWeight.bold,
                      fontFamily: 'WorkSans',
                    ),
                  ),
                ),
              ),
          ],
        ),
        SizedBox(height: _nodeTitleSpacing),
        SizedBox(
          width: _nodeSize + 30, // Slightly less width for title
          child: Text(
            lesson.title,
            style: TextStyle(
              color: isLocked ? Colors.grey[400] : _darkTextColor,
              fontSize: _nodeTitleFontSize,
              fontWeight: FontWeight.w500,
              fontFamily: 'WorkSans',
              height: 1.2,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  Widget _buildAnimatedSelectedLessonCard() {
    bool isCardVisible = _selectedLessonIndex < _lessons.length;
    final lesson = isCardVisible ? _lessons[_selectedLessonIndex] : null;

    // Get the module this lesson belongs to
    Module? lessonModule;
    if (lesson != null) {
      lessonModule = _lessonModuleMap[lesson.id];
    }

    return AnimatedPositioned(
      duration: const Duration(milliseconds: 350),
      curve: Curves.easeOutCubic,
      bottom:
          isCardVisible ? (MediaQuery.of(context).padding.bottom + 10) : -300,
      left: 15,
      right: 15,
      child: AnimatedOpacity(
        duration: const Duration(milliseconds: 300),
        opacity: isCardVisible ? 1.0 : 0.0,
        child:
            lesson == null
                ? const SizedBox.shrink()
                : Container(
                  padding: const EdgeInsets.all(18), // Slightly reduced padding
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(
                      18,
                    ), // Image-like radius
                    boxShadow: [
                      BoxShadow(
                        color: Color.fromRGBO(0, 0, 0, 0.09),
                        blurRadius: 18,
                        spreadRadius: -3,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Module name if available
                      if (lessonModule != null)
                        Text(
                          lessonModule.title.toUpperCase(),
                          style: TextStyle(
                            color: _categoryColor,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            fontFamily: 'WorkSans',
                            letterSpacing: 0.5,
                          ),
                        ),
                      if (lessonModule != null) const SizedBox(height: 6),

                      // Lesson title
                      Text(
                        lesson.title,
                        style: TextStyle(
                          color: _darkTextColor,
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          fontFamily: 'WorkSans',
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 5),

                      // Lesson description
                      Text(
                        lesson.description.isEmpty
                            ? "A new challenge awaits!"
                            : lesson.description,
                        maxLines: 3,
                        overflow: TextOverflow.ellipsis,
                        style: TextStyle(
                          color: _lightTextColor,
                          fontSize: 12.5,
                          fontFamily: 'WorkSans',
                          height: 1.35,
                        ),
                      ),

                      // Lesson metadata
                      const SizedBox(height: 10),
                      Row(
                        children: [
                          // Estimated time
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.grey[100],
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.access_time,
                                  size: 12,
                                  color: _lightTextColor,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  "${lesson.estimatedTimeMinutes} min", // Changed to estimatedTimeMinutes
                                  style: TextStyle(
                                    fontSize: 10,
                                    color: _lightTextColor,
                                    fontFamily: 'WorkSans',
                                  ),
                                ),
                              ],
                            ),
                          ),

                          const SizedBox(width: 8),

                          // Completion status
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color:
                                  (_lessonCompletionStatus[lesson.id] ?? false)
                                      ? Colors.green[50]
                                      : Colors.blue[50],
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  (_lessonCompletionStatus[lesson.id] ?? false)
                                      ? Icons.check_circle
                                      : Icons.play_circle_outline,
                                  size: 12,
                                  color:
                                      (_lessonCompletionStatus[lesson.id] ??
                                              false)
                                          ? Colors.green[700]
                                          : Colors.blue[700],
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  (_lessonCompletionStatus[lesson.id] ?? false)
                                      ? "Completed"
                                      : "Not started",
                                  style: TextStyle(
                                    fontSize: 10,
                                    color:
                                        (_lessonCompletionStatus[lesson.id] ??
                                                false)
                                            ? Colors.green[700]
                                            : Colors.blue[700],
                                    fontFamily: 'WorkSans',
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),

                      const SizedBox(height: 18),
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: () {
                            // Find the module that contains this lesson
                            String moduleId = '';
                            if (lessonModule != null) {
                              moduleId = lessonModule.id;
                            } else {
                              // Fallback to the old method if needed
                              for (final module in _course.modules) {
                                if (module.lessons.any(
                                  (l) => l.id == lesson.id,
                                )) {
                                  moduleId = module.id;
                                  break;
                                }
                              }
                            }

                            if (moduleId.isNotEmpty) {
                              // Use ContinuousLessonScreenNew for a continuous scrolling experience
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder:
                                      (context) => ContinuousLessonScreen(
                                        courseId: widget.courseId,
                                        lessonId: lesson.id,
                                      ),
                                ),
                              ).then((_) {
                                if (mounted) _loadCourseData();
                              });
                            }
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.black,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(
                              vertical: 13,
                            ), // Adjusted padding
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(25),
                            ),
                            elevation: 0,
                          ),
                          child: Text(
                            (_lessonCompletionStatus[lesson.id] ?? false)
                                ? 'Review Lesson'
                                : 'Start lesson',
                            style: const TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                              fontFamily: 'WorkSans',
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
      ),
    );
  }
}

class RoadmapPainter extends CustomPainter {
  final int lessonsCount;
  final double nodeSize;
  final double verticalSpacing;
  final double horizontalNodeOffset;
  final Color pathColor;
  final double roadmapTopPadding;
  final double pathStrokeWidth;
  final Map<String, bool> lessonCompletionStatus;
  final List<LessonDefinition> lessons; // Changed to LessonDefinition
  final Color completedPathColor;

  RoadmapPainter({
    required this.lessonsCount,
    required this.nodeSize,
    required this.verticalSpacing,
    required this.horizontalNodeOffset,
    required this.pathColor,
    required this.roadmapTopPadding,
    required this.pathStrokeWidth,
    required this.lessonCompletionStatus,
    required this.lessons,
    this.completedPathColor = const Color.fromRGBO(124, 66, 210, 1),
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (lessonsCount < 2) return;

    final Paint incompletePaint =
        Paint()
          ..color = pathColor
          ..strokeWidth = pathStrokeWidth
          ..style = PaintingStyle.stroke
          ..strokeCap = StrokeCap.round;

    final Paint completedPaint =
        Paint()
          ..color = completedPathColor
          ..strokeWidth = pathStrokeWidth + 0.5
          ..style = PaintingStyle.stroke
          ..strokeCap = StrokeCap.round;

    List<Offset> nodeCenters = [];
    final double contentWidth = size.width;

    for (int i = 0; i < lessonsCount; i++) {
      double xNodeCenter;
      if (i == 0) {
        xNodeCenter = contentWidth / 2;
      } else if (i % 2 == 1) {
        xNodeCenter = contentWidth / 2 + horizontalNodeOffset;
      } else {
        xNodeCenter = contentWidth / 2 - horizontalNodeOffset;
      }
      xNodeCenter = xNodeCenter.clamp(
        nodeSize / 2 + 5,
        contentWidth - nodeSize / 2 - 5,
      );
      final yPosNodeCenter =
          roadmapTopPadding +
          (i * (nodeSize + verticalSpacing)) +
          (nodeSize / 2);
      nodeCenters.add(Offset(xNodeCenter, yPosNodeCenter));
    }

    for (int i = 0; i < nodeCenters.length - 1; i++) {
      Offset pStartCenter = nodeCenters[i];
      Offset pEndCenter = nodeCenters[i + 1];
      Offset effectiveStart, effectiveEnd, intermediatePoint;

      // Path: effectiveStart -> intermediatePoint -> effectiveEnd
      if (pStartCenter.dx < pEndCenter.dx) {
        // Current is Center/Left, Next is Right
        effectiveStart = Offset(
          pStartCenter.dx + nodeSize / 2,
          pStartCenter.dy,
        );
        intermediatePoint = Offset(pEndCenter.dx, pStartCenter.dy);
        effectiveEnd = Offset(pEndCenter.dx - nodeSize / 2, pEndCenter.dy);
      } else {
        // Current is Right, Next is Left
        effectiveStart = Offset(
          pStartCenter.dx - nodeSize / 2,
          pStartCenter.dy,
        );
        intermediatePoint = Offset(pEndCenter.dx, pStartCenter.dy);
        effectiveEnd = Offset(pEndCenter.dx + nodeSize / 2, pEndCenter.dy);
      }

      // Override for the very first segment (node 0 to node 1)
      // Node 0 is always center, Node 1 is always to its right.
      if (i == 0) {
        effectiveStart = Offset(
          pStartCenter.dx + nodeSize / 2,
          pStartCenter.dy,
        );
        intermediatePoint = Offset(
          pEndCenter.dx,
          pStartCenter.dy,
        ); // X of Node1_center, Y of Node0_center
        effectiveEnd = Offset(pEndCenter.dx - nodeSize / 2, pEndCenter.dy);
      }

      final Path drawingPath = Path();
      drawingPath.moveTo(effectiveStart.dx, effectiveStart.dy);
      drawingPath.lineTo(intermediatePoint.dx, intermediatePoint.dy);
      drawingPath.lineTo(effectiveEnd.dx, effectiveEnd.dy);

      // Determine if this path segment should be highlighted as completed
      final currentLessonCompleted =
          i < lessons.length &&
          (lessonCompletionStatus[lessons[i].id] ?? false);
      final nextLessonCompleted =
          i + 1 < lessons.length &&
          (lessonCompletionStatus[lessons[i + 1].id] ?? false);

      // If both connected lessons are completed, use the completed paint
      if (currentLessonCompleted && nextLessonCompleted) {
        canvas.drawPath(drawingPath, completedPaint);
      } else {
        canvas.drawPath(drawingPath, incompletePaint);
      }
    }
  }

  @override
  bool shouldRepaint(covariant RoadmapPainter oldDelegate) {
    return oldDelegate.lessonsCount != lessonsCount ||
        oldDelegate.nodeSize != nodeSize ||
        oldDelegate.verticalSpacing != verticalSpacing ||
        oldDelegate.horizontalNodeOffset != horizontalNodeOffset ||
        oldDelegate.pathColor != pathColor ||
        oldDelegate.pathStrokeWidth != pathStrokeWidth ||
        oldDelegate.lessonCompletionStatus != lessonCompletionStatus ||
        oldDelegate.lessons != lessons ||
        oldDelegate.completedPathColor != completedPathColor;
  }
}
