import 'package:flutter/material.dart';
import '../../services/service_provider.dart';

class PaymentScreen extends StatelessWidget {
  const PaymentScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final courseService = ServiceProvider.of(context).courseService;
    final user = courseService.currentUser;
    final isGuest = user?.isGuest ?? true;

    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Premium Banner - Full width with no padding
              Container(
                width: double.infinity,
                color: const Color.fromRGBO(124, 66, 210, 1),
                padding: const EdgeInsets.fromLTRB(24, 24, 24, 24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Premium Icon
                    Container(
                      padding: const EdgeInsets.all(10),
                      decoration: BoxDecoration(
                        color: Colors.white.withAlpha(50),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Icon(
                        Icons.workspace_premium,
                        color: Colors.white,
                        size: 24,
                      ),
                    ),

                    const SizedBox(height: 12),

                    // Premium Title
                    const Text(
                      'Upgrade to Premium',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.w600,
                        fontFamily: 'WorkSans',
                      ),
                    ),

                    const SizedBox(height: 6),

                    // Premium Description
                    const Text(
                      'Get unlimited access to all courses, interactive exercises, and premium features.',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontFamily: 'WorkSans',
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Get Started Button
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: () {
                          // Show sign-in prompt for guest users
                          if (isGuest) {
                            _showSignInPrompt(context);
                          } else {
                            // Show payment options
                            _showPaymentOptions(context);
                          }
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.white,
                          foregroundColor: const Color.fromRGBO(
                            124,
                            66,
                            210,
                            1,
                          ),
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(24),
                          ),
                          elevation: 0,
                        ),
                        child: const Text(
                          'Get Started',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            fontFamily: 'WorkSans',
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              // Premium Features Section
              Padding(
                padding: const EdgeInsets.fromLTRB(24, 16, 24, 0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Premium Features',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        fontFamily: 'WorkSans',
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Feature List
                    ..._buildFeatureItems(),

                    const SizedBox(height: 24),

                    // Subscription Plans
                    const Text(
                      'Subscription Plans',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        fontFamily: 'WorkSans',
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Monthly Plan - Simplified
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withAlpha(13),
                            blurRadius: 10,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'Monthly',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                  fontFamily: 'WorkSans',
                                ),
                              ),
                              const SizedBox(height: 4),
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.end,
                                children: [
                                  Text(
                                    '\$9.99',
                                    style: const TextStyle(
                                      fontSize: 24,
                                      fontWeight: FontWeight.w700,
                                      fontFamily: 'WorkSans',
                                      color: Color.fromRGBO(124, 66, 210, 1),
                                    ),
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    'per month',
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: Colors.grey[700],
                                      fontFamily: 'WorkSans',
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                          ElevatedButton(
                            onPressed: () {
                              if (isGuest) {
                                _showSignInPrompt(context);
                              } else {
                                _showPaymentOptions(context);
                              }
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color.fromRGBO(
                                124,
                                66,
                                210,
                                1,
                              ),
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 12,
                              ),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(24),
                              ),
                              elevation: 0,
                            ),
                            child: const Text(
                              'Subscribe',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                                fontFamily: 'WorkSans',
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 24),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Build feature items
  List<Widget> _buildFeatureItems() {
    final features = [
      {
        'icon': Icons.book,
        'title': 'Unlimited Courses',
        'description': 'Access all courses across all categories',
      },
      {
        'icon': Icons.download,
        'title': 'Offline Access',
        'description': 'Download courses to learn without internet',
      },
      {
        'icon': Icons.touch_app,
        'title': 'Interactive Exercises',
        'description': 'Practice with hands-on interactive elements',
      },
      {
        'icon': Icons.verified,
        'title': 'Certificates',
        'description': 'Earn certificates upon course completion',
      },
    ];

    return features.map((feature) {
      return Padding(
        padding: const EdgeInsets.only(bottom: 16),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Feature Icon
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: const Color.fromRGBO(124, 66, 210, 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                feature['icon'] as IconData,
                color: const Color.fromRGBO(124, 66, 210, 1),
                size: 20,
              ),
            ),

            const SizedBox(width: 12),

            // Feature Text
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    feature['title'] as String,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      fontFamily: 'WorkSans',
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    feature['description'] as String,
                    style: TextStyle(
                      color: Colors.grey[700],
                      fontSize: 12,
                      fontFamily: 'WorkSans',
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    }).toList();
  }

  // Show sign-in prompt for guest users
  void _showSignInPrompt(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Sign In Required'),
            content: const Text(
              'You need to create an account or sign in to purchase a subscription.',
              style: TextStyle(fontFamily: 'WorkSans'),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  // TODO: Navigate to sign-in screen
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color.fromRGBO(124, 66, 210, 1),
                ),
                child: const Text('Sign In'),
              ),
            ],
          ),
    );
  }

  // Show payment options
  void _showPaymentOptions(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Payment Method'),
            content: const Text(
              'This is a demo app. No actual payment will be processed.',
              style: TextStyle(fontFamily: 'WorkSans'),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  // Show success message
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Subscription successful (Demo)'),
                      duration: Duration(seconds: 2),
                    ),
                  );
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color.fromRGBO(124, 66, 210, 1),
                ),
                child: const Text('Confirm'),
              ),
            ],
          ),
    );
  }
}
