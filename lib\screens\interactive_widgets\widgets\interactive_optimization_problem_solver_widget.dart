import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that allows users to solve and visualize optimization problems.
class InteractiveOptimizationProblemSolverWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveOptimizationProblemSolverWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  /// Factory constructor to create the widget from JSON data
  factory InteractiveOptimizationProblemSolverWidget.fromData(Map<String, dynamic> data) {
    return InteractiveOptimizationProblemSolverWidget(
      data: data,
    );
  }

  @override
  State<InteractiveOptimizationProblemSolverWidget> createState() => _InteractiveOptimizationProblemSolverWidgetState();
}

class _InteractiveOptimizationProblemSolverWidgetState extends State<InteractiveOptimizationProblemSolverWidget> {
  late double _perimeter;
  late double _sideLengthX; // One side of the rectangle

  // Colors
  late Color _primaryColor;
  late Color _secondaryColor;
  late Color _backgroundColor;
  late Color _textColor;
  late Color _optimalColor;

  @override
  void initState() {
    super.initState();
    _perimeter = widget.data['perimeter']?.toDouble() ?? 20.0;
    _sideLengthX = widget.data['initial_side_length_x']?.toDouble() ?? 1.0;

    _primaryColor = _parseColor(widget.data['primary_color']) ?? Colors.blue;
    _secondaryColor = _parseColor(widget.data['secondary_color']) ?? Colors.orange;
    _backgroundColor = _parseColor(widget.data['background_color']) ?? Colors.white;
    _textColor = _parseColor(widget.data['text_color']) ?? Colors.black87;
    _optimalColor = _parseColor(widget.data['optimal_color']) ?? Colors.green;
  }

  Color? _parseColor(String? hexString) {
    if (hexString == null) return null;
    hexString = hexString.replaceAll('#', '');
    if (hexString.length == 6) {
      hexString = 'FF$hexString';
    }
    try {
      return Color(int.parse(hexString, radix: 16));
    } catch (e) {
      return null;
    }
  }

  double _calculateSideY(double x) {
    return (_perimeter / 2) - x;
  }

  double _calculateArea(double x) {
    final y = _calculateSideY(x);
    if (x <= 0 || y <= 0) return 0; // Area cannot be negative or zero
    return x * y;
  }

  @override
  Widget build(BuildContext context) {
    final sideY = _calculateSideY(_sideLengthX);
    final currentArea = _calculateArea(_sideLengthX);

    // Optimal solution for fixed perimeter rectangle is a square
    final optimalSide = _perimeter / 4;
    final optimalArea = optimalSide * optimalSide;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _backgroundColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.data['title'] ?? 'Maximize Rectangle Area',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),
          const SizedBox(height: 8),
          if (widget.data['description'] != null)
            Text(
              widget.data['description'],
              style: TextStyle(
                fontSize: 14,
                color: _textColor.withOpacity(0.8),
              ),
            ),
          const SizedBox(height: 16),

          Text(
            'Problem: Maximize the area of a rectangle with a perimeter of ${_perimeter.toStringAsFixed(0)} units.',
            style: TextStyle(color: _textColor, fontSize: 16, fontStyle: FontStyle.italic),
          ),
          const SizedBox(height: 16),

          Text(
            'Side Length X: ${_sideLengthX.toStringAsFixed(1)}',
            style: TextStyle(color: _primaryColor, fontSize: 16, fontWeight: FontWeight.bold),
          ),
          Slider(
            value: _sideLengthX,
            min: 0.1, // Avoid zero or negative side length
            max: (_perimeter / 2) - 0.1, // x must be less than half perimeter
            divisions: ((_perimeter / 2) * 10 - 2).toInt(),
            label: _sideLengthX.toStringAsFixed(1),
            activeColor: _primaryColor,
            onChanged: (value) {
              setState(() {
                _sideLengthX = value;
              });
            },
          ),
          const SizedBox(height: 16),

          Center(
            child: Column(
              children: [
                Text(
                  'Side Length Y: ${sideY.toStringAsFixed(1)}',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: _secondaryColor,
                  ),
                ),
                Text(
                  'Current Area: ${currentArea.toStringAsFixed(2)}',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: _primaryColor,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Optimal Area (Square): ${optimalArea.toStringAsFixed(2)} at X = ${optimalSide.toStringAsFixed(1)}',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: _optimalColor,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),

          Container(
            height: 150,
            child: CustomPaint(
              painter: RectangleAreaPainter(
                sideX: _sideLengthX,
                sideY: sideY,
                perimeter: _perimeter,
                primaryColor: _primaryColor,
                secondaryColor: _secondaryColor,
                optimalColor: _optimalColor,
                textColor: _textColor,
              ),
              child: Container(),
            ),
          ),

          // Widget name tag
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'InteractiveOptimizationProblemSolver',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }
}

class RectangleAreaPainter extends CustomPainter {
  final double sideX;
  final double sideY;
  final double perimeter;
  final Color primaryColor;
  final Color secondaryColor;
  final Color optimalColor;
  final Color textColor;

  RectangleAreaPainter({
    required this.sideX,
    required this.sideY,
    required this.perimeter,
    required this.primaryColor,
    required this.secondaryColor,
    required this.optimalColor,
    required this.textColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint();
    final textPainter = TextPainter(textDirection: TextDirection.ltr);
    final textStyle = TextStyle(color: textColor, fontSize: 10);

    // Scale rectangle to fit in canvas
    final double maxSide = perimeter / 2; // Max possible side length
    final double scaleFactor = math.min(size.width / maxSide, size.height / maxSide) * 0.8;

    final scaledX = sideX * scaleFactor;
    final scaledY = sideY * scaleFactor;

    // Center the rectangle
    final offsetX = (size.width - scaledX) / 2;
    final offsetY = (size.height - scaledY) / 2;

    // Draw rectangle
    paint.color = primaryColor.withOpacity(0.3);
    paint.style = PaintingStyle.fill;
    canvas.drawRect(Rect.fromLTWH(offsetX, offsetY, scaledX, scaledY), paint);

    paint.color = primaryColor;
    paint.style = PaintingStyle.stroke;
    paint.strokeWidth = 2.0;
    canvas.drawRect(Rect.fromLTWH(offsetX, offsetY, scaledX, scaledY), paint);

    // Draw side labels
    textPainter.text = TextSpan(text: 'X: ${sideX.toStringAsFixed(1)}', style: textStyle.copyWith(color: primaryColor));
    textPainter.layout();
    textPainter.paint(canvas, Offset(offsetX + scaledX / 2 - textPainter.width / 2, offsetY + scaledY + 5));

    textPainter.text = TextSpan(text: 'Y: ${sideY.toStringAsFixed(1)}', style: textStyle.copyWith(color: secondaryColor));
    textPainter.layout();
    textPainter.paint(canvas, Offset(offsetX - textPainter.width - 5, offsetY + scaledY / 2 - textPainter.height / 2));

    // Draw optimal square indicator
    final optimalSide = perimeter / 4;
    final scaledOptimalSide = optimalSide * scaleFactor;
    final optimalOffsetX = (size.width - scaledOptimalSide) / 2;
    final optimalOffsetY = (size.height - scaledOptimalSide) / 2;

    paint.color = optimalColor.withOpacity(0.2);
    paint.style = PaintingStyle.fill;
    canvas.drawRect(Rect.fromLTWH(optimalOffsetX, optimalOffsetY, scaledOptimalSide, scaledOptimalSide), paint);

    paint.color = optimalColor;
    paint.style = PaintingStyle.stroke;
    paint.strokeWidth = 1.0;
    canvas.drawRect(Rect.fromLTWH(optimalOffsetX, optimalOffsetY, scaledOptimalSide, scaledOptimalSide), paint);

    textPainter.text = TextSpan(text: 'Optimal', style: textStyle.copyWith(color: optimalColor));
    textPainter.layout();
    textPainter.paint(canvas, Offset(optimalOffsetX + scaledOptimalSide / 2 - textPainter.width / 2, optimalOffsetY - textPainter.height - 5));
  }

  @override
  bool shouldRepaint(covariant RectangleAreaPainter oldDelegate) {
    return oldDelegate.sideX != sideX ||
           oldDelegate.sideY != sideY ||
           oldDelegate.perimeter != perimeter ||
           oldDelegate.primaryColor != primaryColor ||
           oldDelegate.secondaryColor != secondaryColor ||
           oldDelegate.optimalColor != optimalColor ||
           oldDelegate.textColor != textColor;
  }
}
