import 'package:flutter/material.dart';
import '../../models/user_preferences.dart';

// Screen 9: Smart every day message
class SmartEveryDayScreen extends StatelessWidget {
  final UserPreferences userPreferences;
  final VoidCallback onContinue;

  const SmartEveryDayScreen({
    Key? key,
    required this.userPreferences,
    required this.onContinue,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Smart icon
          Icon(
            Icons.lightbulb,
            size: 120,
            color: Color.fromRGBO(100, 45, 176, 1),
          ),
          SizedBox(height: 32),
          Text(
            'Smart Every Day',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Color.fromRGBO(16, 17, 20, 1),
              fontFamily: 'WorkSans',
              fontSize: 30,
              fontWeight: FontWeight.w600,
            ),
          ),
          Sized<PERSON><PERSON>(height: 16),
          Text(
            'Just ${userPreferences.dailyGoalMinutes} minutes a day can transform your knowledge and skills over time.',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Color.fromRGBO(57, 59, 66, 1),
              fontFamily: 'WorkSans',
              fontSize: 18,
              fontWeight: FontWeight.w400,
            ),
          ),
          SizedBox(height: 16),
          Text(
            'We\'ll help you make the most of every minute with personalized learning experiences.',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Color.fromRGBO(57, 59, 66, 1),
              fontFamily: 'WorkSans',
              fontSize: 18,
              fontWeight: FontWeight.w400,
            ),
          ),
          SizedBox(height: 48),
          ElevatedButton(
            onPressed: onContinue,
            style: ElevatedButton.styleFrom(
              foregroundColor: Colors.white,
              backgroundColor: Color.fromRGBO(16, 17, 20, 1),
              padding: EdgeInsets.symmetric(horizontal: 24, vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(19),
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'Continue',
                  style: TextStyle(fontFamily: 'WorkSans', fontSize: 16),
                ),
                SizedBox(width: 8),
                Icon(Icons.arrow_forward, size: 16),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

// Screen 10: Final "Let's go" screen
class FinalScreen extends StatelessWidget {
  final UserPreferences userPreferences;
  final VoidCallback onComplete;

  const FinalScreen({
    Key? key,
    required this.userPreferences,
    required this.onComplete,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Celebration icon
          Icon(
            Icons.emoji_events,
            size: 120,
            color: Color.fromRGBO(255, 193, 7, 1), // Amber color
          ),
          SizedBox(height: 32),
          Text(
            'Let\'s Go!',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Color.fromRGBO(16, 17, 20, 1),
              fontFamily: 'WorkSans',
              fontSize: 30,
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 16),
          Text(
            'Your personalized learning journey is ready.',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Color.fromRGBO(57, 59, 66, 1),
              fontFamily: 'WorkSans',
              fontSize: 18,
              fontWeight: FontWeight.w400,
            ),
          ),
          SizedBox(height: 16),
          Text(
            _getPersonalizedSummary(),
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Color.fromRGBO(100, 45, 176, 1),
              fontFamily: 'WorkSans',
              fontSize: 18,
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: 48),
          ElevatedButton(
            onPressed: onComplete,
            style: ElevatedButton.styleFrom(
              foregroundColor: Colors.white,
              backgroundColor: Color.fromRGBO(100, 45, 176, 1),
              padding: EdgeInsets.symmetric(horizontal: 32, vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(19),
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'Start Learning',
                  style: TextStyle(
                    fontFamily: 'WorkSans',
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                SizedBox(width: 8),
                Icon(Icons.arrow_forward, size: 18),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _getPersonalizedSummary() {
    String topic = userPreferences.topicToExplore ?? 'your chosen topics';
    String level = userPreferences.userLevel ?? 'your level';
    int minutes = userPreferences.dailyGoalMinutes ?? 10;

    return 'You\'ll be exploring $topic at $level level for $minutes minutes a day.';
  }
}
