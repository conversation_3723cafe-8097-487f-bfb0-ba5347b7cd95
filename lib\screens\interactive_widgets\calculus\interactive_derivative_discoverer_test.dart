import 'package:flutter/material.dart';
import 'dart:math';

class InteractiveDerivativeDiscovererTest extends StatefulWidget {
  const InteractiveDerivativeDiscovererTest({super.key});

  @override
  State<InteractiveDerivativeDiscovererTest> createState() => _InteractiveDerivativeDiscovererTestState();
}

class _InteractiveDerivativeDiscovererTestState extends State<InteractiveDerivativeDiscovererTest> {
  int _currentQuestionIndex = 0;
  String? _selectedAnswer;
  String? _feedbackMessage;
  bool _isCorrect = false;

  final List<Map<String, dynamic>> _questions = [
    {
      'question': 'What is the derivative of f(x) = x^3?',
      'correctAnswer': '3x^2',
      'options': ['x^2', '3x', '3x^2', 'x^4/4'],
      'explanation': 'Using the Power Rule, if f(x) = x^n, then f\'(x) = nx^(n-1). So for x^3, the derivative is 3x^2.',
    },
    {
      'question': 'What is the derivative of f(x) = 5?',
      'correctAnswer': '0',
      'options': ['5', 'x', '0', 'Undefined'],
      'explanation': 'The derivative of a constant is always 0.',
    },
    {
      'question': 'What is the derivative of f(x) = 4x^2?',
      'correctAnswer': '8x',
      'options': ['4x', 'x^3', '8x', '4'],
      'explanation': 'Using the Constant Multiple Rule and Power Rule, the derivative of 4x^2 is 4 * (2x) = 8x.',
    },
    {
      'question': 'If a function is increasing at a point, what can you say about its derivative at that point?',
      'correctAnswer': 'It is positive',
      'options': ['It is positive', 'It is negative', 'It is zero', 'It is undefined'],
      'explanation': 'A positive derivative indicates that the function is increasing.',
    },
  ];

  void _checkAnswer() {
    final currentQuestion = _questions[_currentQuestionIndex];
    setState(() {
      if (_selectedAnswer == currentQuestion['correctAnswer']) {
        _feedbackMessage = 'Correct! ${currentQuestion['explanation']}';
        _isCorrect = true;
      } else {
        _feedbackMessage = 'Incorrect. ${currentQuestion['explanation']}';
        _isCorrect = false;
      }
    });
  }

  void _nextQuestion() {
    setState(() {
      if (_currentQuestionIndex < _questions.length - 1) {
        _currentQuestionIndex++;
        _selectedAnswer = null;
        _feedbackMessage = null;
        _isCorrect = false;
      } else {
        _feedbackMessage = 'Test Completed! Review your answers.';
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final currentQuestion = _questions[_currentQuestionIndex];

    return Card(
      margin: const EdgeInsets.all(16.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Interactive Derivative Discoverer (Module Test)',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            Text(
              'Question ${_currentQuestionIndex + 1}/${_questions.length}:',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
            ),
            const SizedBox(height: 10),
            Text(
              currentQuestion['question'],
              style: TextStyle(fontSize: 16),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            ...currentQuestion['options'].map<Widget>((option) {
              return RadioListTile<String>(
                title: Text(option),
                value: option,
                groupValue: _selectedAnswer,
                onChanged: (value) {
                  setState(() {
                    _selectedAnswer = value;
                    _feedbackMessage = null;
                  });
                },
              );
            }).toList(),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: _selectedAnswer == null ? null : _checkAnswer,
              child: const Text('Check Answer'),
            ),
            if (_feedbackMessage != null)
              Padding(
                padding: const EdgeInsets.all(10.0),
                child: Text(
                  _feedbackMessage!,
                  style: TextStyle(
                    color: _isCorrect ? Colors.green : Colors.red,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            const SizedBox(height: 10),
            ElevatedButton(
              onPressed: _nextQuestion,
              child: Text(_currentQuestionIndex < _questions.length - 1 ? 'Next Question' : 'Finish Test'),
            ),
          ],
        ),
      ),
    );
  }
}
