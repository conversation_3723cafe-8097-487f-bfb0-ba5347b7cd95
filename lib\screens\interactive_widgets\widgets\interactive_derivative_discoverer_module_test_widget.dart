import 'package:flutter/material.dart';

/// A widget for an interactive module test on Derivatives.
class InteractiveDerivativeDiscovererModuleTestWidget extends StatefulWidget {
  /// The data for this widget, containing questions and options.
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes (e.g., test completed).
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveDerivativeDiscovererModuleTestWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  /// Factory constructor to create the widget from JSON data.
  factory InteractiveDerivativeDiscovererModuleTestWidget.fromData(Map<String, dynamic> data) {
    return InteractiveDerivativeDiscovererModuleTestWidget(
      data: data,
    );
  }

  @override
  State<InteractiveDerivativeDiscovererModuleTestWidget> createState() => _InteractiveDerivativeDiscovererModuleTestWidgetState();
}

class _InteractiveDerivativeDiscovererModuleTestWidgetState extends State<InteractiveDerivativeDiscovererModuleTestWidget> {
  late List<Map<String, dynamic>> _questions;
  int _currentQuestionIndex = 0;
  bool _answerSubmitted = false;
  bool? _isCorrect; // null: not answered, true: correct, false: incorrect

  // Colors
  late Color _primaryColor;
  late Color _backgroundColor;
  late Color _textColor;
  late Color _correctColor;
  late Color _incorrectColor;

  @override
  void initState() {
    super.initState();
    _questions = List<Map<String, dynamic>>.from(widget.data['questions'] ?? []);

    _primaryColor = _parseColor(widget.data['primary_color']) ?? Colors.blue;
    _backgroundColor = _parseColor(widget.data['background_color']) ?? Colors.white;
    _textColor = _parseColor(widget.data['text_color']) ?? Colors.black87;
    _correctColor = _parseColor(widget.data['correct_color']) ?? Colors.green;
    _incorrectColor = _parseColor(widget.data['incorrect_color']) ?? Colors.red;
  }

  Color? _parseColor(String? hexString) {
    if (hexString == null) return null;
    hexString = hexString.replaceAll('#', '');
    if (hexString.length == 6) {
      hexString = 'FF$hexString';
    }
    try {
      return Color(int.parse(hexString, radix: 16));
    } catch (e) {
      return null;
    }
  }

  void _submitAnswer(String selectedOptionId) {
    if (_answerSubmitted) return;

    setState(() {
      _answerSubmitted = true;
      final currentQuestion = _questions[_currentQuestionIndex];
      final correctOption = currentQuestion['options'].firstWhere(
        (option) => option['is_correct'] == true,
        orElse: () => null,
      );
      _isCorrect = (correctOption != null && correctOption['id'] == selectedOptionId);
    });
  }

  void _nextQuestion() {
    setState(() {
      if (_currentQuestionIndex < _questions.length - 1) {
        _currentQuestionIndex++;
        _answerSubmitted = false;
        _isCorrect = null;
      } else {
        // Test completed
        widget.onStateChanged?.call(true);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_questions.isEmpty) {
      return Center(
        child: Text(
          'No questions available for this test.',
          style: TextStyle(color: _textColor),
        ),
      );
    }

    final currentQuestion = _questions[_currentQuestionIndex];

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _backgroundColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.data['title'] ?? 'Derivatives Module Test',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Question ${_currentQuestionIndex + 1} of ${_questions.length}',
            style: TextStyle(
              fontSize: 14,
              color: _textColor.withOpacity(0.7),
            ),
          ),
          const SizedBox(height: 16),

          Text(
            currentQuestion['question'] ?? 'No question text provided.',
            style: TextStyle(
              fontSize: 16,
              color: _textColor,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 16),

          // Options
          ...List.generate(currentQuestion['options']?.length ?? 0, (index) {
            final option = currentQuestion['options'][index];
            final optionId = option['id'];
            final optionText = option['text'];
            final isCorrectOption = option['is_correct'] == true;

            Color? optionColor;
            if (_answerSubmitted) {
              if (isCorrectOption) {
                optionColor = _correctColor.withOpacity(0.2);
              } else if (!isCorrectOption && _isCorrect == false) {
                // If this is the selected incorrect option
                // This logic needs refinement if we want to highlight the selected incorrect option
              }
            }

            return Padding(
              padding: const EdgeInsets.only(bottom: 8.0),
              child: ChoiceChip(
                label: Text(optionText),
                selected: _answerSubmitted && isCorrectOption, // Only show correct if submitted
                onSelected: (selected) {
                  if (selected) {
                    _submitAnswer(optionId);
                  }
                },
                backgroundColor: optionColor ?? _backgroundColor,
                selectedColor: _correctColor.withOpacity(0.5),
                labelStyle: TextStyle(
                  color: _textColor,
                  fontWeight: FontWeight.normal,
                ),
                side: BorderSide(
                  color: _answerSubmitted && isCorrectOption
                      ? _correctColor
                      : _textColor.withOpacity(0.3),
                ),
              ),
            );
          }),
          const SizedBox(height: 16),

          // Feedback
          if (_answerSubmitted)
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: (_isCorrect ?? false) ? _correctColor.withOpacity(0.1) : _incorrectColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: (_isCorrect ?? false) ? _correctColor : _incorrectColor,
                ),
              ),
              child: Text(
                (_isCorrect ?? false)
                    ? currentQuestion['options'].firstWhere((opt) => opt['is_correct'] == true)['feedback_correct'] ?? 'Correct!'
                    : currentQuestion['options'].firstWhere((opt) => opt['id'] == 'selected_incorrect_option_id', orElse: () => {'feedback_incorrect': 'Incorrect. Please review.'})['feedback_incorrect'] ?? 'Incorrect. Please review.',
                style: TextStyle(
                  color: (_isCorrect ?? false) ? _correctColor : _incorrectColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          const SizedBox(height: 16),

          // Navigation Button
          Center(
            child: ElevatedButton(
              onPressed: _answerSubmitted ? _nextQuestion : null,
              child: Text(
                _currentQuestionIndex < _questions.length - 1 ? 'Next Question' : 'Finish Test',
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: _primaryColor,
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              ),
            ),
          ),

          // Widget name tag
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'InteractiveDerivativeDiscovererModuleTest',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }
}
