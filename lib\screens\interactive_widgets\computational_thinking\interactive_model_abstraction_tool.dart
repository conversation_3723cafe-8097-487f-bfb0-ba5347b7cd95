import 'package:flutter/material.dart';

class InteractiveModelAbstractionTool extends StatefulWidget {
  const InteractiveModelAbstractionTool({super.key});

  @override
  State<InteractiveModelAbstractionTool> createState() => _InteractiveModelAbstractionToolState();
}

class _InteractiveModelAbstractionToolState extends State<InteractiveModelAbstractionTool> {
  String _selectedModel = 'Weather Prediction Model';
  String _selectedAbstractionLevel = 'Conceptual Model';
  String _modelDescription = '';

  final Map<String, Map<String, String>> _modelAbstractionDetails = {
    'Weather Prediction Model': {
      'Conceptual Model': 'A simplified idea of how weather works, focusing on key factors like temperature, pressure, and humidity.',
      'Mathematical Model': 'Equations and algorithms that describe atmospheric processes and predict future weather states.',
      'Computational Model': 'Software programs that run complex simulations on supercomputers using vast amounts of data.',
    },
    'Human Brain Model': {
      'Conceptual Model': 'The brain as a control center for thoughts, emotions, and actions.',
      'Mathematical Model': 'Neural network models describing neuron firing patterns and synaptic connections.',
      'Computational Model': 'Simulations of brain activity using artificial intelligence and machine learning algorithms.',
    },
    'Economic Market Model': {
      'Conceptual Model': 'Supply and demand interacting to determine prices and quantities.',
      'Mathematical Model': 'Equations representing consumer behavior, firm production, and market equilibrium.',
      'Computational Model': 'Agent-based simulations where individual economic actors interact to produce market outcomes.',
    },
  };

  @override
  void initState() {
    super.initState();
    _updateModelDescription();
  }

  void _updateModelDescription() {
    setState(() {
      _modelDescription = _modelAbstractionDetails[_selectedModel]?[_selectedAbstractionLevel] ?? 'Select a model and abstraction level.';
    });
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Interactive Model Abstraction Tool',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Text('Model:'),
                const SizedBox(width: 10),
                DropdownButton<String>(
                  value: _selectedModel,
                  onChanged: (String? newValue) {
                    setState(() {
                      _selectedModel = newValue!;
                      _updateModelDescription();
                    });
                  },
                  items: _modelAbstractionDetails.keys
                      .map<DropdownMenuItem<String>>((String value) {
                    return DropdownMenuItem<String>(
                      value: value,
                      child: Text(value),
                    );
                  }).toList(),
                ),
              ],
            ),
            const SizedBox(height: 10),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Text('Abstraction Level:'),
                const SizedBox(width: 10),
                DropdownButton<String>(
                  value: _selectedAbstractionLevel,
                  onChanged: (String? newValue) {
                    setState(() {
                      _selectedAbstractionLevel = newValue!;
                      _updateModelDescription();
                    });
                  },
                  items: _modelAbstractionDetails[_selectedModel]?.keys
                          .map<DropdownMenuItem<String>>((String value) {
                        return DropdownMenuItem<String>(
                          value: value,
                          child: Text(value),
                        );
                      }).toList() ??
                      [],
                ),
              ],
            ),
            const SizedBox(height: 20),
            Text(
              'Description at $_selectedAbstractionLevel:',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: Colors.blue),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 10),
            Text(
              _modelDescription,
              style: TextStyle(fontSize: 14),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            const Text(
              'Models can be represented at various levels of abstraction, from high-level concepts to detailed computational implementations.',
              style: TextStyle(fontStyle: FontStyle.italic, color: Colors.grey),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
