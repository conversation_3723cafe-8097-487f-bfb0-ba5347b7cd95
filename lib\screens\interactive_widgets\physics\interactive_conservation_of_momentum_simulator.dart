import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class InteractiveConservationOfMomentumSimulator extends StatefulWidget {
  const InteractiveConservationOfMomentumSimulator({super.key});

  @override
  State<InteractiveConservationOfMomentumSimulator> createState() => _InteractiveConservationOfMomentumSimulatorState();
}

class _InteractiveConservationOfMomentumSimulatorState extends State<InteractiveConservationOfMomentumSimulator> {
  final TextEditingController _mass1Controller = TextEditingController();
  final TextEditingController _velocity1Controller = TextEditingController();
  final TextEditingController _mass2Controller = TextEditingController();
  final TextEditingController _velocity2Controller = TextEditingController();

  double? _finalVelocity1;
  double? _finalVelocity2;

  void _simulateCollision() {
    setState(() {
      final double? m1 = double.tryParse(_mass1Controller.text);
      final double? v1 = double.tryParse(_velocity1Controller.text);
      final double? m2 = double.tryParse(_mass2Controller.text);
      final double? v2 = double.tryParse(_velocity2Controller.text);

      if (m1 != null && v1 != null && m2 != null && v2 != null) {
        // Assuming an elastic collision for simplicity for now
        // v1_final = ((m1 - m2) / (m1 + m2)) * v1 + ((2 * m2) / (m1 + m2)) * v2;
        // v2_final = ((2 * m1) / (m1 + m2)) * v1 + ((m2 - m1) / (m1 + m2)) * v2;

        // For now, let's assume a perfectly inelastic collision (objects stick together)
        // (m1*v1 + m2*v2) = (m1 + m2) * v_final
        // v_final = (m1*v1 + m2*v2) / (m1 + m2)
        double finalCommonVelocity = (m1 * v1 + m2 * v2) / (m1 + m2);
        _finalVelocity1 = finalCommonVelocity;
        _finalVelocity2 = finalCommonVelocity;
      } else {
        _finalVelocity1 = null;
        _finalVelocity2 = null;
      }
    });
  }

  @override
  void dispose() {
    _mass1Controller.dispose();
    _velocity1Controller.dispose();
    _mass2Controller.dispose();
    _velocity2Controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Conservation of Momentum Simulator'),
        backgroundColor: Colors.blueGrey,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'Object 1',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 10),
            TextField(
              controller: _mass1Controller,
              keyboardType: const TextInputType.numberWithOptions(decimal: true),
              inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^-?\d+\.?\d*'))],
              decoration: const InputDecoration(
                labelText: 'Mass 1 (kg)',
                border: OutlineInputBorder(),
              ),
              onChanged: (_) => _simulateCollision(),
            ),
            const SizedBox(height: 10),
            TextField(
              controller: _velocity1Controller,
              keyboardType: const TextInputType.numberWithOptions(decimal: true),
              inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^-?\d+\.?\d*'))],
              decoration: const InputDecoration(
                labelText: 'Initial Velocity 1 (m/s)',
                border: OutlineInputBorder(),
              ),
              onChanged: (_) => _simulateCollision(),
            ),
            const SizedBox(height: 30),
            const Text(
              'Object 2',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 10),
            TextField(
              controller: _mass2Controller,
              keyboardType: const TextInputType.numberWithOptions(decimal: true),
              inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^-?\d+\.?\d*'))],
              decoration: const InputDecoration(
                labelText: 'Mass 2 (kg)',
                border: OutlineInputBorder(),
              ),
              onChanged: (_) => _simulateCollision(),
            ),
            const SizedBox(height: 10),
            TextField(
              controller: _velocity2Controller,
              keyboardType: const TextInputType.numberWithOptions(decimal: true),
              inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^-?\d+\.?\d*'))],
              decoration: const InputDecoration(
                labelText: 'Initial Velocity 2 (m/s)',
                border: OutlineInputBorder(),
              ),
              onChanged: (_) => _simulateCollision(),
            ),
            const SizedBox(height: 30),
            Text(
              _finalVelocity1 == null
                  ? 'Enter values for both objects to simulate collision.'
                  : 'Final Velocity (both objects): ${_finalVelocity1!.toStringAsFixed(2)} m/s',
              style: const TextStyle(fontSize: 22, fontWeight: FontWeight.bold, color: Colors.blueGrey),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            const Text(
              'Note: This simulation assumes a perfectly inelastic collision (objects stick together).',
              style: TextStyle(fontSize: 14, fontStyle: FontStyle.italic, color: Colors.grey),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
