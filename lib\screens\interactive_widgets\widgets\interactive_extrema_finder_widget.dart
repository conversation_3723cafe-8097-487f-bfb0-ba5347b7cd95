import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that allows users to find and visualize local extrema of a function.
class InteractiveExtremaFinderWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveExtremaFinderWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  /// Factory constructor to create the widget from JSON data
  factory InteractiveExtremaFinderWidget.fromData(Map<String, dynamic> data) {
    return InteractiveExtremaFinderWidget(
      data: data,
    );
  }

  @override
  State<InteractiveExtremaFinderWidget> createState() => _InteractiveExtremaFinderWidgetState();
}

class _InteractiveExtremaFinderWidgetState extends State<InteractiveExtremaFinderWidget> {
  // Function to visualize (e.g., f(x) = x^3 - 3x)
  double _function(double x) {
    return x * x * x - 3 * x;
  }

  // Derivative of the function (f'(x) = 3x^2 - 3)
  double _derivative(double x) {
    return 3 * x * x - 3;
  }

  // Colors
  late Color _primaryColor;
  late Color _secondaryColor;
  late Color _backgroundColor;
  late Color _textColor;
  late Color _maxColor;
  late Color _minColor;

  @override
  void initState() {
    super.initState();
    _primaryColor = _parseColor(widget.data['primary_color']) ?? Colors.blue;
    _secondaryColor = _parseColor(widget.data['secondary_color']) ?? Colors.orange;
    _backgroundColor = _parseColor(widget.data['background_color']) ?? Colors.white;
    _textColor = _parseColor(widget.data['text_color']) ?? Colors.black87;
    _maxColor = _parseColor(widget.data['max_color']) ?? Colors.green;
    _minColor = _parseColor(widget.data['min_color']) ?? Colors.red;
  }

  Color? _parseColor(String? hexString) {
    if (hexString == null) return null;
    hexString = hexString.replaceAll('#', '');
    if (hexString.length == 6) {
      hexString = 'FF$hexString';
    }
    try {
      return Color(int.parse(hexString, radix: 16));
    } catch (e) {
      return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    // Critical points where f'(x) = 0: x = -1, 1
    final localMaxX = -1.0;
    final localMinX = 1.0;
    final localMaxY = _function(localMaxX);
    final localMinY = _function(localMinX);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _backgroundColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.data['title'] ?? 'Extrema Finder',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),
          const SizedBox(height: 8),
          if (widget.data['description'] != null)
            Text(
              widget.data['description'],
              style: TextStyle(
                fontSize: 14,
                color: _textColor.withOpacity(0.8),
              ),
            ),
          const SizedBox(height: 16),

          Text(
            'Function: f(x) = x³ - 3x',
            style: TextStyle(color: _textColor, fontSize: 16, fontStyle: FontStyle.italic),
          ),
          Text(
            'Derivative: f\'(x) = 3x² - 3',
            style: TextStyle(color: _textColor, fontSize: 16, fontStyle: FontStyle.italic),
          ),
          const SizedBox(height: 16),

          Container(
            height: 250,
            child: CustomPaint(
              painter: ExtremaPainter(
                function: _function,
                localMaxX: localMaxX,
                localMaxY: localMaxY,
                localMinX: localMinX,
                localMinY: localMinY,
                primaryColor: _primaryColor,
                maxColor: _maxColor,
                minColor: _minColor,
                textColor: _textColor,
              ),
              child: Container(),
            ),
          ),
          const SizedBox(height: 16),

          Center(
            child: Column(
              children: [
                Text(
                  'Local Maximum: (${localMaxX.toStringAsFixed(1)}, ${localMaxY.toStringAsFixed(1)})',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: _maxColor,
                  ),
                ),
                Text(
                  'Local Minimum: (${localMinX.toStringAsFixed(1)}, ${localMinY.toStringAsFixed(1)})',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: _minColor,
                  ),
                ),
              ],
            ),
          ),

          // Widget name tag
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'InteractiveExtremaFinder',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }
}

class ExtremaPainter extends CustomPainter {
  final Function(double) function;
  final double localMaxX;
  final double localMaxY;
  final double localMinX;
  final double localMinY;
  final Color primaryColor;
  final Color maxColor;
  final Color minColor;
  final Color textColor;

  ExtremaPainter({
    required this.function,
    required this.localMaxX,
    required this.localMaxY,
    required this.localMinX,
    required this.localMinY,
    required this.primaryColor,
    required this.maxColor,
    required this.minColor,
    required this.textColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint();
    final textPainter = TextPainter(textDirection: TextDirection.ltr);
    final textStyle = TextStyle(color: textColor, fontSize: 10);

    // Define plot area padding
    const double padding = 30.0;
    final double plotWidth = size.width - 2 * padding;
    final double plotHeight = size.height - 2 * padding;

    // Determine x and y ranges for plotting
    final double minPlotX = -3.0;
    final double maxPlotX = 3.0;
    final double minPlotY = -5.0;
    final double maxPlotY = 5.0;

    // Scale factors
    final double xScale = plotWidth / (maxPlotX - minPlotX);
    final double yScale = plotHeight / (maxPlotY - minPlotY);

    // Helper to convert plot coordinates to canvas coordinates
    Offset toCanvas(double x, double y) {
      final canvasX = padding + (x - minPlotX) * xScale;
      final canvasY = size.height - padding - (y - minPlotY) * yScale;
      return Offset(canvasX, canvasY);
    }

    // Draw axes
    paint.color = Colors.grey;
    paint.strokeWidth = 1.0;
    // X-axis
    canvas.drawLine(toCanvas(minPlotX, 0), toCanvas(maxPlotX, 0), paint);
    // Y-axis
    canvas.drawLine(toCanvas(0, minPlotY), toCanvas(0, maxPlotY), paint);

    // Draw function curve
    paint.color = primaryColor;
    paint.style = PaintingStyle.stroke;
    paint.strokeWidth = 2.0;

    final path = Path();
    bool firstPoint = true;
    for (double x = minPlotX; x <= maxPlotX; x += (maxPlotX - minPlotX) / 200) {
      final y = function(x);
      final point = toCanvas(x, y);
      if (firstPoint) {
        path.moveTo(point.dx, point.dy);
        firstPoint = false;
      } else {
        path.lineTo(point.dx, point.dy);
      }
    }
    canvas.drawPath(path, paint);

    // Draw local maximum point
    final maxCanvasPoint = toCanvas(localMaxX, localMaxY);
    paint.color = maxColor;
    paint.style = PaintingStyle.fill;
    canvas.drawCircle(maxCanvasPoint, 5, paint);
    // Draw dashed lines to axes for max
    paint.color = maxColor.withOpacity(0.7);
    paint.style = PaintingStyle.stroke;
    paint.strokeWidth = 1.0;
    canvas.drawLine(maxCanvasPoint, toCanvas(localMaxX, 0), paint);
    canvas.drawLine(maxCanvasPoint, toCanvas(0, localMaxY), paint);

    // Draw local minimum point
    final minCanvasPoint = toCanvas(localMinX, localMinY);
    paint.color = minColor;
    paint.style = PaintingStyle.fill;
    canvas.drawCircle(minCanvasPoint, 5, paint);
    // Draw dashed lines to axes for min
    paint.color = minColor.withOpacity(0.7);
    paint.style = PaintingStyle.stroke;
    paint.strokeWidth = 1.0;
    canvas.drawLine(minCanvasPoint, toCanvas(localMinX, 0), paint);
    canvas.drawLine(minCanvasPoint, toCanvas(0, localMinY), paint);

    // Draw labels for extrema
    textPainter.text = TextSpan(text: 'Max', style: textStyle.copyWith(color: maxColor));
    textPainter.layout();
    textPainter.paint(canvas, Offset(maxCanvasPoint.dx + 5, maxCanvasPoint.dy - textPainter.height / 2));

    textPainter.text = TextSpan(text: 'Min', style: textStyle.copyWith(color: minColor));
    textPainter.layout();
    textPainter.paint(canvas, Offset(minCanvasPoint.dx + 5, minCanvasPoint.dy - textPainter.height / 2));
  }

  @override
  bool shouldRepaint(covariant ExtremaPainter oldDelegate) {
    return oldDelegate.localMaxX != localMaxX ||
           oldDelegate.localMaxY != localMaxY ||
           oldDelegate.localMinX != localMinX ||
           oldDelegate.localMinY != localMinY ||
           oldDelegate.primaryColor != primaryColor ||
           oldDelegate.maxColor != maxColor ||
           oldDelegate.minColor != minColor ||
           oldDelegate.textColor != textColor;
  }
}
