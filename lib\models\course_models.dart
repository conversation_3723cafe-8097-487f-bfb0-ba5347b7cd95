// Represents a user in the system
class User {
  final String id;
  final String? name;
  final String? email;
  final bool isGuest;
  final List<String> enrolledCourseIds;
  final Map<String, CourseProgress> courseProgress;
  final int? dailyGoalMinutes;

  User({
    required this.id,
    this.name,
    this.email,
    this.isGuest = false,
    this.enrolledCourseIds = const [],
    this.courseProgress = const {},
    this.dailyGoalMinutes = 15,
  });

  factory User.guest() {
    return User(
      id: 'guest-${DateTime.now().millisecondsSinceEpoch}',
      isGuest: true,
      dailyGoalMinutes: 15,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'isGuest': isGuest,
      'enrolledCourseIds': enrolledCourseIds,
      'courseProgress': courseProgress.map(
        (key, value) => MapEntry(key, value.toJson()),
      ),
      'dailyGoalMinutes': dailyGoalMinutes,
    };
  }

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'] ?? 'unknown_user_id',
      name: json['name'],
      email: json['email'],
      isGuest: json['isGuest'] ?? false,
      enrolledCourseIds: List<String>.from(json['enrolledCourseIds'] ?? []),
      courseProgress:
          (json['courseProgress'] as Map<String, dynamic>?)?.map(
            (key, value) => MapEntry(key, CourseProgress.fromJson(value)),
          ) ??
          {},
      dailyGoalMinutes: json['dailyGoalMinutes'],
    );
  }
}

/// Tracks user progress in a course
class CourseProgress {
  final String courseId;
  final Map<String, LessonProgress> lessonProgress;
  final DateTime lastAccessed;
  final bool isCompleted;

  CourseProgress({
    required this.courseId,
    this.lessonProgress = const {},
    DateTime? lastAccessed,
    this.isCompleted = false,
  }) : lastAccessed = lastAccessed ?? DateTime.now();

  double get progressPercentage {
    if (lessonProgress.isEmpty) return 0.0;
    int completedLessons =
        lessonProgress.values.where((lesson) => lesson.isCompleted).length;
    return completedLessons / lessonProgress.length * 100;
  }

  Map<String, dynamic> toJson() {
    return {
      'courseId': courseId,
      'lessonProgress': lessonProgress.map(
        (key, value) => MapEntry(key, value.toJson()),
      ),
      'lastAccessed': lastAccessed.toIso8601String(),
      'isCompleted': isCompleted,
    };
  }

  factory CourseProgress.fromJson(Map<String, dynamic> json) {
    return CourseProgress(
      courseId: json['courseId'] ?? 'unknown_course_id',
      lessonProgress:
          (json['lessonProgress'] as Map<String, dynamic>?)?.map(
            (key, value) => MapEntry(key, LessonProgress.fromJson(value)),
          ) ??
          {},
      lastAccessed:
          json['lastAccessed'] != null
              ? DateTime.parse(json['lastAccessed'])
              : DateTime.now(),
      isCompleted: json['isCompleted'] ?? false,
    );
  }
}

/// Tracks user progress in a lesson
class LessonProgress {
  final String lessonId;
  final bool isCompleted;
  final double progressPercentage;
  final DateTime lastAccessed;

  LessonProgress({
    required this.lessonId,
    this.isCompleted = false,
    this.progressPercentage = 0.0,
    DateTime? lastAccessed,
  }) : lastAccessed = lastAccessed ?? DateTime.now();

  Map<String, dynamic> toJson() {
    return {
      'lessonId': lessonId,
      'isCompleted': isCompleted,
      'progressPercentage': progressPercentage,
      'lastAccessed': lastAccessed.toIso8601String(),
    };
  }

  factory LessonProgress.fromJson(Map<String, dynamic> json) {
    return LessonProgress(
      lessonId: json['lessonId'] ?? 'unknown_lesson_id',
      isCompleted: json['isCompleted'] ?? false,
      progressPercentage:
          (json['progressPercentage'] as num?)?.toDouble() ?? 0.0,
      lastAccessed:
          json['lastAccessed'] != null
              ? DateTime.parse(json['lastAccessed'])
              : DateTime.now(),
    );
  }
}

/// Represents a course category
class CourseCategory {
  final String id;
  final String name;
  final String description;
  final String iconPath;
  final String color;

  CourseCategory({
    required this.id,
    required this.name,
    required this.description,
    required this.iconPath,
    required this.color,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'iconPath': iconPath,
      'color': color,
    };
  }

  factory CourseCategory.fromJson(Map<String, dynamic> json) {
    return CourseCategory(
      id: json['id'] ?? 'unknown_category_id',
      name: json['name'] ?? 'Unknown Category',
      description: json['description'] ?? '',
      iconPath: json['iconPath'] ?? '',
      color: json['color'] ?? '#FFFFFF',
    );
  }
}

/// Represents a course in the system
class Course {
  final String id;
  final String title;
  final String description;
  final String categoryId;
  final String thumbnailPath;
  final String difficulty;
  final List<Module> modules;
  final List<String>? prerequisites;
  final bool? isEncrypted;
  final String? encryptionKey;

  Course({
    required this.id,
    required this.title,
    required this.description,
    required this.categoryId,
    required this.thumbnailPath,
    required this.difficulty,
    this.modules = const [],
    this.prerequisites,
    this.isEncrypted,
    this.encryptionKey,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'categoryId': categoryId,
      'thumbnailPath': thumbnailPath,
      'difficulty': difficulty,
      'modules': modules.map((module) => module.toJson()).toList(),
      'prerequisites': prerequisites,
      'isEncrypted': isEncrypted,
      'encryptionKey': encryptionKey,
    };
  }

  factory Course.fromJson(Map<String, dynamic> json) {
    return Course(
      id: json['id'] ?? 'unknown_course_id',
      title: json['title'] ?? 'Unknown Course',
      description: json['description'] ?? '',
      categoryId: json['categoryId'] ?? 'unknown_category',
      thumbnailPath: json['thumbnailPath'] ?? '',
      difficulty: json['difficulty'] ?? 'Beginner',
      prerequisites:
          json['prerequisites'] != null
              ? List<String>.from(json['prerequisites'])
              : null,
      isEncrypted: json['isEncrypted'],
      encryptionKey: json['encryptionKey'],
      modules:
          (json['modules'] as List<dynamic>?)
              ?.map(
                (moduleJson) =>
                    Module.fromJson(moduleJson as Map<String, dynamic>),
              )
              .toList() ??
          [],
    );
  }
}

/// Represents a Module within a course
class Module {
  final String id;
  final String title;
  final String description;
  final int order;
  final List<LessonDefinition> lessons;
  final ModuleTest? moduleTest;

  Module({
    required this.id,
    required this.title,
    required this.description,
    required this.order,
    this.lessons = const [],
    this.moduleTest,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'order': order,
      'lessons': lessons.map((lesson) => lesson.toJson()).toList(),
      'moduleTest': moduleTest?.toJson(),
    };
  }

  factory Module.fromJson(Map<String, dynamic> json) {
    return Module(
      id: json['id'] ?? 'unknown_module_id',
      title: json['title'] ?? 'Unknown Module',
      description: json['description'] ?? '',
      order: json['order'] ?? 0,
      lessons:
          (json['lessons'] as List<dynamic>?)
              ?.map(
                (lessonJson) => LessonDefinition.fromJson(
                  lessonJson as Map<String, dynamic>,
                ),
              )
              .toList() ??
          [],
      moduleTest:
          json['moduleTest'] != null
              ? ModuleTest.fromJson(json['moduleTest'] as Map<String, dynamic>)
              : null,
    );
  }
}

// --- Module Test Structures ---

class ModuleTest {
  final String id;
  final String title;
  final String description;
  final String type; // Should be "interactive_test"
  final int estimatedTimeMinutes;
  final List<TestScreenBlock> contentBlocks;

  ModuleTest({
    required this.id,
    required this.title,
    required this.description,
    required this.type,
    required this.estimatedTimeMinutes,
    required this.contentBlocks,
  });

  factory ModuleTest.fromJson(Map<String, dynamic> json) {
    return ModuleTest(
      id: json['id'] ?? 'unknown_test_id',
      title: json['title'] ?? 'Unknown Test',
      description: json['description'] ?? '',
      type: json['type'] ?? 'interactive_test',
      estimatedTimeMinutes: json['estimatedTimeMinutes'] ?? 0,
      contentBlocks:
          (json['contentBlocks'] as List<dynamic>?)
              ?.map(
                (cb) => TestScreenBlock.fromJson(cb as Map<String, dynamic>),
              )
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() => {
    'id': id,
    'title': title,
    'description': description,
    'type': type,
    'estimatedTimeMinutes': estimatedTimeMinutes,
    'contentBlocks': contentBlocks.map((cb) => cb.toJson()).toList(),
  };
}

abstract class TestScreenBlock {
  final String id;
  final String type;
  final int order;

  TestScreenBlock({required this.id, required this.type, required this.order});

  Map<String, dynamic> toJson();

  factory TestScreenBlock.fromJson(Map<String, dynamic> json) {
    switch (json['type'] as String?) {
      case 'test_screen_intro':
        return TestScreenIntroBlock.fromJson(json);
      case 'test_section':
        return TestSectionBlock.fromJson(json);
      case 'test_screen_conclusion':
        return TestScreenConclusionBlock.fromJson(json);
      case 'lesson_screen':
        // Handle lesson_screen as a test section for now
        // This allows the Equations and Algebra course to load properly
        return TestSectionBlock.fromJson({
          'id': json['id'] ?? 'unknown_id',
          'type': 'test_section',
          'order': json['order'] ?? 0,
          'title': json['content']?['headline'] ?? 'Lesson Screen',
          'description': json['content']?['body_md'] ?? '',
          'questions': [],
        });
      default:
        print('Warning: Unknown test screen block type: ${json['type']}');
        // Fallback or throw error
        throw UnimplementedError(
          'Unknown test screen block type: ${json['type']}',
        );
    }
  }
}

class TestScreenIntroBlock extends TestScreenBlock {
  final ScreenContent content; // Reusing ScreenContent for simplicity

  TestScreenIntroBlock({
    required String id,
    required int order,
    required this.content,
  }) : super(id: id, type: 'test_screen_intro', order: order);

  factory TestScreenIntroBlock.fromJson(Map<String, dynamic> json) {
    return TestScreenIntroBlock(
      id: json['id'] ?? 'unknown_intro_block_id',
      order: json['order'] ?? 0,
      content: ScreenContent.fromJson(
        json['content'] as Map<String, dynamic>? ?? {},
      ),
    );
  }

  @override
  Map<String, dynamic> toJson() => {
    'id': id,
    'type': type,
    'order': order,
    'content': content.toJson(),
  };
}

class TestSectionBlock extends TestScreenBlock {
  final String title;
  final List<TestQuestion> questions;

  TestSectionBlock({
    required String id,
    required int order,
    required this.title,
    required this.questions,
  }) : super(id: id, type: 'test_section', order: order);

  factory TestSectionBlock.fromJson(Map<String, dynamic> json) {
    return TestSectionBlock(
      id: json['id'] ?? 'unknown_section_id',
      order: json['order'] ?? 0,
      title: json['title'] ?? 'Unknown Section',
      questions:
          (json['questions'] as List<dynamic>?)
              ?.map((q) => TestQuestion.fromJson(q as Map<String, dynamic>))
              .toList() ??
          [],
    );
  }

  @override
  Map<String, dynamic> toJson() => {
    'id': id,
    'type': type,
    'order': order,
    'title': title,
    'questions': questions.map((q) => q.toJson()).toList(),
  };
}

class TestScreenConclusionBlock extends TestScreenBlock {
  final ScreenContent content; // Reusing ScreenContent
  final bool? score_display; // Added from JSON

  TestScreenConclusionBlock({
    required String id,
    required int order,
    required this.content,
    this.score_display,
  }) : super(id: id, type: 'test_screen_conclusion', order: order);

  factory TestScreenConclusionBlock.fromJson(Map<String, dynamic> json) {
    return TestScreenConclusionBlock(
      id: json['id'] ?? 'unknown_conclusion_block_id',
      order: json['order'] ?? 0,
      content: ScreenContent.fromJson(
        json['content'] as Map<String, dynamic>? ?? {},
      ),
      score_display: json['score_display'],
    );
  }

  @override
  Map<String, dynamic> toJson() => {
    'id': id,
    'type': type,
    'order': order,
    'content': content.toJson(),
    'score_display': score_display,
  };
}

// --- Test Question Structures ---

abstract class TestQuestion {
  final String id;
  final String type;
  final String question_text; // Common field
  final String feedback_correct;
  final String feedback_incorrect;

  TestQuestion({
    required this.id,
    required this.type,
    required this.question_text,
    required this.feedback_correct,
    required this.feedback_incorrect,
  });

  Map<String, dynamic> toJson();

  factory TestQuestion.fromJson(Map<String, dynamic> json) {
    switch (json['type'] as String?) {
      case 'image_sequence_prediction':
        return ImageSequencePredictionQuestion.fromJson(json);
      case 'numerical_pattern_text_input':
        return NumericalPatternTextInputQuestion.fromJson(json);
      case 'conditional_truth_evaluation':
        return ConditionalTruthEvaluationQuestion.fromJson(json);
      case 'proof_by_contradiction_step_choice':
        return ProofByContradictionStepChoiceQuestion.fromJson(json);
      case 'argument_completion_modus_ponens':
        return ArgumentCompletionModusPonensQuestion.fromJson(json);
      case 'fallacy_identification_scenario':
        return FallacyIdentificationScenarioQuestion.fromJson(json);
      // --- Reasoning Course Test Question Types (Foundations) ---
      case 'test_argument_component_identifier':
        return TestArgumentComponentIdentifierQuestion.fromJson(json);
      case 'test_deductive_argument_evaluator':
        return TestDeductiveArgumentEvaluatorQuestion.fromJson(json);
      case 'test_inductive_strength_evaluator':
        return TestInductiveStrengthEvaluatorQuestion.fromJson(json);
      case 'test_everyday_reasoning_scenario':
        return TestEverydayReasoningScenarioQuestion.fromJson(json);
      // --- Reasoning Course Test Question Types (Frontiers) ---
      case 'test_formal_proof_construction':
        return TestFormalProofConstructionQuestion.fromJson(json);
      default:
        print('Warning: Unknown test question type: ${json['type']}');
        throw UnimplementedError('Unknown test question type: ${json['type']}');
    }
  }
}

class ImageOption {
  final String id;
  final String asset;
  final bool is_correct;

  ImageOption({
    required this.id,
    required this.asset,
    required this.is_correct,
  });

  factory ImageOption.fromJson(Map<String, dynamic> json) {
    return ImageOption(
      id: json['id'] ?? 'unknown_img_opt',
      asset: json['asset'] ?? '',
      is_correct: json['is_correct'] ?? false,
    );
  }
  Map<String, dynamic> toJson() => {
    'id': id,
    'asset': asset,
    'is_correct': is_correct,
  };
}

class ImageSequencePredictionQuestion extends TestQuestion {
  final List<String> image_sequence_assets;
  final List<ImageOption> options_image_assets;

  ImageSequencePredictionQuestion({
    required String id,
    required String question_text,
    required this.image_sequence_assets,
    required this.options_image_assets,
    required String feedback_correct,
    required String feedback_incorrect,
  }) : super(
         id: id,
         type: 'image_sequence_prediction',
         question_text: question_text,
         feedback_correct: feedback_correct,
         feedback_incorrect: feedback_incorrect,
       );

  factory ImageSequencePredictionQuestion.fromJson(Map<String, dynamic> json) {
    return ImageSequencePredictionQuestion(
      id: json['id'] ?? 'unknown_q_id',
      question_text: json['question_text'] ?? '',
      image_sequence_assets: List<String>.from(
        json['image_sequence_assets'] ?? [],
      ),
      options_image_assets:
          (json['options_image_assets'] as List<dynamic>?)
              ?.map((opt) => ImageOption.fromJson(opt as Map<String, dynamic>))
              .toList() ??
          [],
      feedback_correct: json['feedback_correct'] ?? 'Correct!',
      feedback_incorrect: json['feedback_incorrect'] ?? 'Try again.',
    );
  }

  @override
  Map<String, dynamic> toJson() => {
    'id': id,
    'type': type,
    'question_text': question_text,
    'image_sequence_assets': image_sequence_assets,
    'options_image_assets':
        options_image_assets.map((o) => o.toJson()).toList(),
    'feedback_correct': feedback_correct,
    'feedback_incorrect': feedback_incorrect,
  };
}

class NumericalPatternTextInputQuestion extends TestQuestion {
  final String correct_answer_regex;

  NumericalPatternTextInputQuestion({
    required String id,
    required String question_text,
    required this.correct_answer_regex,
    required String feedback_correct,
    required String feedback_incorrect,
  }) : super(
         id: id,
         type: 'numerical_pattern_text_input',
         question_text: question_text,
         feedback_correct: feedback_correct,
         feedback_incorrect: feedback_incorrect,
       );

  factory NumericalPatternTextInputQuestion.fromJson(
    Map<String, dynamic> json,
  ) {
    return NumericalPatternTextInputQuestion(
      id: json['id'] ?? 'unknown_q_id',
      question_text: json['question_text'] ?? '',
      correct_answer_regex: json['correct_answer_regex'] ?? '^.*\$',
      feedback_correct: json['feedback_correct'] ?? 'Correct!',
      feedback_incorrect: json['feedback_incorrect'] ?? 'Try again.',
    );
  }

  @override
  Map<String, dynamic> toJson() => {
    'id': id,
    'type': type,
    'question_text': question_text,
    'correct_answer_regex': correct_answer_regex,
    'feedback_correct': feedback_correct,
    'feedback_incorrect': feedback_incorrect,
  };
}

class TextOption {
  final String id;
  final String text;
  final bool is_correct;

  TextOption({required this.id, required this.text, required this.is_correct});

  factory TextOption.fromJson(Map<String, dynamic> json) {
    return TextOption(
      id: json['id'] ?? 'unknown_text_opt',
      text: json['text'] ?? '',
      is_correct: json['is_correct'] ?? false,
    );
  }
  Map<String, dynamic> toJson() => {
    'id': id,
    'text': text,
    'is_correct': is_correct,
  };
}

class ConditionalTruthEvaluationQuestion extends TestQuestion {
  final String statement_p;
  final String statement_q;
  final String scenario_text;
  final List<TextOption> options;

  ConditionalTruthEvaluationQuestion({
    required String id,
    // question_text is scenario_text in JSON
    required this.statement_p,
    required this.statement_q,
    required this.scenario_text,
    required this.options,
    required String feedback_correct,
    required String feedback_incorrect,
  }) : super(
         id: id,
         type: 'conditional_truth_evaluation',
         question_text: scenario_text, // Use scenario_text as question_text
         feedback_correct: feedback_correct,
         feedback_incorrect: feedback_incorrect,
       );

  factory ConditionalTruthEvaluationQuestion.fromJson(
    Map<String, dynamic> json,
  ) {
    return ConditionalTruthEvaluationQuestion(
      id: json['id'] ?? 'unknown_q_id',
      statement_p: json['statement_p'] ?? '',
      statement_q: json['statement_q'] ?? '',
      scenario_text: json['scenario_text'] ?? '',
      options:
          (json['options'] as List<dynamic>?)
              ?.map((opt) => TextOption.fromJson(opt as Map<String, dynamic>))
              .toList() ??
          [],
      feedback_correct: json['feedback_correct'] ?? 'Correct!',
      feedback_incorrect: json['feedback_incorrect'] ?? 'Try again.',
    );
  }

  @override
  Map<String, dynamic> toJson() => {
    'id': id,
    'type': type,
    'statement_p': statement_p,
    'statement_q': statement_q,
    'scenario_text': scenario_text, // This is the question_text
    'options': options.map((o) => o.toJson()).toList(),
    'feedback_correct': feedback_correct,
    'feedback_incorrect': feedback_incorrect,
  };
}

class ProofByContradictionStepChoiceQuestion extends TestQuestion {
  // question_text is problem_statement in JSON
  final List<TextOption> options;

  ProofByContradictionStepChoiceQuestion({
    required String id,
    required String problem_statement,
    required this.options,
    required String feedback_correct,
    required String feedback_incorrect,
  }) : super(
         id: id,
         type: 'proof_by_contradiction_step_choice',
         question_text:
             problem_statement, // Use problem_statement as question_text
         feedback_correct: feedback_correct,
         feedback_incorrect: feedback_incorrect,
       );

  factory ProofByContradictionStepChoiceQuestion.fromJson(
    Map<String, dynamic> json,
  ) {
    return ProofByContradictionStepChoiceQuestion(
      id: json['id'] ?? 'unknown_q_id',
      problem_statement: json['problem_statement'] ?? '',
      options:
          (json['options'] as List<dynamic>?)
              ?.map((opt) => TextOption.fromJson(opt as Map<String, dynamic>))
              .toList() ??
          [],
      feedback_correct: json['feedback_correct'] ?? 'Correct!',
      feedback_incorrect: json['feedback_incorrect'] ?? 'Try again.',
    );
  }

  @override
  Map<String, dynamic> toJson() => {
    'id': id,
    'type': type,
    'problem_statement': question_text, // This is the question_text
    'options': options.map((o) => o.toJson()).toList(),
    'feedback_correct': feedback_correct,
    'feedback_incorrect': feedback_incorrect,
  };
}

class ArgumentCompletionModusPonensQuestion extends TestQuestion {
  final String premise1;
  final String premise2;
  // question_text is passed directly
  final List<TextOption> options;

  ArgumentCompletionModusPonensQuestion({
    required String id,
    required this.premise1,
    required this.premise2,
    required String question_text,
    required this.options,
    required String feedback_correct,
    required String feedback_incorrect,
  }) : super(
         id: id,
         type: 'argument_completion_modus_ponens',
         question_text: question_text,
         feedback_correct: feedback_correct,
         feedback_incorrect: feedback_incorrect,
       );

  factory ArgumentCompletionModusPonensQuestion.fromJson(
    Map<String, dynamic> json,
  ) {
    return ArgumentCompletionModusPonensQuestion(
      id: json['id'] ?? 'unknown_q_id',
      premise1: json['premise1'] ?? '',
      premise2: json['premise2'] ?? '',
      question_text: json['question_text'] ?? '',
      options:
          (json['options'] as List<dynamic>?)
              ?.map((opt) => TextOption.fromJson(opt as Map<String, dynamic>))
              .toList() ??
          [],
      feedback_correct: json['feedback_correct'] ?? 'Correct!',
      feedback_incorrect: json['feedback_incorrect'] ?? 'Try again.',
    );
  }

  @override
  Map<String, dynamic> toJson() => {
    'id': id,
    'type': type,
    'premise1': premise1,
    'premise2': premise2,
    'question_text': question_text,
    'options': options.map((o) => o.toJson()).toList(),
    'feedback_correct': feedback_correct,
    'feedback_incorrect': feedback_incorrect,
  };
}

class FallacyIdentificationScenarioQuestion extends TestQuestion {
  // question_text is scenario_text in JSON
  final List<TextOption> options; // Options are fallacy names

  FallacyIdentificationScenarioQuestion({
    required String id,
    required String scenario_text,
    required this.options,
    required String feedback_correct,
    required String feedback_incorrect,
  }) : super(
         id: id,
         type: 'fallacy_identification_scenario',
         question_text: scenario_text, // Use scenario_text as question_text
         feedback_correct: feedback_correct,
         feedback_incorrect: feedback_incorrect,
       );

  factory FallacyIdentificationScenarioQuestion.fromJson(
    Map<String, dynamic> json,
  ) {
    return FallacyIdentificationScenarioQuestion(
      id: json['id'] ?? 'unknown_q_id',
      scenario_text: json['scenario_text'] ?? '',
      options:
          (json['options'] as List<dynamic>?)
              ?.map((opt) => TextOption.fromJson(opt as Map<String, dynamic>))
              .toList() ??
          [],
      feedback_correct: json['feedback_correct'] ?? 'Correct!',
      feedback_incorrect: json['feedback_incorrect'] ?? 'Try again.',
    );
  }

  @override
  Map<String, dynamic> toJson() => {
    'id': id,
    'type': type,
    'scenario_text': question_text, // This is the question_text
    'options': options.map((o) => o.toJson()).toList(),
    'feedback_correct': feedback_correct,
    'feedback_incorrect': feedback_incorrect,
  };
}

// --- Reasoning Course Test Question Classes (Foundations) ---

// For test_argument_component_identifier (rf-noa-mt1)
class TestArgumentComponentIdentifierQuestion extends TestQuestion {
  final String text; // The argument text itself
  final String prompt_premise;
  final String prompt_conclusion;
  final List<String> correct_premises_keywords;
  final List<String> correct_conclusion_keywords;

  TestArgumentComponentIdentifierQuestion({
    required String id,
    required this.text, // question_text from JSON is 'text' here
    required this.prompt_premise,
    required this.prompt_conclusion,
    required this.correct_premises_keywords,
    required this.correct_conclusion_keywords,
    required String feedback_correct,
    required String feedback_incorrect,
  }) : super(
         id: id,
         type: 'test_argument_component_identifier',
         question_text: text, // Use the 'text' field as the base question_text
         feedback_correct: feedback_correct,
         feedback_incorrect: feedback_incorrect,
       );

  @override
  Map<String, dynamic> toJson() => {
    'id': id,
    'type': type,
    'text': question_text, // Store 'text' as 'question_text' in JSON
    'prompt_premise': prompt_premise,
    'prompt_conclusion': prompt_conclusion,
    'correct_premises_keywords': correct_premises_keywords,
    'correct_conclusion_keywords': correct_conclusion_keywords,
    'feedback_correct': feedback_correct,
    'feedback_incorrect': feedback_incorrect,
  };

  factory TestArgumentComponentIdentifierQuestion.fromJson(
    Map<String, dynamic> json,
  ) {
    return TestArgumentComponentIdentifierQuestion(
      id: json['id'] ?? 'q_unknown',
      text:
          json['text'] ??
          json['question_text'] ??
          '', // Allow 'text' or 'question_text' from JSON
      prompt_premise: json['prompt_premise'] ?? 'Identify the premise(s):',
      prompt_conclusion:
          json['prompt_conclusion'] ?? 'Identify the conclusion:',
      correct_premises_keywords: List<String>.from(
        json['correct_premises_keywords'] ?? [],
      ),
      correct_conclusion_keywords: List<String>.from(
        json['correct_conclusion_keywords'] ?? [],
      ),
      feedback_correct: json['feedback_correct'] ?? 'Correct!',
      feedback_incorrect: json['feedback_incorrect'] ?? 'Try again.',
    );
  }
}

// For test_deductive_argument_evaluator (rf-dr-mt1)
class TestDeductiveArgumentEvaluatorQuestion extends TestQuestion {
  final String argument_text;
  final String prompt_validity;
  final String?
  prompt_soundness_if_valid; // Optional, as it depends on validity
  final bool correct_is_valid;
  final String? validity_explanation_if_invalid;
  final bool?
  correct_premises_true_for_soundness; // Nullable if argument is invalid
  final String?
  soundness_explanation; // Explanation for soundness or why it's unsound

  TestDeductiveArgumentEvaluatorQuestion({
    required String id,
    required this.argument_text,
    required this.prompt_validity,
    this.prompt_soundness_if_valid,
    required this.correct_is_valid,
    this.validity_explanation_if_invalid,
    this.correct_premises_true_for_soundness,
    this.soundness_explanation,
    required String feedback_correct,
    required String feedback_incorrect,
  }) : super(
         id: id,
         type: 'test_deductive_argument_evaluator',
         question_text: argument_text, // Use argument_text as the base question
         feedback_correct: feedback_correct,
         feedback_incorrect: feedback_incorrect,
       );

  @override
  Map<String, dynamic> toJson() => {
    'id': id,
    'type': type,
    'argument_text': question_text,
    'prompt_validity': prompt_validity,
    'prompt_soundness_if_valid': prompt_soundness_if_valid,
    'correct_is_valid': correct_is_valid,
    'validity_explanation_if_invalid': validity_explanation_if_invalid,
    'correct_premises_true_for_soundness': correct_premises_true_for_soundness,
    'soundness_explanation': soundness_explanation,
    'feedback_correct': feedback_correct,
    'feedback_incorrect': feedback_incorrect,
  };

  factory TestDeductiveArgumentEvaluatorQuestion.fromJson(
    Map<String, dynamic> json,
  ) {
    return TestDeductiveArgumentEvaluatorQuestion(
      id: json['id'] ?? 'q_deductive_eval_unknown',
      argument_text: json['argument_text'] ?? json['question_text'] ?? '',
      prompt_validity: json['prompt_validity'] ?? 'Is this argument valid?',
      prompt_soundness_if_valid: json['prompt_soundness_if_valid'],
      correct_is_valid: json['correct_is_valid'] ?? false,
      validity_explanation_if_invalid: json['validity_explanation_if_invalid'],
      correct_premises_true_for_soundness:
          json['correct_premises_true_for_soundness'],
      soundness_explanation: json['soundness_explanation'],
      feedback_correct: json['feedback_correct'] ?? 'Correct!',
      feedback_incorrect:
          json['feedback_incorrect'] ?? 'Check your evaluation.',
    );
  }
}

// For test_inductive_strength_evaluator (rf-ir-mt1)
class TestInductiveStrengthEvaluatorQuestion extends TestQuestion {
  final String argument_text;
  final String
  prompt_strength; // e.g., "How strong is this argument (weak, moderate, strong)?"
  final String?
  prompt_cogency_if_strong; // e.g., "If strong, are the premises plausible (making it cogent)?"
  final String correct_strength_rating; // "weak", "moderate", "strong"
  final String? strength_explanation;
  final bool? correct_premises_plausible_for_cogency; // Nullable if not strong
  final String? cogency_explanation;

  TestInductiveStrengthEvaluatorQuestion({
    required String id,
    required this.argument_text,
    required this.prompt_strength,
    this.prompt_cogency_if_strong,
    required this.correct_strength_rating,
    this.strength_explanation,
    this.correct_premises_plausible_for_cogency,
    this.cogency_explanation,
    required String feedback_correct,
    required String feedback_incorrect,
  }) : super(
         id: id,
         type: 'test_inductive_strength_evaluator',
         question_text: argument_text,
         feedback_correct: feedback_correct,
         feedback_incorrect: feedback_incorrect,
       );

  @override
  Map<String, dynamic> toJson() => {
    'id': id,
    'type': type,
    'argument_text': question_text,
    'prompt_strength': prompt_strength,
    'prompt_cogency_if_strong': prompt_cogency_if_strong,
    'correct_strength_rating': correct_strength_rating,
    'strength_explanation': strength_explanation,
    'correct_premises_plausible_for_cogency':
        correct_premises_plausible_for_cogency,
    'cogency_explanation': cogency_explanation,
    'feedback_correct': feedback_correct,
    'feedback_incorrect': feedback_incorrect,
  };

  factory TestInductiveStrengthEvaluatorQuestion.fromJson(
    Map<String, dynamic> json,
  ) {
    return TestInductiveStrengthEvaluatorQuestion(
      id: json['id'] ?? 'q_inductive_eval_unknown',
      argument_text: json['argument_text'] ?? json['question_text'] ?? '',
      prompt_strength:
          json['prompt_strength'] ?? 'Assess the argument\'s strength.',
      prompt_cogency_if_strong: json['prompt_cogency_if_strong'],
      correct_strength_rating: json['correct_strength_rating'] ?? 'moderate',
      strength_explanation: json['strength_explanation'],
      correct_premises_plausible_for_cogency:
          json['correct_premises_plausible_for_cogency'],
      cogency_explanation: json['cogency_explanation'],
      feedback_correct: json['feedback_correct'] ?? 'Correct evaluation!',
      feedback_incorrect:
          json['feedback_incorrect'] ??
          'Re-assess the argument based on inductive principles.',
    );
  }
}

// For test_everyday_reasoning_scenario (rf-rel-mt1)
class TestEverydayReasoningScenarioQuestion extends TestQuestion {
  final String scenario_text;
  final String prompt; // Prompt for the user on what to evaluate/do
  final List<String> keywords_for_good_answer;

  TestEverydayReasoningScenarioQuestion({
    required String id,
    required this.scenario_text,
    required this.prompt,
    required this.keywords_for_good_answer,
    required String feedback_correct,
    required String feedback_incorrect,
  }) : super(
         id: id,
         type: 'test_everyday_reasoning_scenario',
         question_text: scenario_text, // Use scenario_text as the base question
         feedback_correct: feedback_correct,
         feedback_incorrect: feedback_incorrect,
       );

  @override
  Map<String, dynamic> toJson() => {
    'id': id,
    'type': type,
    'scenario_text': question_text,
    'prompt': prompt,
    'keywords_for_good_answer': keywords_for_good_answer,
    'feedback_correct': feedback_correct,
    'feedback_incorrect': feedback_incorrect,
  };

  factory TestEverydayReasoningScenarioQuestion.fromJson(
    Map<String, dynamic> json,
  ) {
    return TestEverydayReasoningScenarioQuestion(
      id: json['id'] ?? 'q_everyday_reasoning_unknown',
      scenario_text: json['scenario_text'] ?? json['question_text'] ?? '',
      prompt: json['prompt'] ?? 'Evaluate the scenario.',
      keywords_for_good_answer: List<String>.from(
        json['keywords_for_good_answer'] ?? [],
      ),
      feedback_correct: json['feedback_correct'] ?? 'Good reasoning!',
      feedback_incorrect:
          json['feedback_incorrect'] ??
          'Consider applying the reasoning principles discussed.',
    );
  }
}

// --- Reasoning Course Test Question Classes (Frontiers) ---

// For test_formal_proof_construction (rfr-pl-mt1)
class ProofStepOutline {
  final String statement; // Expected statement (can be a pattern or exact)
  final String justification_pattern; // Regex pattern for justification

  ProofStepOutline({
    required this.statement,
    required this.justification_pattern,
  });

  factory ProofStepOutline.fromJson(Map<String, dynamic> json) {
    return ProofStepOutline(
      statement: json['statement'] ?? '',
      justification_pattern: json['justification_pattern'] ?? '',
    );
  }
  Map<String, dynamic> toJson() => {
    'statement': statement,
    'justification_pattern': justification_pattern,
  };
}

class TestFormalProofConstructionQuestion extends TestQuestion {
  final String problem_statement; // Includes premises and conclusion to derive
  final List<String> available_rules;
  final int? max_steps;
  final List<ProofStepOutline> correct_proof_outline;

  TestFormalProofConstructionQuestion({
    required String id,
    required this.problem_statement,
    required this.available_rules,
    this.max_steps,
    required this.correct_proof_outline,
    required String feedback_correct,
    required String feedback_incorrect,
  }) : super(
         id: id,
         type: 'test_formal_proof_construction',
         question_text: problem_statement,
         feedback_correct: feedback_correct,
         feedback_incorrect: feedback_incorrect,
       );

  @override
  Map<String, dynamic> toJson() => {
    'id': id,
    'type': type,
    'problem_statement': question_text,
    'available_rules': available_rules,
    'max_steps': max_steps,
    'correct_proof_outline':
        correct_proof_outline.map((s) => s.toJson()).toList(),
    'feedback_correct': feedback_correct,
    'feedback_incorrect': feedback_incorrect,
  };

  factory TestFormalProofConstructionQuestion.fromJson(
    Map<String, dynamic> json,
  ) {
    return TestFormalProofConstructionQuestion(
      id: json['id'] ?? 'q_formal_proof_unknown',
      problem_statement:
          json['problem_statement'] ?? json['question_text'] ?? '',
      available_rules: List<String>.from(json['available_rules'] ?? []),
      max_steps: json['max_steps'],
      correct_proof_outline:
          (json['correct_proof_outline'] as List<dynamic>?)
              ?.map(
                (sJson) =>
                    ProofStepOutline.fromJson(sJson as Map<String, dynamic>),
              )
              .toList() ??
          [],
      feedback_correct: json['feedback_correct'] ?? 'Proof is correct!',
      feedback_incorrect:
          json['feedback_incorrect'] ??
          'There is an error in your proof. Check your steps and justifications.',
    );
  }
}

/// Represents a lesson definition (interactive lesson or test)
class LessonDefinition {
  final String id;
  final String title;
  final String description;
  final int order;
  final String type;
  final int estimatedTimeMinutes;
  final List<ContentScreen> contentBlocks;

  LessonDefinition({
    required this.id,
    required this.title,
    required this.description,
    required this.order,
    required this.type,
    required this.estimatedTimeMinutes,
    this.contentBlocks = const [],
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'order': order,
      'type': type,
      'estimatedTimeMinutes': estimatedTimeMinutes,
      'contentBlocks': contentBlocks.map((block) => block.toJson()).toList(),
    };
  }

  factory LessonDefinition.fromJson(Map<String, dynamic> json) {
    return LessonDefinition(
      id: json['id'] ?? 'unknown_lesson_def_id',
      title: json['title'] ?? 'Unknown Lesson',
      description: json['description'] ?? '',
      order: json['order'] ?? 0,
      type: json['type'] ?? 'interactive_lesson',
      estimatedTimeMinutes: json['estimatedTimeMinutes'] ?? 0,
      contentBlocks:
          (json['contentBlocks'] as List<dynamic>?)
              ?.map(
                (blockJson) =>
                    ContentScreen.fromJson(blockJson as Map<String, dynamic>),
              )
              .toList() ??
          [],
    );
  }
}

/// Represents a single screen within an interactive lesson or test
class ContentScreen {
  final String id;
  final String type;
  final int order;
  final int estimatedTimeSeconds;
  final ScreenContent content;

  ContentScreen({
    required this.id,
    required this.type,
    required this.order,
    required this.estimatedTimeSeconds,
    required this.content,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type,
      'order': order,
      'estimatedTimeSeconds': estimatedTimeSeconds,
      'content': content.toJson(),
    };
  }

  factory ContentScreen.fromJson(Map<String, dynamic> json) {
    return ContentScreen(
      id: json['id'] ?? 'unknown_screen_id',
      type: json['type'] ?? 'lesson_screen',
      order: json['order'] ?? 0,
      estimatedTimeSeconds: json['estimatedTimeSeconds'] ?? 0,
      content: ScreenContent.fromJson(
        json['content'] as Map<String, dynamic>? ?? {},
      ), // Handle null content
    );
  }
}

/// Holds the actual content of a screen
class ScreenContent {
  final String? headline;
  final String body_md;
  final VisualElement visual;
  final InteractiveElement interactive_element;
  final String? audio_narration_url;
  final String? hook;
  final String? explanation_on_correct;

  ScreenContent({
    this.headline,
    required this.body_md,
    required this.visual,
    required this.interactive_element,
    this.audio_narration_url,
    this.hook,
    this.explanation_on_correct,
  });

  Map<String, dynamic> toJson() {
    return {
      'headline': headline,
      'body_md': body_md,
      'visual': visual.toJson(),
      'interactive_element': interactive_element.toJson(),
      'audio_narration_url': audio_narration_url,
      'hook': hook,
      'explanation_on_correct': explanation_on_correct,
    };
  }

  factory ScreenContent.fromJson(Map<String, dynamic> json) {
    return ScreenContent(
      headline: json['headline'],
      body_md: json['body_md'] ?? '',
      visual:
          json['visual'] != null
              ? VisualElement.fromJson(json['visual'] as Map<String, dynamic>)
              : PlaceholderVisual(
                data: {
                  'type': 'placeholder_visual',
                  'value': 'Missing visual data',
                },
              ),
      interactive_element:
          json['interactive_element'] != null
              ? InteractiveElement.fromJson(
                json['interactive_element'] as Map<String, dynamic>,
              )
              : PlaceholderInteractiveElement(
                data: {
                  'type': 'placeholder_interactive',
                  'text': 'Missing interactive data',
                },
              ),
      audio_narration_url: json['audio_narration_url'],
      hook: json['hook'],
      explanation_on_correct: json['explanation_on_correct'],
    );
  }
}

// --- Visual Elements ---
abstract class VisualElement {
  final String type;

  VisualElement({required this.type});

  Map<String, dynamic> toJson();

  factory VisualElement.fromJson(Map<String, dynamic> json) {
    switch (json['type'] as String?) {
      // Added null check for type
      case 'giphy_search':
        return GiphyVisual.fromJson(json);
      case 'unsplash_search':
        return UnsplashVisual.fromJson(json);
      case 'local_asset':
        return LocalAssetVisual.fromJson(json);
      case 'static_text':
        return StaticTextVisual.fromJson(json);
      case 'animated_sequence_placeholder':
        return AnimatedSequencePlaceholderVisual.fromJson(json);
      case 'local_asset_sequence':
        return LocalAssetSequenceVisual.fromJson(json);
      case 'interactive_triangle_angle_sum':
        return InteractiveTriangleAngleSumVisual.fromJson(json);
      case 'interactive_pythagorean_theorem_viz':
        return InteractivePythagoreanTheoremVisual.fromJson(json);
      case 'interactive_circle_properties_viz':
        return InteractiveCirclePropertiesVisual.fromJson(json);
      case 'interactive_tessellation_creator':
        return InteractiveTessellationCreatorVisual.fromJson(json);
      case 'interactive_transformation_identification_game':
        return InteractiveTransformationIdentificationGameVisual.fromJson(json);
      default:
        print('Warning: Unknown or null visual type: ${json['type']}');
        return PlaceholderVisual.fromJson(json);
    }
  }
}

class PlaceholderVisual extends VisualElement {
  final Map<String, dynamic> data;
  PlaceholderVisual({required this.data})
    : super(type: data['type'] as String? ?? 'unknown_visual');

  @override
  Map<String, dynamic> toJson() => {'type': type, 'data': data};

  factory PlaceholderVisual.fromJson(Map<String, dynamic> json) =>
      PlaceholderVisual(data: json);
}

class GiphyVisual extends VisualElement {
  final String value;

  GiphyVisual({required this.value}) : super(type: 'giphy_search');

  @override
  Map<String, dynamic> toJson() => {'type': type, 'value': value};

  factory GiphyVisual.fromJson(Map<String, dynamic> json) =>
      GiphyVisual(value: json['value'] ?? '');
}

class UnsplashVisual extends VisualElement {
  final String value;

  UnsplashVisual({required this.value}) : super(type: 'unsplash_search');

  @override
  Map<String, dynamic> toJson() => {'type': type, 'value': value};

  factory UnsplashVisual.fromJson(Map<String, dynamic> json) =>
      UnsplashVisual(value: json['value'] ?? '');
}

class LocalAssetVisual extends VisualElement {
  final String value;

  LocalAssetVisual({required this.value}) : super(type: 'local_asset');

  @override
  Map<String, dynamic> toJson() => {'type': type, 'value': value};

  factory LocalAssetVisual.fromJson(Map<String, dynamic> json) =>
      LocalAssetVisual(value: json['value'] ?? '');
}

class StaticTextVisual extends VisualElement {
  final String value;

  StaticTextVisual({required this.value}) : super(type: 'static_text');

  @override
  Map<String, dynamic> toJson() => {'type': type, 'value': value};

  factory StaticTextVisual.fromJson(Map<String, dynamic> json) =>
      StaticTextVisual(value: json['value'] ?? '');
}

class AnimatedSequencePlaceholderVisual extends VisualElement {
  final List<String> value;

  AnimatedSequencePlaceholderVisual({required this.value})
    : super(type: 'animated_sequence_placeholder');

  @override
  Map<String, dynamic> toJson() => {'type': type, 'value': value};

  factory AnimatedSequencePlaceholderVisual.fromJson(
    Map<String, dynamic> json,
  ) => AnimatedSequencePlaceholderVisual(
    value: List<String>.from(json['value'] ?? []),
  );
}

class LocalAssetSequenceVisual extends VisualElement {
  final List<String> value;

  LocalAssetSequenceVisual({required this.value})
    : super(type: 'local_asset_sequence');

  @override
  Map<String, dynamic> toJson() => {'type': type, 'value': value};

  factory LocalAssetSequenceVisual.fromJson(Map<String, dynamic> json) =>
      LocalAssetSequenceVisual(value: List<String>.from(json['value'] ?? []));
}

class InteractiveTriangleAngleSumVisual extends VisualElement {
  final String? initial_shape;
  InteractiveTriangleAngleSumVisual({this.initial_shape})
    : super(type: 'interactive_triangle_angle_sum');
  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'initial_shape': initial_shape,
  };
  factory InteractiveTriangleAngleSumVisual.fromJson(
    Map<String, dynamic> json,
  ) => InteractiveTriangleAngleSumVisual(initial_shape: json['initial_shape']);
}

class InteractivePythagoreanTheoremVisual extends VisualElement {
  InteractivePythagoreanTheoremVisual()
    : super(type: 'interactive_pythagorean_theorem_viz');
  @override
  Map<String, dynamic> toJson() => {'type': type};
  factory InteractivePythagoreanTheoremVisual.fromJson(
    Map<String, dynamic> json,
  ) => InteractivePythagoreanTheoremVisual();
}

class InteractiveCirclePropertiesVisual extends VisualElement {
  InteractiveCirclePropertiesVisual()
    : super(type: 'interactive_circle_properties_viz');
  @override
  Map<String, dynamic> toJson() => {'type': type};
  factory InteractiveCirclePropertiesVisual.fromJson(
    Map<String, dynamic> json,
  ) => InteractiveCirclePropertiesVisual();
}

class InteractiveTessellationCreatorVisual extends VisualElement {
  final List<String>? shapes;
  InteractiveTessellationCreatorVisual({this.shapes})
    : super(type: 'interactive_tessellation_creator');
  @override
  Map<String, dynamic> toJson() => {'type': type, 'shapes': shapes};
  factory InteractiveTessellationCreatorVisual.fromJson(
    Map<String, dynamic> json,
  ) => InteractiveTessellationCreatorVisual(
    shapes: json['shapes'] != null ? List<String>.from(json['shapes']) : null,
  );
}

class InteractiveTransformationIdentificationGameVisual extends VisualElement {
  final String? figure_a_src;
  final String? figure_b_src;
  final String? correct_transformation;
  InteractiveTransformationIdentificationGameVisual({
    this.figure_a_src,
    this.figure_b_src,
    this.correct_transformation,
  }) : super(type: 'interactive_transformation_identification_game');
  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'figure_a_src': figure_a_src,
    'figure_b_src': figure_b_src,
    'correct_transformation': correct_transformation,
  };
  factory InteractiveTransformationIdentificationGameVisual.fromJson(
    Map<String, dynamic> json,
  ) => InteractiveTransformationIdentificationGameVisual(
    figure_a_src: json['figure_a_src'],
    figure_b_src: json['figure_b_src'],
    correct_transformation: json['correct_transformation'],
  );
}

// --- Interactive Elements ---
abstract class InteractiveElement {
  final String type;

  InteractiveElement({required this.type});

  Map<String, dynamic> toJson();

  factory InteractiveElement.fromJson(Map<String, dynamic> json) {
    switch (json['type'] as String?) {
      // Added null check for type
      case 'button':
        return ButtonElement.fromJson(json);
      case 'multiple_choice_text':
        return MultipleChoiceTextElement.fromJson(json);
      case 'text_input':
        return TextInputElement.fromJson(json);
      case 'multiple_choice_image':
        return MultipleChoiceImageElement.fromJson(json);
      case 'text_input_reveal':
        return TextInputRevealElement.fromJson(json);
      case 'text_input_quick':
        return TextInputQuickElement.fromJson(json);
      case 'multiple_choice_icon':
        return MultipleChoiceIconElement.fromJson(json);
      case 'sieve_mini_game':
        return SieveMiniGameElement.fromJson(json);
      case 'mental_math_challenge_sequence':
        return MentalMathChallengeSequenceElement.fromJson(json);
      case 'drag_drop_angle_sorter':
        return DragDropAngleSorterElement.fromJson(json);
      case 'translation_interactive_game':
        return TranslationInteractiveGameElement.fromJson(json);
      case 'reflection_interactive_game':
        return ReflectionInteractiveGameElement.fromJson(json);
      case 'rotation_interactive_game':
        return RotationInteractiveGameElement.fromJson(json);
      case 'dilation_interactive_game':
        return DilationInteractiveGameElement.fromJson(json);
      case 'static_text_display':
        return StaticTextDisplayElement.fromJson(json);
      case 'interactive_number_line_jump':
        return InteractiveNumberLineJumpElement.fromJson(json);
      case 'multiplication_facts_game':
        return MultiplicationFactsGameElement.fromJson(json);
      case 'drag_drop_sharing_game':
        return DragDropSharingGameElement.fromJson(json);
      case 'grouping_items_game':
        return GroupingItemsGameElement.fromJson(json);
      case 'interactive_base_ten_blocks':
        return InteractiveBaseTenBlocksElement.fromJson(json);
      case 'interactive_number_ordering_game':
        return InteractiveNumberOrderingGameElement.fromJson(json);
      case 'chart_interpretation_mcq':
        return ChartInterpretationMcqElement.fromJson(json);
      case 'interactive_plotting_game':
        return InteractivePlottingGameElement.fromJson(json);
      case 'interactive_function_machine':
        return InteractiveFunctionMachineElement.fromJson(json);
      case 'interactive_graph_selection_game':
        return InteractiveGraphSelectionGameElement.fromJson(json);
      case 'text_input_custom_feedback':
        return TextInputCustomFeedbackElement.fromJson(json);
      // New Interactive Elements for Logical Deduction Module
      case 'truth_table_builder_conditional':
        return TruthTableBuilderConditionalElement.fromJson(json);
      case 'interactive_conditional_scenario_evaluator':
        return InteractiveConditionalScenarioEvaluatorElement.fromJson(json);
      case 'guided_proof_steps':
        return GuidedProofStepsElement.fromJson(json);
      case 'argument_chain_builder_game':
        return ArgumentChainBuilderGameElement.fromJson(json);
      case 'fallacy_identification_quick_poll':
        return FallacyIdentificationQuickPollElement.fromJson(json);
      case 'fallacy_identifier_game_multiple_choice':
        return FallacyIdentifierGameMultipleChoiceElement.fromJson(json);
      case 'number_line_comparison_task': // Added new case
        return NumberLineComparisonTaskElement.fromJson(json);
      case 'draw_line_of_symmetry_game': // Added new case
        return DrawLineOfSymmetryGameElement.fromJson(json);
      case 'multiple_choice_image_from_visual': // Added new case
        return MultipleChoiceImageFromVisualElement.fromJson(json);
      // --- Algebra Course Interactive Elements ---
      case 'variable_value_explorer':
        return VariableValueExplorerElement.fromJson(json);
      case 'interactive_expression_builder':
        return InteractiveExpressionBuilderElement.fromJson(json);
      case 'interactive_expression_evaluator':
        return InteractiveExpressionEvaluatorElement.fromJson(json);
      case 'interactive_like_terms_combiner':
        return InteractiveLikeTermsCombinerElement.fromJson(json);
      case 'interactive_balance_scale_analogy':
        return InteractiveBalanceScaleAnalogyElement.fromJson(json);
      case 'interactive_number_line_solution_plotter': // New case
        return InteractiveNumberLineSolutionPlotterElement.fromJson(json);
      case 'interactive_solution_checker': // New case
        return InteractiveSolutionCheckerElement.fromJson(json);
      case 'interactive_simplify_then_solve': // New case
        return InteractiveSimplifyThenSolveElement.fromJson(json);
      case 'interactive_word_problem_solver': // New case
        return InteractiveWordProblemSolverElement.fromJson(json);
      case 'interactive_inequality_symbol_matcher': // New case
        return InteractiveInequalitySymbolMatcherElement.fromJson(json);
      case 'interactive_relationship_to_equation': // New case
        return InteractiveRelationshipToEquationElement.fromJson(json);
      case 'interactive_linear_equation_grapher': // New case
        return InteractiveLinearEquationGrapherElement.fromJson(json);
      case 'interactive_slope_explorer': // New case
        return InteractiveSlopeExplorerElement.fromJson(json);
      case 'interactive_graph_to_equation_matcher': // New case
        return InteractiveGraphToEquationMatcherElement.fromJson(json);
      case 'interactive_system_of_equations_grapher': // New case
        return InteractiveSystemOfEquationsGrapherElement.fromJson(json);
      // --- Calculus Course Interactive Elements ---
      case 'interactive_function_plotter_limit_explorer':
        return InteractiveFunctionPlotterLimitExplorerElement.fromJson(json);
      case 'interactive_epsilon_delta_explorer':
        return InteractiveEpsilonDeltaExplorerElement.fromJson(json);
      case 'interactive_secant_line_explorer':
        return InteractiveSecantLineExplorerElement.fromJson(json);
      case 'interactive_secant_to_tangent_visualizer':
        return InteractiveSecantToTangentVisualizerElement.fromJson(json);
      case 'interactive_derivative_definition_explorer':
        return InteractiveDerivativeDefinitionExplorerElement.fromJson(json);
      case 'interactive_function_derivative_visualizer':
        return InteractiveFunctionDerivativeVisualizerElement.fromJson(json);
      case 'interactive_first_derivative_test_explorer':
        return InteractiveFirstDerivativeTestExplorerElement.fromJson(json);
      case 'interactive_concavity_inflection_explorer':
        return InteractiveConcavityInflectionExplorerElement.fromJson(json);
      case 'interactive_riemann_sum_explorer':
        return InteractiveRiemannSumExplorerElement.fromJson(json);
      case 'interactive_area_between_curves_explorer':
        return InteractiveAreaBetweenCurvesExplorerElement.fromJson(json);
      case 'interactive_volume_disk_method_explorer':
        return InteractiveVolumeDiskMethodExplorerElement.fromJson(json);
      case 'interactive_work_calculation_explorer':
        return InteractiveWorkCalculationExplorerElement.fromJson(json);
      // --- Reasoning Course Interactive Elements (Foundations) ---
      case 'interactive_premise_conclusion_sorter':
        return InteractivePremiseConclusionSorterElement.fromJson(json);
      case 'interactive_claim_classifier':
        return InteractiveClaimClassifierElement.fromJson(json);
      case 'interactive_argument_diagram_builder':
        return InteractiveArgumentDiagramBuilderElement.fromJson(json);
      case 'interactive_find_the_argument_in_text':
        return InteractiveFindTheArgumentInTextElement.fromJson(json);
      case 'interactive_modus_ponens_tollens_builder':
        return InteractiveModusPonensTollensBuilderElement.fromJson(json);
      case 'interactive_syllogism_evaluator':
        return InteractiveSyllogismEvaluatorElement.fromJson(json);
      case 'interactive_validity_tester_counterexample':
        return InteractiveValidityTesterCounterexampleElement.fromJson(json);
      case 'interactive_generalization_evaluator':
        return InteractiveGeneralizationEvaluatorElement.fromJson(json);
      case 'interactive_analogy_strength_sorter':
        return InteractiveAnalogyStrengthSorterElement.fromJson(json);
      case 'interactive_causal_inference_builder':
        return InteractiveCausalInferenceBuilderElement.fromJson(json);
      case 'interactive_fallacy_matcher':
        return InteractiveFallacyMatcherElement.fromJson(json);
      case 'interactive_spot_the_fallacy_in_dialogue':
        return InteractiveSpotTheFallacyInDialogueElement.fromJson(json);
      case 'interactive_news_article_analysis':
        return InteractiveNewsArticleAnalysisElement.fromJson(json);
      case 'interactive_advertisement_deconstructor':
        return InteractiveAdvertisementDeconstructorElement.fromJson(json);
      case 'interactive_decision_making_scenario':
        return InteractiveDecisionMakingScenarioElement.fromJson(json);
      case 'interactive_argument_constructor':
        return InteractiveArgumentConstructorElement.fromJson(json);
      // --- Reasoning Course Interactive Elements (Frontiers) ---
      case 'interactive_symbol_connective_matcher':
        return InteractiveSymbolConnectiveMatcherElement.fromJson(json);
      case 'interactive_truth_table_generator':
        return InteractiveTruthTableGeneratorElement.fromJson(json);
      case 'interactive_equivalence_checker':
        return InteractiveEquivalenceCheckerElement.fromJson(json);
      case 'interactive_rule_of_inference_applier':
        return InteractiveRuleOfInferenceApplierElement.fromJson(json);
      case 'interactive_formal_proof_builder':
        return InteractiveFormalProofBuilderElement.fromJson(json);
      default:
        print(
          'Warning: Unknown or null interactive element type: ${json['type']}',
        );
        return PlaceholderInteractiveElement.fromJson(json);
    }
  }
}

// --- New Interactive Element Classes ---

// --- Reasoning Course Interactive Element Classes (Foundations) ---

// For interactive_premise_conclusion_sorter (rf-noa-l1)
class PremiseConclusionSorterStatement {
  final String id;
  final String text;

  PremiseConclusionSorterStatement({required this.id, required this.text});

  factory PremiseConclusionSorterStatement.fromJson(Map<String, dynamic> json) {
    return PremiseConclusionSorterStatement(
      id: json['id'] ?? 's_unknown',
      text: json['text'] ?? '',
    );
  }

  Map<String, dynamic> toJson() => {'id': id, 'text': text};
}

class PremiseConclusionSorterSolution {
  final List<String> premises; // List of statement IDs
  final List<String> conclusions; // List of statement IDs

  PremiseConclusionSorterSolution({
    required this.premises,
    required this.conclusions,
  });

  factory PremiseConclusionSorterSolution.fromJson(Map<String, dynamic> json) {
    return PremiseConclusionSorterSolution(
      premises: List<String>.from(json['premises'] ?? []),
      conclusions: List<String>.from(json['conclusions'] ?? []),
    );
  }

  Map<String, dynamic> toJson() => {
    'premises': premises,
    'conclusions': conclusions,
  };
}

class InteractivePremiseConclusionSorterElement extends InteractiveElement {
  final String prompt;
  final List<PremiseConclusionSorterStatement> statements;
  final PremiseConclusionSorterSolution solution;
  final String? feedback_correct;
  final String? feedback_incorrect;

  InteractivePremiseConclusionSorterElement({
    required this.prompt,
    required this.statements,
    required this.solution,
    this.feedback_correct,
    this.feedback_incorrect,
  }) : super(type: 'interactive_premise_conclusion_sorter');

  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'prompt': prompt,
    'statements': statements.map((s) => s.toJson()).toList(),
    'solution': solution.toJson(),
    'feedback_correct': feedback_correct,
    'feedback_incorrect': feedback_incorrect,
  };

  factory InteractivePremiseConclusionSorterElement.fromJson(
    Map<String, dynamic> json,
  ) {
    return InteractivePremiseConclusionSorterElement(
      prompt: json['prompt'] ?? 'Sort the statements.',
      statements:
          (json['statements'] as List<dynamic>?)
              ?.map(
                (sJson) => PremiseConclusionSorterStatement.fromJson(
                  sJson as Map<String, dynamic>,
                ),
              )
              .toList() ??
          [],
      solution: PremiseConclusionSorterSolution.fromJson(
        json['solution'] as Map<String, dynamic>? ?? {},
      ),
      feedback_correct: json['feedback_correct'] ?? 'Correctly sorted!',
      feedback_incorrect:
          json['feedback_incorrect'] ?? 'Some items are misplaced. Try again!',
    );
  }
}

// For interactive_claim_classifier (rf-noa-l2)
class ClaimToClassify {
  final String id;
  final String text;
  final String correct_type; // "fact", "opinion", "inference"

  ClaimToClassify({
    required this.id,
    required this.text,
    required this.correct_type,
  });

  factory ClaimToClassify.fromJson(Map<String, dynamic> json) {
    return ClaimToClassify(
      id: json['id'] ?? 'c_unknown',
      text: json['text'] ?? '',
      correct_type: json['correct_type'] ?? 'fact',
    );
  }

  Map<String, dynamic> toJson() => {
    'id': id,
    'text': text,
    'correct_type': correct_type,
  };
}

class InteractiveClaimClassifierElement extends InteractiveElement {
  final String prompt;
  final List<ClaimToClassify> statements;
  final List<String> claim_types; // e.g., ["Fact", "Opinion", "Inference"]
  final String? feedback_correct_template; // "Correct! '{text}' is a {type}."
  final String?
  feedback_incorrect_template; // "Not quite. '{text}' is actually a {correct_type} because..."

  InteractiveClaimClassifierElement({
    required this.prompt,
    required this.statements,
    this.claim_types = const ["Fact", "Opinion", "Inference"],
    this.feedback_correct_template,
    this.feedback_incorrect_template,
  }) : super(type: 'interactive_claim_classifier');

  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'prompt': prompt,
    'statements': statements.map((s) => s.toJson()).toList(),
    'claim_types': claim_types,
    'feedback_correct_template': feedback_correct_template,
    'feedback_incorrect_template': feedback_incorrect_template,
  };

  factory InteractiveClaimClassifierElement.fromJson(
    Map<String, dynamic> json,
  ) {
    return InteractiveClaimClassifierElement(
      prompt: json['prompt'] ?? 'Classify each claim.',
      statements:
          (json['statements'] as List<dynamic>?)
              ?.map(
                (sJson) =>
                    ClaimToClassify.fromJson(sJson as Map<String, dynamic>),
              )
              .toList() ??
          [],
      claim_types:
          json['claim_types'] != null
              ? List<String>.from(json['claim_types'])
              : ["Fact", "Opinion", "Inference"],
      feedback_correct_template:
          json['feedback_correct_template'] ?? "Correct! '{text}' is a {type}.",
      feedback_incorrect_template:
          json['feedback_incorrect_template'] ??
          "Not quite. '{text}' is actually a {correct_type}.",
    );
  }
}

// For interactive_argument_diagram_builder (rf-noa-l3)
class DiagramStatement {
  final String id;
  final String text;

  DiagramStatement({required this.id, required this.text});

  factory DiagramStatement.fromJson(Map<String, dynamic> json) {
    return DiagramStatement(
      id: json['id'] ?? 'ds_unknown',
      text: json['text'] ?? '',
    );
  }
  Map<String, dynamic> toJson() => {'id': id, 'text': text};
}

class DiagramConnection {
  final List<String> from; // List of statement IDs (premises)
  final String to; // Statement ID (conclusion)

  DiagramConnection({required this.from, required this.to});

  factory DiagramConnection.fromJson(Map<String, dynamic> json) {
    return DiagramConnection(
      from: List<String>.from(json['from'] ?? []),
      to: json['to'] ?? '',
    );
  }
  Map<String, dynamic> toJson() => {'from': from, 'to': to};
}

class InteractiveArgumentDiagramBuilderElement extends InteractiveElement {
  final String prompt;
  final List<DiagramStatement> statements;
  final List<DiagramConnection>
  connections; // Correct connections for validation
  final String? feedback_correct;
  final String? feedback_incorrect;

  InteractiveArgumentDiagramBuilderElement({
    required this.prompt,
    required this.statements,
    required this.connections,
    this.feedback_correct,
    this.feedback_incorrect,
  }) : super(type: 'interactive_argument_diagram_builder');

  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'prompt': prompt,
    'statements': statements.map((s) => s.toJson()).toList(),
    'connections': connections.map((c) => c.toJson()).toList(),
    'feedback_correct': feedback_correct,
    'feedback_incorrect': feedback_incorrect,
  };

  factory InteractiveArgumentDiagramBuilderElement.fromJson(
    Map<String, dynamic> json,
  ) {
    return InteractiveArgumentDiagramBuilderElement(
      prompt: json['prompt'] ?? 'Build the argument diagram.',
      statements:
          (json['statements'] as List<dynamic>?)
              ?.map(
                (sJson) =>
                    DiagramStatement.fromJson(sJson as Map<String, dynamic>),
              )
              .toList() ??
          [],
      connections:
          (json['connections'] as List<dynamic>?)
              ?.map(
                (cJson) =>
                    DiagramConnection.fromJson(cJson as Map<String, dynamic>),
              )
              .toList() ??
          [],
      feedback_correct: json['feedback_correct'] ?? 'Diagram correct!',
      feedback_incorrect:
          json['feedback_incorrect'] ??
          'Some connections are incorrect. Review the argument structure.',
    );
  }
}

// For interactive_find_the_argument_in_text (rf-noa-l5)
class FindArgumentSolution {
  final String conclusion;
  final List<String> premises;

  FindArgumentSolution({required this.conclusion, required this.premises});

  factory FindArgumentSolution.fromJson(Map<String, dynamic> json) {
    return FindArgumentSolution(
      conclusion: json['conclusion'] ?? '',
      premises: List<String>.from(json['premises'] ?? []),
    );
  }
  Map<String, dynamic> toJson() => {
    'conclusion': conclusion,
    'premises': premises,
  };
}

class InteractiveFindTheArgumentInTextElement extends InteractiveElement {
  final String prompt;
  final String text_passage;
  final FindArgumentSolution solution; // For validation
  final String? feedback_correct;
  final String? feedback_incorrect;

  InteractiveFindTheArgumentInTextElement({
    required this.prompt,
    required this.text_passage,
    required this.solution,
    this.feedback_correct,
    this.feedback_incorrect,
  }) : super(type: 'interactive_find_the_argument_in_text');

  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'prompt': prompt,
    'text_passage': text_passage,
    'solution': solution.toJson(),
    'feedback_correct': feedback_correct,
    'feedback_incorrect': feedback_incorrect,
  };

  factory InteractiveFindTheArgumentInTextElement.fromJson(
    Map<String, dynamic> json,
  ) {
    return InteractiveFindTheArgumentInTextElement(
      prompt: json['prompt'] ?? 'Identify the argument components.',
      text_passage: json['text_passage'] ?? '',
      solution: FindArgumentSolution.fromJson(
        json['solution'] as Map<String, dynamic>? ?? {},
      ),
      feedback_correct: json['feedback_correct'] ?? 'Excellent identification!',
      feedback_incorrect:
          json['feedback_incorrect'] ??
          'Review the passage for the main claim and its support.',
    );
  }
}

// For interactive_modus_ponens_tollens_builder (rf-dr-l2)
class ModusArgumentScenario {
  final String form_type; // "modus_ponens" or "modus_tollens"
  final String premise1_if;
  final String premise1_then;
  final String premise2_given;
  final String conclusion_to_find;
  final List<String>? distractors; // Optional distractor conclusions

  ModusArgumentScenario({
    required this.form_type,
    required this.premise1_if,
    required this.premise1_then,
    required this.premise2_given,
    required this.conclusion_to_find,
    this.distractors,
  });

  factory ModusArgumentScenario.fromJson(Map<String, dynamic> json) {
    return ModusArgumentScenario(
      form_type: json['form_type'] ?? 'modus_ponens',
      premise1_if: json['premise1_if'] ?? '',
      premise1_then: json['premise1_then'] ?? '',
      premise2_given: json['premise2_given'] ?? '',
      conclusion_to_find: json['conclusion_to_find'] ?? '',
      distractors:
          json['distractors'] != null
              ? List<String>.from(json['distractors'])
              : null,
    );
  }

  Map<String, dynamic> toJson() => {
    'form_type': form_type,
    'premise1_if': premise1_if,
    'premise1_then': premise1_then,
    'premise2_given': premise2_given,
    'conclusion_to_find': conclusion_to_find,
    'distractors': distractors,
  };
}

class InteractiveModusPonensTollensBuilderElement extends InteractiveElement {
  final String prompt;
  final List<ModusArgumentScenario> scenarios;
  final String? feedback_correct;
  final String? feedback_incorrect;

  InteractiveModusPonensTollensBuilderElement({
    required this.prompt,
    required this.scenarios,
    this.feedback_correct,
    this.feedback_incorrect,
  }) : super(type: 'interactive_modus_ponens_tollens_builder');

  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'prompt': prompt,
    'scenarios': scenarios.map((s) => s.toJson()).toList(),
    'feedback_correct': feedback_correct,
    'feedback_incorrect': feedback_incorrect,
  };

  factory InteractiveModusPonensTollensBuilderElement.fromJson(
    Map<String, dynamic> json,
  ) {
    return InteractiveModusPonensTollensBuilderElement(
      prompt: json['prompt'] ?? 'Complete the argument.',
      scenarios:
          (json['scenarios'] as List<dynamic>?)
              ?.map(
                (sJson) => ModusArgumentScenario.fromJson(
                  sJson as Map<String, dynamic>,
                ),
              )
              .toList() ??
          [],
      feedback_correct: json['feedback_correct'] ?? 'Correctly completed!',
      feedback_incorrect:
          json['feedback_incorrect'] ??
          'That doesn\'t follow the logical form. Try again.',
    );
  }
}

// For interactive_syllogism_evaluator (rf-dr-l3)
class SyllogismToEvaluate {
  final String premise1;
  final String premise2;
  final String conclusion;

  SyllogismToEvaluate({
    required this.premise1,
    required this.premise2,
    required this.conclusion,
  });

  factory SyllogismToEvaluate.fromJson(Map<String, dynamic> json) {
    return SyllogismToEvaluate(
      premise1: json['premise1'] ?? '',
      premise2: json['premise2'] ?? '',
      conclusion: json['conclusion'] ?? '',
    );
  }
  Map<String, dynamic> toJson() => {
    'premise1': premise1,
    'premise2': premise2,
    'conclusion': conclusion,
  };
}

class SyllogismSolution {
  final String major_term;
  final String minor_term;
  final String middle_term;
  final bool is_valid;

  SyllogismSolution({
    required this.major_term,
    required this.minor_term,
    required this.middle_term,
    required this.is_valid,
  });

  factory SyllogismSolution.fromJson(Map<String, dynamic> json) {
    return SyllogismSolution(
      major_term: json['major_term'] ?? '',
      minor_term: json['minor_term'] ?? '',
      middle_term: json['middle_term'] ?? '',
      is_valid: json['is_valid'] ?? false,
    );
  }
  Map<String, dynamic> toJson() => {
    'major_term': major_term,
    'minor_term': minor_term,
    'middle_term': middle_term,
    'is_valid': is_valid,
  };
}

class InteractiveSyllogismEvaluatorElement extends InteractiveElement {
  final String prompt;
  final SyllogismToEvaluate syllogism;
  final SyllogismSolution solution;
  final String? feedback_correct_terms;
  final String? feedback_incorrect_terms;
  final String? feedback_valid;
  final String? feedback_invalid;

  InteractiveSyllogismEvaluatorElement({
    required this.prompt,
    required this.syllogism,
    required this.solution,
    this.feedback_correct_terms,
    this.feedback_incorrect_terms,
    this.feedback_valid,
    this.feedback_invalid,
  }) : super(type: 'interactive_syllogism_evaluator');

  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'prompt': prompt,
    'syllogism': syllogism.toJson(),
    'solution': solution.toJson(),
    'feedback_correct_terms': feedback_correct_terms,
    'feedback_incorrect_terms': feedback_incorrect_terms,
    'feedback_valid': feedback_valid,
    'feedback_invalid': feedback_invalid,
  };

  factory InteractiveSyllogismEvaluatorElement.fromJson(
    Map<String, dynamic> json,
  ) {
    return InteractiveSyllogismEvaluatorElement(
      prompt: json['prompt'] ?? 'Evaluate the syllogism.',
      syllogism: SyllogismToEvaluate.fromJson(
        json['syllogism'] as Map<String, dynamic>? ?? {},
      ),
      solution: SyllogismSolution.fromJson(
        json['solution'] as Map<String, dynamic>? ?? {},
      ),
      feedback_correct_terms:
          json['feedback_correct_terms'] ?? 'Terms correctly identified!',
      feedback_incorrect_terms:
          json['feedback_incorrect_terms'] ?? 'Check your term identification.',
      feedback_valid:
          json['feedback_valid'] ?? 'Correct, this syllogism is valid.',
      feedback_invalid:
          json['feedback_invalid'] ?? 'Correct, this syllogism is invalid.',
    );
  }
}

// For interactive_validity_tester_counterexample (rf-dr-l4)
class ArgumentFormTemplates {
  final String premise1_template;
  final String premise2_template;
  final String conclusion_template;

  ArgumentFormTemplates({
    required this.premise1_template,
    required this.premise2_template,
    required this.conclusion_template,
  });

  factory ArgumentFormTemplates.fromJson(Map<String, dynamic> json) {
    return ArgumentFormTemplates(
      premise1_template: json['premise1_template'] ?? '',
      premise2_template: json['premise2_template'] ?? '',
      conclusion_template: json['conclusion_template'] ?? '',
    );
  }
  Map<String, dynamic> toJson() => {
    'premise1_template': premise1_template,
    'premise2_template': premise2_template,
    'conclusion_template': conclusion_template,
  };
}

class ExampleCounterexample {
  final String X_instance;
  final String Y_instance;
  final String? Z_instance; // For three-variable forms if needed
  final String feedback;

  ExampleCounterexample({
    required this.X_instance,
    required this.Y_instance,
    this.Z_instance,
    required this.feedback,
  });

  factory ExampleCounterexample.fromJson(Map<String, dynamic> json) {
    return ExampleCounterexample(
      X_instance: json['X_instance'] ?? '',
      Y_instance: json['Y_instance'] ?? '',
      Z_instance: json['Z_instance'],
      feedback: json['feedback'] ?? '',
    );
  }
  Map<String, dynamic> toJson() => {
    'X_instance': X_instance,
    'Y_instance': Y_instance,
    'Z_instance': Z_instance,
    'feedback': feedback,
  };
}

class InteractiveValidityTesterCounterexampleElement
    extends InteractiveElement {
  final String prompt;
  final ArgumentFormTemplates argument_form;
  final bool
  is_form_valid; // True if the form is actually valid, false if invalid (user tries to find counterexample)
  final ExampleCounterexample?
  example_counterexample; // Provided if form is invalid
  final String?
  feedback_if_valid_form; // e.g., "This form is valid, no counterexample exists."
  final String?
  feedback_if_user_finds_counterexample; // e.g., "Good job! That's a counterexample."
  final String?
  feedback_if_user_fails_to_find_counterexample_for_invalid; // e.g., "Keep trying, a counterexample exists."

  InteractiveValidityTesterCounterexampleElement({
    required this.prompt,
    required this.argument_form,
    required this.is_form_valid,
    this.example_counterexample,
    this.feedback_if_valid_form,
    this.feedback_if_user_finds_counterexample,
    this.feedback_if_user_fails_to_find_counterexample_for_invalid,
  }) : super(type: 'interactive_validity_tester_counterexample');

  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'prompt': prompt,
    'argument_form': argument_form.toJson(),
    'is_form_valid': is_form_valid,
    'example_counterexample': example_counterexample?.toJson(),
    'feedback_if_valid_form': feedback_if_valid_form,
    'feedback_if_user_finds_counterexample':
        feedback_if_user_finds_counterexample,
    'feedback_if_user_fails_to_find_counterexample_for_invalid':
        feedback_if_user_fails_to_find_counterexample_for_invalid,
  };

  factory InteractiveValidityTesterCounterexampleElement.fromJson(
    Map<String, dynamic> json,
  ) {
    return InteractiveValidityTesterCounterexampleElement(
      prompt: json['prompt'] ?? 'Test the validity of this argument form.',
      argument_form: ArgumentFormTemplates.fromJson(
        json['argument_form'] as Map<String, dynamic>? ?? {},
      ),
      is_form_valid: json['is_form_valid'] ?? true,
      example_counterexample:
          json['example_counterexample'] != null
              ? ExampleCounterexample.fromJson(
                json['example_counterexample'] as Map<String, dynamic>,
              )
              : null,
      feedback_if_valid_form:
          json['feedback_if_valid_form'] ??
          "This form is valid. You won't find a counterexample.",
      feedback_if_user_finds_counterexample:
          json['feedback_if_user_finds_counterexample'] ??
          "That's a valid counterexample, proving the form is invalid!",
      feedback_if_user_fails_to_find_counterexample_for_invalid:
          json['feedback_if_user_fails_to_find_counterexample_for_invalid'] ??
          "A counterexample does exist for this invalid form. Think of scenarios with true premises and a false conclusion.",
    );
  }
}

// For interactive_generalization_evaluator (rf-ir-l2)
class GeneralizationScenario {
  final String sample_description;
  final String population_description;
  final String property_observed;

  GeneralizationScenario({
    required this.sample_description,
    required this.population_description,
    required this.property_observed,
  });

  factory GeneralizationScenario.fromJson(Map<String, dynamic> json) {
    return GeneralizationScenario(
      sample_description: json['sample_description'] ?? '',
      population_description: json['population_description'] ?? '',
      property_observed: json['property_observed'] ?? '',
    );
  }

  Map<String, dynamic> toJson() => {
    'sample_description': sample_description,
    'population_description': population_description,
    'property_observed': property_observed,
  };
}

class InteractiveGeneralizationEvaluatorElement extends InteractiveElement {
  final String prompt;
  final GeneralizationScenario scenario;
  final List<String>
  factors_to_consider; // e.g., ["sample_size", "sample_bias"]
  final String ideal_strength_rating; // "weak", "moderate", "strong"
  final String explanation_for_rating;

  InteractiveGeneralizationEvaluatorElement({
    required this.prompt,
    required this.scenario,
    required this.factors_to_consider,
    required this.ideal_strength_rating,
    required this.explanation_for_rating,
  }) : super(type: 'interactive_generalization_evaluator');

  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'prompt': prompt,
    'scenario': scenario.toJson(),
    'factors_to_consider': factors_to_consider,
    'ideal_strength_rating': ideal_strength_rating,
    'explanation_for_rating': explanation_for_rating,
  };

  factory InteractiveGeneralizationEvaluatorElement.fromJson(
    Map<String, dynamic> json,
  ) {
    return InteractiveGeneralizationEvaluatorElement(
      prompt: json['prompt'] ?? 'Assess the generalization.',
      scenario: GeneralizationScenario.fromJson(
        json['scenario'] as Map<String, dynamic>? ?? {},
      ),
      factors_to_consider: List<String>.from(json['factors_to_consider'] ?? []),
      ideal_strength_rating: json['ideal_strength_rating'] ?? 'moderate',
      explanation_for_rating: json['explanation_for_rating'] ?? '',
    );
  }
}

// For interactive_analogy_strength_sorter (rf-ir-l2)
class AnalogyToSort {
  final String id;
  final String text;
  final int strength_score; // Higher is stronger, for correct ordering

  AnalogyToSort({
    required this.id,
    required this.text,
    required this.strength_score,
  });

  factory AnalogyToSort.fromJson(Map<String, dynamic> json) {
    return AnalogyToSort(
      id: json['id'] ?? 'an_unknown',
      text: json['text'] ?? '',
      strength_score: json['strength_score'] ?? 0,
    );
  }
  Map<String, dynamic> toJson() => {
    'id': id,
    'text': text,
    'strength_score': strength_score,
  };
}

class InteractiveAnalogyStrengthSorterElement extends InteractiveElement {
  final String prompt;
  final List<AnalogyToSort> analogies;
  final String? feedback_correct_order;
  final String? feedback_incorrect_order;

  InteractiveAnalogyStrengthSorterElement({
    required this.prompt,
    required this.analogies,
    this.feedback_correct_order,
    this.feedback_incorrect_order,
  }) : super(type: 'interactive_analogy_strength_sorter');

  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'prompt': prompt,
    'analogies': analogies.map((a) => a.toJson()).toList(),
    'feedback_correct_order': feedback_correct_order,
    'feedback_incorrect_order': feedback_incorrect_order,
  };

  factory InteractiveAnalogyStrengthSorterElement.fromJson(
    Map<String, dynamic> json,
  ) {
    return InteractiveAnalogyStrengthSorterElement(
      prompt: json['prompt'] ?? 'Sort the analogies by strength.',
      analogies:
          (json['analogies'] as List<dynamic>?)
              ?.map(
                (aJson) =>
                    AnalogyToSort.fromJson(aJson as Map<String, dynamic>),
              )
              .toList() ??
          [],
      feedback_correct_order:
          json['feedback_correct_order'] ?? 'Correctly ordered!',
      feedback_incorrect_order:
          json['feedback_incorrect_order'] ??
          'The order isn\'t quite right. Re-evaluate the similarities.',
    );
  }
}

// For interactive_causal_inference_builder (rf-ir-l2)
class InteractiveCausalInferenceBuilderElement extends InteractiveElement {
  final String prompt;
  final String observed_effect;
  final List<String> potential_causes_examples; // For UI hints or validation
  final String? feedback_on_submission;

  InteractiveCausalInferenceBuilderElement({
    required this.prompt,
    required this.observed_effect,
    required this.potential_causes_examples,
    this.feedback_on_submission,
  }) : super(type: 'interactive_causal_inference_builder');

  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'prompt': prompt,
    'observed_effect': observed_effect,
    'potential_causes_examples': potential_causes_examples,
    'feedback_on_submission': feedback_on_submission,
  };

  factory InteractiveCausalInferenceBuilderElement.fromJson(
    Map<String, dynamic> json,
  ) {
    return InteractiveCausalInferenceBuilderElement(
      prompt: json['prompt'] ?? 'Suggest possible causes.',
      observed_effect: json['observed_effect'] ?? '',
      potential_causes_examples: List<String>.from(
        json['potential_causes_examples'] ?? [],
      ),
      feedback_on_submission:
          json['feedback_on_submission'] ??
          'Good suggestions! Thinking about multiple causes is key.',
    );
  }
}

// For interactive_fallacy_matcher (rf-if-l1)
class FallacyMatchingItem {
  final String id;
  final String text; // Fallacy name or example

  FallacyMatchingItem({required this.id, required this.text});

  factory FallacyMatchingItem.fromJson(Map<String, dynamic> json) {
    return FallacyMatchingItem(
      id: json['id'] ?? 'fm_unknown',
      text: json['text'] ?? '',
    );
  }
  Map<String, dynamic> toJson() => {'id': id, 'text': text};
}

class FallacyDefinitionItem {
  final String id;
  final String text; // Definition

  FallacyDefinitionItem({required this.id, required this.text});

  factory FallacyDefinitionItem.fromJson(Map<String, dynamic> json) {
    return FallacyDefinitionItem(
      id: json['id'] ?? 'fd_unknown',
      text: json['text'] ?? '',
    );
  }
  Map<String, dynamic> toJson() => {'id': id, 'text': text};
}

class FallacyCorrectPair {
  final String item_id;
  final String definition_id;

  FallacyCorrectPair({required this.item_id, required this.definition_id});

  factory FallacyCorrectPair.fromJson(Map<String, dynamic> json) {
    return FallacyCorrectPair(
      item_id: json['item_id'] ?? '',
      definition_id: json['definition_id'] ?? '',
    );
  }
  Map<String, dynamic> toJson() => {
    'item_id': item_id,
    'definition_id': definition_id,
  };
}

class InteractiveFallacyMatcherElement extends InteractiveElement {
  final String prompt;
  final List<FallacyMatchingItem> items_to_match;
  final List<FallacyDefinitionItem> definitions;
  final List<FallacyCorrectPair> correct_pairs;
  final String? feedback_correct;
  final String? feedback_incorrect;

  InteractiveFallacyMatcherElement({
    required this.prompt,
    required this.items_to_match,
    required this.definitions,
    required this.correct_pairs,
    this.feedback_correct,
    this.feedback_incorrect,
  }) : super(type: 'interactive_fallacy_matcher');

  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'prompt': prompt,
    'items_to_match': items_to_match.map((i) => i.toJson()).toList(),
    'definitions': definitions.map((d) => d.toJson()).toList(),
    'correct_pairs': correct_pairs.map((p) => p.toJson()).toList(),
    'feedback_correct': feedback_correct,
    'feedback_incorrect': feedback_incorrect,
  };

  factory InteractiveFallacyMatcherElement.fromJson(Map<String, dynamic> json) {
    return InteractiveFallacyMatcherElement(
      prompt: json['prompt'] ?? 'Match the fallacies.',
      items_to_match:
          (json['items_to_match'] as List<dynamic>?)
              ?.map(
                (iJson) =>
                    FallacyMatchingItem.fromJson(iJson as Map<String, dynamic>),
              )
              .toList() ??
          [],
      definitions:
          (json['definitions'] as List<dynamic>?)
              ?.map(
                (dJson) => FallacyDefinitionItem.fromJson(
                  dJson as Map<String, dynamic>,
                ),
              )
              .toList() ??
          [],
      correct_pairs:
          (json['correct_pairs'] as List<dynamic>?)
              ?.map(
                (pJson) =>
                    FallacyCorrectPair.fromJson(pJson as Map<String, dynamic>),
              )
              .toList() ??
          [],
      feedback_correct: json['feedback_correct'] ?? 'All matched correctly!',
      feedback_incorrect:
          json['feedback_incorrect'] ??
          'Some matches are incorrect. Try again.',
    );
  }
}

// For interactive_spot_the_fallacy_in_dialogue (rf-if-l3, rf-if-l5)
class DialogueLine {
  final String speaker;
  final String line;

  DialogueLine({required this.speaker, required this.line});

  factory DialogueLine.fromJson(Map<String, dynamic> json) {
    return DialogueLine(
      speaker: json['speaker'] ?? 'Unknown',
      line: json['line'] ?? '',
    );
  }
  Map<String, dynamic> toJson() => {'speaker': speaker, 'line': line};
}

class InteractiveSpotTheFallacyInDialogueElement extends InteractiveElement {
  final String prompt;
  final List<DialogueLine> dialogue;
  final String fallacy_type; // The correct fallacy to identify
  final String explanation; // Explanation of why it's that fallacy
  final List<String>? other_fallacy_options; // For multiple choice if needed
  final String? feedback_correct;
  final String? feedback_incorrect;

  InteractiveSpotTheFallacyInDialogueElement({
    required this.prompt,
    required this.dialogue,
    required this.fallacy_type,
    required this.explanation,
    this.other_fallacy_options,
    this.feedback_correct,
    this.feedback_incorrect,
  }) : super(type: 'interactive_spot_the_fallacy_in_dialogue');

  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'prompt': prompt,
    'dialogue': dialogue.map((d) => d.toJson()).toList(),
    'fallacy_type': fallacy_type,
    'explanation': explanation,
    'other_fallacy_options': other_fallacy_options,
    'feedback_correct': feedback_correct,
    'feedback_incorrect': feedback_incorrect,
  };

  factory InteractiveSpotTheFallacyInDialogueElement.fromJson(
    Map<String, dynamic> json,
  ) {
    return InteractiveSpotTheFallacyInDialogueElement(
      prompt: json['prompt'] ?? 'Identify the fallacy.',
      dialogue:
          (json['dialogue'] as List<dynamic>?)
              ?.map(
                (dJson) => DialogueLine.fromJson(dJson as Map<String, dynamic>),
              )
              .toList() ??
          [],
      fallacy_type: json['fallacy_type'] ?? '',
      explanation: json['explanation'] ?? '',
      other_fallacy_options:
          json['other_fallacy_options'] != null
              ? List<String>.from(json['other_fallacy_options'])
              : null,
      feedback_correct: json['feedback_correct'] ?? 'Correctly identified!',
      feedback_incorrect:
          json['feedback_incorrect'] ??
          'That\'s not the primary fallacy here. Consider other options or the explanation.',
    );
  }
}

// For interactive_news_article_analysis (rf-rel-l1)
class NewsAnalysisPoint {
  final String point; // e.g., "Main Claim", "Evidence"
  final String example_answer;

  NewsAnalysisPoint({required this.point, required this.example_answer});

  factory NewsAnalysisPoint.fromJson(Map<String, dynamic> json) {
    return NewsAnalysisPoint(
      point: json['point'] ?? '',
      example_answer: json['example_answer'] ?? '',
    );
  }
  Map<String, dynamic> toJson() => {
    'point': point,
    'example_answer': example_answer,
  };
}

class InteractiveNewsArticleAnalysisElement extends InteractiveElement {
  final String prompt;
  final String news_snippet;
  final List<NewsAnalysisPoint>
  analysis_points; // User will fill these based on prompts
  final String? feedback_on_completion;

  InteractiveNewsArticleAnalysisElement({
    required this.prompt,
    required this.news_snippet,
    required this.analysis_points,
    this.feedback_on_completion,
  }) : super(type: 'interactive_news_article_analysis');

  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'prompt': prompt,
    'news_snippet': news_snippet,
    'analysis_points': analysis_points.map((p) => p.toJson()).toList(),
    'feedback_on_completion': feedback_on_completion,
  };

  factory InteractiveNewsArticleAnalysisElement.fromJson(
    Map<String, dynamic> json,
  ) {
    return InteractiveNewsArticleAnalysisElement(
      prompt: json['prompt'] ?? 'Analyze the news snippet.',
      news_snippet: json['news_snippet'] ?? '',
      analysis_points:
          (json['analysis_points'] as List<dynamic>?)
              ?.map(
                (pJson) =>
                    NewsAnalysisPoint.fromJson(pJson as Map<String, dynamic>),
              )
              .toList() ??
          [],
      feedback_on_completion:
          json['feedback_on_completion'] ?? 'Good critical analysis!',
    );
  }
}

// For interactive_advertisement_deconstructor (rf-rel-l2)
class AdvertisementDetails {
  final String? image_description;
  final String text;

  AdvertisementDetails({this.image_description, required this.text});

  factory AdvertisementDetails.fromJson(Map<String, dynamic> json) {
    return AdvertisementDetails(
      image_description: json['image_description'],
      text: json['text'] ?? '',
    );
  }
  Map<String, dynamic> toJson() => {
    'image_description': image_description,
    'text': text,
  };
}

class AdAnalysisPrompt {
  final String prompt;
  final String example_answer;

  AdAnalysisPrompt({required this.prompt, required this.example_answer});

  factory AdAnalysisPrompt.fromJson(Map<String, dynamic> json) {
    return AdAnalysisPrompt(
      prompt: json['prompt'] ?? '',
      example_answer: json['example_answer'] ?? '',
    );
  }
  Map<String, dynamic> toJson() => {
    'prompt': prompt,
    'example_answer': example_answer,
  };
}

class InteractiveAdvertisementDeconstructorElement extends InteractiveElement {
  final String prompt;
  final AdvertisementDetails advertisement;
  final List<AdAnalysisPrompt> analysis_prompts;
  final String? feedback_on_completion;

  InteractiveAdvertisementDeconstructorElement({
    required this.prompt,
    required this.advertisement,
    required this.analysis_prompts,
    this.feedback_on_completion,
  }) : super(type: 'interactive_advertisement_deconstructor');

  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'prompt': prompt,
    'advertisement': advertisement.toJson(),
    'analysis_prompts': analysis_prompts.map((p) => p.toJson()).toList(),
    'feedback_on_completion': feedback_on_completion,
  };

  factory InteractiveAdvertisementDeconstructorElement.fromJson(
    Map<String, dynamic> json,
  ) {
    return InteractiveAdvertisementDeconstructorElement(
      prompt: json['prompt'] ?? 'Deconstruct the advertisement.',
      advertisement: AdvertisementDetails.fromJson(
        json['advertisement'] as Map<String, dynamic>? ?? {},
      ),
      analysis_prompts:
          (json['analysis_prompts'] as List<dynamic>?)
              ?.map(
                (pJson) =>
                    AdAnalysisPrompt.fromJson(pJson as Map<String, dynamic>),
              )
              .toList() ??
          [],
      feedback_on_completion:
          json['feedback_on_completion'] ?? 'Great deconstruction!',
    );
  }
}

// For interactive_decision_making_scenario (rf-rel-l3)
class DecisionOption {
  final String name;
  final num price;
  final String battery;
  final String weight;
  final String? notes;

  DecisionOption({
    required this.name,
    required this.price,
    required this.battery,
    required this.weight,
    this.notes,
  });

  factory DecisionOption.fromJson(Map<String, dynamic> json) {
    return DecisionOption(
      name: json['name'] ?? '',
      price: json['price'] ?? 0,
      battery: json['battery'] ?? '',
      weight: json['weight'] ?? '',
      notes: json['notes'],
    );
  }
  Map<String, dynamic> toJson() => {
    'name': name,
    'price': price,
    'battery': battery,
    'weight': weight,
    'notes': notes,
  };
}

class DecisionScenarioDetails {
  final String decision_to_make;
  final List<String> criteria;
  final List<DecisionOption> options;

  DecisionScenarioDetails({
    required this.decision_to_make,
    required this.criteria,
    required this.options,
  });

  factory DecisionScenarioDetails.fromJson(Map<String, dynamic> json) {
    return DecisionScenarioDetails(
      decision_to_make: json['decision_to_make'] ?? '',
      criteria: List<String>.from(json['criteria'] ?? []),
      options:
          (json['options'] as List<dynamic>?)
              ?.map(
                (oJson) =>
                    DecisionOption.fromJson(oJson as Map<String, dynamic>),
              )
              .toList() ??
          [],
    );
  }
  Map<String, dynamic> toJson() => {
    'decision_to_make': decision_to_make,
    'criteria': criteria,
    'options': options.map((o) => o.toJson()).toList(),
  };
}

class InteractiveDecisionMakingScenarioElement extends InteractiveElement {
  final String prompt;
  final DecisionScenarioDetails scenario_details;
  final String user_task_prompt; // e.g., "Which would you choose and why?"
  final String? feedback_on_submission;

  InteractiveDecisionMakingScenarioElement({
    required this.prompt,
    required this.scenario_details,
    required this.user_task_prompt,
    this.feedback_on_submission,
  }) : super(type: 'interactive_decision_making_scenario');

  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'prompt': prompt,
    'scenario_details': scenario_details.toJson(),
    'user_task_prompt': user_task_prompt,
    'feedback_on_submission': feedback_on_submission,
  };

  factory InteractiveDecisionMakingScenarioElement.fromJson(
    Map<String, dynamic> json,
  ) {
    return InteractiveDecisionMakingScenarioElement(
      prompt: json['prompt'] ?? 'Make a decision.',
      scenario_details: DecisionScenarioDetails.fromJson(
        json['scenario_details'] as Map<String, dynamic>? ?? {},
      ),
      user_task_prompt: json['user_task_prompt'] ?? 'Explain your choice.',
      feedback_on_submission:
          json['feedback_on_submission'] ?? 'Thoughtful decision process!',
    );
  }
}

// For interactive_argument_constructor (rf-rel-l4)
class InteractiveArgumentConstructorElement extends InteractiveElement {
  final String prompt;
  final String topic;
  final String guidance;
  final String? feedback_on_submission;

  InteractiveArgumentConstructorElement({
    required this.prompt,
    required this.topic,
    required this.guidance,
    this.feedback_on_submission,
  }) : super(type: 'interactive_argument_constructor');

  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'prompt': prompt,
    'topic': topic,
    'guidance': guidance,
    'feedback_on_submission': feedback_on_submission,
  };

  factory InteractiveArgumentConstructorElement.fromJson(
    Map<String, dynamic> json,
  ) {
    return InteractiveArgumentConstructorElement(
      prompt: json['prompt'] ?? 'Construct an argument.',
      topic: json['topic'] ?? '',
      guidance: json['guidance'] ?? '',
      feedback_on_submission:
          json['feedback_on_submission'] ?? 'Well-structured argument!',
    );
  }
}

// --- Reasoning Course Interactive Element Classes (Frontiers) ---

// For interactive_symbol_connective_matcher (rfr-pl-l1)
class SymbolConnectiveItem {
  final String id;
  final String symbol_text;

  SymbolConnectiveItem({required this.id, required this.symbol_text});

  factory SymbolConnectiveItem.fromJson(Map<String, dynamic> json) {
    return SymbolConnectiveItem(
      id: json['id'] ?? 'sym_unknown',
      symbol_text: json['symbol_text'] ?? '',
    );
  }
  Map<String, dynamic> toJson() => {'id': id, 'symbol_text': symbol_text};
}

class MeaningConnectiveItem {
  final String id;
  final String name;
  final String meaning;

  MeaningConnectiveItem({
    required this.id,
    required this.name,
    required this.meaning,
  });

  factory MeaningConnectiveItem.fromJson(Map<String, dynamic> json) {
    return MeaningConnectiveItem(
      id: json['id'] ?? 'mean_unknown',
      name: json['name'] ?? '',
      meaning: json['meaning'] ?? '',
    );
  }
  Map<String, dynamic> toJson() => {'id': id, 'name': name, 'meaning': meaning};
}

class SymbolMeaningPair {
  final String symbol_id;
  final String meaning_id;

  SymbolMeaningPair({required this.symbol_id, required this.meaning_id});

  factory SymbolMeaningPair.fromJson(Map<String, dynamic> json) {
    return SymbolMeaningPair(
      symbol_id: json['symbol_id'] ?? '',
      meaning_id: json['meaning_id'] ?? '',
    );
  }
  Map<String, dynamic> toJson() => {
    'symbol_id': symbol_id,
    'meaning_id': meaning_id,
  };
}

class InteractiveSymbolConnectiveMatcherElement extends InteractiveElement {
  final String prompt;
  final List<SymbolConnectiveItem> symbols;
  final List<MeaningConnectiveItem> meanings;
  final List<SymbolMeaningPair> correct_pairs;

  InteractiveSymbolConnectiveMatcherElement({
    required this.prompt,
    required this.symbols,
    required this.meanings,
    required this.correct_pairs,
  }) : super(type: 'interactive_symbol_connective_matcher');

  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'prompt': prompt,
    'symbols': symbols.map((s) => s.toJson()).toList(),
    'meanings': meanings.map((m) => m.toJson()).toList(),
    'correct_pairs': correct_pairs.map((p) => p.toJson()).toList(),
  };

  factory InteractiveSymbolConnectiveMatcherElement.fromJson(
    Map<String, dynamic> json,
  ) {
    return InteractiveSymbolConnectiveMatcherElement(
      prompt: json['prompt'] ?? 'Match symbols to meanings.',
      symbols:
          (json['symbols'] as List<dynamic>?)
              ?.map(
                (sJson) => SymbolConnectiveItem.fromJson(
                  sJson as Map<String, dynamic>,
                ),
              )
              .toList() ??
          [],
      meanings:
          (json['meanings'] as List<dynamic>?)
              ?.map(
                (mJson) => MeaningConnectiveItem.fromJson(
                  mJson as Map<String, dynamic>,
                ),
              )
              .toList() ??
          [],
      correct_pairs:
          (json['correct_pairs'] as List<dynamic>?)
              ?.map(
                (pJson) =>
                    SymbolMeaningPair.fromJson(pJson as Map<String, dynamic>),
              )
              .toList() ??
          [],
    );
  }
}

// For interactive_truth_table_generator (rfr-pl-l2)
class InteractiveTruthTableGeneratorElement extends InteractiveElement {
  final String prompt;
  final String proposition_string;
  final List<String> atomic_propositions;
  final List<bool> solution_truth_values; // Final column values

  InteractiveTruthTableGeneratorElement({
    required this.prompt,
    required this.proposition_string,
    required this.atomic_propositions,
    required this.solution_truth_values,
  }) : super(type: 'interactive_truth_table_generator');

  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'prompt': prompt,
    'proposition_string': proposition_string,
    'atomic_propositions': atomic_propositions,
    'solution_truth_values': solution_truth_values,
  };

  factory InteractiveTruthTableGeneratorElement.fromJson(
    Map<String, dynamic> json,
  ) {
    return InteractiveTruthTableGeneratorElement(
      prompt: json['prompt'] ?? 'Complete the truth table.',
      proposition_string: json['proposition_string'] ?? '',
      atomic_propositions: List<String>.from(json['atomic_propositions'] ?? []),
      solution_truth_values: List<bool>.from(
        json['solution_truth_values'] ?? [],
      ),
    );
  }
}

// For interactive_equivalence_checker (rfr-pl-l3)
class InteractiveEquivalenceCheckerElement extends InteractiveElement {
  final String prompt;
  final String proposition1_string;
  final String proposition2_string;
  final bool are_equivalent;
  final String explanation;

  InteractiveEquivalenceCheckerElement({
    required this.prompt,
    required this.proposition1_string,
    required this.proposition2_string,
    required this.are_equivalent,
    required this.explanation,
  }) : super(type: 'interactive_equivalence_checker');

  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'prompt': prompt,
    'proposition1_string': proposition1_string,
    'proposition2_string': proposition2_string,
    'are_equivalent': are_equivalent,
    'explanation': explanation,
  };

  factory InteractiveEquivalenceCheckerElement.fromJson(
    Map<String, dynamic> json,
  ) {
    return InteractiveEquivalenceCheckerElement(
      prompt: json['prompt'] ?? 'Check for logical equivalence.',
      proposition1_string: json['proposition1_string'] ?? '',
      proposition2_string: json['proposition2_string'] ?? '',
      are_equivalent: json['are_equivalent'] ?? false,
      explanation: json['explanation'] ?? '',
    );
  }
}

// For interactive_rule_of_inference_applier (rfr-pl-l4)
class InferenceScenario {
  final List<String> premises;
  final String rule;
  final String correct_conclusion;
  final List<String>? distractors;

  InferenceScenario({
    required this.premises,
    required this.rule,
    required this.correct_conclusion,
    this.distractors,
  });

  factory InferenceScenario.fromJson(Map<String, dynamic> json) {
    return InferenceScenario(
      premises: List<String>.from(json['premises'] ?? []),
      rule: json['rule'] ?? '',
      correct_conclusion: json['correct_conclusion'] ?? '',
      distractors:
          json['distractors'] != null
              ? List<String>.from(json['distractors'])
              : null,
    );
  }
  Map<String, dynamic> toJson() => {
    'premises': premises,
    'rule': rule,
    'correct_conclusion': correct_conclusion,
    'distractors': distractors,
  };
}

class InteractiveRuleOfInferenceApplierElement extends InteractiveElement {
  final String prompt;
  final List<InferenceScenario> scenarios;

  InteractiveRuleOfInferenceApplierElement({
    required this.prompt,
    required this.scenarios,
  }) : super(type: 'interactive_rule_of_inference_applier');

  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'prompt': prompt,
    'scenarios': scenarios.map((s) => s.toJson()).toList(),
  };

  factory InteractiveRuleOfInferenceApplierElement.fromJson(
    Map<String, dynamic> json,
  ) {
    return InteractiveRuleOfInferenceApplierElement(
      prompt: json['prompt'] ?? 'Apply the rule of inference.',
      scenarios:
          (json['scenarios'] as List<dynamic>?)
              ?.map(
                (sJson) =>
                    InferenceScenario.fromJson(sJson as Map<String, dynamic>),
              )
              .toList() ??
          [],
    );
  }
}

// For interactive_formal_proof_builder (rfr-pl-l5)
class ProofSolutionStep {
  final int line;
  final String statement;
  final String justification; // e.g., "Modus Ponens (1, 2)"

  ProofSolutionStep({
    required this.line,
    required this.statement,
    required this.justification,
  });

  factory ProofSolutionStep.fromJson(Map<String, dynamic> json) {
    return ProofSolutionStep(
      line: json['line'] ?? 0,
      statement: json['statement'] ?? '',
      justification: json['justification'] ?? '',
    );
  }
  Map<String, dynamic> toJson() => {
    'line': line,
    'statement': statement,
    'justification': justification,
  };
}

class InteractiveFormalProofBuilderElement extends InteractiveElement {
  final String prompt;
  final List<String> premises_list;
  final String target_conclusion;
  final List<String> available_rules;
  final List<ProofSolutionStep> solution_steps; // For validation and guidance

  InteractiveFormalProofBuilderElement({
    required this.prompt,
    required this.premises_list,
    required this.target_conclusion,
    required this.available_rules,
    required this.solution_steps,
  }) : super(type: 'interactive_formal_proof_builder');

  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'prompt': prompt,
    'premises_list': premises_list,
    'target_conclusion': target_conclusion,
    'available_rules': available_rules,
    'solution_steps': solution_steps.map((s) => s.toJson()).toList(),
  };

  factory InteractiveFormalProofBuilderElement.fromJson(
    Map<String, dynamic> json,
  ) {
    return InteractiveFormalProofBuilderElement(
      prompt: json['prompt'] ?? 'Construct the proof.',
      premises_list: List<String>.from(json['premises_list'] ?? []),
      target_conclusion: json['target_conclusion'] ?? '',
      available_rules: List<String>.from(json['available_rules'] ?? []),
      solution_steps:
          (json['solution_steps'] as List<dynamic>?)
              ?.map(
                (sJson) =>
                    ProofSolutionStep.fromJson(sJson as Map<String, dynamic>),
              )
              .toList() ??
          [],
    );
  }
}

// --- Calculus Course Interactive Element Classes ---

// For interactive_function_plotter_limit_explorer (iol-l1-s2)
class InteractiveFunctionPlotterLimitExplorerElement
    extends InteractiveElement {
  final String function_expression;
  final num target_x;
  final num x_min;
  final num x_max;
  final num initial_x_slider;
  final String? x_axis_label;
  final String? y_axis_label;
  final String? action_button_text;

  InteractiveFunctionPlotterLimitExplorerElement({
    required this.function_expression,
    required this.target_x,
    required this.x_min,
    required this.x_max,
    required this.initial_x_slider,
    this.x_axis_label,
    this.y_axis_label,
    this.action_button_text,
  }) : super(type: 'interactive_function_plotter_limit_explorer');

  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'function_expression': function_expression,
    'target_x': target_x,
    'x_min': x_min,
    'x_max': x_max,
    'initial_x_slider': initial_x_slider,
    'x_axis_label': x_axis_label,
    'y_axis_label': y_axis_label,
    'action_button_text': action_button_text,
  };

  factory InteractiveFunctionPlotterLimitExplorerElement.fromJson(
    Map<String, dynamic> json,
  ) {
    return InteractiveFunctionPlotterLimitExplorerElement(
      function_expression: json['function_expression'] ?? 'x',
      target_x: json['target_x'] ?? 0,
      x_min: json['x_min'] ?? -5,
      x_max: json['x_max'] ?? 5,
      initial_x_slider: json['initial_x_slider'] ?? 0,
      x_axis_label: json['x_axis_label'] ?? 'x',
      y_axis_label: json['y_axis_label'] ?? 'f(x)',
      action_button_text: json['action_button_text'] ?? 'Explore',
    );
  }
}

// For interactive_epsilon_delta_explorer (iol-l2-s2)
class InteractiveEpsilonDeltaExplorerElement extends InteractiveElement {
  final String function_expression;
  final num c_value; // The point x approaches
  final num L_value; // The limit value
  final num initial_epsilon;
  final num? epsilon_min;
  final num? epsilon_max;
  final num? epsilon_step;
  final num
  x_min_graph; // Renamed from x_min to avoid conflict with a potential delta_x_min
  final num x_max_graph; // Renamed from x_max
  final String? prompt_epsilon;
  final String? prompt_delta;
  final String? feedback_success;
  final String?
  feedback_failure_template; // e.g. "With this delta, not all f(x) are within epsilon."
  final String? action_button_text;

  InteractiveEpsilonDeltaExplorerElement({
    required this.function_expression,
    required this.c_value,
    required this.L_value,
    required this.initial_epsilon,
    this.epsilon_min,
    this.epsilon_max,
    this.epsilon_step,
    required this.x_min_graph,
    required this.x_max_graph,
    this.prompt_epsilon,
    this.prompt_delta,
    this.feedback_success,
    this.feedback_failure_template,
    this.action_button_text,
  }) : super(type: 'interactive_epsilon_delta_explorer');

  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'function_expression': function_expression,
    'c_value': c_value,
    'L_value': L_value,
    'initial_epsilon': initial_epsilon,
    'epsilon_min': epsilon_min,
    'epsilon_max': epsilon_max,
    'epsilon_step': epsilon_step,
    'x_min_graph': x_min_graph,
    'x_max_graph': x_max_graph,
    'prompt_epsilon': prompt_epsilon,
    'prompt_delta': prompt_delta,
    'feedback_success': feedback_success,
    'feedback_failure_template': feedback_failure_template,
    'action_button_text': action_button_text,
  };

  factory InteractiveEpsilonDeltaExplorerElement.fromJson(
    Map<String, dynamic> json,
  ) {
    return InteractiveEpsilonDeltaExplorerElement(
      function_expression: json['function_expression'] ?? 'x',
      c_value: json['c_value'] ?? 0,
      L_value: json['L_value'] ?? 0,
      initial_epsilon: json['initial_epsilon'] ?? 0.5,
      epsilon_min: json['epsilon_min'] ?? 0.01,
      epsilon_max: json['epsilon_max'] ?? 1.0,
      epsilon_step: json['epsilon_step'] ?? 0.01,
      x_min_graph:
          json['x_min_graph'] ??
          json['x_min'] ??
          -5, // Added fallback for old 'x_min'
      x_max_graph:
          json['x_max_graph'] ??
          json['x_max'] ??
          5, // Added fallback for old 'x_max'
      prompt_epsilon: json['prompt_epsilon'] ?? 'Set Epsilon (ε):',
      prompt_delta: json['prompt_delta'] ?? 'Find Delta (δ):',
      feedback_success:
          json['feedback_success'] ?? 'Success! You found a suitable delta.',
      feedback_failure_template:
          json['feedback_failure_template'] ??
          'Try adjusting delta. Not all f(x) values are within the epsilon band.',
      action_button_text: json['action_button_text'] ?? 'Check Delta',
    );
  }
}

// For interactive_secant_line_explorer (dmc-l1-s2)
class InteractiveSecantLineExplorerElement extends InteractiveElement {
  final String function_expression;
  final num x_min;
  final num x_max;
  final num initial_a;
  final num initial_b;
  final bool? show_slope_calculation;
  final String? x_axis_label;
  final String? y_axis_label;
  final String? action_button_text;

  InteractiveSecantLineExplorerElement({
    required this.function_expression,
    required this.x_min,
    required this.x_max,
    required this.initial_a,
    required this.initial_b,
    this.show_slope_calculation,
    this.x_axis_label,
    this.y_axis_label,
    this.action_button_text,
  }) : super(type: 'interactive_secant_line_explorer');

  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'function_expression': function_expression,
    'x_min': x_min,
    'x_max': x_max,
    'initial_a': initial_a,
    'initial_b': initial_b,
    'show_slope_calculation': show_slope_calculation,
    'x_axis_label': x_axis_label,
    'y_axis_label': y_axis_label,
    'action_button_text': action_button_text,
  };

  factory InteractiveSecantLineExplorerElement.fromJson(
    Map<String, dynamic> json,
  ) {
    return InteractiveSecantLineExplorerElement(
      function_expression: json['function_expression'] ?? 'x',
      x_min: json['x_min'] ?? -5,
      x_max: json['x_max'] ?? 5,
      initial_a: json['initial_a'] ?? -1,
      initial_b: json['initial_b'] ?? 1,
      show_slope_calculation: json['show_slope_calculation'] ?? false,
      x_axis_label: json['x_axis_label'] ?? 'x',
      y_axis_label: json['y_axis_label'] ?? 'f(x)',
      action_button_text: json['action_button_text'] ?? 'Update Secant',
    );
  }
}

// For interactive_secant_to_tangent_visualizer (dmc-l2-s2)
class InteractiveSecantToTangentVisualizerElement extends InteractiveElement {
  final String function_expression;
  final num x_min;
  final num x_max;
  final num fixed_point_x;
  final String? x_axis_label;
  final String? y_axis_label;
  final String? moving_point_label;
  final String? fixed_point_label;
  final String? action_button_text;

  InteractiveSecantToTangentVisualizerElement({
    required this.function_expression,
    required this.x_min,
    required this.x_max,
    required this.fixed_point_x,
    this.x_axis_label,
    this.y_axis_label,
    this.moving_point_label,
    this.fixed_point_label,
    this.action_button_text,
  }) : super(type: 'interactive_secant_to_tangent_visualizer');

  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'function_expression': function_expression,
    'x_min': x_min,
    'x_max': x_max,
    'fixed_point_x': fixed_point_x,
    'x_axis_label': x_axis_label,
    'y_axis_label': y_axis_label,
    'moving_point_label': moving_point_label,
    'fixed_point_label': fixed_point_label,
    'action_button_text': action_button_text,
  };

  factory InteractiveSecantToTangentVisualizerElement.fromJson(
    Map<String, dynamic> json,
  ) {
    return InteractiveSecantToTangentVisualizerElement(
      function_expression: json['function_expression'] ?? 'x',
      x_min: json['x_min'] ?? -5,
      x_max: json['x_max'] ?? 5,
      fixed_point_x: json['fixed_point_x'] ?? 0,
      x_axis_label: json['x_axis_label'] ?? 'x',
      y_axis_label: json['y_axis_label'] ?? 'f(x)',
      moving_point_label: json['moving_point_label'] ?? 'B',
      fixed_point_label: json['fixed_point_label'] ?? 'A',
      action_button_text: json['action_button_text'] ?? 'Animate',
    );
  }
}

// For interactive_derivative_definition_explorer (dmc-l3-s2)
class InteractiveDerivativeDefinitionExplorerElement
    extends InteractiveElement {
  final String function_expression;
  final num x_slider_min;
  final num x_slider_max;
  final num initial_x;
  final num h_slider_min;
  final num h_slider_max;
  final num initial_h;
  final bool? show_derivative_value; // Shows the actual f'(x)
  final String? x_axis_label;
  final String? y_axis_label;
  final String? action_button_text;

  InteractiveDerivativeDefinitionExplorerElement({
    required this.function_expression,
    required this.x_slider_min,
    required this.x_slider_max,
    required this.initial_x,
    required this.h_slider_min,
    required this.h_slider_max,
    required this.initial_h,
    this.show_derivative_value,
    this.x_axis_label,
    this.y_axis_label,
    this.action_button_text,
  }) : super(type: 'interactive_derivative_definition_explorer');

  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'function_expression': function_expression,
    'x_slider_min': x_slider_min,
    'x_slider_max': x_slider_max,
    'initial_x': initial_x,
    'h_slider_min': h_slider_min,
    'h_slider_max': h_slider_max,
    'initial_h': initial_h,
    'show_derivative_value': show_derivative_value,
    'x_axis_label': x_axis_label,
    'y_axis_label': y_axis_label,
    'action_button_text': action_button_text,
  };

  factory InteractiveDerivativeDefinitionExplorerElement.fromJson(
    Map<String, dynamic> json,
  ) {
    return InteractiveDerivativeDefinitionExplorerElement(
      function_expression: json['function_expression'] ?? 'x',
      x_slider_min: json['x_slider_min'] ?? -3,
      x_slider_max: json['x_slider_max'] ?? 3,
      initial_x: json['initial_x'] ?? 0,
      h_slider_min: json['h_slider_min'] ?? 0.01, // h should not be 0
      h_slider_max: json['h_slider_max'] ?? 1,
      initial_h: json['initial_h'] ?? 0.5,
      show_derivative_value: json['show_derivative_value'] ?? false,
      x_axis_label: json['x_axis_label'] ?? 'x',
      y_axis_label: json['y_axis_label'] ?? 'f(x)',
      action_button_text: json['action_button_text'] ?? 'Update',
    );
  }
}

// For interactive_function_derivative_visualizer (aod-l1-s2)
class InteractiveFunctionDerivativeVisualizerElement
    extends InteractiveElement {
  final String function_expression;
  final String derivative_expression;
  final num x_min;
  final num x_max;
  final String? x_axis_label;
  final String? y_axis_label;
  final String? function_label;
  final String? derivative_label;

  InteractiveFunctionDerivativeVisualizerElement({
    required this.function_expression,
    required this.derivative_expression,
    required this.x_min,
    required this.x_max,
    this.x_axis_label,
    this.y_axis_label,
    this.function_label,
    this.derivative_label,
  }) : super(type: 'interactive_function_derivative_visualizer');

  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'function_expression': function_expression,
    'derivative_expression': derivative_expression,
    'x_min': x_min,
    'x_max': x_max,
    'x_axis_label': x_axis_label,
    'y_axis_label': y_axis_label,
    'function_label': function_label,
    'derivative_label': derivative_label,
  };

  factory InteractiveFunctionDerivativeVisualizerElement.fromJson(
    Map<String, dynamic> json,
  ) {
    return InteractiveFunctionDerivativeVisualizerElement(
      function_expression: json['function_expression'] ?? 'x',
      derivative_expression: json['derivative_expression'] ?? '1',
      x_min: json['x_min'] ?? -5,
      x_max: json['x_max'] ?? 5,
      x_axis_label: json['x_axis_label'] ?? 'x',
      y_axis_label: json['y_axis_label'] ?? 'y',
      function_label: json['function_label'] ?? 'f(x)',
      derivative_label: json['derivative_label'] ?? "f'(x)",
    );
  }
}

// For interactive_first_derivative_test_explorer (aod-l2-s2)
class InteractiveFirstDerivativeTestExplorerElement extends InteractiveElement {
  final String function_expression;
  final List<num> critical_points;
  final num x_min;
  final num x_max;
  // TODO: Add fields for user interaction to select intervals and test sign of f'(x)
  // For now, it might just be a visualizer showing where f'(x) is +, -, or 0

  InteractiveFirstDerivativeTestExplorerElement({
    required this.function_expression,
    required this.critical_points,
    required this.x_min,
    required this.x_max,
  }) : super(type: 'interactive_first_derivative_test_explorer');

  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'function_expression': function_expression,
    'critical_points': critical_points,
    'x_min': x_min,
    'x_max': x_max,
  };

  factory InteractiveFirstDerivativeTestExplorerElement.fromJson(
    Map<String, dynamic> json,
  ) {
    return InteractiveFirstDerivativeTestExplorerElement(
      function_expression: json['function_expression'] ?? 'x',
      critical_points: List<num>.from(json['critical_points'] ?? []),
      x_min: json['x_min'] ?? -5,
      x_max: json['x_max'] ?? 5,
    );
  }
}

// For interactive_concavity_inflection_explorer (aod-l3-s2)
class InteractiveConcavityInflectionExplorerElement extends InteractiveElement {
  final String function_expression;
  final String second_derivative_expression;
  final num x_min;
  final num x_max;
  // TODO: Add fields for user interaction to identify inflection points / concavity intervals

  InteractiveConcavityInflectionExplorerElement({
    required this.function_expression,
    required this.second_derivative_expression,
    required this.x_min,
    required this.x_max,
  }) : super(type: 'interactive_concavity_inflection_explorer');

  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'function_expression': function_expression,
    'second_derivative_expression': second_derivative_expression,
    'x_min': x_min,
    'x_max': x_max,
  };

  factory InteractiveConcavityInflectionExplorerElement.fromJson(
    Map<String, dynamic> json,
  ) {
    return InteractiveConcavityInflectionExplorerElement(
      function_expression: json['function_expression'] ?? 'x',
      second_derivative_expression: json['second_derivative_expression'] ?? '0',
      x_min: json['x_min'] ?? -5,
      x_max: json['x_max'] ?? 5,
    );
  }
}

// For interactive_riemann_sum_explorer (iac-l1-s2)
class InteractiveRiemannSumExplorerElement extends InteractiveElement {
  final String function_expression;
  final num x_min_bound;
  final num x_max_bound;
  final int initial_n_rectangles;
  final int? max_n_rectangles;
  final List<String>?
  sum_types; // e.g., ["left", "right", "midpoint", "trapezoid"]
  final String? x_axis_label;
  final String? y_axis_label;
  final String? action_button_text;

  InteractiveRiemannSumExplorerElement({
    required this.function_expression,
    required this.x_min_bound,
    required this.x_max_bound,
    required this.initial_n_rectangles,
    this.max_n_rectangles,
    this.sum_types,
    this.x_axis_label,
    this.y_axis_label,
    this.action_button_text,
  }) : super(type: 'interactive_riemann_sum_explorer');

  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'function_expression': function_expression,
    'x_min_bound': x_min_bound,
    'x_max_bound': x_max_bound,
    'initial_n_rectangles': initial_n_rectangles,
    'max_n_rectangles': max_n_rectangles,
    'sum_types': sum_types,
    'x_axis_label': x_axis_label,
    'y_axis_label': y_axis_label,
    'action_button_text': action_button_text,
  };

  factory InteractiveRiemannSumExplorerElement.fromJson(
    Map<String, dynamic> json,
  ) {
    return InteractiveRiemannSumExplorerElement(
      function_expression: json['function_expression'] ?? 'x',
      x_min_bound: json['x_min_bound'] ?? 0,
      x_max_bound: json['x_max_bound'] ?? 1,
      initial_n_rectangles: json['initial_n_rectangles'] ?? 4,
      max_n_rectangles: json['max_n_rectangles'] ?? 100,
      sum_types:
          json['sum_types'] != null
              ? List<String>.from(json['sum_types'])
              : ['left', 'right', 'midpoint'],
      x_axis_label: json['x_axis_label'] ?? 'x',
      y_axis_label: json['y_axis_label'] ?? 'f(x)',
      action_button_text: json['action_button_text'] ?? 'Update Sum',
    );
  }
}

// For interactive_area_between_curves_explorer (aoi-l1-s2)
class InteractiveAreaBetweenCurvesExplorerElement extends InteractiveElement {
  final String function1_expression;
  final String function2_expression;
  final num x_min_bound;
  final num x_max_bound;
  final String? x_axis_label;
  final String? y_axis_label;
  final String? action_button_text;
  final bool? show_intersection_points;

  InteractiveAreaBetweenCurvesExplorerElement({
    required this.function1_expression,
    required this.function2_expression,
    required this.x_min_bound,
    required this.x_max_bound,
    this.x_axis_label,
    this.y_axis_label,
    this.action_button_text,
    this.show_intersection_points,
  }) : super(type: 'interactive_area_between_curves_explorer');

  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'function1_expression': function1_expression,
    'function2_expression': function2_expression,
    'x_min_bound': x_min_bound,
    'x_max_bound': x_max_bound,
    'x_axis_label': x_axis_label,
    'y_axis_label': y_axis_label,
    'action_button_text': action_button_text,
    'show_intersection_points': show_intersection_points,
  };

  factory InteractiveAreaBetweenCurvesExplorerElement.fromJson(
    Map<String, dynamic> json,
  ) {
    return InteractiveAreaBetweenCurvesExplorerElement(
      function1_expression: json['function1_expression'] ?? 'x',
      function2_expression: json['function2_expression'] ?? '0',
      x_min_bound: json['x_min_bound'] ?? -5,
      x_max_bound: json['x_max_bound'] ?? 5,
      x_axis_label: json['x_axis_label'] ?? 'x',
      y_axis_label: json['y_axis_label'] ?? 'y',
      action_button_text: json['action_button_text'] ?? 'Show Area',
      show_intersection_points: json['show_intersection_points'] ?? true,
    );
  }
}

// For interactive_volume_disk_method_explorer (aoi-l2-s2)
class InteractiveVolumeDiskMethodExplorerElement extends InteractiveElement {
  final String function_expression;
  final num x_min_bound;
  final num x_max_bound;
  final String axis_of_rotation; // "x-axis" or "y-axis"
  final int? num_disks_slider_max;
  final String? x_axis_label;
  final String? y_axis_label;
  final String? action_button_text;

  InteractiveVolumeDiskMethodExplorerElement({
    required this.function_expression,
    required this.x_min_bound,
    required this.x_max_bound,
    required this.axis_of_rotation,
    this.num_disks_slider_max,
    this.x_axis_label,
    this.y_axis_label,
    this.action_button_text,
  }) : super(type: 'interactive_volume_disk_method_explorer');

  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'function_expression': function_expression,
    'x_min_bound': x_min_bound,
    'x_max_bound': x_max_bound,
    'axis_of_rotation': axis_of_rotation,
    'num_disks_slider_max': num_disks_slider_max,
    'x_axis_label': x_axis_label,
    'y_axis_label': y_axis_label,
    'action_button_text': action_button_text,
  };

  factory InteractiveVolumeDiskMethodExplorerElement.fromJson(
    Map<String, dynamic> json,
  ) {
    return InteractiveVolumeDiskMethodExplorerElement(
      function_expression: json['function_expression'] ?? 'x',
      x_min_bound: json['x_min_bound'] ?? 0,
      x_max_bound: json['x_max_bound'] ?? 1,
      axis_of_rotation: json['axis_of_rotation'] ?? 'x-axis',
      num_disks_slider_max: json['num_disks_slider_max'] ?? 50,
      x_axis_label: json['x_axis_label'] ?? 'x',
      y_axis_label: json['y_axis_label'] ?? 'y',
      action_button_text: json['action_button_text'] ?? 'Visualize Volume',
    );
  }
}

// For interactive_work_calculation_explorer (aoi-l3-s1)
class InteractiveWorkCalculationExplorerElement extends InteractiveElement {
  final String force_function_expression; // F(x)
  final num x_min_bound; // Start position a
  final num x_max_bound; // End position b
  final String? distance_unit;
  final String? force_unit;
  final String? work_unit;
  final String? x_axis_label;
  final String? y_axis_label_force;
  final String? action_button_text;
  final bool? show_area_under_force_curve;

  InteractiveWorkCalculationExplorerElement({
    required this.force_function_expression,
    required this.x_min_bound,
    required this.x_max_bound,
    this.distance_unit,
    this.force_unit,
    this.work_unit,
    this.x_axis_label,
    this.y_axis_label_force,
    this.action_button_text,
    this.show_area_under_force_curve,
  }) : super(type: 'interactive_work_calculation_explorer');

  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'force_function_expression': force_function_expression,
    'x_min_bound': x_min_bound,
    'x_max_bound': x_max_bound,
    'distance_unit': distance_unit,
    'force_unit': force_unit,
    'work_unit': work_unit,
    'x_axis_label': x_axis_label,
    'y_axis_label_force': y_axis_label_force,
    'action_button_text': action_button_text,
    'show_area_under_force_curve': show_area_under_force_curve,
  };

  factory InteractiveWorkCalculationExplorerElement.fromJson(
    Map<String, dynamic> json,
  ) {
    return InteractiveWorkCalculationExplorerElement(
      force_function_expression: json['force_function_expression'] ?? '1',
      x_min_bound: json['x_min_bound'] ?? 0,
      x_max_bound: json['x_max_bound'] ?? 1,
      distance_unit: json['distance_unit'] ?? 'm',
      force_unit: json['force_unit'] ?? 'N',
      work_unit: json['work_unit'] ?? 'J',
      x_axis_label: json['x_axis_label'] ?? 'Distance',
      y_axis_label_force: json['y_axis_label_force'] ?? 'Force',
      action_button_text: json['action_button_text'] ?? 'Calculate Work',
      show_area_under_force_curve: json['show_area_under_force_curve'] ?? true,
    );
  }
}

// For interactive_system_of_equations_grapher (ear-l5)
class InteractiveSystemOfEquationsGrapherElement extends InteractiveElement {
  final String equation1_string;
  final String equation2_string;
  final String prompt_graph_lines;
  final String prompt_find_intersection;
  final PointToGraph correct_intersection_point;
  final String feedback_correct_intersection;
  final String? feedback_incorrect_intersection;
  final String? action_button_text;
  final num? x_min;
  final num? x_max;
  final num? y_min;
  final num? y_max;

  InteractiveSystemOfEquationsGrapherElement({
    required this.equation1_string,
    required this.equation2_string,
    required this.prompt_graph_lines,
    required this.prompt_find_intersection,
    required this.correct_intersection_point,
    required this.feedback_correct_intersection,
    this.feedback_incorrect_intersection,
    this.action_button_text,
    this.x_min,
    this.x_max,
    this.y_min,
    this.y_max,
  }) : super(type: 'interactive_system_of_equations_grapher');

  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'equation1_string': equation1_string,
    'equation2_string': equation2_string,
    'prompt_graph_lines': prompt_graph_lines,
    'prompt_find_intersection': prompt_find_intersection,
    'correct_intersection_point': correct_intersection_point.toJson(),
    'feedback_correct_intersection': feedback_correct_intersection,
    'feedback_incorrect_intersection': feedback_incorrect_intersection,
    'action_button_text': action_button_text,
    'x_min': x_min,
    'x_max': x_max,
    'y_min': y_min,
    'y_max': y_max,
  };

  factory InteractiveSystemOfEquationsGrapherElement.fromJson(
    Map<String, dynamic> json,
  ) {
    return InteractiveSystemOfEquationsGrapherElement(
      equation1_string: json['equation1_string'] ?? 'y = x',
      equation2_string: json['equation2_string'] ?? 'y = -x',
      prompt_graph_lines: json['prompt_graph_lines'] ?? 'Graph both lines.',
      prompt_find_intersection:
          json['prompt_find_intersection'] ??
          'Identify the intersection point.',
      correct_intersection_point: PointToGraph.fromJson(
        json['correct_intersection_point'] as Map<String, dynamic>? ??
            {'x': 0, 'y': 0},
      ),
      feedback_correct_intersection:
          json['feedback_correct_intersection'] ?? 'Intersection correct!',
      feedback_incorrect_intersection: json['feedback_incorrect_intersection'],
      action_button_text: json['action_button_text'] ?? 'Check Intersection',
      x_min: json['x_min'],
      x_max: json['x_max'],
      y_min: json['y_min'],
      y_max: json['y_max'],
    );
  }
}

// For interactive_graph_to_equation_matcher (ear-l4)
class InteractiveGraphToEquationMatcherElement extends InteractiveElement {
  final String graph_image_path; // Path to a static image of the graph
  final String prompt_m;
  final String prompt_c;
  final String prompt_equation;
  final num correct_m;
  final num correct_c;
  final String
  correct_equation_regex; // Regex for "y = mx + c" or "y=mx+c" etc.
  final String feedback_correct_m;
  final String feedback_correct_c;
  final String feedback_correct_equation;
  final String? feedback_incorrect_m;
  final String? feedback_incorrect_c;
  final String? feedback_incorrect_equation;
  final String? action_button_text;

  InteractiveGraphToEquationMatcherElement({
    required this.graph_image_path,
    required this.prompt_m,
    required this.prompt_c,
    required this.prompt_equation,
    required this.correct_m,
    required this.correct_c,
    required this.correct_equation_regex,
    required this.feedback_correct_m,
    required this.feedback_correct_c,
    required this.feedback_correct_equation,
    this.feedback_incorrect_m,
    this.feedback_incorrect_c,
    this.feedback_incorrect_equation,
    this.action_button_text,
  }) : super(type: 'interactive_graph_to_equation_matcher');

  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'graph_image_path': graph_image_path,
    'prompt_m': prompt_m,
    'prompt_c': prompt_c,
    'prompt_equation': prompt_equation,
    'correct_m': correct_m,
    'correct_c': correct_c,
    'correct_equation_regex': correct_equation_regex,
    'feedback_correct_m': feedback_correct_m,
    'feedback_correct_c': feedback_correct_c,
    'feedback_correct_equation': feedback_correct_equation,
    'feedback_incorrect_m': feedback_incorrect_m,
    'feedback_incorrect_c': feedback_incorrect_c,
    'feedback_incorrect_equation': feedback_incorrect_equation,
    'action_button_text': action_button_text,
  };

  factory InteractiveGraphToEquationMatcherElement.fromJson(
    Map<String, dynamic> json,
  ) {
    return InteractiveGraphToEquationMatcherElement(
      graph_image_path: json['graph_image_path'] ?? '',
      prompt_m: json['prompt_m'] ?? 'What is the slope (m)?',
      prompt_c: json['prompt_c'] ?? 'What is the y-intercept (c)?',
      prompt_equation:
          json['prompt_equation'] ?? 'Write the equation in y = mx + c form.',
      correct_m: json['correct_m'] ?? 1,
      correct_c: json['correct_c'] ?? 0,
      correct_equation_regex:
          json['correct_equation_regex'] ?? 'y=x', // Simplified default regex
      feedback_correct_m: json['feedback_correct_m'] ?? 'Slope correct!',
      feedback_correct_c: json['feedback_correct_c'] ?? 'Y-intercept correct!',
      feedback_correct_equation:
          json['feedback_correct_equation'] ?? 'Equation correct!',
      feedback_incorrect_m: json['feedback_incorrect_m'],
      feedback_incorrect_c: json['feedback_incorrect_c'],
      feedback_incorrect_equation: json['feedback_incorrect_equation'],
      action_button_text: json['action_button_text'] ?? 'Check Equation',
    );
  }
}

// For interactive_slope_explorer (ear-l3)
class InteractiveSlopeExplorerElement extends InteractiveElement {
  final String line_equation_template; // e.g., "y = mx + c"
  final num initial_m;
  final num initial_c;
  final num? m_min;
  final num? m_max;
  final num? m_step;
  final num? c_min;
  final num? c_max;
  final num? c_step;
  final String prompt;
  final num? x_axis_min;
  final num? x_axis_max;
  final num? y_axis_min;
  final num? y_axis_max;

  InteractiveSlopeExplorerElement({
    required this.line_equation_template,
    required this.initial_m,
    required this.initial_c,
    this.m_min,
    this.m_max,
    this.m_step,
    this.c_min,
    this.c_max,
    this.c_step,
    required this.prompt,
    this.x_axis_min,
    this.x_axis_max,
    this.y_axis_min,
    this.y_axis_max,
  }) : super(type: 'interactive_slope_explorer');

  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'line_equation_template': line_equation_template,
    'initial_m': initial_m,
    'initial_c': initial_c,
    'm_min': m_min,
    'm_max': m_max,
    'm_step': m_step,
    'c_min': c_min,
    'c_max': c_max,
    'c_step': c_step,
    'prompt': prompt,
    'x_axis_min': x_axis_min,
    'x_axis_max': x_axis_max,
    'y_axis_min': y_axis_min,
    'y_axis_max': y_axis_max,
  };

  factory InteractiveSlopeExplorerElement.fromJson(Map<String, dynamic> json) {
    return InteractiveSlopeExplorerElement(
      line_equation_template: json['line_equation_template'] ?? 'y = mx + c',
      initial_m: json['initial_m'] ?? 1,
      initial_c: json['initial_c'] ?? 0,
      m_min: json['m_min'],
      m_max: json['m_max'],
      m_step: json['m_step'],
      c_min: json['c_min'],
      c_max: json['c_max'],
      c_step: json['c_step'],
      prompt: json['prompt'] ?? 'Explore the slope!',
      x_axis_min: json['x_axis_min'] ?? -10,
      x_axis_max: json['x_axis_max'] ?? 10,
      y_axis_min: json['y_axis_min'] ?? -10,
      y_axis_max: json['y_axis_max'] ?? 10,
    );
  }
}

// For interactive_linear_equation_grapher (ear-l2)
class PointToGraph {
  final num x;
  final num y;

  PointToGraph({required this.x, required this.y});

  factory PointToGraph.fromJson(Map<String, dynamic> json) {
    return PointToGraph(x: json['x'] ?? 0, y: json['y'] ?? 0);
  }
  Map<String, dynamic> toJson() => {'x': x, 'y': y};
}

class InteractiveLinearEquationGrapherElement extends InteractiveElement {
  final String equation_string;
  final List<PointToGraph> points_to_plot; // User needs to plot these
  final String prompt_plot_points;
  final String prompt_draw_line;
  final String feedback_correct_points;
  final String feedback_correct_line;
  final String? feedback_incorrect_points;
  final String? feedback_incorrect_line;
  final String? action_button_text;
  final num? x_min;
  final num? x_max;
  final num? y_min;
  final num? y_max;

  InteractiveLinearEquationGrapherElement({
    required this.equation_string,
    required this.points_to_plot,
    required this.prompt_plot_points,
    required this.prompt_draw_line,
    required this.feedback_correct_points,
    required this.feedback_correct_line,
    this.feedback_incorrect_points,
    this.feedback_incorrect_line,
    this.action_button_text,
    this.x_min,
    this.x_max,
    this.y_min,
    this.y_max,
  }) : super(type: 'interactive_linear_equation_grapher');

  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'equation_string': equation_string,
    'points_to_plot': points_to_plot.map((p) => p.toJson()).toList(),
    'prompt_plot_points': prompt_plot_points,
    'prompt_draw_line': prompt_draw_line,
    'feedback_correct_points': feedback_correct_points,
    'feedback_correct_line': feedback_correct_line,
    'feedback_incorrect_points': feedback_incorrect_points,
    'feedback_incorrect_line': feedback_incorrect_line,
    'action_button_text': action_button_text,
    'x_min': x_min,
    'x_max': x_max,
    'y_min': y_min,
    'y_max': y_max,
  };

  factory InteractiveLinearEquationGrapherElement.fromJson(
    Map<String, dynamic> json,
  ) {
    return InteractiveLinearEquationGrapherElement(
      equation_string: json['equation_string'] ?? 'y = x',
      points_to_plot:
          (json['points_to_plot'] as List<dynamic>?)
              ?.map(
                (pJson) => PointToGraph.fromJson(pJson as Map<String, dynamic>),
              )
              .toList() ??
          [],
      prompt_plot_points: json['prompt_plot_points'] ?? 'Plot the points.',
      prompt_draw_line: json['prompt_draw_line'] ?? 'Now draw the line.',
      feedback_correct_points:
          json['feedback_correct_points'] ?? 'Points correct!',
      feedback_correct_line: json['feedback_correct_line'] ?? 'Line correct!',
      feedback_incorrect_points: json['feedback_incorrect_points'],
      feedback_incorrect_line: json['feedback_incorrect_line'],
      action_button_text: json['action_button_text'] ?? 'Check Graph',
      x_min: json['x_min'],
      x_max: json['x_max'],
      y_min: json['y_min'],
      y_max: json['y_max'],
    );
  }
}

// For interactive_relationship_to_equation (ear-l1)
class InteractiveRelationshipToEquationElement extends InteractiveElement {
  final String scenario_text_md;
  final String prompt_equation;
  final String
  correct_equation_regex; // e.g. "w\\s*=\\s*2b|w\\s*=\\s*2\\*b|2b\\s*=\\s*w|2\\*b\\s*=\\s*w"
  final String feedback_correct;
  final String? feedback_incorrect;
  final String? action_button_text;

  InteractiveRelationshipToEquationElement({
    required this.scenario_text_md,
    required this.prompt_equation,
    required this.correct_equation_regex,
    required this.feedback_correct,
    this.feedback_incorrect,
    this.action_button_text,
  }) : super(type: 'interactive_relationship_to_equation');

  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'scenario_text_md': scenario_text_md,
    'prompt_equation': prompt_equation,
    'correct_equation_regex': correct_equation_regex,
    'feedback_correct': feedback_correct,
    'feedback_incorrect': feedback_incorrect,
    'action_button_text': action_button_text,
  };

  factory InteractiveRelationshipToEquationElement.fromJson(
    Map<String, dynamic> json,
  ) {
    return InteractiveRelationshipToEquationElement(
      scenario_text_md: json['scenario_text_md'] ?? '',
      prompt_equation: json['prompt_equation'] ?? 'Write the equation.',
      correct_equation_regex: json['correct_equation_regex'] ?? '^.*\$',
      feedback_correct: json['feedback_correct'] ?? 'Correct equation!',
      feedback_incorrect: json['feedback_incorrect'],
      action_button_text: json['action_button_text'] ?? 'Check Equation',
    );
  }
}

// For interactive_inequality_symbol_matcher (iti-l1)
class InequalityPair {
  final String id;
  final String value1_text;
  final String value2_text;
  final List<String> options_symbols; // e.g. ["<", ">", "≤", "≥", "="]
  final String correct_symbol;
  final String feedback_correct;
  final String
  feedback_incorrect_template; // e.g. "Consider if {value1_text} is truly {user_choice} {value2_text}."

  InequalityPair({
    required this.id,
    required this.value1_text,
    required this.value2_text,
    required this.options_symbols,
    required this.correct_symbol,
    required this.feedback_correct,
    required this.feedback_incorrect_template,
  });

  factory InequalityPair.fromJson(Map<String, dynamic> json) {
    return InequalityPair(
      id: json['id'] ?? 'pair_unknown',
      value1_text: json['value1_text'] ?? '',
      value2_text: json['value2_text'] ?? '',
      options_symbols: List<String>.from(
        json['options_symbols'] ?? ['<', '>', '≤', '≥', '='],
      ),
      correct_symbol: json['correct_symbol'] ?? '=',
      feedback_correct: json['feedback_correct'] ?? 'Correct!',
      feedback_incorrect_template:
          json['feedback_incorrect_template'] ??
          'Try again. The correct symbol is {correct_symbol}.',
    );
  }

  Map<String, dynamic> toJson() => {
    'id': id,
    'value1_text': value1_text,
    'value2_text': value2_text,
    'options_symbols': options_symbols,
    'correct_symbol': correct_symbol,
    'feedback_correct': feedback_correct,
    'feedback_incorrect_template': feedback_incorrect_template,
  };
}

class InteractiveInequalitySymbolMatcherElement extends InteractiveElement {
  final String overall_prompt;
  final List<InequalityPair> pairs;
  final String? check_button_text;

  InteractiveInequalitySymbolMatcherElement({
    required this.overall_prompt,
    required this.pairs,
    this.check_button_text,
  }) : super(type: 'interactive_inequality_symbol_matcher');

  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'overall_prompt': overall_prompt,
    'pairs': pairs.map((p) => p.toJson()).toList(),
    'check_button_text': check_button_text,
  };

  factory InteractiveInequalitySymbolMatcherElement.fromJson(
    Map<String, dynamic> json,
  ) {
    return InteractiveInequalitySymbolMatcherElement(
      overall_prompt:
          json['overall_prompt'] ?? 'Match the correct inequality symbols.',
      pairs:
          (json['pairs'] as List<dynamic>?)
              ?.map(
                (pJson) =>
                    InequalityPair.fromJson(pJson as Map<String, dynamic>),
              )
              .toList() ??
          [],
      check_button_text: json['check_button_text'] ?? 'Check Answers',
    );
  }
}

// For interactive_word_problem_solver (ttse-l5)
class InteractiveWordProblemSolverElement extends InteractiveElement {
  final String problem_statement_md;
  final String prompt_equation;
  final String correct_equation_regex; // Regex to check the user's equation
  final String feedback_correct_equation;
  final String? feedback_incorrect_equation;
  final String prompt_solve;
  final num correct_solution_value;
  final String? solution_unit;
  final String feedback_correct_solution;
  final String? feedback_incorrect_solution;
  final String? action_button_text_equation;
  final String? action_button_text_solve;

  InteractiveWordProblemSolverElement({
    required this.problem_statement_md,
    required this.prompt_equation,
    required this.correct_equation_regex,
    required this.feedback_correct_equation,
    this.feedback_incorrect_equation,
    required this.prompt_solve,
    required this.correct_solution_value,
    this.solution_unit,
    required this.feedback_correct_solution,
    this.feedback_incorrect_solution,
    this.action_button_text_equation,
    this.action_button_text_solve,
  }) : super(type: 'interactive_word_problem_solver');

  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'problem_statement_md': problem_statement_md,
    'prompt_equation': prompt_equation,
    'correct_equation_regex': correct_equation_regex,
    'feedback_correct_equation': feedback_correct_equation,
    'feedback_incorrect_equation': feedback_incorrect_equation,
    'prompt_solve': prompt_solve,
    'correct_solution_value': correct_solution_value,
    'solution_unit': solution_unit,
    'feedback_correct_solution': feedback_correct_solution,
    'feedback_incorrect_solution': feedback_incorrect_solution,
    'action_button_text_equation': action_button_text_equation,
    'action_button_text_solve': action_button_text_solve,
  };

  factory InteractiveWordProblemSolverElement.fromJson(
    Map<String, dynamic> json,
  ) {
    return InteractiveWordProblemSolverElement(
      problem_statement_md: json['problem_statement_md'] ?? '',
      prompt_equation:
          json['prompt_equation'] ?? 'Write the equation for this problem.',
      correct_equation_regex: json['correct_equation_regex'] ?? '^.*\$',
      feedback_correct_equation:
          json['feedback_correct_equation'] ?? 'Correct equation!',
      feedback_incorrect_equation: json['feedback_incorrect_equation'],
      prompt_solve: json['prompt_solve'] ?? 'Now solve your equation.',
      correct_solution_value: json['correct_solution_value'] ?? 0,
      solution_unit: json['solution_unit'],
      feedback_correct_solution:
          json['feedback_correct_solution'] ?? 'Solution correct!',
      feedback_incorrect_solution: json['feedback_incorrect_solution'],
      action_button_text_equation:
          json['action_button_text_equation'] ?? 'Check Equation',
      action_button_text_solve:
          json['action_button_text_solve'] ?? 'Check Solution',
    );
  }
}

// For interactive_simplify_then_solve (ttse-l2)
class InteractiveSimplifyThenSolveElement extends InteractiveElement {
  final String equation_string_unsimplified;
  final String prompt_simplify;
  final String correct_simplified_form_regex; // Regex to check simplified form
  final String feedback_correct_simplify;
  final String? feedback_incorrect_simplify;
  final String prompt_solve_simplified;
  final num correct_solution_value;
  final String feedback_correct_solve;
  final String? feedback_incorrect_solve;
  final String? action_button_text_simplify;
  final String? action_button_text_solve;

  InteractiveSimplifyThenSolveElement({
    required this.equation_string_unsimplified,
    required this.prompt_simplify,
    required this.correct_simplified_form_regex,
    required this.feedback_correct_simplify,
    this.feedback_incorrect_simplify,
    required this.prompt_solve_simplified,
    required this.correct_solution_value,
    required this.feedback_correct_solve,
    this.feedback_incorrect_solve,
    this.action_button_text_simplify,
    this.action_button_text_solve,
  }) : super(type: 'interactive_simplify_then_solve');

  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'equation_string_unsimplified': equation_string_unsimplified,
    'prompt_simplify': prompt_simplify,
    'correct_simplified_form_regex': correct_simplified_form_regex,
    'feedback_correct_simplify': feedback_correct_simplify,
    'feedback_incorrect_simplify': feedback_incorrect_simplify,
    'prompt_solve_simplified': prompt_solve_simplified,
    'correct_solution_value': correct_solution_value,
    'feedback_correct_solve': feedback_correct_solve,
    'feedback_incorrect_solve': feedback_incorrect_solve,
    'action_button_text_simplify': action_button_text_simplify,
    'action_button_text_solve': action_button_text_solve,
  };

  factory InteractiveSimplifyThenSolveElement.fromJson(
    Map<String, dynamic> json,
  ) {
    return InteractiveSimplifyThenSolveElement(
      equation_string_unsimplified: json['equation_string_unsimplified'] ?? '',
      prompt_simplify: json['prompt_simplify'] ?? 'Simplify the equation.',
      correct_simplified_form_regex:
          json['correct_simplified_form_regex'] ?? '^.*\$',
      feedback_correct_simplify:
          json['feedback_correct_simplify'] ?? 'Correctly simplified!',
      feedback_incorrect_simplify: json['feedback_incorrect_simplify'],
      prompt_solve_simplified:
          json['prompt_solve_simplified'] ??
          'Now solve the simplified equation.',
      correct_solution_value: json['correct_solution_value'] ?? 0,
      feedback_correct_solve:
          json['feedback_correct_solve'] ?? 'Solution correct!',
      feedback_incorrect_solve: json['feedback_incorrect_solve'],
      action_button_text_simplify:
          json['action_button_text_simplify'] ?? 'Check Simplified Form',
      action_button_text_solve:
          json['action_button_text_solve'] ?? 'Check Solution',
    );
  }
}

// For interactive_solution_checker (sose-l5)
class InteractiveSolutionCheckerElement extends InteractiveElement {
  final String original_equation_display;
  final String variable_name;
  final num proposed_solution_value;
  final String prompt_check;
  final bool
  is_solution_correct; // True if proposed_solution_value is actually correct
  final String
  feedback_if_correct_and_balanced; // e.g. "Correct! 10 - 7 = 3. It balances!"
  final String
  feedback_if_correct_and_unbalanced; // Should not happen if logic is right, but for safety
  final String
  feedback_if_incorrect_and_balanced; // User made a mistake in checking
  final String
  feedback_if_incorrect_and_unbalanced; // e.g. "Good catch! 9 - 7 != 3. The proposed solution was wrong."
  final String? action_button_text;

  InteractiveSolutionCheckerElement({
    required this.original_equation_display,
    required this.variable_name,
    required this.proposed_solution_value,
    required this.prompt_check,
    required this.is_solution_correct,
    required this.feedback_if_correct_and_balanced,
    required this.feedback_if_correct_and_unbalanced,
    required this.feedback_if_incorrect_and_balanced,
    required this.feedback_if_incorrect_and_unbalanced,
    this.action_button_text,
  }) : super(type: 'interactive_solution_checker');

  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'original_equation_display': original_equation_display,
    'variable_name': variable_name,
    'proposed_solution_value': proposed_solution_value,
    'prompt_check': prompt_check,
    'is_solution_correct': is_solution_correct,
    'feedback_if_correct_and_balanced': feedback_if_correct_and_balanced,
    'feedback_if_correct_and_unbalanced': feedback_if_correct_and_unbalanced,
    'feedback_if_incorrect_and_balanced': feedback_if_incorrect_and_balanced,
    'feedback_if_incorrect_and_unbalanced':
        feedback_if_incorrect_and_unbalanced,
    'action_button_text': action_button_text,
  };

  factory InteractiveSolutionCheckerElement.fromJson(
    Map<String, dynamic> json,
  ) {
    return InteractiveSolutionCheckerElement(
      original_equation_display: json['original_equation_display'] ?? 'x = 0',
      variable_name: json['variable_name'] ?? 'x',
      proposed_solution_value: json['proposed_solution_value'] ?? 0,
      prompt_check:
          json['prompt_check'] ??
          'Substitute the value and check if the equation balances.',
      is_solution_correct: json['is_solution_correct'] ?? false,
      feedback_if_correct_and_balanced:
          json['feedback_if_correct_and_balanced'] ?? 'Correct, it balances!',
      feedback_if_correct_and_unbalanced:
          json['feedback_if_correct_and_unbalanced'] ??
          'Hmm, it should balance. Check your substitution.',
      feedback_if_incorrect_and_balanced:
          json['feedback_if_incorrect_and_balanced'] ??
          'Careful, the proposed solution was incorrect, but your check made it balance. Double-check!',
      feedback_if_incorrect_and_unbalanced:
          json['feedback_if_incorrect_and_unbalanced'] ??
          'Correct, it does not balance because the proposed solution was wrong.',
      action_button_text: json['action_button_text'] ?? 'Verify Check',
    );
  }
}

// For interactive_number_line_solution_plotter (sose-l4)
class InteractiveNumberLineSolutionPlotterElement extends InteractiveElement {
  final String? equation_string;
  final String variable_to_solve;
  final num correct_solution_value;
  final num number_line_min;
  final num number_line_max;
  final String prompt_plot;
  final String feedback_correct_plot;
  final String? feedback_incorrect_plot;
  final String? action_button_text;

  InteractiveNumberLineSolutionPlotterElement({
    this.equation_string,
    required this.variable_to_solve,
    required this.correct_solution_value,
    required this.number_line_min,
    required this.number_line_max,
    required this.prompt_plot,
    required this.feedback_correct_plot,
    this.feedback_incorrect_plot,
    this.action_button_text,
  }) : super(type: 'interactive_number_line_solution_plotter');

  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'equation_string': equation_string,
    'variable_to_solve': variable_to_solve,
    'correct_solution_value': correct_solution_value,
    'number_line_min': number_line_min,
    'number_line_max': number_line_max,
    'prompt_plot': prompt_plot,
    'feedback_correct_plot': feedback_correct_plot,
    'feedback_incorrect_plot': feedback_incorrect_plot,
    'action_button_text': action_button_text,
  };

  factory InteractiveNumberLineSolutionPlotterElement.fromJson(
    Map<String, dynamic> json,
  ) {
    return InteractiveNumberLineSolutionPlotterElement(
      equation_string: json['equation_string'],
      variable_to_solve: json['variable_to_solve'] ?? 'x',
      correct_solution_value: json['correct_solution_value'] ?? 0,
      number_line_min: json['number_line_min'] ?? -10,
      number_line_max: json['number_line_max'] ?? 10,
      prompt_plot: json['prompt_plot'] ?? 'Plot the solution.',
      feedback_correct_plot:
          json['feedback_correct_plot'] ?? 'Correctly plotted!',
      feedback_incorrect_plot: json['feedback_incorrect_plot'],
      action_button_text: json['action_button_text'] ?? 'Check Plot',
    );
  }
}

// --- Algebra Course Interactive Elements ---

// For variable_value_explorer (lov-l1)
class VariableScenario {
  final String id;
  final String? icon_path;
  final String scenario_title;
  final String context_text_before_input;
  final String input_type; // e.g., "number", "text"
  final num initial_value;
  final String? placeholder_text;
  final num? target_value;
  final String? feedback_on_target;
  final String feedback_general_template;

  VariableScenario({
    required this.id,
    this.icon_path,
    required this.scenario_title,
    required this.context_text_before_input,
    required this.input_type,
    required this.initial_value,
    this.placeholder_text,
    this.target_value,
    this.feedback_on_target,
    required this.feedback_general_template,
  });

  factory VariableScenario.fromJson(Map<String, dynamic> json) {
    return VariableScenario(
      id: json['id'] ?? 's_unknown',
      icon_path: json['icon_path'],
      scenario_title: json['scenario_title'] ?? 'Scenario',
      context_text_before_input: json['context_text_before_input'] ?? '',
      input_type: json['input_type'] ?? 'number',
      initial_value: json['initial_value'] ?? 0,
      placeholder_text: json['placeholder_text'],
      target_value: json['target_value'],
      feedback_on_target: json['feedback_on_target'],
      feedback_general_template:
          json['feedback_general_template'] ?? 'The value of n is now {value}.',
    );
  }

  Map<String, dynamic> toJson() => {
    'id': id,
    'icon_path': icon_path,
    'scenario_title': scenario_title,
    'context_text_before_input': context_text_before_input,
    'input_type': input_type,
    'initial_value': initial_value,
    'placeholder_text': placeholder_text,
    'target_value': target_value,
    'feedback_on_target': feedback_on_target,
    'feedback_general_template': feedback_general_template,
  };
}

class VariableValueExplorerElement extends InteractiveElement {
  final String variable_name;
  final String overall_prompt;
  final List<VariableScenario> scenarios;
  final String? check_button_text;

  VariableValueExplorerElement({
    required this.variable_name,
    required this.overall_prompt,
    required this.scenarios,
    this.check_button_text,
  }) : super(type: 'variable_value_explorer');

  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'variable_name': variable_name,
    'overall_prompt': overall_prompt,
    'scenarios': scenarios.map((s) => s.toJson()).toList(),
    'check_button_text': check_button_text,
  };

  factory VariableValueExplorerElement.fromJson(Map<String, dynamic> json) {
    return VariableValueExplorerElement(
      variable_name: json['variable_name'] ?? 'x',
      overall_prompt: json['overall_prompt'] ?? 'Explore the variable.',
      scenarios:
          (json['scenarios'] as List<dynamic>?)
              ?.map(
                (sJson) =>
                    VariableScenario.fromJson(sJson as Map<String, dynamic>),
              )
              .toList() ??
          [],
      check_button_text: json['check_button_text'] ?? 'Check',
    );
  }
}

// For interactive_expression_builder (lov-l2)
class ExpressionBuilderChallenge {
  final String id;
  final String target_phrase;
  final List<String> target_expression_components;
  final List<String> available_tokens;
  final String? hint;
  final String feedback_correct;
  final String feedback_incorrect_template;

  ExpressionBuilderChallenge({
    required this.id,
    required this.target_phrase,
    required this.target_expression_components,
    required this.available_tokens,
    this.hint,
    required this.feedback_correct,
    required this.feedback_incorrect_template,
  });

  factory ExpressionBuilderChallenge.fromJson(Map<String, dynamic> json) {
    return ExpressionBuilderChallenge(
      id: json['id'] ?? 'challenge_unknown',
      target_phrase: json['target_phrase'] ?? '',
      target_expression_components: List<String>.from(
        json['target_expression_components'] ?? [],
      ),
      available_tokens: List<String>.from(json['available_tokens'] ?? []),
      hint: json['hint'],
      feedback_correct: json['feedback_correct'] ?? 'Correct!',
      feedback_incorrect_template:
          json['feedback_incorrect_template'] ?? 'Try again.',
    );
  }

  Map<String, dynamic> toJson() => {
    'id': id,
    'target_phrase': target_phrase,
    'target_expression_components': target_expression_components,
    'available_tokens': available_tokens,
    'hint': hint,
    'feedback_correct': feedback_correct,
    'feedback_incorrect_template': feedback_incorrect_template,
  };
}

class InteractiveExpressionBuilderElement extends InteractiveElement {
  final String overall_prompt;
  final List<ExpressionBuilderChallenge> challenges;
  final String? check_button_text;

  InteractiveExpressionBuilderElement({
    required this.overall_prompt,
    required this.challenges,
    this.check_button_text,
  }) : super(type: 'interactive_expression_builder');

  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'overall_prompt': overall_prompt,
    'challenges': challenges.map((c) => c.toJson()).toList(),
    'check_button_text': check_button_text,
  };

  factory InteractiveExpressionBuilderElement.fromJson(
    Map<String, dynamic> json,
  ) {
    return InteractiveExpressionBuilderElement(
      overall_prompt: json['overall_prompt'] ?? 'Build the expressions.',
      challenges:
          (json['challenges'] as List<dynamic>?)
              ?.map(
                (cJson) => ExpressionBuilderChallenge.fromJson(
                  cJson as Map<String, dynamic>,
                ),
              )
              .toList() ??
          [],
      check_button_text: json['check_button_text'] ?? 'Check Expression',
    );
  }
}

// For interactive_expression_evaluator (lov-l3)
class TaskVariableDefinition {
  final String name;
  final num value;

  TaskVariableDefinition({required this.name, required this.value});

  factory TaskVariableDefinition.fromJson(Map<String, dynamic> json) {
    return TaskVariableDefinition(
      name: json['name'] ?? 'unknown_var',
      value: json['value'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() => {'name': name, 'value': value};
}

class ExpressionEvaluationTask {
  final String id;
  final String expression_string_template;
  final String display_expression;
  final List<TaskVariableDefinition> variables_to_define;
  final String prompt_with_values;
  final num correct_answer;
  final List<String> solution_steps;
  final String feedback_correct;
  final String feedback_incorrect;

  ExpressionEvaluationTask({
    required this.id,
    required this.expression_string_template,
    required this.display_expression,
    required this.variables_to_define,
    required this.prompt_with_values,
    required this.correct_answer,
    required this.solution_steps,
    required this.feedback_correct,
    required this.feedback_incorrect,
  });

  factory ExpressionEvaluationTask.fromJson(Map<String, dynamic> json) {
    return ExpressionEvaluationTask(
      id: json['id'] ?? 'task_unknown',
      expression_string_template: json['expression_string_template'] ?? '',
      display_expression: json['display_expression'] ?? '',
      variables_to_define:
          (json['variables_to_define'] as List<dynamic>?)
              ?.map(
                (vJson) => TaskVariableDefinition.fromJson(
                  vJson as Map<String, dynamic>,
                ),
              )
              .toList() ??
          [],
      prompt_with_values: json['prompt_with_values'] ?? '',
      correct_answer: json['correct_answer'] ?? 0,
      solution_steps: List<String>.from(json['solution_steps'] ?? []),
      feedback_correct: json['feedback_correct'] ?? 'Correct!',
      feedback_incorrect: json['feedback_incorrect'] ?? 'Try again.',
    );
  }

  Map<String, dynamic> toJson() => {
    'id': id,
    'expression_string_template': expression_string_template,
    'display_expression': display_expression,
    'variables_to_define': variables_to_define.map((v) => v.toJson()).toList(),
    'prompt_with_values': prompt_with_values,
    'correct_answer': correct_answer,
    'solution_steps': solution_steps,
    'feedback_correct': feedback_correct,
    'feedback_incorrect': feedback_incorrect,
  };
}

class InteractiveExpressionEvaluatorElement extends InteractiveElement {
  final String overall_prompt;
  final List<ExpressionEvaluationTask> tasks;
  final String input_type;
  final String? check_button_text;
  final String? show_solution_button_text;

  InteractiveExpressionEvaluatorElement({
    required this.overall_prompt,
    required this.tasks,
    required this.input_type,
    this.check_button_text,
    this.show_solution_button_text,
  }) : super(type: 'interactive_expression_evaluator');

  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'overall_prompt': overall_prompt,
    'tasks': tasks.map((t) => t.toJson()).toList(),
    'input_type': input_type,
    'check_button_text': check_button_text,
    'show_solution_button_text': show_solution_button_text,
  };

  factory InteractiveExpressionEvaluatorElement.fromJson(
    Map<String, dynamic> json,
  ) {
    return InteractiveExpressionEvaluatorElement(
      overall_prompt: json['overall_prompt'] ?? 'Evaluate the expressions.',
      tasks:
          (json['tasks'] as List<dynamic>?)
              ?.map(
                (tJson) => ExpressionEvaluationTask.fromJson(
                  tJson as Map<String, dynamic>,
                ),
              )
              .toList() ??
          [],
      input_type: json['input_type'] ?? 'number',
      check_button_text: json['check_button_text'] ?? 'Check Answer',
      show_solution_button_text:
          json['show_solution_button_text'] ?? 'Show Steps',
    );
  }
}

// For interactive_like_terms_combiner (lov-l4)
class ExpressionPart {
  final String term;
  final String? variable;
  final int power;
  final num coefficient;

  ExpressionPart({
    required this.term,
    this.variable,
    required this.power,
    required this.coefficient,
  });

  factory ExpressionPart.fromJson(Map<String, dynamic> json) {
    return ExpressionPart(
      term: json['term'] ?? '',
      variable: json['variable'],
      power: json['power'] ?? 0,
      coefficient: json['coefficient'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() => {
    'term': term,
    'variable': variable,
    'power': power,
    'coefficient': coefficient,
  };
}

class LikeTermsSolution {
  final String simplified_expression;
  final List<String> steps;

  LikeTermsSolution({required this.simplified_expression, required this.steps});

  factory LikeTermsSolution.fromJson(Map<String, dynamic> json) {
    return LikeTermsSolution(
      simplified_expression: json['simplified_expression'] ?? '',
      steps: List<String>.from(json['steps'] ?? []),
    );
  }

  Map<String, dynamic> toJson() => {
    'simplified_expression': simplified_expression,
    'steps': steps,
  };
}

class InteractiveLikeTermsCombinerElement extends InteractiveElement {
  final List<ExpressionPart> expression_parts;
  final String prompt;
  final LikeTermsSolution solution;

  InteractiveLikeTermsCombinerElement({
    required this.expression_parts,
    required this.prompt,
    required this.solution,
  }) : super(type: 'interactive_like_terms_combiner');

  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'expression_parts': expression_parts.map((p) => p.toJson()).toList(),
    'prompt': prompt,
    'solution': solution.toJson(),
  };

  factory InteractiveLikeTermsCombinerElement.fromJson(
    Map<String, dynamic> json,
  ) {
    return InteractiveLikeTermsCombinerElement(
      expression_parts:
          (json['expression_parts'] as List<dynamic>?)
              ?.map(
                (pJson) =>
                    ExpressionPart.fromJson(pJson as Map<String, dynamic>),
              )
              .toList() ??
          [],
      prompt: json['prompt'] ?? 'Combine like terms.',
      solution: LikeTermsSolution.fromJson(
        json['solution'] as Map<String, dynamic>? ?? {},
      ),
    );
  }
}

// For interactive_balance_scale_analogy (lov-l5)
class BalanceScaleSetupItem {
  final String name; // e.g., "x", "unit"
  final int count;

  BalanceScaleSetupItem({required this.name, required this.count});

  factory BalanceScaleSetupItem.fromJson(Map<String, dynamic> json) {
    return BalanceScaleSetupItem(
      name: json['name'] ?? 'unit',
      count: json['count'] ?? 0,
    );
  }
  Map<String, dynamic> toJson() => {'name': name, 'count': count};
}

class BalanceScaleSideSetup {
  final List<BalanceScaleSetupItem>? variable_blocks;
  final int unit_blocks;

  BalanceScaleSideSetup({this.variable_blocks, required this.unit_blocks});

  factory BalanceScaleSideSetup.fromJson(Map<String, dynamic> json) {
    return BalanceScaleSideSetup(
      variable_blocks:
          (json['variable_blocks'] as List<dynamic>?)
              ?.map(
                (vbJson) => BalanceScaleSetupItem.fromJson(
                  vbJson as Map<String, dynamic>,
                ),
              )
              .toList(),
      unit_blocks: json['unit_blocks'] ?? 0,
    );
  }
  Map<String, dynamic> toJson() => {
    'variable_blocks': variable_blocks?.map((vb) => vb.toJson()).toList(),
    'unit_blocks': unit_blocks,
  };
}

class InteractiveBalanceScaleAnalogyElement extends InteractiveElement {
  final BalanceScaleSideSetup left_side_setup;
  final BalanceScaleSideSetup right_side_setup;
  final String variable_name;
  final String prompt;
  final num target_x_value_for_balance;
  final String feedback_on_balance;
  final String feedback_on_unbalance;

  InteractiveBalanceScaleAnalogyElement({
    required this.left_side_setup,
    required this.right_side_setup,
    required this.variable_name,
    required this.prompt,
    required this.target_x_value_for_balance,
    required this.feedback_on_balance,
    required this.feedback_on_unbalance,
  }) : super(type: 'interactive_balance_scale_analogy');

  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'left_side_setup': left_side_setup.toJson(),
    'right_side_setup': right_side_setup.toJson(),
    'variable_name': variable_name,
    'prompt': prompt,
    'target_x_value_for_balance': target_x_value_for_balance,
    'feedback_on_balance': feedback_on_balance,
    'feedback_on_unbalance': feedback_on_unbalance,
  };

  factory InteractiveBalanceScaleAnalogyElement.fromJson(
    Map<String, dynamic> json,
  ) {
    return InteractiveBalanceScaleAnalogyElement(
      left_side_setup: BalanceScaleSideSetup.fromJson(
        json['left_side_setup'] as Map<String, dynamic>? ?? {},
      ),
      right_side_setup: BalanceScaleSideSetup.fromJson(
        json['right_side_setup'] as Map<String, dynamic>? ?? {},
      ),
      variable_name: json['variable_name'] ?? 'x',
      prompt: json['prompt'] ?? 'Balance the scale.',
      target_x_value_for_balance: json['target_x_value_for_balance'] ?? 0,
      feedback_on_balance: json['feedback_on_balance'] ?? 'Balanced!',
      feedback_on_unbalance: json['feedback_on_unbalance'] ?? 'Not balanced.',
    );
  }
}

// For multiple_choice_image_from_visual (new)
// This element assumes the visual part (e.g., interactive_graph_selection_game)
// presents the image options, and this element handles the selection logic.
class MultipleChoiceImageFromVisualElement extends InteractiveElement {
  final String question_text;
  // The actual image options are part of the visual element it's paired with.
  // This element needs to know which visual option is correct.
  final String correct_option_id_from_visual;
  final String feedback_correct;
  final String? feedback_incorrect;
  final String? action_button_text;

  MultipleChoiceImageFromVisualElement({
    required this.question_text,
    required this.correct_option_id_from_visual,
    required this.feedback_correct,
    this.feedback_incorrect,
    this.action_button_text,
  }) : super(type: 'multiple_choice_image_from_visual');

  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'question_text': question_text,
    'correct_option_id_from_visual': correct_option_id_from_visual,
    'feedback_correct': feedback_correct,
    'feedback_incorrect': feedback_incorrect,
    'action_button_text': action_button_text,
  };

  factory MultipleChoiceImageFromVisualElement.fromJson(
    Map<String, dynamic> json,
  ) => MultipleChoiceImageFromVisualElement(
    question_text: json['question_text'] ?? 'Select the correct image.',
    correct_option_id_from_visual: json['correct_option_id_from_visual'] ?? '',
    feedback_correct: json['feedback_correct'] ?? 'Correct!',
    feedback_incorrect: json['feedback_incorrect'],
    action_button_text: json['action_button_text'] ?? 'Continue',
  );
}

// For draw_line_of_symmetry_game (new)
class DrawLineOfSymmetryGameElement extends InteractiveElement {
  final String
  shape; // e.g., "isosceles_triangle", "rectangle", "custom_svg_path"
  // Potentially define expected lines if multiple or specific ones are needed
  // final List<Map<String, num>>? expected_lines; // e.g., [{"x1":0, "y1":50, "x2":100, "y2":50}]
  final String feedback_correct;
  final String? action_button_text;

  DrawLineOfSymmetryGameElement({
    required this.shape,
    required this.feedback_correct,
    this.action_button_text,
  }) : super(type: 'draw_line_of_symmetry_game');

  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'shape': shape,
    'feedback_correct': feedback_correct,
    'action_button_text': action_button_text,
  };

  factory DrawLineOfSymmetryGameElement.fromJson(Map<String, dynamic> json) =>
      DrawLineOfSymmetryGameElement(
        shape: json['shape'] ?? 'unknown_shape',
        feedback_correct: json['feedback_correct'] ?? 'Correct!',
        action_button_text: json['action_button_text'] ?? 'Continue',
      );
}

// For number_line_comparison_task (new)
class NumberLineComparisonTaskElement extends InteractiveElement {
  final String? question_text;
  final List<num> numbers_on_line;
  final List<MultipleChoiceTextOption> options;
  final String? action_button_text;

  NumberLineComparisonTaskElement({
    this.question_text,
    required this.numbers_on_line,
    required this.options,
    this.action_button_text,
  }) : super(type: 'number_line_comparison_task');

  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'question_text': question_text,
    'numbers_on_line': numbers_on_line,
    'options': options.map((o) => o.toJson()).toList(),
    'action_button_text': action_button_text,
  };

  factory NumberLineComparisonTaskElement.fromJson(Map<String, dynamic> json) =>
      NumberLineComparisonTaskElement(
        question_text: json['question_text'],
        numbers_on_line: List<num>.from(json['numbers_on_line'] ?? []),
        options:
            (json['options'] as List<dynamic>?)
                ?.map(
                  (optJson) => MultipleChoiceTextOption.fromJson(
                    optJson as Map<String, dynamic>,
                  ),
                )
                .toList() ??
            [],
        action_button_text: json['action_button_text'] ?? 'Check Answer',
      );
}

// For truth_table_builder_conditional (pit-screen4-truth-table-interactive)
class TruthTableRow {
  final bool p_value;
  final bool q_value;
  final String p_text;
  final String q_text;
  final bool expected_pq_value;
  final String explanation;

  TruthTableRow({
    required this.p_value,
    required this.q_value,
    required this.p_text,
    required this.q_text,
    required this.expected_pq_value,
    required this.explanation,
  });

  factory TruthTableRow.fromJson(Map<String, dynamic> json) {
    return TruthTableRow(
      p_value: json['p_value'] ?? false,
      q_value: json['q_value'] ?? false,
      p_text: json['p_text'] ?? '',
      q_text: json['q_text'] ?? '',
      expected_pq_value: json['expected_pq_value'] ?? false,
      explanation: json['explanation'] ?? '',
    );
  }

  Map<String, dynamic> toJson() => {
    'p_value': p_value,
    'q_value': q_value,
    'p_text': p_text,
    'q_text': q_text,
    'expected_pq_value': expected_pq_value,
    'explanation': explanation,
  };
}

class TruthTableBuilderConditionalElement extends InteractiveElement {
  final String statement_p;
  final String statement_q;
  final List<TruthTableRow> rows;
  final String feedback_overall_correct;
  final String feedback_overall_incorrect;
  final String? action_button_text;

  TruthTableBuilderConditionalElement({
    required this.statement_p,
    required this.statement_q,
    required this.rows,
    required this.feedback_overall_correct,
    required this.feedback_overall_incorrect,
    this.action_button_text,
  }) : super(type: 'truth_table_builder_conditional');

  factory TruthTableBuilderConditionalElement.fromJson(
    Map<String, dynamic> json,
  ) {
    return TruthTableBuilderConditionalElement(
      statement_p: json['statement_p'] ?? '',
      statement_q: json['statement_q'] ?? '',
      rows:
          (json['rows'] as List<dynamic>?)
              ?.map((r) => TruthTableRow.fromJson(r as Map<String, dynamic>))
              .toList() ??
          [],
      feedback_overall_correct: json['feedback_overall_correct'] ?? '',
      feedback_overall_incorrect: json['feedback_overall_incorrect'] ?? '',
      action_button_text: json['action_button_text'],
    );
  }

  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'statement_p': statement_p,
    'statement_q': statement_q,
    'rows': rows.map((r) => r.toJson()).toList(),
    'feedback_overall_correct': feedback_overall_correct,
    'feedback_overall_incorrect': feedback_overall_incorrect,
    'action_button_text': action_button_text,
  };
}

// For interactive_conditional_scenario_evaluator (pit-screen6-interactive-scenario)
class ConditionalScenarioOption {
  final String text;
  final bool is_correct;
  final String feedback;

  ConditionalScenarioOption({
    required this.text,
    required this.is_correct,
    required this.feedback,
  });

  factory ConditionalScenarioOption.fromJson(Map<String, dynamic> json) {
    return ConditionalScenarioOption(
      text: json['text'] ?? '',
      is_correct: json['is_correct'] ?? false,
      feedback: json['feedback'] ?? '',
    );
  }

  Map<String, dynamic> toJson() => {
    'text': text,
    'is_correct': is_correct,
    'feedback': feedback,
  };
}

class ConditionalScenario {
  final String id;
  final String situation_p_text;
  final bool p_is_true;
  final String situation_q_text;
  final bool q_is_true;
  final String question;
  final List<ConditionalScenarioOption> options;

  ConditionalScenario({
    required this.id,
    required this.situation_p_text,
    required this.p_is_true,
    required this.situation_q_text,
    required this.q_is_true,
    required this.question,
    required this.options,
  });

  factory ConditionalScenario.fromJson(Map<String, dynamic> json) {
    return ConditionalScenario(
      id: json['id'] ?? '',
      situation_p_text: json['situation_p_text'] ?? '',
      p_is_true: json['p_is_true'] ?? false,
      situation_q_text: json['situation_q_text'] ?? '',
      q_is_true: json['q_is_true'] ?? false,
      question: json['question'] ?? '',
      options:
          (json['options'] as List<dynamic>?)
              ?.map(
                (o) => ConditionalScenarioOption.fromJson(
                  o as Map<String, dynamic>,
                ),
              )
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() => {
    'id': id,
    'situation_p_text': situation_p_text,
    'p_is_true': p_is_true,
    'situation_q_text': situation_q_text,
    'q_is_true': q_is_true,
    'question': question,
    'options': options.map((o) => o.toJson()).toList(),
  };
}

class InteractiveConditionalScenarioEvaluatorElement
    extends InteractiveElement {
  final List<ConditionalScenario> scenarios;
  final String? action_button_text;

  InteractiveConditionalScenarioEvaluatorElement({
    required this.scenarios,
    this.action_button_text,
  }) : super(type: 'interactive_conditional_scenario_evaluator');

  factory InteractiveConditionalScenarioEvaluatorElement.fromJson(
    Map<String, dynamic> json,
  ) {
    return InteractiveConditionalScenarioEvaluatorElement(
      scenarios:
          (json['scenarios'] as List<dynamic>?)
              ?.map(
                (s) => ConditionalScenario.fromJson(s as Map<String, dynamic>),
              )
              .toList() ??
          [],
      action_button_text: json['action_button_text'],
    );
  }

  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'scenarios': scenarios.map((s) => s.toJson()).toList(),
    'action_button_text': action_button_text,
  };
}

// For guided_proof_steps (tir-screen7-sqrt2-interactive-proof)
class ProofStepOption {
  final String text;
  final bool is_correct;
  final String feedback;

  ProofStepOption({
    required this.text,
    required this.is_correct,
    required this.feedback,
  });

  factory ProofStepOption.fromJson(Map<String, dynamic> json) {
    return ProofStepOption(
      text: json['text'] ?? '',
      is_correct: json['is_correct'] ?? false,
      feedback: json['feedback'] ?? '',
    );
  }
  Map<String, dynamic> toJson() => {
    'text': text,
    'is_correct': is_correct,
    'feedback': feedback,
  };
}

class UserInputDetails {
  final String type;
  final String? initial_equation;
  final List<String>? steps_to_simplify;
  final String? correct_simplified_form;
  final String? feedback_correct;
  final String? feedback_incorrect_step;
  final String? feedback_incorrect_final;

  UserInputDetails({
    required this.type,
    this.initial_equation,
    this.steps_to_simplify,
    this.correct_simplified_form,
    this.feedback_correct,
    this.feedback_incorrect_step,
    this.feedback_incorrect_final,
  });

  factory UserInputDetails.fromJson(Map<String, dynamic> json) {
    return UserInputDetails(
      type: json['user_input_type'] ?? json['type'] ?? '',
      initial_equation: json['initial_equation'],
      steps_to_simplify:
          (json['steps_to_simplify'] as List<dynamic>?)
              ?.map((s) => s.toString())
              .toList(),
      correct_simplified_form: json['correct_simplified_form'],
      feedback_correct: json['feedback_correct'],
      feedback_incorrect_step: json['feedback_incorrect_step'],
      feedback_incorrect_final: json['feedback_incorrect_final'],
    );
  }
  Map<String, dynamic> toJson() => {
    'user_input_type': type,
    'initial_equation': initial_equation,
    'steps_to_simplify': steps_to_simplify,
    'correct_simplified_form': correct_simplified_form,
    'feedback_correct': feedback_correct,
    'feedback_incorrect_step': feedback_incorrect_step,
    'feedback_incorrect_final': feedback_incorrect_final,
  };
}

class ProofStep {
  final String id;
  final String prompt_text;
  final List<ProofStepOption>? options;
  final UserInputDetails? user_input_details;
  final String? depends_on_correct;

  ProofStep({
    required this.id,
    required this.prompt_text,
    this.options,
    this.user_input_details,
    this.depends_on_correct,
  });

  factory ProofStep.fromJson(Map<String, dynamic> json) {
    return ProofStep(
      id: json['id'] ?? '',
      prompt_text: json['prompt_text'] ?? '',
      options:
          (json['options'] as List<dynamic>?)
              ?.map((o) => ProofStepOption.fromJson(o as Map<String, dynamic>))
              .toList(),
      user_input_details:
          json['user_input_type'] != null
              ? UserInputDetails.fromJson({
                'type': json['user_input_type'],
                ...json,
              })
              : json['user_input_details'] != null
              ? UserInputDetails.fromJson(
                json['user_input_details'] as Map<String, dynamic>,
              )
              : null,
      depends_on_correct: json['depends_on_correct'],
    );
  }
  Map<String, dynamic> toJson() => {
    'id': id,
    'prompt_text': prompt_text,
    'options': options?.map((o) => o.toJson()).toList(),
    'user_input_details': user_input_details?.toJson(),
    'depends_on_correct': depends_on_correct,
  };
}

class GuidedProofStepsElement extends InteractiveElement {
  final String proof_title;
  final List<ProofStep> steps;
  final String conclusion_text_on_complete;
  final String? action_button_text;

  GuidedProofStepsElement({
    required this.proof_title,
    required this.steps,
    required this.conclusion_text_on_complete,
    this.action_button_text,
  }) : super(type: 'guided_proof_steps');

  factory GuidedProofStepsElement.fromJson(Map<String, dynamic> json) {
    return GuidedProofStepsElement(
      proof_title: json['proof_title'] ?? '',
      steps:
          (json['steps'] as List<dynamic>?)
              ?.map((s) => ProofStep.fromJson(s as Map<String, dynamic>))
              .toList() ??
          [],
      conclusion_text_on_complete: json['conclusion_text_on_complete'] ?? '',
      action_button_text: json['action_button_text'],
    );
  }
  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'proof_title': proof_title,
    'steps': steps.map((s) => s.toJson()).toList(),
    'conclusion_text_on_complete': conclusion_text_on_complete,
    'action_button_text': action_button_text,
  };
}

// For argument_chain_builder_game (blc-screen6-interactive-argument-builder)
class ArgumentPremise {
  final String id;
  final String text;

  ArgumentPremise({required this.id, required this.text});

  factory ArgumentPremise.fromJson(Map<String, dynamic> json) {
    return ArgumentPremise(id: json['id'] ?? '', text: json['text'] ?? '');
  }
  Map<String, dynamic> toJson() => {'id': id, 'text': text};
}

class ValidArgumentChain {
  final List<String> ordered_premises_ids;
  final String rule_applied;
  final List<String>? intermediate_conclusions;

  ValidArgumentChain({
    required this.ordered_premises_ids,
    required this.rule_applied,
    this.intermediate_conclusions,
  });

  factory ValidArgumentChain.fromJson(Map<String, dynamic> json) {
    return ValidArgumentChain(
      ordered_premises_ids:
          (json['ordered_premises_ids'] as List<dynamic>?)
              ?.map((id) => id.toString())
              .toList() ??
          [],
      rule_applied: json['rule_applied'] ?? '',
      intermediate_conclusions:
          (json['intermediate_conclusions'] as List<dynamic>?)
              ?.map((c) => c.toString())
              .toList(),
    );
  }
  Map<String, dynamic> toJson() => {
    'ordered_premises_ids': ordered_premises_ids,
    'rule_applied': rule_applied,
    'intermediate_conclusions': intermediate_conclusions,
  };
}

class ArgumentChainBuilderGameElement extends InteractiveElement {
  final List<ArgumentPremise> premises_pool;
  final String target_conclusion;
  final List<ValidArgumentChain> valid_chains;
  final String feedback_correct_chain;
  final String feedback_incorrect_chain;
  final List<String>? distractor_premises_ids;
  final String? action_button_text;

  ArgumentChainBuilderGameElement({
    required this.premises_pool,
    required this.target_conclusion,
    required this.valid_chains,
    required this.feedback_correct_chain,
    required this.feedback_incorrect_chain,
    this.distractor_premises_ids,
    this.action_button_text,
  }) : super(type: 'argument_chain_builder_game');

  factory ArgumentChainBuilderGameElement.fromJson(Map<String, dynamic> json) {
    return ArgumentChainBuilderGameElement(
      premises_pool:
          (json['premises_pool'] as List<dynamic>?)
              ?.map((p) => ArgumentPremise.fromJson(p as Map<String, dynamic>))
              .toList() ??
          [],
      target_conclusion: json['target_conclusion'] ?? '',
      valid_chains:
          (json['valid_chains'] as List<dynamic>?)
              ?.map(
                (c) => ValidArgumentChain.fromJson(c as Map<String, dynamic>),
              )
              .toList() ??
          [],
      feedback_correct_chain: json['feedback_correct_chain'] ?? '',
      feedback_incorrect_chain: json['feedback_incorrect_chain'] ?? '',
      distractor_premises_ids:
          (json['distractor_premises_ids'] as List<dynamic>?)
              ?.map((id) => id.toString())
              .toList(),
      action_button_text: json['action_button_text'],
    );
  }
  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'premises_pool': premises_pool.map((p) => p.toJson()).toList(),
    'target_conclusion': target_conclusion,
    'valid_chains': valid_chains.map((c) => c.toJson()).toList(),
    'feedback_correct_chain': feedback_correct_chain,
    'feedback_incorrect_chain': feedback_incorrect_chain,
    'distractor_premises_ids': distractor_premises_ids,
    'action_button_text': action_button_text,
  };
}

// For fallacy_identification_quick_poll (alt-screen3-ad-hominem)
class FallacyIdentificationQuickPollElement extends InteractiveElement {
  final String statement;
  final List<String> options;
  final String correct_answer;
  final String feedback;

  FallacyIdentificationQuickPollElement({
    required this.statement,
    required this.options,
    required this.correct_answer,
    required this.feedback,
  }) : super(type: 'fallacy_identification_quick_poll');

  factory FallacyIdentificationQuickPollElement.fromJson(
    Map<String, dynamic> json,
  ) {
    return FallacyIdentificationQuickPollElement(
      statement: json['statement'] ?? '',
      options:
          (json['options'] as List<dynamic>?)
              ?.map((o) => o.toString())
              .toList() ??
          [],
      correct_answer: json['correct_answer'] ?? '',
      feedback: json['feedback'] ?? '',
    );
  }
  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'statement': statement,
    'options': options,
    'correct_answer': correct_answer,
    'feedback': feedback,
  };
}

// For fallacy_identifier_game_multiple_choice (alt-screen8 to alt-screen10)
class FallacyOption {
  final String id;
  final String text;

  FallacyOption({required this.id, required this.text});

  factory FallacyOption.fromJson(Map<String, dynamic> json) {
    return FallacyOption(id: json['id'] ?? '', text: json['text'] ?? '');
  }
  Map<String, dynamic> toJson() => {'id': id, 'text': text};
}

class FallacyIdentifierGameMultipleChoiceElement extends InteractiveElement {
  final String argument_text;
  final List<FallacyOption> fallacy_options;
  final String correct_fallacy_id;
  final String feedback_correct;
  final String feedback_incorrect;
  final String? action_button_text;

  FallacyIdentifierGameMultipleChoiceElement({
    required this.argument_text,
    required this.fallacy_options,
    required this.correct_fallacy_id,
    required this.feedback_correct,
    required this.feedback_incorrect,
    this.action_button_text,
  }) : super(type: 'fallacy_identifier_game_multiple_choice');

  factory FallacyIdentifierGameMultipleChoiceElement.fromJson(
    Map<String, dynamic> json,
  ) {
    return FallacyIdentifierGameMultipleChoiceElement(
      argument_text: json['argument_text'] ?? '',
      fallacy_options:
          (json['fallacy_options'] as List<dynamic>?)
              ?.map((fo) => FallacyOption.fromJson(fo as Map<String, dynamic>))
              .toList() ??
          [],
      correct_fallacy_id: json['correct_fallacy_id'] ?? '',
      feedback_correct: json['feedback_correct'] ?? '',
      feedback_incorrect: json['feedback_incorrect'] ?? '',
      action_button_text: json['action_button_text'],
    );
  }
  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'argument_text': argument_text,
    'fallacy_options': fallacy_options.map((fo) => fo.toJson()).toList(),
    'correct_fallacy_id': correct_fallacy_id,
    'feedback_correct': feedback_correct,
    'feedback_incorrect': feedback_incorrect,
    'action_button_text': action_button_text,
  };
}

class PlaceholderInteractiveElement extends InteractiveElement {
  final Map<String, dynamic> data;
  PlaceholderInteractiveElement({required this.data})
    : super(type: data['type'] as String? ?? 'unknown_interactive');

  @override
  Map<String, dynamic> toJson() => {'type': type, 'data': data};

  factory PlaceholderInteractiveElement.fromJson(Map<String, dynamic> json) =>
      PlaceholderInteractiveElement(data: json);
}

class ButtonElement extends InteractiveElement {
  final String text;
  final String action;

  ButtonElement({required this.text, required this.action})
    : super(type: 'button');

  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'text': text,
    'action': action,
  };

  factory ButtonElement.fromJson(Map<String, dynamic> json) => ButtonElement(
    text: json['text'] ?? '',
    action: json['action'] ?? 'next_screen',
  );
}

class MultipleChoiceTextOption {
  final String id;
  final String text;
  final bool is_correct;
  final String? feedback_correct;
  final String? feedback_incorrect;

  MultipleChoiceTextOption({
    required this.id,
    required this.text,
    required this.is_correct,
    this.feedback_correct,
    this.feedback_incorrect,
  });

  Map<String, dynamic> toJson() => {
    'id': id,
    'text': text,
    'is_correct': is_correct,
    'feedback_correct': feedback_correct,
    'feedback_incorrect': feedback_incorrect,
  };

  factory MultipleChoiceTextOption.fromJson(Map<String, dynamic> json) =>
      MultipleChoiceTextOption(
        id: json['id'] ?? 'opt_unknown',
        text: json['text'] ?? '',
        is_correct: json['is_correct'] ?? false,
        feedback_correct: json['feedback_correct'],
        feedback_incorrect: json['feedback_incorrect'],
      );
}

class MultipleChoiceTextElement extends InteractiveElement {
  final String? question_text;
  final List<MultipleChoiceTextOption> options;
  final String? action_button_text;

  MultipleChoiceTextElement({
    this.question_text,
    required this.options,
    this.action_button_text,
  }) : super(type: 'multiple_choice_text');

  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'question_text': question_text,
    'options': options.map((o) => o.toJson()).toList(),
    'action_button_text': action_button_text,
  };

  factory MultipleChoiceTextElement.fromJson(Map<String, dynamic> json) =>
      MultipleChoiceTextElement(
        question_text: json['question_text'],
        options:
            (json['options'] as List<dynamic>?)
                ?.map(
                  (optJson) => MultipleChoiceTextOption.fromJson(
                    optJson as Map<String, dynamic>,
                  ),
                )
                .toList() ??
            [],
        action_button_text: json['action_button_text'],
      );
}

class TextInputElement extends InteractiveElement {
  final String? question_text;
  final String placeholder;
  final String correct_answer_regex;
  final String feedback_correct;
  final String? feedback_incorrect;
  final String? explanation_on_correct;
  final String action_button_text;

  TextInputElement({
    this.question_text,
    required this.placeholder,
    required this.correct_answer_regex,
    required this.feedback_correct,
    this.feedback_incorrect,
    this.explanation_on_correct,
    required this.action_button_text,
  }) : super(type: 'text_input');

  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'question_text': question_text,
    'placeholder': placeholder,
    'correct_answer_regex': correct_answer_regex,
    'feedback_correct': feedback_correct,
    'feedback_incorrect': feedback_incorrect,
    'explanation_on_correct': explanation_on_correct,
    'action_button_text': action_button_text,
  };

  factory TextInputElement.fromJson(Map<String, dynamic> json) =>
      TextInputElement(
        question_text: json['question_text'],
        placeholder: json['placeholder'] ?? '',
        correct_answer_regex:
            json['correct_answer_regex'] ??
            '^.*\$', // Default to match anything
        feedback_correct: json['feedback_correct'] ?? 'Correct!',
        feedback_incorrect: json['feedback_incorrect'],
        explanation_on_correct: json['explanation_on_correct'],
        action_button_text: json['action_button_text'] ?? 'Continue',
      );
}

class MultipleChoiceImageOption {
  final String id;
  final VisualElement image_visual;
  final bool is_correct;
  final String? feedback_correct;
  final String? feedback_incorrect;

  MultipleChoiceImageOption({
    required this.id,
    required this.image_visual,
    required this.is_correct,
    this.feedback_correct,
    this.feedback_incorrect,
  });

  Map<String, dynamic> toJson() => {
    'id': id,
    'image_visual': image_visual.toJson(),
    'is_correct': is_correct,
    'feedback_correct': feedback_correct,
    'feedback_incorrect': feedback_incorrect,
  };

  factory MultipleChoiceImageOption.fromJson(Map<String, dynamic> json) =>
      MultipleChoiceImageOption(
        id: json['id'] ?? 'img_opt_unknown',
        image_visual:
            json['image_visual'] != null
                ? VisualElement.fromJson(
                  json['image_visual'] as Map<String, dynamic>,
                )
                : PlaceholderVisual(
                  data: {
                    'type': 'placeholder_visual',
                    'value': 'Missing image visual',
                  },
                ),
        is_correct: json['is_correct'] ?? false,
        feedback_correct: json['feedback_correct'],
        feedback_incorrect: json['feedback_incorrect'],
      );
}

class MultipleChoiceImageElement extends InteractiveElement {
  final String question_text;
  final List<MultipleChoiceImageOption> options;
  final String action_button_text;

  MultipleChoiceImageElement({
    required this.question_text,
    required this.options,
    required this.action_button_text,
  }) : super(type: 'multiple_choice_image');

  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'question_text': question_text,
    'options': options.map((o) => o.toJson()).toList(),
    'action_button_text': action_button_text,
  };

  factory MultipleChoiceImageElement.fromJson(Map<String, dynamic> json) =>
      MultipleChoiceImageElement(
        question_text: json['question_text'] ?? '',
        options:
            (json['options'] as List<dynamic>?)
                ?.map(
                  (optJson) => MultipleChoiceImageOption.fromJson(
                    optJson as Map<String, dynamic>,
                  ),
                )
                .toList() ??
            [],
        action_button_text: json['action_button_text'] ?? 'Continue',
      );
}

class TextInputRevealElement extends InteractiveElement {
  final String placeholder;
  final String reveal_text_on_submit;
  final String action_button_text;

  TextInputRevealElement({
    required this.placeholder,
    required this.reveal_text_on_submit,
    required this.action_button_text,
  }) : super(type: 'text_input_reveal');

  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'placeholder': placeholder,
    'reveal_text_on_submit': reveal_text_on_submit,
    'action_button_text': action_button_text,
  };

  factory TextInputRevealElement.fromJson(Map<String, dynamic> json) =>
      TextInputRevealElement(
        placeholder: json['placeholder'] ?? '',
        reveal_text_on_submit: json['reveal_text_on_submit'] ?? '',
        action_button_text: json['action_button_text'] ?? 'Reveal',
      );
}

class TextInputQuickElement extends InteractiveElement {
  final String correct_answer_regex;
  final String feedback_correct;
  final String action_on_correct;

  TextInputQuickElement({
    required this.correct_answer_regex,
    required this.feedback_correct,
    required this.action_on_correct,
  }) : super(type: 'text_input_quick');

  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'correct_answer_regex': correct_answer_regex,
    'feedback_correct': feedback_correct,
    'action_on_correct': action_on_correct,
  };

  factory TextInputQuickElement.fromJson(Map<String, dynamic> json) =>
      TextInputQuickElement(
        correct_answer_regex: json['correct_answer_regex'] ?? '^.*\$',
        feedback_correct: json['feedback_correct'] ?? 'Correct!',
        action_on_correct: json['action_on_correct'] ?? 'next_screen_auto',
      );
}

class MultipleChoiceIconOption {
  final String id;
  final String icon_asset;
  final bool is_correct;

  MultipleChoiceIconOption({
    required this.id,
    required this.icon_asset,
    required this.is_correct,
  });

  Map<String, dynamic> toJson() => {
    'id': id,
    'icon_asset': icon_asset,
    'is_correct': is_correct,
  };

  factory MultipleChoiceIconOption.fromJson(Map<String, dynamic> json) =>
      MultipleChoiceIconOption(
        id: json['id'] ?? 'icon_opt_unknown',
        icon_asset: json['icon_asset'] ?? '',
        is_correct: json['is_correct'] ?? false,
      );
}

class MultipleChoiceIconElement extends InteractiveElement {
  final List<MultipleChoiceIconOption> options;
  final String feedback_correct;
  final String action_on_correct;

  MultipleChoiceIconElement({
    required this.options,
    required this.feedback_correct,
    required this.action_on_correct,
  }) : super(type: 'multiple_choice_icon');

  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'options': options.map((o) => o.toJson()).toList(),
    'feedback_correct': feedback_correct,
    'action_on_correct': action_on_correct,
  };

  factory MultipleChoiceIconElement.fromJson(Map<String, dynamic> json) =>
      MultipleChoiceIconElement(
        options:
            (json['options'] as List<dynamic>?)
                ?.map(
                  (optJson) => MultipleChoiceIconOption.fromJson(
                    optJson as Map<String, dynamic>,
                  ),
                )
                .toList() ??
            [],
        feedback_correct: json['feedback_correct'] ?? 'Correct!',
        action_on_correct: json['action_on_correct'] ?? 'next_screen_auto',
      );
}

class SieveMiniGameElement extends InteractiveElement {
  final int limit;
  final String feedback_correct;
  final String action_button_text;

  SieveMiniGameElement({
    required this.limit,
    required this.feedback_correct,
    required this.action_button_text,
  }) : super(type: 'sieve_mini_game');

  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'limit': limit,
    'feedback_correct': feedback_correct,
    'action_button_text': action_button_text,
  };

  factory SieveMiniGameElement.fromJson(Map<String, dynamic> json) =>
      SieveMiniGameElement(
        limit: json['limit'] ?? 30,
        feedback_correct: json['feedback_correct'] ?? 'Well done!',
        action_button_text: json['action_button_text'] ?? 'Continue',
      );
}

class MentalMathChallengeQuestion {
  final String id;
  final String question_text;
  final String correct_answer_regex;

  MentalMathChallengeQuestion({
    required this.id,
    required this.question_text,
    required this.correct_answer_regex,
  });

  Map<String, dynamic> toJson() => {
    'id': id,
    'question_text': question_text,
    'correct_answer_regex': correct_answer_regex,
  };

  factory MentalMathChallengeQuestion.fromJson(Map<String, dynamic> json) =>
      MentalMathChallengeQuestion(
        id: json['id'] ?? 'mm_q_unknown',
        question_text: json['question_text'] ?? '',
        correct_answer_regex: json['correct_answer_regex'] ?? '^.*\$',
      );
}

class MentalMathChallengeSequenceElement extends InteractiveElement {
  final List<MentalMathChallengeQuestion> questions;
  final String feedback_correct_all;
  final String action_button_text;

  MentalMathChallengeSequenceElement({
    required this.questions,
    required this.feedback_correct_all,
    required this.action_button_text,
  }) : super(type: 'mental_math_challenge_sequence');

  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'questions': questions.map((q) => q.toJson()).toList(),
    'feedback_correct_all': feedback_correct_all,
    'action_button_text': action_button_text,
  };

  factory MentalMathChallengeSequenceElement.fromJson(
    Map<String, dynamic> json,
  ) => MentalMathChallengeSequenceElement(
    questions:
        (json['questions'] as List<dynamic>?)
            ?.map(
              (qJson) => MentalMathChallengeQuestion.fromJson(
                qJson as Map<String, dynamic>,
              ),
            )
            .toList() ??
        [],
    feedback_correct_all: json['feedback_correct_all'] ?? 'Amazing!',
    action_button_text: json['action_button_text'] ?? 'Continue',
  );
}

class AngleToSort {
  final String id;
  final int degrees;
  final String type;

  AngleToSort({required this.id, required this.degrees, required this.type});
  Map<String, dynamic> toJson() => {'id': id, 'degrees': degrees, 'type': type};
  factory AngleToSort.fromJson(Map<String, dynamic> json) => AngleToSort(
    id: json['id'] ?? 'angle_unknown',
    degrees: json['degrees'] ?? 0,
    type: json['type'] ?? 'acute',
  );
}

class DragDropAngleSorterElement extends InteractiveElement {
  final List<AngleToSort> angles_to_sort;
  final List<String> categories;
  final String feedback_correct;
  final String action_button_text;

  DragDropAngleSorterElement({
    required this.angles_to_sort,
    required this.categories,
    required this.feedback_correct,
    required this.action_button_text,
  }) : super(type: 'drag_drop_angle_sorter');

  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'angles_to_sort': angles_to_sort.map((a) => a.toJson()).toList(),
    'categories': categories,
    'feedback_correct': feedback_correct,
    'action_button_text': action_button_text,
  };

  factory DragDropAngleSorterElement.fromJson(Map<String, dynamic> json) =>
      DragDropAngleSorterElement(
        angles_to_sort:
            (json['angles_to_sort'] as List<dynamic>?)
                ?.map(
                  (aJson) =>
                      AngleToSort.fromJson(aJson as Map<String, dynamic>),
                )
                .toList() ??
            [],
        categories: List<String>.from(json['categories'] ?? []),
        feedback_correct: json['feedback_correct'] ?? 'Great sorting!',
        action_button_text: json['action_button_text'] ?? 'Continue',
      );
}

class TargetPosition {
  final num x;
  final num y;
  TargetPosition({required this.x, required this.y});
  Map<String, dynamic> toJson() => {'x': x, 'y': y};
  factory TargetPosition.fromJson(Map<String, dynamic> json) =>
      TargetPosition(x: json['x'] ?? 0, y: json['y'] ?? 0);
}

class TranslationInteractiveGameElement extends InteractiveElement {
  final String shape;
  final TargetPosition target_position;
  final String feedback_correct;
  final String action_button_text;

  TranslationInteractiveGameElement({
    required this.shape,
    required this.target_position,
    required this.feedback_correct,
    required this.action_button_text,
  }) : super(type: 'translation_interactive_game');

  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'shape': shape,
    'target_position': target_position.toJson(),
    'feedback_correct': feedback_correct,
    'action_button_text': action_button_text,
  };

  factory TranslationInteractiveGameElement.fromJson(
    Map<String, dynamic> json,
  ) => TranslationInteractiveGameElement(
    shape: json['shape'] ?? 'triangle',
    target_position: TargetPosition.fromJson(
      json['target_position'] as Map<String, dynamic>? ?? {'x': 0, 'y': 0},
    ),
    feedback_correct: json['feedback_correct'] ?? 'Perfect slide!',
    action_button_text: json['action_button_text'] ?? 'Continue',
  );
}

class ReflectionInteractiveGameElement extends InteractiveElement {
  final String shape;
  final String reflection_line;
  final String feedback_correct;
  final String action_button_text;

  ReflectionInteractiveGameElement({
    required this.shape,
    required this.reflection_line,
    required this.feedback_correct,
    required this.action_button_text,
  }) : super(type: 'reflection_interactive_game');

  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'shape': shape,
    'reflection_line': reflection_line,
    'feedback_correct': feedback_correct,
    'action_button_text': action_button_text,
  };

  factory ReflectionInteractiveGameElement.fromJson(
    Map<String, dynamic> json,
  ) => ReflectionInteractiveGameElement(
    shape: json['shape'] ?? 'L_shape',
    reflection_line: json['reflection_line'] ?? 'y_axis',
    feedback_correct: json['feedback_correct'] ?? 'Spot on!',
    action_button_text: json['action_button_text'] ?? 'Continue',
  );
}

class RotationInteractiveGameElement extends InteractiveElement {
  final String shape;
  final String center_of_rotation;
  final int angle_degrees;
  final String direction;
  final String feedback_correct;
  final String action_button_text;

  RotationInteractiveGameElement({
    required this.shape,
    required this.center_of_rotation,
    required this.angle_degrees,
    required this.direction,
    required this.feedback_correct,
    required this.action_button_text,
  }) : super(type: 'rotation_interactive_game');

  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'shape': shape,
    'center_of_rotation': center_of_rotation,
    'angle_degrees': angle_degrees,
    'direction': direction,
    'feedback_correct': feedback_correct,
    'action_button_text': action_button_text,
  };

  factory RotationInteractiveGameElement.fromJson(Map<String, dynamic> json) =>
      RotationInteractiveGameElement(
        shape: json['shape'] ?? 'arrow',
        center_of_rotation: json['center_of_rotation'] ?? 'origin',
        angle_degrees: json['angle_degrees'] ?? 90,
        direction: json['direction'] ?? 'clockwise',
        feedback_correct: json['feedback_correct'] ?? 'Nice turn!',
        action_button_text: json['action_button_text'] ?? 'Continue',
      );
}

class DilationInteractiveGameElement extends InteractiveElement {
  final String shape;
  final num scale_factor;
  final String feedback_correct;
  final String action_button_text;

  DilationInteractiveGameElement({
    required this.shape,
    required this.scale_factor,
    required this.feedback_correct,
    required this.action_button_text,
  }) : super(type: 'dilation_interactive_game');

  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'shape': shape,
    'scale_factor': scale_factor,
    'feedback_correct': feedback_correct,
    'action_button_text': action_button_text,
  };

  factory DilationInteractiveGameElement.fromJson(Map<String, dynamic> json) =>
      DilationInteractiveGameElement(
        shape: json['shape'] ?? 'square',
        scale_factor: json['scale_factor'] ?? 2,
        feedback_correct: json['feedback_correct'] ?? 'Perfectly resized!',
        action_button_text: json['action_button_text'] ?? 'Continue',
      );
}

class StaticTextDisplayElement extends InteractiveElement {
  final String text_template;

  StaticTextDisplayElement({required this.text_template})
    : super(type: 'static_text_display');

  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'text_template': text_template,
  };

  factory StaticTextDisplayElement.fromJson(Map<String, dynamic> json) =>
      StaticTextDisplayElement(text_template: json['text_template'] ?? '');
}

class InteractiveNumberLineJumpElement extends InteractiveElement {
  final String operation;
  final num start_val;
  final num hop_val;
  final num correct_end_val;

  InteractiveNumberLineJumpElement({
    required this.operation,
    required this.start_val,
    required this.hop_val,
    required this.correct_end_val,
  }) : super(type: 'interactive_number_line_jump');

  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'operation': operation,
    'start_val': start_val,
    'hop_val': hop_val,
    'correct_end_val': correct_end_val,
  };

  factory InteractiveNumberLineJumpElement.fromJson(
    Map<String, dynamic> json,
  ) => InteractiveNumberLineJumpElement(
    operation: json['operation'] ?? 'addition',
    start_val: json['start_val'] ?? 0,
    hop_val: json['hop_val'] ?? 1,
    correct_end_val: json['correct_end_val'] ?? 1,
  );
}

class MultiplicationFactQuestion {
  final String q;
  final String a;

  MultiplicationFactQuestion({required this.q, required this.a});
  Map<String, dynamic> toJson() => {'q': q, 'a': a};
  factory MultiplicationFactQuestion.fromJson(Map<String, dynamic> json) =>
      MultiplicationFactQuestion(q: json['q'] ?? '', a: json['a'] ?? '');
}

class MultiplicationFactsGameElement extends InteractiveElement {
  final List<MultiplicationFactQuestion> questions;
  final String feedback_correct_all;
  final String action_button_text;

  MultiplicationFactsGameElement({
    required this.questions,
    required this.feedback_correct_all,
    required this.action_button_text,
  }) : super(type: 'multiplication_facts_game');

  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'questions': questions.map((q) => q.toJson()).toList(),
    'feedback_correct_all': feedback_correct_all,
    'action_button_text': action_button_text,
  };

  factory MultiplicationFactsGameElement.fromJson(Map<String, dynamic> json) =>
      MultiplicationFactsGameElement(
        questions:
            (json['questions'] as List<dynamic>?)
                ?.map(
                  (qJson) => MultiplicationFactQuestion.fromJson(
                    qJson as Map<String, dynamic>,
                  ),
                )
                .toList() ??
            [],
        feedback_correct_all: json['feedback_correct_all'] ?? 'Great job!',
        action_button_text: json['action_button_text'] ?? 'Continue',
      );
}

class DragDropSharingGameElement extends InteractiveElement {
  final int items_to_share;
  final int groups_to_share_among;
  final String item_image_src;
  final String feedback_correct;
  final String action_button_text;

  DragDropSharingGameElement({
    required this.items_to_share,
    required this.groups_to_share_among,
    required this.item_image_src,
    required this.feedback_correct,
    required this.action_button_text,
  }) : super(type: 'drag_drop_sharing_game');

  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'items_to_share': items_to_share,
    'groups_to_share_among': groups_to_share_among,
    'item_image_src': item_image_src,
    'feedback_correct': feedback_correct,
    'action_button_text': action_button_text,
  };

  factory DragDropSharingGameElement.fromJson(Map<String, dynamic> json) =>
      DragDropSharingGameElement(
        items_to_share: json['items_to_share'] ?? 0,
        groups_to_share_among: json['groups_to_share_among'] ?? 1,
        item_image_src: json['item_image_src'] ?? '',
        feedback_correct: json['feedback_correct'] ?? 'Perfect sharing!',
        action_button_text: json['action_button_text'] ?? 'Continue',
      );
}

class GroupingItemsGameElement extends InteractiveElement {
  final int total_items;
  final int group_size;
  final String item_image_src;
  final String feedback_correct;
  final String action_button_text;

  GroupingItemsGameElement({
    required this.total_items,
    required this.group_size,
    required this.item_image_src,
    required this.feedback_correct,
    required this.action_button_text,
  }) : super(type: 'grouping_items_game');

  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'total_items': total_items,
    'group_size': group_size,
    'item_image_src': item_image_src,
    'feedback_correct': feedback_correct,
    'action_button_text': action_button_text,
  };

  factory GroupingItemsGameElement.fromJson(Map<String, dynamic> json) =>
      GroupingItemsGameElement(
        total_items: json['total_items'] ?? 0,
        group_size: json['group_size'] ?? 1,
        item_image_src: json['item_image_src'] ?? '',
        feedback_correct: json['feedback_correct'] ?? 'Exactly!',
        action_button_text: json['action_button_text'] ?? 'Continue',
      );
}

class InteractiveBaseTenBlocksElement extends InteractiveElement {
  final int target_number;
  final List<String> block_options;

  InteractiveBaseTenBlocksElement({
    required this.target_number,
    required this.block_options,
  }) : super(type: 'interactive_base_ten_blocks');

  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'target_number': target_number,
    'block_options': block_options,
  };

  factory InteractiveBaseTenBlocksElement.fromJson(Map<String, dynamic> json) =>
      InteractiveBaseTenBlocksElement(
        target_number: json['target_number'] ?? 0,
        block_options: List<String>.from(json['block_options'] ?? []),
      );
}

class InteractiveNumberOrderingGameElement extends InteractiveElement {
  final List<num> numbers_to_order;
  final String order_type;

  InteractiveNumberOrderingGameElement({
    required this.numbers_to_order,
    required this.order_type,
  }) : super(type: 'interactive_number_ordering_game');

  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'numbers_to_order': numbers_to_order,
    'order_type': order_type,
  };

  factory InteractiveNumberOrderingGameElement.fromJson(
    Map<String, dynamic> json,
  ) => InteractiveNumberOrderingGameElement(
    numbers_to_order: List<num>.from(json['numbers_to_order'] ?? []),
    order_type: json['order_type'] ?? 'ascending',
  );
}

class ChartInterpretationMcqData {
  final List<String>? categories;
  final List<num>? values;

  ChartInterpretationMcqData({this.categories, this.values});

  Map<String, dynamic> toJson() => {'categories': categories, 'values': values};

  factory ChartInterpretationMcqData.fromJson(Map<String, dynamic> json) =>
      ChartInterpretationMcqData(
        categories:
            json['categories'] != null
                ? List<String>.from(json['categories'])
                : null,
        values: json['values'] != null ? List<num>.from(json['values']) : null,
      );
}

class ChartInterpretationMcqElement extends InteractiveElement {
  final String chart_type;
  final ChartInterpretationMcqData data;
  final String question_text;
  final List<MultipleChoiceTextOption> options;
  final String feedback_correct;
  final String action_button_text;

  ChartInterpretationMcqElement({
    required this.chart_type,
    required this.data,
    required this.question_text,
    required this.options,
    required this.feedback_correct,
    required this.action_button_text,
  }) : super(type: 'chart_interpretation_mcq');

  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'chart_type': chart_type,
    'data': data.toJson(),
    'question_text': question_text,
    'options': options.map((o) => o.toJson()).toList(),
    'feedback_correct': feedback_correct,
    'action_button_text': action_button_text,
  };

  factory ChartInterpretationMcqElement.fromJson(Map<String, dynamic> json) =>
      ChartInterpretationMcqElement(
        chart_type: json['chart_type'] ?? 'bar_chart',
        data: ChartInterpretationMcqData.fromJson(
          json['data'] as Map<String, dynamic>? ?? {},
        ),
        question_text: json['question_text'] ?? '',
        options:
            (json['options'] as List<dynamic>?)
                ?.map(
                  (optJson) => MultipleChoiceTextOption.fromJson(
                    optJson as Map<String, dynamic>,
                  ),
                )
                .toList() ??
            [],
        feedback_correct: json['feedback_correct'] ?? 'Correct!',
        action_button_text: json['action_button_text'] ?? 'Continue',
      );
}

class PlottingPoint {
  final num x;
  final num y;
  PlottingPoint({required this.x, required this.y});
  Map<String, dynamic> toJson() => {'x': x, 'y': y};
  factory PlottingPoint.fromJson(Map<String, dynamic> json) =>
      PlottingPoint(x: json['x'] ?? 0, y: json['y'] ?? 0);
}

class InteractivePlottingGameElement extends InteractiveElement {
  final List<PlottingPoint> points_to_plot;
  final String x_axis_label;
  final String y_axis_label;

  InteractivePlottingGameElement({
    required this.points_to_plot,
    required this.x_axis_label,
    required this.y_axis_label,
  }) : super(type: 'interactive_plotting_game');

  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'points_to_plot': points_to_plot.map((p) => p.toJson()).toList(),
    'x_axis_label': x_axis_label,
    'y_axis_label': y_axis_label,
  };

  factory InteractivePlottingGameElement.fromJson(Map<String, dynamic> json) =>
      InteractivePlottingGameElement(
        points_to_plot:
            (json['points_to_plot'] as List<dynamic>?)
                ?.map(
                  (pJson) =>
                      PlottingPoint.fromJson(pJson as Map<String, dynamic>),
                )
                .toList() ??
            [],
        x_axis_label: json['x_axis_label'] ?? 'X-Axis',
        y_axis_label: json['y_axis_label'] ?? 'Y-Axis',
      );
}

class InteractiveFunctionMachineElement extends InteractiveElement {
  final String function_rule_display;
  final String function_logic;

  InteractiveFunctionMachineElement({
    required this.function_rule_display,
    required this.function_logic,
  }) : super(type: 'interactive_function_machine');

  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'function_rule_display': function_rule_display,
    'function_logic': function_logic,
  };

  factory InteractiveFunctionMachineElement.fromJson(
    Map<String, dynamic> json,
  ) => InteractiveFunctionMachineElement(
    function_rule_display: json['function_rule_display'] ?? 'f(x) = ?',
    function_logic: json['function_logic'] ?? '',
  );
}

class GraphOption {
  final String id;
  final String image_src;
  final bool is_correct;

  GraphOption({
    required this.id,
    required this.image_src,
    required this.is_correct,
  });
  Map<String, dynamic> toJson() => {
    'id': id,
    'image_src': image_src,
    'is_correct': is_correct,
  };
  factory GraphOption.fromJson(Map<String, dynamic> json) => GraphOption(
    id: json['id'] ?? 'graph_opt_unknown',
    image_src: json['image_src'] ?? '',
    is_correct: json['is_correct'] ?? false,
  );
}

class InteractiveGraphSelectionGameElement extends InteractiveElement {
  final List<GraphOption> options;
  final String question_text;
  final String feedback_correct;
  final String feedback_incorrect;
  final String action_button_text;

  InteractiveGraphSelectionGameElement({
    required this.options,
    required this.question_text,
    required this.feedback_correct,
    required this.feedback_incorrect,
    required this.action_button_text,
  }) : super(type: 'interactive_graph_selection_game');

  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'options': options.map((o) => o.toJson()).toList(),
    'question_text': question_text,
    'feedback_correct': feedback_correct,
    'feedback_incorrect': feedback_incorrect,
    'action_button_text': action_button_text,
  };

  factory InteractiveGraphSelectionGameElement.fromJson(
    Map<String, dynamic> json,
  ) {
    return InteractiveGraphSelectionGameElement(
      options:
          (json['options'] as List<dynamic>?)
              ?.map(
                (optJson) =>
                    GraphOption.fromJson(optJson as Map<String, dynamic>),
              )
              .toList() ??
          [],
      question_text: json['question_text'] ?? "Select the correct graph:",
      feedback_correct: json['feedback_correct'] ?? "Correct!",
      feedback_incorrect: json['feedback_incorrect'] ?? "Try again.",
      action_button_text: json['action_button_text'] ?? "Continue",
    );
  }
}

class TextInputCustomFeedbackElement extends InteractiveElement {
  final String question_text;
  final String placeholder;
  final List<String> correct_answer_keywords_positive;
  final List<String>? correct_answer_keywords_negative;
  final String feedback_correct;
  final String feedback_incorrect;
  final String action_button_text;

  TextInputCustomFeedbackElement({
    required this.question_text,
    required this.placeholder,
    required this.correct_answer_keywords_positive,
    this.correct_answer_keywords_negative,
    required this.feedback_correct,
    required this.feedback_incorrect,
    required this.action_button_text,
  }) : super(type: 'text_input_custom_feedback');

  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'question_text': question_text,
    'placeholder': placeholder,
    'correct_answer_keywords_positive': correct_answer_keywords_positive,
    'correct_answer_keywords_negative': correct_answer_keywords_negative,
    'feedback_correct': feedback_correct,
    'feedback_incorrect': feedback_incorrect,
    'action_button_text': action_button_text,
  };

  factory TextInputCustomFeedbackElement.fromJson(Map<String, dynamic> json) =>
      TextInputCustomFeedbackElement(
        question_text: json['question_text'] ?? '',
        placeholder: json['placeholder'] ?? '',
        correct_answer_keywords_positive: List<String>.from(
          json['correct_answer_keywords_positive'] ?? [],
        ),
        correct_answer_keywords_negative:
            json['correct_answer_keywords_negative'] != null
                ? List<String>.from(json['correct_answer_keywords_negative'])
                : null,
        feedback_correct: json['feedback_correct'] ?? 'Correct!',
        feedback_incorrect: json['feedback_incorrect'] ?? 'Try again.',
        action_button_text: json['action_button_text'] ?? 'Continue',
      );
}

// --- Potentially legacy or separate assessment structures ---
/// Represents an Assessment, typically at the end of a module (might be legacy)
class Assessment {
  final String id;
  final String title;
  final String description;
  final List<QuizBlock> questions;
  final int? timeLimitMinutes;
  final double? passingScorePercentage;

  Assessment({
    required this.id,
    required this.title,
    required this.description,
    this.questions = const [],
    this.timeLimitMinutes,
    this.passingScorePercentage,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'questions': questions.map((q) => q.toJson()).toList(),
      'timeLimitMinutes': timeLimitMinutes,
      'passingScorePercentage': passingScorePercentage,
    };
  }

  factory Assessment.fromJson(Map<String, dynamic> json) {
    return Assessment(
      id: json['id'] ?? 'unknown_assessment_id',
      title: json['title'] ?? 'Unknown Assessment',
      description: json['description'] ?? '',
      questions:
          (json['questions'] as List<dynamic>?)
              ?.map(
                (qJson) => QuizBlock.fromJson(qJson as Map<String, dynamic>),
              )
              .toList() ??
          [],
      timeLimitMinutes: json['timeLimitMinutes'],
      passingScorePercentage:
          (json['passingScorePercentage'] as num?)?.toDouble(),
    );
  }
}

/// Quiz content block (might be legacy or for simpler quizzes)
class QuizBlock {
  final String id;
  final String type = 'quiz';
  final int order;
  final String question;
  final List<String> options;
  final int correctOptionIndex;
  final String? explanation;

  QuizBlock({
    required this.id,
    required this.order,
    required this.question,
    required this.options,
    required this.correctOptionIndex,
    this.explanation,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type,
      'order': order,
      'question': question,
      'options': options,
      'correctOptionIndex': correctOptionIndex,
      'explanation': explanation,
    };
  }

  factory QuizBlock.fromJson(Map<String, dynamic> json) {
    return QuizBlock(
      id: json['id'] ?? 'unknown_quiz_block_id',
      order: json['order'] ?? 0,
      question: json['question'] ?? '',
      options: List<String>.from(json['options'] ?? []),
      correctOptionIndex: json['correctOptionIndex'] ?? 0,
      explanation: json['explanation'],
    );
  }
}
