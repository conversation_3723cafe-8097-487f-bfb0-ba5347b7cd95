import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:resonance_app/services/interactive_widget_service.dart';
import 'package:resonance_app/widgets/animated_button.dart';
import 'dart:math';

class InteractiveSortingChallengeTest extends StatefulWidget {
  final String widgetId;

  const InteractiveSortingChallengeTest({Key? key, required this.widgetId}) : super(key: key);

  @override
  _InteractiveSortingChallengeTestState createState() => _InteractiveSortingChallengeTestState();
}

class _InteractiveSortingChallengeTestState extends State<InteractiveSortingChallengeTest> {
  int _currentQuestionIndex = 0;
  List<Map<String, dynamic>> _questions = [];
  String? _selectedAnswer;
  String _feedback = '';
  int _score = 0;
  bool _quizCompleted = false;

  @override
  void initState() {
    super.initState();
    _loadQuestions();
    _loadState();
  }

  void _loadQuestions() {
    _questions = [
      {
        'question': 'Which sorting algorithm has the best-case time complexity of O(n)?',
        'options': ['Bubble Sort', 'Insertion Sort', 'Merge Sort', 'Quick Sort'],
        'correctAnswer': 'Insertion Sort',
      },
      {
        'question': 'Which sorting algorithm is generally considered unstable?',
        'options': ['Merge Sort', 'Bubble Sort', 'Insertion Sort', 'Quick Sort'],
        'correctAnswer': 'Quick Sort',
      },
      {
        'question': 'What is the space complexity of Merge Sort?',
        'options': ['O(1)', 'O(log n)', 'O(n)', 'O(n^2)'],
        'correctAnswer': 'O(n)',
      },
      {
        'question': 'Which sorting algorithm is most efficient for nearly sorted data?',
        'options': ['Bubble Sort', 'Insertion Sort', 'Merge Sort', 'Quick Sort'],
        'correctAnswer': 'Insertion Sort',
      },
      {
        'question': 'Which sorting algorithm uses a "divide and conquer" approach?',
        'options': ['Bubble Sort', 'Insertion Sort', 'Merge Sort', 'Selection Sort'],
        'correctAnswer': 'Merge Sort',
      },
    ];
    _questions.shuffle(); // Randomize question order
  }

  void _loadState() async {
    final service = Provider.of<InteractiveWidgetService>(context, listen: false);
    final savedState = await service.getWidgetState(widget.widgetId);
    if (savedState != null) {
      setState(() {
        _currentQuestionIndex = savedState['currentQuestionIndex'] ?? 0;
        _score = savedState['score'] ?? 0;
        _quizCompleted = savedState['quizCompleted'] ?? false;
      });
    }
  }

  void _saveState() {
    final service = Provider.of<InteractiveWidgetService>(context, listen: false);
    service.saveWidgetState(widget.widgetId, {
      'currentQuestionIndex': _currentQuestionIndex,
      'score': _score,
      'quizCompleted': _quizCompleted,
    });
  }

  void _checkAnswer() {
    if (_selectedAnswer == null) {
      setState(() {
        _feedback = 'Please select an answer.';
      });
      return;
    }

    if (_selectedAnswer == _questions[_currentQuestionIndex]['correctAnswer']) {
      setState(() {
        _feedback = 'Correct!';
        _score++;
      });
    } else {
      setState(() {
        _feedback = 'Incorrect. The correct answer was: ${_questions[_currentQuestionIndex]['correctAnswer']}';
      });
    }
    _saveState();
  }

  void _nextQuestion() {
    setState(() {
      if (_currentQuestionIndex < _questions.length - 1) {
        _currentQuestionIndex++;
        _selectedAnswer = null;
        _feedback = '';
      } else {
        _quizCompleted = true;
      }
    });
    _saveState();
  }

  void _resetQuiz() {
    setState(() {
      _currentQuestionIndex = 0;
      _score = 0;
      _selectedAnswer = null;
      _feedback = '';
      _quizCompleted = false;
      _loadQuestions(); // Re-shuffle for a new quiz
    });
    _saveState();
  }

  @override
  Widget build(BuildContext context) {
    if (_quizCompleted) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'Quiz Completed!',
              style: Theme.of(context).textTheme.headlineMedium,
            ),
            const SizedBox(height: 20),
            Text(
              'Your Score: $_score / ${_questions.length}',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 20),
            AnimatedButton(
              onTap: _resetQuiz,
              text: 'Retake Quiz',
              color: Colors.blue,
            ),
          ],
        ),
      );
    }

    final currentQuestion = _questions[_currentQuestionIndex];

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Sorting Algorithms Challenge (Question ${_currentQuestionIndex + 1}/${_questions.length})',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 20),
          Text(
            currentQuestion['question'],
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 20),
          ...currentQuestion['options'].map<Widget>((option) {
            return RadioListTile<String>(
              title: Text(option),
              value: option,
              groupValue: _selectedAnswer,
              onChanged: (String? value) {
                setState(() {
                  _selectedAnswer = value;
                  _feedback = ''; // Clear feedback on new selection
                });
              },
            );
          }).toList(),
          const SizedBox(height: 20),
          Text(
            _feedback,
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: _feedback.contains('Correct') ? Colors.green : Colors.red,
            ),
          ),
          const SizedBox(height: 20),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              AnimatedButton(
                onTap: _checkAnswer,
                text: 'Check Answer',
                color: Colors.blue,
              ),
              AnimatedButton(
                onTap: _nextQuestion,
                text: _currentQuestionIndex == _questions.length - 1 ? 'Finish Quiz' : 'Next Question',
                color: Colors.green,
              ),
            ],
          ),
        ],
      ),
    );
  }
}
