import 'package:flutter/material.dart';
import 'dart:math';

class InteractiveIncreasingDecreasingFunctionAnalyzer extends StatefulWidget {
  const InteractiveIncreasingDecreasingFunctionAnalyzer({super.key});

  @override
  State<InteractiveIncreasingDecreasingFunctionAnalyzer> createState() => _InteractiveIncreasingDecreasingFunctionAnalyzerState();
}

class _InteractiveIncreasingDecreasingFunctionAnalyzerState extends State<InteractiveIncreasingDecreasingFunctionAnalyzer> {
  double _xValue = 0.0;
  final TextEditingController _functionController = TextEditingController(text: 'x*x*x - 3*x'); // Default function x^3 - 3x

  double _function(double x) {
    try {
      if (_functionController.text.contains('x*x*x - 3*x')) {
        return x * x * x - 3 * x;
      } else if (_functionController.text.contains('sin(x)')) {
        return sin(x);
      } else if (_functionController.text.contains('e^x')) {
        return exp(x);
      }
      return double.nan;
    } catch (e) {
      return double.nan;
    }
  }

  double _derivative(double x) {
    // For f(x) = x^3 - 3x, f'(x) = 3x^2 - 3
    if (_functionController.text.contains('x*x*x - 3*x')) {
      return 3 * x * x - 3;
    } else if (_functionController.text.contains('sin(x)')) {
      return cos(x);
    } else if (_functionController.text.contains('e^x')) {
      return exp(x);
    }
    return double.nan;
  }

  String _getFunctionBehavior(double x) {
    double deriv = _derivative(x);
    if (deriv.isNaN) {
      return 'Undefined';
    } else if (deriv > 0.01) {
      return 'Increasing';
    } else if (deriv < -0.01) {
      return 'Decreasing';
    } else {
      return 'Constant / Local Extremum';
    }
  }

  @override
  Widget build(BuildContext context) {
    String behavior = _getFunctionBehavior(_xValue);

    return Card(
      margin: const EdgeInsets.all(16.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Interactive Increasing/Decreasing Function Analyzer',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            TextField(
              controller: _functionController,
              decoration: InputDecoration(
                labelText: 'Enter Function f(x) (e.g., x*x*x - 3*x, sin(x))',
                border: OutlineInputBorder(),
              ),
              onChanged: (value) {
                setState(() {});
              },
            ),
            const SizedBox(height: 20),
            Text(
              'Current x: ${_xValue.toStringAsFixed(2)}',
              style: TextStyle(fontSize: 16),
            ),
            Slider(
              value: _xValue,
              min: -3.0,
              max: 3.0,
              divisions: 60,
              onChanged: (newValue) {
                setState(() {
                  _xValue = newValue;
                });
              },
            ),
            const SizedBox(height: 10),
            Text(
              'f\'(${_xValue.toStringAsFixed(2)}): ${_derivative(_xValue).toStringAsFixed(2)}',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: Colors.blue),
            ),
            const SizedBox(height: 20),
            Text(
              'Function is: $behavior',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: behavior == 'Increasing' ? Colors.green : (behavior == 'Decreasing' ? Colors.red : Colors.orange),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            CustomPaint(
              size: Size(300, 200),
              painter: _FunctionBehaviorGraphPainter(_function, _xValue, _derivative),
            ),
          ],
        ),
      ),
    );
  }
}

class _FunctionBehaviorGraphPainter extends CustomPainter {
  final Function(double) _function;
  final double _x;
  final Function(double) _derivative;

  _FunctionBehaviorGraphPainter(this._function, this._x, this._derivative);

  @override
  void paint(Canvas canvas, Size size) {
    final Paint paint = Paint()
      ..color = Colors.blue
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    final Paint pointPaint = Paint()
      ..color = Colors.red
      ..strokeWidth = 8
      ..style = PaintingStyle.fill;

    final Paint tangentLinePaint = Paint()
      ..color = Colors.green
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    final double minX = -3.0;
    final double maxX = 3.0;
    final double minY = -10.0;
    final double maxY = 10.0;

    final double rangeX = maxX - minX;
    final double rangeY = maxY - minY;

    final double scaleX = size.width / rangeX;
    final double scaleY = size.height / rangeY;

    Offset toCanvas(double x, double y) {
      final double canvasX = (x - minX) * scaleX;
      final double canvasY = size.height - (y - minY) * scaleY;
      return Offset(canvasX, canvasY);
    }

    // Draw axes
    canvas.drawLine(toCanvas(minX, 0), toCanvas(maxX, 0), paint..color = Colors.grey); // X-axis
    canvas.drawLine(toCanvas(0, minY), toCanvas(0, maxY), paint..color = Colors.grey); // Y-axis

    // Draw function graph
    final Path path = Path();
    bool firstPoint = true;
    for (double i = minX; i <= maxX; i += 0.05) {
      double xVal = i;
      double yVal = _function(xVal);
      if (!yVal.isNaN && yVal.isFinite) {
        if (firstPoint) {
          path.moveTo(toCanvas(xVal, yVal).dx, toCanvas(xVal, yVal).dy);
          firstPoint = false;
        } else {
          path.lineTo(toCanvas(xVal, yVal).dx, toCanvas(xVal, yVal).dy);
        }
      } else {
        firstPoint = true;
      }
    }
    canvas.drawPath(path, paint);

    // Draw point (x, f(x))
    double y = _function(_x);
    if (!y.isNaN && y.isFinite) {
      canvas.drawCircle(toCanvas(_x, y), 4, pointPaint);

      // Draw tangent line
      double slope = _derivative(_x);
      if (!slope.isNaN && slope.isFinite) {
        double xTangent1 = minX;
        double yTangent1 = slope * (xTangent1 - _x) + y;
        double xTangent2 = maxX;
        double yTangent2 = slope * (xTangent2 - _x) + y;

        canvas.drawLine(toCanvas(xTangent1, yTangent1), toCanvas(xTangent2, yTangent2), tangentLinePaint);
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    _FunctionBehaviorGraphPainter oldPainter = oldDelegate as _FunctionBehaviorGraphPainter;
    return oldPainter._x != _x;
  }
}
