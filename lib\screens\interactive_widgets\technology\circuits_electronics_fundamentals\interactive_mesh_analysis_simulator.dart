import 'package:flutter/material.dart';
import 'package:resonance/utils/page_transitions.dart';
import 'package:resonance/widgets/animated_button.dart';

class InteractiveMeshAnalysisSimulator extends StatefulWidget {
  const InteractiveMeshAnalysisSimulator({super.key});

  @override
  State<InteractiveMeshAnalysisSimulator> createState() => _InteractiveMeshAnalysisSimulatorState();
}

class _InteractiveMeshAnalysisSimulatorState extends State<InteractiveMeshAnalysisSimulator> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Interactive Mesh Analysis Simulator'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            Navigator.of(context).pop();
          },
        ),
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Text(
                'This is the Interactive Mesh Analysis Simulator.',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 18),
              ),
              const SizedBox(height: 30),
              AnimatedButton(
                onTap: () {
                  // Add functionality for the button here
                },
                text: 'Explore Mesh Analysis',
                color: Colors.blueAccent,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
