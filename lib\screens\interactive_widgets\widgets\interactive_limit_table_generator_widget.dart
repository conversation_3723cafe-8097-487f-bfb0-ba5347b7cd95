import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that generates a table of function values to explore limits.
class InteractiveLimitTableGeneratorWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveLimitTableGeneratorWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  /// Factory constructor to create the widget from JSON data
  factory InteractiveLimitTableGeneratorWidget.fromData(Map<String, dynamic> data) {
    return InteractiveLimitTableGeneratorWidget(
      data: data,
    );
  }

  @override
  State<InteractiveLimitTableGeneratorWidget> createState() => _InteractiveLimitTableGeneratorWidgetState();
}

class _InteractiveLimitTableGeneratorWidgetState extends State<InteractiveLimitTableGeneratorWidget> {
  late double _limitPointX;
  late String _functionExpression;

  // Function to evaluate (e.g., f(x) = sin(x)/x)
  double _function(double x) {
    // This can be made more robust with expression parsing
    if (x == 0.0) return 1.0; // Handle sin(x)/x at x=0
    return math.sin(x) / x;
  }

  // Colors
  late Color _primaryColor;
  late Color _backgroundColor;
  late Color _textColor;

  @override
  void initState() {
    super.initState();
    _limitPointX = widget.data['limit_point_x']?.toDouble() ?? 0.0;
    _functionExpression = widget.data['function_expression'] ?? 'sin(x) / x';

    _primaryColor = _parseColor(widget.data['primary_color']) ?? Colors.blue;
    _backgroundColor = _parseColor(widget.data['background_color']) ?? Colors.white;
    _textColor = _parseColor(widget.data['text_color']) ?? Colors.black87;
  }

  Color? _parseColor(String? hexString) {
    if (hexString == null) return null;
    hexString = hexString.replaceAll('#', '');
    if (hexString.length == 6) {
      hexString = 'FF$hexString';
    }
    try {
      return Color(int.parse(hexString, radix: 16));
    } catch (e) {
      return null;
    }
  }

  List<Map<String, double>> _generateTableData() {
    List<Map<String, double>> data = [];
    List<double> xValues = [
      _limitPointX - 0.1,
      _limitPointX - 0.01,
      _limitPointX - 0.001,
      _limitPointX - 0.0001,
      _limitPointX + 0.0001,
      _limitPointX + 0.001,
      _limitPointX + 0.01,
      _limitPointX + 0.1,
    ];

    for (double x in xValues) {
      double y = _function(x);
      data.add({'x': x, 'f(x)': y});
    }
    return data;
  }

  @override
  Widget build(BuildContext context) {
    final tableData = _generateTableData();

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _backgroundColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.data['title'] ?? 'Limit Table Generator',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),
          const SizedBox(height: 8),
          if (widget.data['description'] != null)
            Text(
              widget.data['description'],
              style: TextStyle(
                fontSize: 14,
                color: _textColor.withOpacity(0.8),
              ),
            ),
          const SizedBox(height: 16),

          Text(
            'Function: f(x) = $_functionExpression',
            style: TextStyle(color: _textColor, fontSize: 16, fontStyle: FontStyle.italic),
          ),
          Text(
            'Approaching x = ${_limitPointX.toStringAsFixed(1)}',
            style: TextStyle(color: _textColor, fontSize: 16, fontStyle: FontStyle.italic),
          ),
          const SizedBox(height: 16),

          Center(
            child: Container(
              decoration: BoxDecoration(
                border: Border.all(color: _primaryColor.withOpacity(0.5)),
                borderRadius: BorderRadius.circular(8),
              ),
              child: DataTable(
                columnSpacing: 24,
                columns: [
                  DataColumn(
                    label: Text(
                      'x',
                      style: TextStyle(fontWeight: FontWeight.bold, color: _textColor),
                    ),
                  ),
                  DataColumn(
                    label: Text(
                      'f(x)',
                      style: TextStyle(fontWeight: FontWeight.bold, color: _textColor),
                    ),
                  ),
                ],
                rows: tableData.map((row) {
                  return DataRow(
                    cells: [
                      DataCell(Text(row['x']!.toStringAsFixed(4), style: TextStyle(color: _textColor))),
                      DataCell(Text(row['f(x)']!.toStringAsFixed(4), style: TextStyle(color: _primaryColor, fontWeight: FontWeight.bold))),
                    ],
                  );
                }).toList(),
              ),
            ),
          ),

          // Widget name tag
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'InteractiveLimitTableGenerator',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }
}
