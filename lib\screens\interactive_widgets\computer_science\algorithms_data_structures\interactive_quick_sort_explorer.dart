import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:resonance_app/services/interactive_widget_service.dart';
import 'package:resonance_app/widgets/animated_button.dart';
import 'dart:math';

class InteractiveQuickSortExplorer extends StatefulWidget {
  final String widgetId;

  const InteractiveQuickSortExplorer({Key? key, required this.widgetId}) : super(key: key);

  @override
  _InteractiveQuickSortExplorerState createState() => _InteractiveQuickSortExplorerState();
}

class _InteractiveQuickSortExplorerState extends State<InteractiveQuickSortExplorer> {
  List<int> _numbers = [];
  int _pivotIndex = -1;
  int _leftPointer = -1;
  int _rightPointer = -1;
  bool _isSorting = false;
  String _message = '';
  int _arraySize = 10;
  double _animationSpeed = 500; // milliseconds

  @override
  void initState() {
    super.initState();
    _loadState();
    _generateRandomArray();
  }

  void _loadState() async {
    final service = Provider.of<InteractiveWidgetService>(context, listen: false);
    final savedState = await service.getWidgetState(widget.widgetId);
    if (savedState != null) {
      setState(() {
        _numbers = List<int>.from(savedState['numbers'] ?? []);
        _pivotIndex = savedState['pivotIndex'] ?? -1;
        _leftPointer = savedState['leftPointer'] ?? -1;
        _rightPointer = savedState['rightPointer'] ?? -1;
        _isSorting = savedState['isSorting'] ?? false;
        _message = savedState['message'] ?? '';
        _arraySize = savedState['arraySize'] ?? 10;
        _animationSpeed = savedState['animationSpeed'] ?? 500;
      });
    }
  }

  void _saveState() {
    final service = Provider.of<InteractiveWidgetService>(context, listen: false);
    service.saveWidgetState(widget.widgetId, {
      'numbers': _numbers,
      'pivotIndex': _pivotIndex,
      'leftPointer': _leftPointer,
      'rightPointer': _rightPointer,
      'isSorting': _isSorting,
      'message': _message,
      'arraySize': _arraySize,
      'animationSpeed': _animationSpeed,
    });
  }

  void _generateRandomArray() {
    setState(() {
      _numbers = List.generate(_arraySize, (index) => Random().nextInt(90) + 10); // Numbers between 10 and 99
      _pivotIndex = -1;
      _leftPointer = -1;
      _rightPointer = -1;
      _isSorting = false;
      _message = 'New array generated.';
    });
    _saveState();
  }

  Future<void> _quickSort(List<int> arr, int low, int high) async {
    if (low < high) {
      int pi = await _partition(arr, low, high);

      await _quickSort(arr, low, pi - 1);
      await _quickSort(arr, pi + 1, high);
    }
  }

  Future<int> _partition(List<int> arr, int low, int high) async {
    int pivot = arr[high];
    int i = (low - 1);

    setState(() {
      _pivotIndex = high;
      _message = 'Pivot selected: ${pivot} at index $high';
    });
    _saveState();
    await Future.delayed(Duration(milliseconds: _animationSpeed.toInt()));

    for (int j = low; j < high; j++) {
      setState(() {
        _leftPointer = i;
        _rightPointer = j;
        _message = 'Comparing ${arr[j]} with pivot ${pivot}';
      });
      _saveState();
      await Future.delayed(Duration(milliseconds: _animationSpeed.toInt()));

      if (arr[j] < pivot) {
        i++;
        int temp = arr[i];
        arr[i] = arr[j];
        arr[j] = temp;
        setState(() {
          _numbers = List.from(arr);
          _message = 'Swapping ${arr[j]} and ${arr[i]}';
        });
        _saveState();
        await Future.delayed(Duration(milliseconds: _animationSpeed.toInt()));
      }
    }

    int temp = arr[i + 1];
    arr[i + 1] = arr[high];
    arr[high] = temp;

    setState(() {
      _numbers = List.from(arr);
      _message = 'Placing pivot ${pivot} at its correct position';
      _pivotIndex = i + 1;
      _leftPointer = -1;
      _rightPointer = -1;
    });
    _saveState();
    await Future.delayed(Duration(milliseconds: _animationSpeed.toInt()));

    return i + 1;
  }

  Future<void> _startQuickSort() async {
    if (_isSorting) return;

    setState(() {
      _isSorting = true;
      _message = 'Quick Sort started...';
    });
    _saveState();

    await _quickSort(_numbers, 0, _numbers.length - 1);

    setState(() {
      _pivotIndex = -1;
      _leftPointer = -1;
      _rightPointer = -1;
      _isSorting = false;
      _message = 'Quick Sort completed!';
    });
    _saveState();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Interactive Quick Sort Explorer',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                child: Slider(
                  value: _arraySize.toDouble(),
                  min: 5,
                  max: 20,
                  divisions: 15,
                  label: _arraySize.round().toString(),
                  onChanged: (double value) {
                    setState(() {
                      _arraySize = value.round();
                    });
                  },
                  onChangeEnd: (double value) {
                    _generateRandomArray();
                  },
                ),
              ),
              Text('Array Size: $_arraySize'),
            ],
          ),
          Row(
            children: [
              Expanded(
                child: Slider(
                  value: _animationSpeed,
                  min: 50,
                  max: 1000,
                  divisions: 19,
                  label: _animationSpeed.round().toString(),
                  onChanged: (double value) {
                    setState(() {
                      _animationSpeed = value;
                    });
                  },
                ),
              ),
              Text('Speed: ${_animationSpeed.round()}ms'),
            ],
          ),
          const SizedBox(height: 10),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              AnimatedButton(
                onTap: _generateRandomArray,
                text: 'Generate New Array',
                color: Colors.blue,
              ),
              AnimatedButton(
                onTap: _isSorting ? null : _startQuickSort,
                text: _isSorting ? 'Sorting...' : 'Start Sort',
                color: _isSorting ? Colors.grey : Colors.green,
              ),
            ],
          ),
          const SizedBox(height: 20),
          Text(
            'Array:',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 10),
          Container(
            height: 100,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: _numbers.asMap().entries.map((entry) {
                int index = entry.key;
                int number = entry.value;
                Color color = Colors.grey;
                if (index == _pivotIndex) {
                  color = Colors.purple; // Pivot
                } else if (index == _leftPointer || index == _rightPointer) {
                  color = Colors.red; // Pointers
                } else if (!_isSorting) {
                  color = Colors.green; // Sorted
                }

                return Container(
                  margin: const EdgeInsets.symmetric(horizontal: 2),
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: color,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    number.toString(),
                    style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                  ),
                );
              }).toList(),
            ),
          ),
          const SizedBox(height: 20),
          Text(
            'Message: $_message',
            style: Theme.of(context).textTheme.bodyLarge,
          ),
        ],
      ),
    );
  }
}
