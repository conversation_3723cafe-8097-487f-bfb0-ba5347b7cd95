import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that allows users to explore simple differential equations.
class InteractiveDifferentialEquationExplorerWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveDifferentialEquationExplorerWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  /// Factory constructor to create the widget from JSON data
  factory InteractiveDifferentialEquationExplorerWidget.fromData(Map<String, dynamic> data) {
    return InteractiveDifferentialEquationExplorerWidget(
      data: data,
    );
  }

  @override
  State<InteractiveDifferentialEquationExplorerWidget> createState() => _InteractiveDifferentialEquationExplorerWidgetState();
}

class _InteractiveDifferentialEquationExplorerWidgetState extends State<InteractiveDifferentialEquationExplorerWidget> {
  late double _k; // Growth/decay constant
  late double _y0; // Initial condition y(0)

  // Solution to dy/dx = ky is y(x) = y0 * e^(kx)
  double _solutionFunction(double x) {
    return _y0 * math.exp(_k * x);
  }

  // Colors
  late Color _primaryColor; // For the solution curve
  late Color _backgroundColor;
  late Color _textColor;

  @override
  void initState() {
    super.initState();
    _k = widget.data['initial_k']?.toDouble() ?? 0.5;
    _y0 = widget.data['initial_y0']?.toDouble() ?? 1.0;

    _primaryColor = _parseColor(widget.data['primary_color']) ?? Colors.blue;
    _backgroundColor = _parseColor(widget.data['background_color']) ?? Colors.white;
    _textColor = _parseColor(widget.data['text_color']) ?? Colors.black87;
  }

  Color? _parseColor(String? hexString) {
    if (hexString == null) return null;
    hexString = hexString.replaceAll('#', '');
    if (hexString.length == 6) {
      hexString = 'FF$hexString';
    }
    try {
      return Color(int.parse(hexString, radix: 16));
    } catch (e) {
      return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _backgroundColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.data['title'] ?? 'Differential Equation Explorer',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),
          const SizedBox(height: 8),
          if (widget.data['description'] != null)
            Text(
              widget.data['description'],
              style: TextStyle(
                fontSize: 14,
                color: _textColor.withOpacity(0.8),
              ),
            ),
          const SizedBox(height: 16),

          Text(
            'Equation: dy/dx = ky',
            style: TextStyle(color: _textColor, fontSize: 16, fontStyle: FontStyle.italic),
          ),
          Text(
            'Solution: y(x) = y₀e^(kx)',
            style: TextStyle(color: _textColor, fontSize: 16, fontStyle: FontStyle.italic),
          ),
          const SizedBox(height: 16),

          Text(
            'Growth/Decay Constant (k): ${_k.toStringAsFixed(2)}',
            style: TextStyle(color: _primaryColor, fontSize: 16, fontWeight: FontWeight.bold),
          ),
          Slider(
            value: _k,
            min: -1.0,
            max: 1.0,
            divisions: 200,
            label: _k.toStringAsFixed(2),
            activeColor: _primaryColor,
            onChanged: (value) {
              setState(() {
                _k = value;
              });
            },
          ),
          const SizedBox(height: 8),

          Text(
            'Initial Condition (y₀): ${_y0.toStringAsFixed(1)}',
            style: TextStyle(color: _primaryColor, fontSize: 16, fontWeight: FontWeight.bold),
          ),
          Slider(
            value: _y0,
            min: 0.1,
            max: 10.0,
            divisions: 99,
            label: _y0.toStringAsFixed(1),
            activeColor: _primaryColor,
            onChanged: (value) {
              setState(() {
                _y0 = value;
              });
            },
          ),
          const SizedBox(height: 16),

          Container(
            height: 250,
            child: CustomPaint(
              painter: DifferentialEquationPainter(
                solutionFunction: _solutionFunction,
                k: _k,
                y0: _y0,
                primaryColor: _primaryColor,
                textColor: _textColor,
              ),
              child: Container(),
            ),
          ),

          // Widget name tag
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'InteractiveDifferentialEquationExplorer',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }
}

class DifferentialEquationPainter extends CustomPainter {
  final Function(double) solutionFunction;
  final double k;
  final double y0;
  final Color primaryColor;
  final Color textColor;

  DifferentialEquationPainter({
    required this.solutionFunction,
    required this.k,
    required this.y0,
    required this.primaryColor,
    required this.textColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint();
    final textPainter = TextPainter(textDirection: TextDirection.ltr);
    final textStyle = TextStyle(color: textColor, fontSize: 10);

    // Define plot area padding
    const double padding = 30.0;
    final double plotWidth = size.width - 2 * padding;
    final double plotHeight = size.height - 2 * padding;

    // Determine x and y ranges for plotting
    final double minPlotX = -3.0;
    final double maxPlotX = 3.0;
    final double minPlotY = 0.0;
    final double maxPlotY = 10.0; // Adjust based on expected y values

    // Dynamic y-axis scaling based on y0 and k
    if (y0 > 0) {
      if (k > 0) { // Growth
        maxPlotY = solutionFunction(maxPlotX) * 1.2;
      } else { // Decay or constant
        maxPlotY = y0 * 1.2;
      }
    } else { // y0 <= 0
      if (k > 0) { // Growth towards negative infinity
        minPlotY = solutionFunction(maxPlotX) * 1.2;
      } else { // Decay towards 0 or constant
        minPlotY = y0 * 1.2;
      }
    }
    if (maxPlotY.isNaN || maxPlotY.isInfinite || maxPlotY == 0) maxPlotY = 10.0;
    if (minPlotY.isNaN || minPlotY.isInfinite || minPlotY == 0) minPlotY = -10.0;


    // Scale factors
    final double xScale = plotWidth / (maxPlotX - minPlotX);
    final double yScale = plotHeight / (maxPlotY - minPlotY);

    // Helper to convert plot coordinates to canvas coordinates
    Offset toCanvas(double x, double y) {
      final canvasX = padding + (x - minPlotX) * xScale;
      final canvasY = size.height - padding - (y - minPlotY) * yScale;
      return Offset(canvasX, canvasY);
    }

    // Draw axes
    paint.color = Colors.grey;
    paint.strokeWidth = 1.0;
    // X-axis
    canvas.drawLine(toCanvas(minPlotX, 0), toCanvas(maxPlotX, 0), paint);
    // Y-axis
    canvas.drawLine(toCanvas(0, minPlotY), toCanvas(0, maxPlotY), paint);

    // Draw solution curve
    paint.color = primaryColor;
    paint.style = PaintingStyle.stroke;
    paint.strokeWidth = 2.0;

    final path = Path();
    bool firstPoint = true;
    for (double x = minPlotX; x <= maxPlotX; x += (maxPlotX - minPlotX) / 100) {
      final y = solutionFunction(x);
      if (y.isFinite) { // Only draw if y is a finite number
        final point = toCanvas(x, y);
        if (firstPoint) {
          path.moveTo(point.dx, point.dy);
          firstPoint = false;
        } else {
          path.lineTo(point.dx, point.dy);
        }
      } else {
        firstPoint = true; // Reset for next segment if discontinuity
      }
    }
    canvas.drawPath(path, paint);

    // Draw initial condition point (0, y0)
    paint.color = primaryColor;
    paint.style = PaintingStyle.fill;
    canvas.drawCircle(toCanvas(0, y0), 5, paint);

    // Draw labels for axes
    textPainter.text = TextSpan(text: 'x', style: textStyle);
    textPainter.layout();
    textPainter.paint(canvas, Offset(size.width - padding + 5, toCanvas(0, 0).dy - textPainter.height / 2));

    textPainter.text = TextSpan(text: 'y', style: textStyle);
    textPainter.layout();
    textPainter.paint(canvas, Offset(toCanvas(0, maxPlotY).dx - textPainter.width / 2, padding - textPainter.height - 5));
  }

  @override
  bool shouldRepaint(covariant DifferentialEquationPainter oldDelegate) {
    return oldDelegate.k != k ||
           oldDelegate.y0 != y0 ||
           oldDelegate.primaryColor != primaryColor ||
           oldDelegate.textColor != textColor;
  }
}
