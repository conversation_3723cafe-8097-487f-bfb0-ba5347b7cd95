import 'package:flutter/material.dart';
import 'dart:math' as math;
import 'package:collection/collection.dart'; // For median calculation

/// A widget that allows users to perform basic data analysis and visualization.
class InteractiveDataAnalysisToolWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveDataAnalysisToolWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  /// Factory constructor to create the widget from JSON data
  factory InteractiveDataAnalysisToolWidget.fromData(Map<String, dynamic> data) {
    return InteractiveDataAnalysisToolWidget(
      data: data,
    );
  }

  @override
  State<InteractiveDataAnalysisToolWidget> createState() => _InteractiveDataAnalysisToolWidgetState();
}

class _InteractiveDataAnalysisToolWidgetState extends State<InteractiveDataAnalysisToolWidget> {
  List<double> _data = [];
  final TextEditingController _inputController = TextEditingController();

  // Calculated statistics
  double _mean = 0.0;
  double _median = 0.0;
  double _stdDev = 0.0;

  // Histogram data
  List<int> _binCounts = [];
  double _binWidth = 0.0;
  double _minDataValue = 0.0;
  double _maxDataValue = 0.0;

  // Colors
  late Color _primaryColor;
  late Color _secondaryColor;
  late Color _backgroundColor;
  late Color _textColor;

  @override
  void initState() {
    super.initState();
    _primaryColor = _parseColor(widget.data['primary_color']) ?? Colors.blue;
    _secondaryColor = _parseColor(widget.data['secondary_color']) ?? Colors.orange;
    _backgroundColor = _parseColor(widget.data['background_color']) ?? Colors.white;
    _textColor = _parseColor(widget.data['text_color']) ?? Colors.black87;

    // Initialize with some default data if provided
    if (widget.data['initial_data'] != null && widget.data['initial_data'] is List) {
      _data = List<double>.from(widget.data['initial_data'].map((e) => e.toDouble()));
      _analyzeData();
    }
  }

  @override
  void dispose() {
    _inputController.dispose();
    super.dispose();
  }

  Color? _parseColor(String? hexString) {
    if (hexString == null) return null;
    hexString = hexString.replaceAll('#', '');
    if (hexString.length == 6) {
      hexString = 'FF$hexString';
    }
    try {
      return Color(int.parse(hexString, radix: 16));
    } catch (e) {
      return null;
    }
  }

  void _addDataPoint() {
    final text = _inputController.text;
    final value = double.tryParse(text);
    if (value != null) {
      setState(() {
        _data.add(value);
        _data.sort(); // Keep data sorted for median
        _analyzeData();
        _inputController.clear();
      });
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter a valid number.')),
      );
    }
  }

  void _removeDataPoint(int index) {
    setState(() {
      _data.removeAt(index);
      _analyzeData();
    });
  }

  void _analyzeData() {
    if (_data.isEmpty) {
      _mean = 0.0;
      _median = 0.0;
      _stdDev = 0.0;
      _binCounts = [];
      _binWidth = 0.0;
      _minDataValue = 0.0;
      _maxDataValue = 0.0;
      return;
    }

    // Calculate Mean
    _mean = _data.sum / _data.length;

    // Calculate Median
    _median = _data.median;

    // Calculate Standard Deviation
    final sumOfSquaredDifferences = _data.map((x) => math.pow(x - _mean, 2)).sum;
    _stdDev = math.sqrt(sumOfSquaredDifferences / _data.length); // Population std dev

    // Calculate Histogram Bins
    _minDataValue = _data.reduce(math.min);
    _maxDataValue = _data.reduce(math.max);

    const int numberOfBins = 5;
    if (_data.length < 2 || _maxDataValue == _minDataValue) {
      _binWidth = 1.0;
      _binCounts = [ _data.length ]; // All in one bin
      _minDataValue = _data.isNotEmpty ? _data[0] - 0.5 : 0.0;
      _maxDataValue = _data.isNotEmpty ? _data[0] + 0.5 : 1.0;
    } else {
      _binWidth = (_maxDataValue - _minDataValue) / numberOfBins;
      _binCounts = List.filled(numberOfBins, 0);

      for (final value in _data) {
        int binIndex = ((value - _minDataValue) / _binWidth).floor();
        if (binIndex >= numberOfBins) binIndex = numberOfBins - 1; // Handle max value
        if (binIndex < 0) binIndex = 0; // Handle min value edge case
        _binCounts[binIndex]++;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _backgroundColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.data['title'] ?? 'Interactive Data Analysis Tool',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),
          const SizedBox(height: 8),
          if (widget.data['description'] != null)
            Text(
              widget.data['description'],
              style: TextStyle(
                fontSize: 14,
                color: _textColor.withOpacity(0.8),
              ),
            ),
          const SizedBox(height: 16),

          // Data Input
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _inputController,
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    labelText: 'Enter a number',
                    border: OutlineInputBorder(),
                    labelStyle: TextStyle(color: _textColor),
                    enabledBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: _textColor.withOpacity(0.5)),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: _primaryColor),
                    ),
                  ),
                  style: TextStyle(color: _textColor),
                ),
              ),
              const SizedBox(width: 8),
              ElevatedButton(
                onPressed: _addDataPoint,
                child: const Text('Add'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: _primaryColor,
                  padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 15),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Current Data
          Text(
            'Current Data:',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),
          const SizedBox(height: 8),
          _data.isEmpty
              ? Text('No data points yet.', style: TextStyle(color: _textColor.withOpacity(0.7)))
              : Wrap(
                  spacing: 8.0,
                  runSpacing: 4.0,
                  children: List.generate(_data.length, (index) {
                    return Chip(
                      label: Text(_data[index].toStringAsFixed(1)),
                      onDeleted: () => _removeDataPoint(index),
                      deleteIconColor: _secondaryColor,
                      backgroundColor: _primaryColor.withOpacity(0.1),
                      labelStyle: TextStyle(color: _primaryColor),
                    );
                  }),
                ),
          const SizedBox(height: 16),

          // Statistics Display
          Text(
            'Statistics:',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),
          const SizedBox(height: 8),
          _data.isEmpty
              ? Text('Add data to see statistics.', style: TextStyle(color: _textColor.withOpacity(0.7)))
              : Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Mean: ${_mean.toStringAsFixed(2)}', style: TextStyle(color: _textColor)),
                    Text('Median: ${_median.toStringAsFixed(2)}', style: TextStyle(color: _textColor)),
                    Text('Standard Deviation: ${_stdDev.toStringAsFixed(2)}', style: TextStyle(color: _textColor)),
                  ],
                ),
          const SizedBox(height: 16),

          // Histogram Visualization
          Text(
            'Histogram:',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),
          const SizedBox(height: 8),
          _data.isEmpty
              ? Text('Add data to see histogram.', style: TextStyle(color: _textColor.withOpacity(0.7)))
              : Container(
                  height: 150,
                  child: CustomPaint(
                    painter: HistogramPainter(
                      binCounts: _binCounts,
                      minDataValue: _minDataValue,
                      maxDataValue: _maxDataValue,
                      binWidth: _binWidth,
                      barColor: _secondaryColor,
                      textColor: _textColor,
                    ),
                    child: Container(),
                  ),
                ),

          // Widget name tag
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'InteractiveDataAnalysisTool',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }
}

class HistogramPainter extends CustomPainter {
  final List<int> binCounts;
  final double minDataValue;
  final double maxDataValue;
  final double binWidth;
  final Color barColor;
  final Color textColor;

  HistogramPainter({
    required this.binCounts,
    required this.minDataValue,
    required this.maxDataValue,
    required this.binWidth,
    required this.barColor,
    required this.textColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (binCounts.isEmpty) return;

    final paint = Paint();
    final textPainter = TextPainter(textDirection: TextDirection.ltr);
    final textStyle = TextStyle(color: textColor, fontSize: 10);

    final double maxCount = binCounts.reduce(math.max).toDouble();
    if (maxCount == 0) return;

    final double barWidth = size.width / binCounts.length;
    final double scaleY = size.height / maxCount;

    // Draw bars
    for (int i = 0; i < binCounts.length; i++) {
      final count = binCounts[i];
      final barHeight = count * scaleY;
      final x = i * barWidth;
      final y = size.height - barHeight;

      paint.color = barColor.withOpacity(0.7);
      canvas.drawRect(Rect.fromLTWH(x, y, barWidth * 0.8, barHeight), paint);

      // Draw bar value (count)
      textPainter.text = TextSpan(text: count.toString(), style: textStyle);
      textPainter.layout();
      textPainter.paint(canvas, Offset(x + barWidth / 2 - textPainter.width / 2, y - textPainter.height - 2));

      // Draw bin label (range)
      final binMin = minDataValue + i * binWidth;
      final binMax = binMin + binWidth;
      textPainter.text = TextSpan(text: '${binMin.toStringAsFixed(1)}-${binMax.toStringAsFixed(1)}', style: textStyle);
      textPainter.layout();
      textPainter.paint(canvas, Offset(x + barWidth / 2 - textPainter.width / 2, size.height + 2));
    }

    // Draw x-axis line
    paint.color = textColor;
    paint.strokeWidth = 1.0;
    canvas.drawLine(Offset(0, size.height), Offset(size.width, size.height), paint);
  }

  @override
  bool shouldRepaint(covariant HistogramPainter oldDelegate) {
    return oldDelegate.binCounts != binCounts ||
           oldDelegate.minDataValue != minDataValue ||
           oldDelegate.maxDataValue != maxDataValue ||
           oldDelegate.binWidth != binWidth;
  }
}
