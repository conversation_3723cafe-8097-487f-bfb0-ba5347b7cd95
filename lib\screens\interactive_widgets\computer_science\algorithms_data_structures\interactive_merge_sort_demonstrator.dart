import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:resonance_app/services/interactive_widget_service.dart';
import 'package:resonance_app/widgets/animated_button.dart';
import 'dart:math';

class InteractiveMergeSortDemonstrator extends StatefulWidget {
  final String widgetId;

  const InteractiveMergeSortDemonstrator({Key? key, required this.widgetId}) : super(key: key);

  @override
  _InteractiveMergeSortDemonstratorState createState() => _InteractiveMergeSortDemonstratorState();
}

class _InteractiveMergeSortDemonstratorState extends State<InteractiveMergeSortDemonstrator> {
  List<int> _numbers = [];
  List<int> _auxiliaryArray = [];
  int _leftIndex = -1;
  int _rightIndex = -1;
  int _mergeIndex = -1;
  bool _isSorting = false;
  String _message = '';
  int _arraySize = 10;
  double _animationSpeed = 500; // milliseconds

  @override
  void initState() {
    super.initState();
    _loadState();
    _generateRandomArray();
  }

  void _loadState() async {
    final service = Provider.of<InteractiveWidgetService>(context, listen: false);
    final savedState = await service.getWidgetState(widget.widgetId);
    if (savedState != null) {
      setState(() {
        _numbers = List<int>.from(savedState['numbers'] ?? []);
        _auxiliaryArray = List<int>.from(savedState['auxiliaryArray'] ?? []);
        _leftIndex = savedState['leftIndex'] ?? -1;
        _rightIndex = savedState['rightIndex'] ?? -1;
        _mergeIndex = savedState['mergeIndex'] ?? -1;
        _isSorting = savedState['isSorting'] ?? false;
        _message = savedState['message'] ?? '';
        _arraySize = savedState['arraySize'] ?? 10;
        _animationSpeed = savedState['animationSpeed'] ?? 500;
      });
    }
  }

  void _saveState() {
    final service = Provider.of<InteractiveWidgetService>(context, listen: false);
    service.saveWidgetState(widget.widgetId, {
      'numbers': _numbers,
      'auxiliaryArray': _auxiliaryArray,
      'leftIndex': _leftIndex,
      'rightIndex': _rightIndex,
      'mergeIndex': _mergeIndex,
      'isSorting': _isSorting,
      'message': _message,
      'arraySize': _arraySize,
      'animationSpeed': _animationSpeed,
    });
  }

  void _generateRandomArray() {
    setState(() {
      _numbers = List.generate(_arraySize, (index) => Random().nextInt(90) + 10); // Numbers between 10 and 99
      _auxiliaryArray = List.filled(_arraySize, 0);
      _leftIndex = -1;
      _rightIndex = -1;
      _mergeIndex = -1;
      _isSorting = false;
      _message = 'New array generated.';
    });
    _saveState();
  }

  Future<void> _mergeSort(List<int> arr, int l, int r) async {
    if (l < r) {
      int m = (l + r) ~/ 2;

      setState(() {
        _message = 'Dividing array: [${arr.sublist(l, m + 1).join(', ')}] and [${arr.sublist(m + 1, r + 1).join(', ')}]';
      });
      _saveState();
      await Future.delayed(Duration(milliseconds: _animationSpeed.toInt()));

      await _mergeSort(arr, l, m);
      await _mergeSort(arr, m + 1, r);
      await _merge(arr, l, m, r);
    }
  }

  Future<void> _merge(List<int> arr, int l, int m, int r) async {
    int i = l;
    int j = m + 1;
    int k = l;

    setState(() {
      _message = 'Merging sub-arrays from index $l to $r';
    });
    _saveState();
    await Future.delayed(Duration(milliseconds: _animationSpeed.toInt()));

    while (i <= m && j <= r) {
      setState(() {
        _leftIndex = i;
        _rightIndex = j;
        _mergeIndex = k;
        _message = 'Comparing ${arr[i]} and ${arr[j]}';
      });
      _saveState();
      await Future.delayed(Duration(milliseconds: _animationSpeed.toInt()));

      if (arr[i] <= arr[j]) {
        _auxiliaryArray[k] = arr[i];
        i++;
      } else {
        _auxiliaryArray[k] = arr[j];
        j++;
      }
      k++;
    }

    while (i <= m) {
      setState(() {
        _leftIndex = i;
        _mergeIndex = k;
        _message = 'Copying remaining element ${arr[i]} from left sub-array';
      });
      _saveState();
      await Future.delayed(Duration(milliseconds: _animationSpeed.toInt()));
      _auxiliaryArray[k] = arr[i];
      i++;
      k++;
    }

    while (j <= r) {
      setState(() {
        _rightIndex = j;
        _mergeIndex = k;
        _message = 'Copying remaining element ${arr[j]} from right sub-array';
      });
      _saveState();
      await Future.delayed(Duration(milliseconds: _animationSpeed.toInt()));
      _auxiliaryArray[k] = arr[j];
      j++;
      k++;
    }

    for (int x = l; x <= r; x++) {
      arr[x] = _auxiliaryArray[x];
      setState(() {
        _numbers = List.from(arr); // Update main array for visualization
      });
      _saveState();
      await Future.delayed(Duration(milliseconds: (_animationSpeed / 2).toInt()));
    }
  }

  Future<void> _startMergeSort() async {
    if (_isSorting) return;

    setState(() {
      _isSorting = true;
      _message = 'Merge Sort started...';
    });
    _saveState();

    await _mergeSort(_numbers, 0, _numbers.length - 1);

    setState(() {
      _leftIndex = -1;
      _rightIndex = -1;
      _mergeIndex = -1;
      _isSorting = false;
      _message = 'Merge Sort completed!';
    });
    _saveState();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Interactive Merge Sort Demonstrator',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                child: Slider(
                  value: _arraySize.toDouble(),
                  min: 5,
                  max: 20,
                  divisions: 15,
                  label: _arraySize.round().toString(),
                  onChanged: (double value) {
                    setState(() {
                      _arraySize = value.round();
                    });
                  },
                  onChangeEnd: (double value) {
                    _generateRandomArray();
                  },
                ),
              ),
              Text('Array Size: $_arraySize'),
            ],
          ),
          Row(
            children: [
              Expanded(
                child: Slider(
                  value: _animationSpeed,
                  min: 50,
                  max: 1000,
                  divisions: 19,
                  label: _animationSpeed.round().toString(),
                  onChanged: (double value) {
                    setState(() {
                      _animationSpeed = value;
                    });
                  },
                ),
              ),
              Text('Speed: ${_animationSpeed.round()}ms'),
            ],
          ),
          const SizedBox(height: 10),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              AnimatedButton(
                onTap: _generateRandomArray,
                text: 'Generate New Array',
                color: Colors.blue,
              ),
              AnimatedButton(
                onTap: _isSorting ? null : _startMergeSort,
                text: _isSorting ? 'Sorting...' : 'Start Sort',
                color: _isSorting ? Colors.grey : Colors.green,
              ),
            ],
          ),
          const SizedBox(height: 20),
          Text(
            'Array:',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 10),
          Container(
            height: 100,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: _numbers.asMap().entries.map((entry) {
                int index = entry.key;
                int number = entry.value;
                Color color = Colors.grey;
                if (index == _leftIndex || index == _rightIndex) {
                  color = Colors.red; // Elements being compared
                } else if (index == _mergeIndex) {
                  color = Colors.blue; // Element being placed in auxiliary array
                } else if (!_isSorting) {
                  color = Colors.green; // Sorted
                }

                return Container(
                  margin: const EdgeInsets.symmetric(horizontal: 2),
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: color,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    number.toString(),
                    style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                  ),
                );
              }).toList(),
            ),
          ),
          const SizedBox(height: 20),
          Text(
            'Message: $_message',
            style: Theme.of(context).textTheme.bodyLarge,
          ),
        ],
      ),
    );
  }
}
