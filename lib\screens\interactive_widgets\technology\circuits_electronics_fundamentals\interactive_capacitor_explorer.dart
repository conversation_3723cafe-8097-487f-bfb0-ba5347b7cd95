import 'package:flutter/material.dart';
import 'package:resonance_app/widgets/interactive_widget_scaffold.dart';

class InteractiveCapacitorExplorer extends StatefulWidget {
  const InteractiveCapacitorExplorer({super.key});

  @override
  State<InteractiveCapacitorExplorer> createState() => _InteractiveCapacitorExplorerState();
}

class _InteractiveCapacitorExplorerState extends State<InteractiveCapacitorExplorer> {
  double capacitance = 1.0; // Farads
  double voltage = 0.0; // Volts
  double charge = 0.0; // Coulombs

  @override
  void initState() {
    super.initState();
    _calculateCharge();
  }

  void _calculateCharge() {
    setState(() {
      charge = capacitance * voltage;
    });
  }

  @override
  Widget build(BuildContext context) {
    return InteractiveWidgetScaffold(
      title: 'Interactive Capacitor Explorer',
      description: 'Explore the relationship between capacitance, voltage, and charge (Q = CV).',
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text(
              'Capacitance (C): ${capacitance.toStringAsFixed(2)} F',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            Slider(
              value: capacitance,
              min: 0.1,
              max: 10.0,
              divisions: 99,
              label: capacitance.toStringAsFixed(2),
              onChanged: (value) {
                setState(() {
                  capacitance = value;
                  _calculateCharge();
                });
              },
            ),
            const SizedBox(height: 16.0),
            Text(
              'Voltage (V): ${voltage.toStringAsFixed(2)} V',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            Slider(
              value: voltage,
              min: 0.0,
              max: 12.0,
              divisions: 120,
              label: voltage.toStringAsFixed(2),
              onChanged: (value) {
                setState(() {
                  voltage = value;
                  _calculateCharge();
                });
              },
            ),
            const SizedBox(height: 24.0),
            Text(
              'Charge (Q): ${charge.toStringAsFixed(2)} C',
              style: Theme.of(context).textTheme.headlineSmall,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
