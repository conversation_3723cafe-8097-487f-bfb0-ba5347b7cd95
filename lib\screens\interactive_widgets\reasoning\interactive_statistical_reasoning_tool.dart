import 'package:flutter/material.dart';
import 'package:resonance/widgets/animated_button.dart';

class InteractiveStatisticalReasoningTool extends StatefulWidget {
  const InteractiveStatisticalReasoningTool({super.key});

  @override
  State<InteractiveStatisticalReasoningTool> createState() => _InteractiveStatisticalReasoningToolState();
}

class _InteractiveStatisticalReasoningToolState extends State<InteractiveStatisticalReasoningTool> {
  final TextEditingController _dataController = TextEditingController();
  String _analysisResult = '';

  void _performStatisticalAnalysis() {
    setState(() {
      final String dataText = _dataController.text;
      if (dataText.isEmpty) {
        _analysisResult = 'Please enter some data to analyze.';
        return;
      }

      final List<double> data = dataText.split(',').map((s) => double.tryParse(s.trim()) ?? double.nan).where((n) => !n.isNaN).toList();

      if (data.isEmpty) {
        _analysisResult = 'Invalid data format. Please enter numbers separated by commas.';
        return;
      }

      // Placeholder for actual statistical analysis logic
      double sum = data.fold(0, (prev, element) => prev + element);
      double mean = sum / data.length;
      data.sort();
      double median;
      if (data.length % 2 == 1) {
        median = data[data.length ~/ 2];
      } else {
        median = (data[data.length ~/ 2 - 1] + data[data.length ~/ 2]) / 2;
      }

      _analysisResult = 'Statistical Analysis Results:\n\n'
          'Data Points: ${data.length}\n'
          'Sum: ${sum.toStringAsFixed(2)}\n'
          'Mean: ${mean.toStringAsFixed(2)}\n'
          'Median: ${median.toStringAsFixed(2)}\n\n'
          'This tool demonstrates basic statistical reasoning. '
          'In a full implementation, it would include standard deviation, variance, '
          'confidence intervals, hypothesis testing, and various visualizations.';
    });
  }

  @override
  void dispose() {
    _dataController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text(
            'Statistical Reasoning Tool',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.primary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 20),
          TextField(
            controller: _dataController,
            keyboardType: TextInputType.number,
            maxLines: 3,
            decoration: InputDecoration(
              labelText: 'Enter data (comma-separated numbers)',
              hintText: 'e.g., "10, 20, 30, 40, 50"',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              filled: true,
              fillColor: Theme.of(context).cardColor,
            ),
          ),
          const SizedBox(height: 20),
          AnimatedButton(
            onTap: _performStatisticalAnalysis,
            label: 'Perform Analysis',
            color: Theme.of(context).colorScheme.secondary,
            labelColor: Theme.of(context).colorScheme.onSecondary,
          ),
          const SizedBox(height: 20),
          Expanded(
            child: SingleChildScrollView(
              child: Card(
                elevation: 4,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                color: Theme.of(context).colorScheme.surfaceVariant,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Analysis Result:',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                      const SizedBox(height: 10),
                      Text(
                        _analysisResult.isEmpty ? 'Enter data and click "Perform Analysis" to see the result.' : _analysisResult,
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
