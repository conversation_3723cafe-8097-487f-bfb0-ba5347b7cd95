import 'package:flutter/material.dart';
import 'dart:math';

class InteractiveAverageRateOfChangeCalculator extends StatefulWidget {
  const InteractiveAverageRateOfChangeCalculator({super.key});

  @override
  State<InteractiveAverageRateOfChangeCalculator> createState() => _InteractiveAverageRateOfChangeCalculatorState();
}

class _InteractiveAverageRateOfChangeCalculatorState extends State<InteractiveAverageRateOfChangeCalculator> {
  double _x1 = 1.0;
  double _x2 = 3.0;
  final TextEditingController _functionController = TextEditingController(text: 'x*x'); // Default function x^2

  double _function(double x) {
    try {
      // Basic parser for simple functions. For a robust solution, a math expression parser library would be needed.
      // This example handles x*x, x+2, sin(x)
      if (_functionController.text.contains('x*x')) {
        return x * x;
      } else if (_functionController.text.contains('x+2')) {
        return x + 2;
      } else if (_functionController.text.contains('sin(x)')) {
        return sin(x);
      }
      return double.nan; // Indicate unsupported function
    } catch (e) {
      return double.nan;
    }
  }

  double _calculateAverageRateOfChange() {
    double y1 = _function(_x1);
    double y2 = _function(_x2);

    if (y1.isNaN || y2.isNaN || _x1 == _x2) {
      return double.nan;
    }

    return (y2 - y1) / (_x2 - _x1);
  }

  @override
  Widget build(BuildContext context) {
    double y1 = _function(_x1);
    double y2 = _function(_x2);
    double averageRateOfChange = _calculateAverageRateOfChange();

    return Card(
      margin: const EdgeInsets.all(16.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Interactive Average Rate of Change Calculator',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            TextField(
              controller: _functionController,
              decoration: InputDecoration(
                labelText: 'Enter Function f(x) (e.g., x*x, x+2, sin(x))',
                border: OutlineInputBorder(),
              ),
              onChanged: (value) {
                setState(() {}); // Rebuild on function change
              },
            ),
            const SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                Column(
                  children: [
                    Text('x1: ${_x1.toStringAsFixed(1)}'),
                    Slider(
                      value: _x1,
                      min: -5.0,
                      max: 5.0,
                      divisions: 100,
                      onChanged: (newValue) {
                        setState(() {
                          _x1 = newValue;
                        });
                      },
                    ),
                    Text('f(x1): ${y1.isNaN ? 'Undefined' : y1.toStringAsFixed(2)}'),
                  ],
                ),
                Column(
                  children: [
                    Text('x2: ${_x2.toStringAsFixed(1)}'),
                    Slider(
                      value: _x2,
                      min: -5.0,
                      max: 5.0,
                      divisions: 100,
                      onChanged: (newValue) {
                        setState(() {
                          _x2 = newValue;
                        });
                      },
                    ),
                    Text('f(x2): ${y2.isNaN ? 'Undefined' : y2.toStringAsFixed(2)}'),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 20),
            Text(
              'Average Rate of Change: ${averageRateOfChange.isNaN ? 'Undefined' : averageRateOfChange.toStringAsFixed(3)}',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: Colors.blue),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            CustomPaint(
              size: Size(300, 200),
              painter: _AverageRateOfChangeGraphPainter(_function, _x1, _x2),
            ),
          ],
        ),
      ),
    );
  }
}

class _AverageRateOfChangeGraphPainter extends CustomPainter {
  final Function(double) _function;
  final double _x1;
  final double _x2;

  _AverageRateOfChangeGraphPainter(this._function, this._x1, this._x2);

  @override
  void paint(Canvas canvas, Size size) {
    final Paint paint = Paint()
      ..color = Colors.blue
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    final Paint pointPaint = Paint()
      ..color = Colors.red
      ..strokeWidth = 8
      ..style = PaintingStyle.fill;

    final Paint secantLinePaint = Paint()
      ..color = Colors.green
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    final double minX = -5.0;
    final double maxX = 5.0;
    final double minY = -10.0; // Adjusted for x^2 function
    final double maxY = 25.0;

    final double rangeX = maxX - minX;
    final double rangeY = maxY - minY;

    final double scaleX = size.width / rangeX;
    final double scaleY = size.height / rangeY;

    Offset toCanvas(double x, double y) {
      final double canvasX = (x - minX) * scaleX;
      final double canvasY = size.height - (y - minY) * scaleY;
      return Offset(canvasX, canvasY);
    }

    // Draw axes
    canvas.drawLine(toCanvas(minX, 0), toCanvas(maxX, 0), paint..color = Colors.grey); // X-axis
    canvas.drawLine(toCanvas(0, minY), toCanvas(0, maxY), paint..color = Colors.grey); // Y-axis

    // Draw function graph
    final Path path = Path();
    bool firstPoint = true;
    for (double i = minX; i <= maxX; i += 0.1) {
      double x = i;
      double y = _function(x);
      if (!y.isNaN && y.isFinite) {
        if (firstPoint) {
          path.moveTo(toCanvas(x, y).dx, toCanvas(x, y).dy);
          firstPoint = false;
        } else {
          path.lineTo(toCanvas(x, y).dx, toCanvas(x, y).dy);
        }
      } else {
        firstPoint = true;
      }
    }
    canvas.drawPath(path, paint);

    // Draw points (x1, f(x1)) and (x2, f(x2))
    double y1 = _function(_x1);
    double y2 = _function(_x2);

    if (!y1.isNaN && y1.isFinite) {
      canvas.drawCircle(toCanvas(_x1, y1), 4, pointPaint);
      TextPainter(
        text: TextSpan(text: '(${_x1.toStringAsFixed(1)}, ${y1.toStringAsFixed(1)})', style: TextStyle(color: Colors.red, fontSize: 10)),
        textDirection: TextDirection.ltr,
      )..layout()..paint(canvas, toCanvas(_x1 + 0.1, y1 + 0.1));
    }
    if (!y2.isNaN && y2.isFinite) {
      canvas.drawCircle(toCanvas(_x2, y2), 4, pointPaint);
      TextPainter(
        text: TextSpan(text: '(${_x2.toStringAsFixed(1)}, ${y2.toStringAsFixed(1)})', style: TextStyle(color: Colors.red, fontSize: 10)),
        textDirection: TextDirection.ltr,
      )..layout()..paint(canvas, toCanvas(_x2 + 0.1, y2 + 0.1));
    }

    // Draw secant line
    if (!y1.isNaN && !y2.isNaN && _x1 != _x2) {
      canvas.drawLine(toCanvas(_x1, y1), toCanvas(_x2, y2), secantLinePaint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    _AverageRateOfChangeGraphPainter oldPainter = oldDelegate as _AverageRateOfChangeGraphPainter;
    return oldPainter._x1 != _x1 || oldPainter._x2 != _x2;
  }
}
