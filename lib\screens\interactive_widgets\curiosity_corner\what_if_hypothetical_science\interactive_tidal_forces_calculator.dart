import 'package:flutter/material.dart';
import 'package:resonance/widgets/interactive_widget_scaffold.dart';
import 'dart:math';

class InteractiveTidalForcesCalculator extends StatefulWidget {
  const InteractiveTidalForcesCalculator({super.key});

  @override
  State<InteractiveTidalForcesCalculator> createState() => _InteractiveTidalForcesCalculatorState();
}

class _InteractiveTidalForcesCalculatorState extends State<InteractiveTidalForcesCalculator> {
  double _moonMassFactor = 1.0; // Relative to Earth's Moon
  double _distanceFactor = 1.0; // Relative to Earth-Moon distance
  double _tidalForce = 0.0;

  @override
  void initState() {
    super.initState();
    _calculateTidalForce();
  }

  void _calculateTidalForce() {
    // Simplified formula for tidal force: F_tidal ~ M_moon / r^3
    // Using arbitrary constants for visualization purposes
    setState(() {
      if (_distanceFactor == 0) {
        _tidalForce = double.infinity;
      } else {
        _tidalForce = _moonMassFactor / pow(_distanceFactor, 3);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return InteractiveWidgetScaffold(
      title: 'Tidal Forces Calculator',
      description: 'Calculate and visualize tidal forces with an additional moon.',
      interactiveContent: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'Moon Mass Factor: ${_moonMassFactor.toStringAsFixed(1)}x (relative to Earth\'s Moon)',
              style: const TextStyle(fontSize: 18),
            ),
            Slider(
              value: _moonMassFactor,
              min: 0.1,
              max: 5.0,
              divisions: 49,
              label: _moonMassFactor.toStringAsFixed(1),
              onChanged: (value) {
                setState(() {
                  _moonMassFactor = value;
                  _calculateTidalForce();
                });
              },
            ),
            const SizedBox(height: 10),
            Text(
              'Distance Factor: ${_distanceFactor.toStringAsFixed(1)}x (relative to Earth-Moon distance)',
              style: const TextStyle(fontSize: 18),
            ),
            Slider(
              value: _distanceFactor,
              min: 0.1,
              max: 3.0,
              divisions: 29,
              label: _distanceFactor.toStringAsFixed(1),
              onChanged: (value) {
                setState(() {
                  _distanceFactor = value;
                  _calculateTidalForce();
                });
              },
            ),
            const SizedBox(height: 20),
            Text(
              'Calculated Tidal Force: ${_tidalForce.toStringAsFixed(2)} (arbitrary units)',
              style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: Text(
                'Adjust the moon\'s mass and distance to see how it affects the calculated tidal force. Notice the strong inverse-cube relationship with distance.',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 16),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
