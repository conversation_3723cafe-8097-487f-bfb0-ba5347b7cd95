import 'package:flutter/material.dart';

class InteractiveAbstractionBuilder extends StatefulWidget {
  const InteractiveAbstractionBuilder({super.key});

  @override
  State<InteractiveAbstractionBuilder> createState() => _InteractiveAbstractionBuilderState();
}

class _InteractiveAbstractionBuilderState extends State<InteractiveAbstractionBuilder> {
  String _selectedLevel = 'High-Level';
  String _originalConcept = 'A car';
  String _abstractedConcept = '';

  final Map<String, Map<String, String>> _abstractionLevels = {
    'High-Level': {
      'A car': 'A vehicle for transportation',
      'A computer': 'A device for processing information',
      'A house': 'A dwelling for shelter',
    },
    'Mid-Level': {
      'A car': 'An engine, wheels, and a chassis',
      'A computer': 'CPU, RAM, storage, and I/O devices',
      'A house': 'Walls, roof, foundation, and rooms',
    },
    'Low-Level': {
      'A car': 'Combustion, friction, gears, and electrical signals',
      'A computer': 'Transistors, logic gates, and binary code',
      'A house': 'Bricks, wood, nails, and insulation',
    },
  };

  @override
  void initState() {
    super.initState();
    _updateAbstraction();
  }

  void _updateAbstraction() {
    setState(() {
      _abstractedConcept = _abstractionLevels[_selectedLevel]?[_originalConcept] ?? 'N/A';
    });
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Interactive Abstraction Builder',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Text('Concept:'),
                const SizedBox(width: 10),
                DropdownButton<String>(
                  value: _originalConcept,
                  onChanged: (String? newValue) {
                    setState(() {
                      _originalConcept = newValue!;
                      _updateAbstraction();
                    });
                  },
                  items: <String>['A car', 'A computer', 'A house']
                      .map<DropdownMenuItem<String>>((String value) {
                    return DropdownMenuItem<String>(
                      value: value,
                      child: Text(value),
                    );
                  }).toList(),
                ),
              ],
            ),
            const SizedBox(height: 10),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Text('Abstraction Level:'),
                const SizedBox(width: 10),
                DropdownButton<String>(
                  value: _selectedLevel,
                  onChanged: (String? newValue) {
                    setState(() {
                      _selectedLevel = newValue!;
                      _updateAbstraction();
                    });
                  },
                  items: <String>['High-Level', 'Mid-Level', 'Low-Level']
                      .map<DropdownMenuItem<String>>((String value) {
                    return DropdownMenuItem<String>(
                      value: value,
                      child: Text(value),
                    );
                  }).toList(),
                ),
              ],
            ),
            const SizedBox(height: 20),
            Text(
              'Original Concept: $_originalConcept',
              style: TextStyle(fontSize: 16, color: Colors.blue),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 10),
            Text(
              'Abstracted Concept (${_selectedLevel}): $_abstractedConcept',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: Colors.green),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            const Text(
              'Abstraction helps in managing complexity by focusing on essential details and hiding unnecessary ones.',
              style: TextStyle(fontStyle: FontStyle.italic, color: Colors.grey),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
