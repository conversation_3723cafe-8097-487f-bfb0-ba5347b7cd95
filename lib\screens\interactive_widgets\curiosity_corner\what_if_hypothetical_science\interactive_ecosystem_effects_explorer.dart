import 'package:flutter/material.dart';
import 'package:resonance/widgets/interactive_widget_scaffold.dart';

class InteractiveEcosystemEffectsExplorer extends StatefulWidget {
  const InteractiveEcosystemEffectsExplorer({super.key});

  @override
  State<InteractiveEcosystemEffectsExplorer> createState() => _InteractiveEcosystemEffectsExplorerState();
}

class _InteractiveEcosystemEffectsExplorerState extends State<InteractiveEcosystemEffectsExplorer> {
  double _tidalStrength = 0.5; // 0.0 to 1.0, representing tidal strength
  String _ecosystemImpact = 'Minimal impact on coastal ecosystems.';

  void _updateEcosystemImpact() {
    setState(() {
      if (_tidalStrength < 0.2) {
        _ecosystemImpact = 'Minimal impact on coastal ecosystems. Marine life cycles remain largely undisturbed.';
      } else if (_tidalStrength < 0.5) {
        _ecosystemImpact = 'Moderate impact: Increased tidal ranges affect intertidal zones, leading to shifts in species distribution and some habitat loss.';
      } else if (_tidalStrength < 0.8) {
        _ecosystemImpact = 'Significant impact: Drastic changes in tidal patterns cause widespread erosion, alter breeding cycles of marine animals, and lead to significant loss of coastal habitats.';
      } else {
        _ecosystemImpact = 'Extreme impact: Severe tidal forces reshape coastlines, leading to mass extinctions in vulnerable ecosystems and fundamental changes in global marine biology.';
      }
    });
  }

  @override
  void initState() {
    super.initState();
    _updateEcosystemImpact();
  }

  @override
  Widget build(BuildContext context) {
    return InteractiveWidgetScaffold(
      title: 'Ecosystem Effects Explorer',
      description: 'Explore the effects on ecosystems with two moons.',
      interactiveContent: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.eco, size: 100, color: Colors.green.shade700),
            const SizedBox(height: 20),
            Text(
              'Tidal Strength Factor: ${(_tidalStrength * 100).toStringAsFixed(0)}%',
              style: const TextStyle(fontSize: 18),
            ),
            Slider(
              value: _tidalStrength,
              min: 0.0,
              max: 1.0,
              divisions: 10,
              label: (_tidalStrength * 100).toStringAsFixed(0),
              onChanged: (value) {
                _tidalStrength = value;
                _updateEcosystemImpact();
              },
            ),
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: Text(
                'Adjust the slider to simulate different tidal strengths caused by an additional moon. Read the description below to understand the hypothetical impact on Earth\'s ecosystems, especially coastal and marine environments.',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 16),
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Text(
                _ecosystemImpact,
                textAlign: TextAlign.center,
                style: const TextStyle(fontSize: 16, fontStyle: FontStyle.italic),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
