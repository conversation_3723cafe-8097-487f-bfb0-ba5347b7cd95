import 'package:flutter/material.dart';
import 'dart:math';

class InteractivePatternRecognitionTrainer extends StatefulWidget {
  const InteractivePatternRecognitionTrainer({super.key});

  @override
  State<InteractivePatternRecognitionTrainer> createState() => _InteractivePatternRecognitionTrainerState();
}

class _InteractivePatternRecognitionTrainerState extends State<InteractivePatternRecognitionTrainer> {
  List<int> _currentPattern = [];
  String _patternRule = '';
  final TextEditingController _answerController = TextEditingController();
  String? _feedbackMessage;
  bool _isCorrect = false;

  final List<Map<String, dynamic>> _patterns = [
    {
      'sequence': [2, 4, 6, 8, 10],
      'rule': 'Add 2',
      'next': '12',
    },
    {
      'sequence': [1, 3, 9, 27, 81],
      'rule': 'Multiply by 3',
      'next': '243',
    },
    {
      'sequence': [1, 4, 9, 16, 25],
      'rule': 'Square numbers (n^2)',
      'next': '36',
    },
    {
      'sequence': [5, 10, 15, 20, 25],
      'rule': 'Add 5',
      'next': '30',
    },
    {
      'sequence': [100, 50, 25, 12, 6],
      'rule': 'Divide by 2 (approximately)',
      'next': '3',
    },
  ];

  @override
  void initState() {
    super.initState();
    _generateNewPattern();
  }

  void _generateNewPattern() {
    final random = Random();
    final patternData = _patterns[random.nextInt(_patterns.length)];
    setState(() {
      _currentPattern = List<int>.from(patternData['sequence']);
      _patternRule = patternData['rule'];
      _answerController.clear();
      _feedbackMessage = null;
      _isCorrect = false;
    });
  }

  void _checkAnswer() {
    final currentPatternData = _patterns.firstWhere((p) => List<int>.from(p['sequence']).toString() == _currentPattern.toString());
    setState(() {
      if (_answerController.text.trim() == currentPatternData['next']) {
        _feedbackMessage = 'Correct! The rule is: $_patternRule';
        _isCorrect = true;
      } else {
        _feedbackMessage = 'Incorrect. Try again or generate a new pattern.';
        _isCorrect = false;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Interactive Pattern Recognition Trainer',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            const Text(
              'What comes next in the sequence?',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 10),
            Text(
              _currentPattern.join(', '),
              style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold, color: Colors.blue),
            ),
            const SizedBox(height: 20),
            TextField(
              controller: _answerController,
              keyboardType: TextInputType.number,
              decoration: InputDecoration(
                labelText: 'Your Answer',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 10),
            ElevatedButton(
              onPressed: _checkAnswer,
              child: const Text('Check Answer'),
            ),
            const SizedBox(height: 10),
            if (_feedbackMessage != null)
              Text(
                _feedbackMessage!,
                style: TextStyle(
                  color: _isCorrect ? Colors.green : Colors.red,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: _generateNewPattern,
              child: const Text('New Pattern'),
            ),
          ],
        ),
      ),
    );
  }
}
