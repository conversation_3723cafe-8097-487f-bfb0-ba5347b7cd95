import 'package:flutter/material.dart';
import '../../../models/course_models.dart';
import '../../../services/service_provider.dart';

class RoadmapCourseCard extends StatelessWidget {
  final Course course;
  final bool isEnrolled;
  final double progress;
  final bool isLocked;
  final VoidCallback onTap;

  const RoadmapCourseCard({
    Key? key,
    required this.course,
    required this.isEnrolled,
    required this.progress,
    required this.isLocked,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final courseService = ServiceProvider.of(context).courseService;
    final category = courseService.getCategoryById(course.categoryId);

    // Parse category color
    final categoryColor =
        category != null
            ? _parseColor(category.color)
            : const Color.fromRGBO(124, 66, 210, 1);

    // Determine card color based on locked status
    final cardColor = isLocked ? Colors.grey[200] : Colors.white;

    // Determine text color based on locked status
    final textColor = isLocked ? Colors.grey[600] : Colors.black;

    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: cardColor,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
          border: Border.all(
            color:
                isLocked ? Colors.grey[300]! : categoryColor.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Course Header
            ClipRRect(
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
              child: Stack(
                children: [
                  // Course Image
                  AspectRatio(
                    aspectRatio: 16 / 9,
                    child: Image.asset(
                      course.thumbnailPath,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          color:
                              isLocked
                                  ? Colors.grey[300]
                                  : categoryColor.withOpacity(0.1),
                          child: Center(
                            child: Icon(
                              Icons.image_not_supported_outlined,
                              color: isLocked ? Colors.grey : categoryColor,
                            ),
                          ),
                        );
                      },
                    ),
                  ),

                  // Locked Overlay
                  if (isLocked)
                    Positioned.fill(
                      child: Container(
                        color: Colors.black.withOpacity(0.5),
                        child: const Center(
                          child: Icon(
                            Icons.lock,
                            color: Colors.white,
                            size: 40,
                          ),
                        ),
                      ),
                    ),

                  // Category Badge
                  if (category != null && !isLocked)
                    Positioned(
                      top: 12,
                      left: 12,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 6,
                        ),
                        decoration: BoxDecoration(
                          color: categoryColor.withOpacity(0.9),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          category.name,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                            fontFamily: 'WorkSans',
                          ),
                        ),
                      ),
                    ),

                  // Difficulty Badge
                  if (!isLocked)
                    Positioned(
                      top: 12,
                      right: 12,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 6,
                        ),
                        decoration: BoxDecoration(
                          color: _getDifficultyColor(
                            course.difficulty,
                          ).withOpacity(0.9),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          course.difficulty,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                            fontFamily: 'WorkSans',
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),

            // Course Info
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Course Title
                  Text(
                    course.title,
                    style: TextStyle(
                      color: textColor,
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      fontFamily: 'WorkSans',
                    ),
                  ),

                  const SizedBox(height: 8),

                  // Course Description
                  Text(
                    course.description,
                    style: TextStyle(
                      color: isLocked ? Colors.grey : Colors.grey[700],
                      fontSize: 14,
                      fontFamily: 'WorkSans',
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),

                  const SizedBox(height: 16),

                  // Course Stats
                  Row(
                    children: [
                      // Lesson Count
                      Icon(
                        Icons.book_outlined,
                        size: 16,
                        color: isLocked ? Colors.grey : Colors.grey[700],
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '${course.modules.fold(0, (sum, module) => sum + module.lessons.length)} ${course.modules.fold(0, (sum, module) => sum + module.lessons.length) == 1 ? 'Lesson' : 'Lessons'}',
                        style: TextStyle(
                          color: isLocked ? Colors.grey : Colors.grey[700],
                          fontSize: 14,
                          fontFamily: 'WorkSans',
                        ),
                      ),

                      const SizedBox(width: 16),

                      // Estimated Time
                      Icon(
                        Icons.access_time,
                        size: 16,
                        color: isLocked ? Colors.grey : Colors.grey[700],
                      ),
                      const SizedBox(width: 4),
                      Text(
                        _calculateTotalTime(course),
                        style: TextStyle(
                          color: isLocked ? Colors.grey : Colors.grey[700],
                          fontSize: 14,
                          fontFamily: 'WorkSans',
                        ),
                      ),
                    ],
                  ),

                  // Progress Bar (if enrolled)
                  if (isEnrolled && progress > 0 && !isLocked) ...[
                    const SizedBox(height: 16),
                    LinearProgressIndicator(
                      value: progress / 100,
                      backgroundColor: Colors.grey[200],
                      valueColor: AlwaysStoppedAnimation<Color>(categoryColor),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${progress.toInt()}% Complete',
                      style: TextStyle(
                        color: categoryColor,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        fontFamily: 'WorkSans',
                      ),
                    ),
                  ],

                  // Action Button
                  if (!isLocked) ...[
                    const SizedBox(height: 16),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: onTap,
                        style: ElevatedButton.styleFrom(
                          backgroundColor:
                              isEnrolled ? Colors.white : categoryColor,
                          foregroundColor:
                              isEnrolled ? categoryColor : Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                            side: BorderSide(color: categoryColor, width: 1),
                          ),
                          elevation: 0,
                        ),
                        child: Text(
                          isEnrolled
                              ? (progress > 0
                                  ? 'Continue Learning'
                                  : 'Start Learning')
                              : 'Enroll Now',
                          style: const TextStyle(
                            fontFamily: 'WorkSans',
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Parse hex color string to Color
  Color _parseColor(String hexColor) {
    try {
      hexColor = hexColor.replaceAll('#', '');
      if (hexColor.length == 6) {
        hexColor = 'FF$hexColor';
      }
      return Color(int.parse(hexColor, radix: 16));
    } catch (e) {
      // Default to purple if parsing fails
      return const Color.fromRGBO(124, 66, 210, 1);
    }
  }

  // Get color for difficulty level
  Color _getDifficultyColor(String difficulty) {
    switch (difficulty.toLowerCase()) {
      case 'beginner':
        return Colors.green;
      case 'intermediate':
        return Colors.orange;
      case 'advanced':
        return Colors.red;
      case 'expert':
        return Colors.purple;
      default:
        return Colors.blue;
    }
  }

  // Calculate total course time
  String _calculateTotalTime(Course course) {
    int totalMinutes = 0;
    final allLessons =
        course.modules.expand((module) => module.lessons).toList();
    for (final lesson in allLessons) {
      totalMinutes += lesson.estimatedTimeMinutes;
    }

    if (totalMinutes < 60) {
      return '$totalMinutes min';
    } else {
      final hours = totalMinutes ~/ 60;
      final minutes = totalMinutes % 60;
      return hours > 0
          ? '$hours hr${hours > 1 ? 's' : ''}${minutes > 0 ? ' $minutes min' : ''}'
          : '$minutes min';
    }
  }
}
