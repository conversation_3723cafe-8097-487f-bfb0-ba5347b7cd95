import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_markdown/flutter_markdown.dart';

class InteractiveSystemsThinkingTool extends ConsumerStatefulWidget {
  const InteractiveSystemsThinkingTool({super.key});

  @override
  ConsumerState<InteractiveSystemsThinkingTool> createState() => _InteractiveSystemsThinkingToolState();
}

class _InteractiveSystemsThinkingToolState extends ConsumerState<InteractiveSystemsThinkingTool> {
  String _systemName = '';
  String _components = '';
  String _interconnections = '';
  String _feedbackLoops = '';
  String _emergentProperties = '';
  String _systemDescription = '';

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Interactive Systems Thinking Tool',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 20),
          const MarkdownBody(
            data: '''
            This tool helps you analyze complex systems by identifying their components,
            interconnections, feedback loops, and emergent properties.
            ''',
          ),
          const SizedBox(height: 20),
          _buildSectionTitle('1. System Name'),
          TextField(
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
              hintText: 'e.g., "Ecosystem", "Supply Chain", "Human Body"',
            ),
            onChanged: (value) {
              setState(() {
                _systemName = value;
              });
            },
          ),
          const SizedBox(height: 20),
          _buildSectionTitle('2. Components (What are the individual parts of the system?)'),
          TextField(
            maxLines: 5,
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
              hintText: 'List the key elements or subsystems.',
            ),
            onChanged: (value) {
              setState(() {
                _components = value;
              });
            },
          ),
          const SizedBox(height: 20),
          _buildSectionTitle('3. Interconnections (How do the components relate to each other?)'),
          TextField(
            maxLines: 5,
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
              hintText: 'Describe the relationships, flows, and dependencies between components.',
            ),
            onChanged: (value) {
              setState(() {
                _interconnections = value;
              });
            },
          ),
          const SizedBox(height: 20),
          _buildSectionTitle('4. Feedback Loops (How do outputs influence inputs?)'),
          TextField(
            maxLines: 5,
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
              hintText: 'Identify positive (reinforcing) and negative (balancing) feedback loops.',
            ),
            onChanged: (value) {
              setState(() {
                _feedbackLoops = value;
              });
            },
          ),
          const SizedBox(height: 20),
          _buildSectionTitle('5. Emergent Properties (What behaviors arise from the system as a whole?)'),
          TextField(
            maxLines: 5,
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
              hintText: 'What characteristics does the system exhibit that are not present in its individual components?',
            ),
            onChanged: (value) {
              setState(() {
                _emergentProperties = value;
              });
            },
          ),
          const SizedBox(height: 30),
          _buildSystemSummaryCard(context),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Text(
        title,
        style: const TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
      ),
    );
  }

  Widget _buildSystemSummaryCard(BuildContext context) {
    _systemDescription = '''
**System Name:** ${_systemName.isNotEmpty ? _systemName : 'N/A'}
**Components:** ${_components.isNotEmpty ? _components : 'N/A'}
**Interconnections:** ${_interconnections.isNotEmpty ? _interconnections : 'N/A'}
**Feedback Loops:** ${_feedbackLoops.isNotEmpty ? _feedbackLoops : 'N/A'}
**Emergent Properties:** ${_emergentProperties.isNotEmpty ? _emergentProperties : 'N/A'}
''';

    return Card(
      elevation: 4,
      margin: const EdgeInsets.symmetric(vertical: 20),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Your Systems Thinking Analysis:',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold, color: Colors.purple),
            ),
            const Divider(),
            MarkdownBody(data: _systemDescription),
          ],
        ),
      ),
    );
  }
}
