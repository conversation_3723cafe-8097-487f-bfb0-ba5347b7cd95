import 'package:flutter/material.dart';
import '../../../models/course_models.dart';

class CategoryCard extends StatelessWidget {
  final CourseCategory category;
  final VoidCallback onTap;

  const CategoryCard({
    Key? key,
    required this.category,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Parse the color from hex string
    final color = _parseColor(category.color);

    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 120,
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: color.withOpacity(0.3),
            width: 1,
          ),
        ),
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Category Icon
            Image.asset(
              category.iconPath,
              width: 40,
              height: 40,
              errorBuilder: (context, error, stackTrace) {
                return Icon(
                  _getCategoryIcon(category.id),
                  size: 40,
                  color: color,
                );
              },
            ),
            const SizedBox(height: 12),
            
            // Category Name
            Text(
              category.name,
              style: TextStyle(
                color: color,
                fontSize: 14,
                fontWeight: FontWeight.w600,
                fontFamily: 'WorkSans',
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }
  
  // Parse hex color string to Color
  Color _parseColor(String hexColor) {
    try {
      hexColor = hexColor.replaceAll('#', '');
      if (hexColor.length == 6) {
        hexColor = 'FF$hexColor';
      }
      return Color(int.parse(hexColor, radix: 16));
    } catch (e) {
      // Default to purple if parsing fails
      return const Color.fromRGBO(124, 66, 210, 1);
    }
  }
  
  // Get fallback icon for category
  IconData _getCategoryIcon(String categoryId) {
    switch (categoryId.toLowerCase()) {
      case 'math':
        return Icons.calculate;
      case 'science':
        return Icons.science;
      case 'computer_science':
        return Icons.computer;
      case 'language':
        return Icons.translate;
      case 'arts':
        return Icons.palette;
      default:
        return Icons.school;
    }
  }
}
