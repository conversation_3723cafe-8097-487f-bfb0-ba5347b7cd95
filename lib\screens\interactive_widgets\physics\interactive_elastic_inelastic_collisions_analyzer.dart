import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class InteractiveElasticInelasticCollisionsAnalyzer extends StatefulWidget {
  const InteractiveElasticInelasticCollisionsAnalyzer({super.key});

  @override
  State<InteractiveElasticInelasticCollisionsAnalyzer> createState() => _InteractiveElasticInelasticCollisionsAnalyzerState();
}

class _InteractiveElasticInelasticCollisionsAnalyzerState extends State<InteractiveElasticInelasticCollisionsAnalyzer> {
  final TextEditingController _mass1Controller = TextEditingController();
  final TextEditingController _velocity1Controller = TextEditingController();
  final TextEditingController _mass2Controller = TextEditingController();
  final TextEditingController _velocity2Controller = TextEditingController();

  double? _elasticV1Final;
  double? _elasticV2Final;
  double? _inelasticVFinal;

  void _analyzeCollisions() {
    setState(() {
      final double? m1 = double.tryParse(_mass1Controller.text);
      final double? v1 = double.tryParse(_velocity1Controller.text);
      final double? m2 = double.tryParse(_mass2Controller.text);
      final double? v2 = double.tryParse(_velocity2Controller.text);

      if (m1 != null && v1 != null && m2 != null && v2 != null) {
        // Perfectly Elastic Collision
        // v1_final = ((m1 - m2) / (m1 + m2)) * v1 + ((2 * m2) / (m1 + m2)) * v2;
        // v2_final = ((2 * m1) / (m1 + m2)) * v1 + ((m2 - m1) / (m1 + m2)) * v2;
        _elasticV1Final = ((m1 - m2) / (m1 + m2)) * v1 + ((2 * m2) / (m1 + m2)) * v2;
        _elasticV2Final = ((2 * m1) / (m1 + m2)) * v1 + ((m2 - m1) / (m1 + m2)) * v2;

        // Perfectly Inelastic Collision
        // v_final = (m1*v1 + m2*v2) / (m1 + m2)
        _inelasticVFinal = (m1 * v1 + m2 * v2) / (m1 + m2);
      } else {
        _elasticV1Final = null;
        _elasticV2Final = null;
        _inelasticVFinal = null;
      }
    });
  }

  @override
  void dispose() {
    _mass1Controller.dispose();
    _velocity1Controller.dispose();
    _mass2Controller.dispose();
    _velocity2Controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Elastic & Inelastic Collisions'),
        backgroundColor: Colors.deepOrange,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'Object 1',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 10),
            TextField(
              controller: _mass1Controller,
              keyboardType: const TextInputType.numberWithOptions(decimal: true),
              inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^-?\d+\.?\d*'))],
              decoration: const InputDecoration(
                labelText: 'Mass 1 (kg)',
                border: OutlineInputBorder(),
              ),
              onChanged: (_) => _analyzeCollisions(),
            ),
            const SizedBox(height: 10),
            TextField(
              controller: _velocity1Controller,
              keyboardType: const TextInputType.numberWithOptions(decimal: true),
              inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^-?\d+\.?\d*'))],
              decoration: const InputDecoration(
                labelText: 'Initial Velocity 1 (m/s)',
                border: OutlineInputBorder(),
              ),
              onChanged: (_) => _analyzeCollisions(),
            ),
            const SizedBox(height: 30),
            const Text(
              'Object 2',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 10),
            TextField(
              controller: _mass2Controller,
              keyboardType: const TextInputType.numberWithOptions(decimal: true),
              inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^-?\d+\.?\d*'))],
              decoration: const InputDecoration(
                labelText: 'Mass 2 (kg)',
                border: OutlineInputBorder(),
              ),
              onChanged: (_) => _analyzeCollisions(),
            ),
            const SizedBox(height: 10),
            TextField(
              controller: _velocity2Controller,
              keyboardType: const TextInputType.numberWithOptions(decimal: true),
              inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^-?\d+\.?\d*'))],
              decoration: const InputDecoration(
                labelText: 'Initial Velocity 2 (m/s)',
                border: OutlineInputBorder(),
              ),
              onChanged: (_) => _analyzeCollisions(),
            ),
            const SizedBox(height: 30),
            Text(
              _elasticV1Final == null
                  ? 'Enter values for both objects to analyze collisions.'
                  : 'Elastic Collision:\n'
                    '  Final Velocity 1: ${_elasticV1Final!.toStringAsFixed(2)} m/s\n'
                    '  Final Velocity 2: ${_elasticV2Final!.toStringAsFixed(2)} m/s',
              style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold, color: Colors.deepOrange),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            Text(
              _inelasticVFinal == null
                  ? ''
                  : 'Inelastic Collision (objects stick together):\n'
                    '  Final Common Velocity: ${_inelasticVFinal!.toStringAsFixed(2)} m/s',
              style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold, color: Colors.deepOrange),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            const Text(
              'Note: Elastic collision assumes conservation of both momentum and kinetic energy. Inelastic collision assumes objects stick together.',
              style: TextStyle(fontSize: 14, fontStyle: FontStyle.italic, color: Colors.grey),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
