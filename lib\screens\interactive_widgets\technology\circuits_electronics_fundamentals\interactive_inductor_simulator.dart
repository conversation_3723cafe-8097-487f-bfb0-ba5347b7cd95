import 'package:flutter/material.dart';
import 'package:resonance_app/widgets/interactive_widget_scaffold.dart';

class InteractiveInductorSimulator extends StatefulWidget {
  const InteractiveInductorSimulator({super.key});

  @override
  State<InteractiveInductorSimulator> createState() => _InteractiveInductorSimulatorState();
}

class _InteractiveInductorSimulatorState extends State<InteractiveInductorSimulator> {
  double inductance = 1.0; // Henries
  double currentChangeRate = 0.0; // Amperes per second
  double inducedVoltage = 0.0; // Volts

  @override
  void initState() {
    super.initState();
    _calculateInducedVoltage();
  }

  void _calculateInducedVoltage() {
    setState(() {
      inducedVoltage = -inductance * currentChangeRate;
    });
  }

  @override
  Widget build(BuildContext context) {
    return InteractiveWidgetScaffold(
      title: 'Interactive Inductor Simulator',
      description: 'Simulate induced voltage across an inductor (V = -L * dI/dt).',
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text(
              'Inductance (L): ${inductance.toStringAsFixed(2)} H',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            Slider(
              value: inductance,
              min: 0.1,
              max: 10.0,
              divisions: 99,
              label: inductance.toStringAsFixed(2),
              onChanged: (value) {
                setState(() {
                  inductance = value;
                  _calculateInducedVoltage();
                });
              },
            ),
            const SizedBox(height: 16.0),
            Text(
              'Rate of Current Change (dI/dt): ${currentChangeRate.toStringAsFixed(2)} A/s',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            Slider(
              value: currentChangeRate,
              min: -5.0,
              max: 5.0,
              divisions: 100,
              label: currentChangeRate.toStringAsFixed(2),
              onChanged: (value) {
                setState(() {
                  currentChangeRate = value;
                  _calculateInducedVoltage();
                });
              },
            ),
            const SizedBox(height: 24.0),
            Text(
              'Induced Voltage (V): ${inducedVoltage.toStringAsFixed(2)} V',
              style: Theme.of(context).textTheme.headlineSmall,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
