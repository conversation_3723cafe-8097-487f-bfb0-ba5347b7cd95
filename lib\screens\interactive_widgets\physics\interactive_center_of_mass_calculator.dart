import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class InteractiveCenterOfMassCalculator extends StatefulWidget {
  const InteractiveCenterOfMassCalculator({super.key});

  @override
  State<InteractiveCenterOfMassCalculator> createState() => _InteractiveCenterOfMassCalculatorState();
}

class _InteractiveCenterOfMassCalculatorState extends State<InteractiveCenterOfMassCalculator> {
  final List<TextEditingController> _massControllers = [];
  final List<TextEditingController> _xControllers = [];
  final List<TextEditingController> _yControllers = [];
  int _numberOfObjects = 2;

  double? _centerOfMassX;
  double? _centerOfMassY;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
  }

  void _initializeControllers() {
    _massControllers.clear();
    _xControllers.clear();
    _yControllers.clear();
    for (int i = 0; i < _numberOfObjects; i++) {
      _massControllers.add(TextEditingController());
      _xControllers.add(TextEditingController());
      _yControllers.add(TextEditingController());
    }
  }

  void _addRemoveObject(bool add) {
    setState(() {
      if (add) {
        _numberOfObjects++;
        _massControllers.add(TextEditingController());
        _xControllers.add(TextEditingController());
        _yControllers.add(TextEditingController());
      } else if (_numberOfObjects > 1) {
        _numberOfObjects--;
        _massControllers.removeLast().dispose();
        _xControllers.removeLast().dispose();
        _yControllers.removeLast().dispose();
      }
      _calculateCenterOfMass();
    });
  }

  void _calculateCenterOfMass() {
    setState(() {
      double totalMass = 0;
      double sumMx = 0;
      double sumMy = 0;
      bool allInputsValid = true;

      for (int i = 0; i < _numberOfObjects; i++) {
        final double? mass = double.tryParse(_massControllers[i].text);
        final double? x = double.tryParse(_xControllers[i].text);
        final double? y = double.tryParse(_yControllers[i].text);

        if (mass != null && x != null && y != null) {
          totalMass += mass;
          sumMx += mass * x;
          sumMy += mass * y;
        } else {
          allInputsValid = false;
          break;
        }
      }

      if (allInputsValid && totalMass != 0) {
        _centerOfMassX = sumMx / totalMass;
        _centerOfMassY = sumMy / totalMass;
      } else {
        _centerOfMassX = null;
        _centerOfMassY = null;
      }
    });
  }

  @override
  void dispose() {
    for (var controller in _massControllers) {
      controller.dispose();
    }
    for (var controller in _xControllers) {
      controller.dispose();
    }
    for (var controller in _yControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Center of Mass Calculator'),
        backgroundColor: Colors.purple,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ElevatedButton(
                  onPressed: () => _addRemoveObject(false),
                  child: const Text('- Object'),
                ),
                const SizedBox(width: 20),
                ElevatedButton(
                  onPressed: () => _addRemoveObject(true),
                  child: const Text('+ Object'),
                ),
              ],
            ),
            const SizedBox(height: 20),
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _numberOfObjects,
              itemBuilder: (context, index) {
                return Card(
                  margin: const EdgeInsets.symmetric(vertical: 10),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Object ${index + 1}',
                          style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 10),
                        TextField(
                          controller: _massControllers[index],
                          keyboardType: const TextInputType.numberWithOptions(decimal: true),
                          inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d*'))],
                          decoration: const InputDecoration(
                            labelText: 'Mass (kg)',
                            border: OutlineInputBorder(),
                          ),
                          onChanged: (_) => _calculateCenterOfMass(),
                        ),
                        const SizedBox(height: 10),
                        TextField(
                          controller: _xControllers[index],
                          keyboardType: const TextInputType.numberWithOptions(decimal: true),
                          inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^-?\d+\.?\d*'))],
                          decoration: const InputDecoration(
                            labelText: 'X-coordinate (m)',
                            border: OutlineInputBorder(),
                          ),
                          onChanged: (_) => _calculateCenterOfMass(),
                        ),
                        const SizedBox(height: 10),
                        TextField(
                          controller: _yControllers[index],
                          keyboardType: const TextInputType.numberWithOptions(decimal: true),
                          inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^-?\d+\.?\d*'))],
                          decoration: const InputDecoration(
                            labelText: 'Y-coordinate (m)',
                            border: OutlineInputBorder(),
                          ),
                          onChanged: (_) => _calculateCenterOfMass(),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
            const SizedBox(height: 30),
            Text(
              _centerOfMassX == null
                  ? 'Enter values for objects to calculate center of mass.'
                  : 'Center of Mass (X, Y): (${_centerOfMassX!.toStringAsFixed(2)} m, ${_centerOfMassY!.toStringAsFixed(2)} m)',
              style: const TextStyle(fontSize: 22, fontWeight: FontWeight.bold, color: Colors.purple),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            const Text(
              'Add or remove objects to calculate the center of mass for a system of particles.',
              style: TextStyle(fontSize: 14, fontStyle: FontStyle.italic, color: Colors.grey),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
