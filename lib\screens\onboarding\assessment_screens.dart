import 'package:flutter/material.dart';
import '../../models/user_preferences.dart';
import '../../widgets/animated_selection_option.dart';
import '../../widgets/animated_text.dart';
import '../../widgets/animated_icon_widget.dart';
import '../../widgets/animated_button.dart';

// Screen 1: Introduction with animation
class IntroductionScreen extends StatelessWidget {
  final VoidCallback onContinue;

  const IntroductionScreen({Key? key, required this.onContinue})
    : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Assistant icon with animation
          AnimatedIconWidget(
            icon: Icons.smart_toy,
            size: 120,
            color: Color.fromRGBO(100, 45, 176, 1),
          ),
          SizedBox(height: 32),
          AnimatedHeadingText(
            text: 'Hey, I\'m <PERSON><PERSON>!',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Color.fromRGBO(16, 17, 20, 1),
              fontFamily: 'WorkSans',
              fontSize: 30,
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 16),
          AnimatedBodyText(
            text: 'Let\'s build a learning path just for you.',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Color.fromRGBO(57, 59, 66, 1),
              fontFamily: 'WorkSans',
              fontSize: 18,
              fontWeight: FontWeight.w400,
            ),
            delay: 200,
          ),
          SizedBox(height: 48),
          AnimatedButton(
            onPressed: onContinue,
            text: 'Continue',
            icon: Icons.arrow_forward,
            backgroundColor: Color.fromRGBO(16, 17, 20, 1),
            textColor: Colors.white,
            delay: 400,
          ),
        ],
      ),
    );
  }
}

// Screen 2: Learning goal selection
class LearningGoalScreen extends StatefulWidget {
  final UserPreferences userPreferences;
  final VoidCallback onContinue;

  const LearningGoalScreen({
    Key? key,
    required this.userPreferences,
    required this.onContinue,
  }) : super(key: key);

  @override
  _LearningGoalScreenState createState() => _LearningGoalScreenState();
}

class _LearningGoalScreenState extends State<LearningGoalScreen> {
  final List<String> _goals = [
    'Professional growth',
    'Staying sharp',
    'Excelling at school',
    'Helping my child learn',
    'Helping my students learn',
    'Something else',
  ];

  String? _selectedGoal;
  String _aiMessage = '';

  @override
  void initState() {
    super.initState();
    _selectedGoal = widget.userPreferences.learningGoal;
    _updateAiMessage();
  }

  void _selectGoal(String goal) {
    setState(() {
      _selectedGoal = goal;
      widget.userPreferences.learningGoal = goal;
      _updateAiMessage();
    });
  }

  void _updateAiMessage() {
    if (_selectedGoal == null) return;

    setState(() {
      switch (_selectedGoal) {
        case 'Professional growth':
          _aiMessage = 'Prepare to be prepared for the future!';
          break;
        case 'Staying sharp':
          _aiMessage = 'Keep your mind active and engaged!';
          break;
        case 'Excelling at school':
          _aiMessage = 'Let\'s boost your academic performance!';
          break;
        case 'Helping my child learn':
          _aiMessage = 'Be the best guide for your child\'s education!';
          break;
        case 'Helping my students learn':
          _aiMessage = 'Enhance your teaching with new approaches!';
          break;
        case 'Something else':
          _aiMessage = 'Let\'s explore your unique learning journey!';
          break;
        default:
          _aiMessage = '';
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Assistant icon at the top
          Container(
            height: 80,
            width: 80,
            alignment: Alignment.centerLeft,
            child: AnimatedIconWidget(
              icon: Icons.smart_toy,
              size: 40,
              color: Color.fromRGBO(100, 45, 176, 1),
              delay: 100,
            ),
          ),

          AnimatedHeadingText(
            text: 'What\'s your goal?',
            style: TextStyle(
              color: Color.fromRGBO(16, 17, 20, 1),
              fontFamily: 'WorkSans',
              fontSize: 30,
              fontWeight: FontWeight.w600,
            ),
            delay: 200,
          ),
          SizedBox(height: 24),

          // Goal options
          Expanded(
            child: ListView.builder(
              itemCount: _goals.length,
              itemBuilder: (context, index) {
                return AnimatedSelectionOption(
                  text: _goals[index],
                  isSelected: _selectedGoal == _goals[index],
                  onTap: () => _selectGoal(_goals[index]),
                  icon: _getIconForGoal(_goals[index]),
                  index: index,
                );
              },
            ),
          ),

          // AI message with animation
          if (_aiMessage.isNotEmpty)
            AnimatedContainer(
              duration: Duration(milliseconds: 500),
              curve: Curves.easeOutCubic,
              margin: EdgeInsets.only(top: 16),
              padding: EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Color.fromRGBO(240, 247, 254, 1),
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Color.fromRGBO(100, 45, 176, 0.1),
                    blurRadius: 8,
                    offset: Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                children: [
                  PulsingIconWidget(
                    icon: Icons.lightbulb_outline,
                    size: 24,
                    color: Color.fromRGBO(100, 45, 176, 1),
                  ),
                  SizedBox(width: 8),
                  Expanded(
                    child: AnimatedBodyText(
                      text: _aiMessage,
                      style: TextStyle(
                        color: Color.fromRGBO(16, 17, 20, 1),
                        fontFamily: 'WorkSans',
                        fontSize: 14,
                      ),
                      delay: 100,
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  IconData? _getIconForGoal(String goal) {
    switch (goal) {
      case 'Professional growth':
        return Icons.business_center;
      case 'Staying sharp':
        return Icons.psychology;
      case 'Excelling at school':
        return Icons.school;
      case 'Helping my child learn':
        return Icons.child_care;
      case 'Helping my students learn':
        return Icons.groups;
      case 'Something else':
        return Icons.more_horiz;
      default:
        return null;
    }
  }
}

// Screen 3: Learning focus selection
class LearningFocusScreen extends StatefulWidget {
  final UserPreferences userPreferences;
  final VoidCallback onContinue;

  const LearningFocusScreen({
    Key? key,
    required this.userPreferences,
    required this.onContinue,
  }) : super(key: key);

  @override
  _LearningFocusScreenState createState() => _LearningFocusScreenState();
}

class _LearningFocusScreenState extends State<LearningFocusScreen> {
  final List<String> _focuses = [
    'Learning specific skills',
    'Following my curiosity',
    'Building my problem-solving skills',
    'Brushing up on the basics',
    'Something else',
  ];

  String? _selectedFocus;

  @override
  void initState() {
    super.initState();
    _selectedFocus = widget.userPreferences.learningFocus;
  }

  void _selectFocus(String focus) {
    setState(() {
      _selectedFocus = focus;
      widget.userPreferences.learningFocus = focus;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Assistant icon at the top
          Container(
            height: 80,
            width: 80,
            alignment: Alignment.centerLeft,
            child: AnimatedIconWidget(
              icon: Icons.smart_toy,
              size: 40,
              color: Color.fromRGBO(100, 45, 176, 1),
              delay: 100,
            ),
          ),

          AnimatedHeadingText(
            text: 'What do you want to focus on?',
            style: TextStyle(
              color: Color.fromRGBO(16, 17, 20, 1),
              fontFamily: 'WorkSans',
              fontSize: 30,
              fontWeight: FontWeight.w600,
            ),
            delay: 200,
          ),
          SizedBox(height: 24),

          // Focus options
          Expanded(
            child: ListView.builder(
              itemCount: _focuses.length,
              itemBuilder: (context, index) {
                return AnimatedSelectionOption(
                  text: _focuses[index],
                  isSelected: _selectedFocus == _focuses[index],
                  onTap: () => _selectFocus(_focuses[index]),
                  icon: _getIconForFocus(_focuses[index]),
                  index: index,
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  IconData? _getIconForFocus(String focus) {
    switch (focus) {
      case 'Learning specific skills':
        return Icons.build;
      case 'Following my curiosity':
        return Icons.explore;
      case 'Building my problem-solving skills':
        return Icons.lightbulb;
      case 'Brushing up on the basics':
        return Icons.auto_stories;
      case 'Something else':
        return Icons.more_horiz;
      default:
        return null;
    }
  }
}

// Screen 4: Personalized message
class PersonalizedMessageScreen extends StatelessWidget {
  final UserPreferences userPreferences;
  final VoidCallback onContinue;

  const PersonalizedMessageScreen({
    Key? key,
    required this.userPreferences,
    required this.onContinue,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    String message = _getPersonalizedMessage();

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Success icon with animation
          AnimatedIconWidget(
            icon: Icons.check_circle,
            size: 120,
            color: Color.fromRGBO(76, 175, 80, 1), // Green color
          ),
          SizedBox(height: 32),
          AnimatedHeadingText(
            text: 'You\'ll fit right in!',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Color.fromRGBO(16, 17, 20, 1),
              fontFamily: 'WorkSans',
              fontSize: 30,
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 16),
          AnimatedBodyText(
            text: message,
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Color.fromRGBO(57, 59, 66, 1),
              fontFamily: 'WorkSans',
              fontSize: 18,
              fontWeight: FontWeight.w400,
            ),
            delay: 200,
          ),
          SizedBox(height: 48),
          AnimatedButton(
            onPressed: onContinue,
            text: 'Continue',
            icon: Icons.arrow_forward,
            backgroundColor: Color.fromRGBO(16, 17, 20, 1),
            textColor: Colors.white,
            delay: 400,
          ),
        ],
      ),
    );
  }

  String _getPersonalizedMessage() {
    String goal = userPreferences.learningGoal ?? '';
    // We can use the focus in future to further personalize messages

    if (goal.contains('Professional')) {
      return 'We\'ll help you develop the skills you need to advance your career and stay ahead in your field.';
    } else if (goal.contains('school')) {
      return 'Our personalized learning approach will help you excel in your studies and achieve academic success.';
    } else if (goal.contains('child')) {
      return 'You\'ll find resources and guidance to support your child\'s learning journey effectively.';
    } else if (goal.contains('students')) {
      return 'We\'ll provide you with innovative teaching approaches to engage and inspire your students.';
    } else {
      return 'We\'re excited to join you on your learning journey and help you achieve your goals.';
    }
  }
}
