import 'package:flutter/material.dart';
import 'package:flutter/services.dart'; // For HapticFeedback
import '../../../../models/course_models.dart';

class TextInputElementWidget extends StatefulWidget {
  final TextInputElement textInputElement;
  final PageController? pageController;
  final VoidCallback? onNextAction; // For advancing slide or lesson
  final bool isLastSlideInLesson;

  const TextInputElementWidget({
    super.key,
    required this.textInputElement,
    this.pageController,
    this.onNextAction,
    this.isLastSlideInLesson = false,
  });

  @override
  State<TextInputElementWidget> createState() => _TextInputElementWidgetState();
}

class _TextInputElementWidgetState extends State<TextInputElementWidget> {
  final TextEditingController _textController = TextEditingController();
  bool _isSubmitted = false;
  bool _isCorrect = false;
  String _feedbackMessage = "";

  void _handleSubmit(String value) {
    if (_isSubmitted) return; // Prevent re-submission

    HapticFeedback.lightImpact();
    bool correct = false;
    try {
      final regex = RegExp(widget.textInputElement.correct_answer_regex);
      correct = regex.hasMatch(value.trim());
    } catch (e) {
      print(
        "Error with regex: ${widget.textInputElement.correct_answer_regex} - $e",
      );
      // Default to false if regex is invalid
    }

    setState(() {
      _isSubmitted = true;
      _isCorrect = correct;
      _feedbackMessage =
          correct
              ? widget.textInputElement.feedback_correct
              : (widget.textInputElement.feedback_incorrect ??
                  "Let's try that again.");
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(_feedbackMessage),
        backgroundColor: _isCorrect ? Colors.green : Colors.redAccent,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  @override
  void dispose() {
    _textController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: 8.0,
        vertical: 16.0,
      ), // Adjusted padding
      margin: const EdgeInsets.symmetric(
        vertical: 12.0,
        horizontal: 4.0,
      ), // Added small horizontal margin
      decoration: BoxDecoration(
        color: Colors.teal[50], // Different background for variety
        borderRadius: BorderRadius.circular(16.0), // Softer radius
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          if (widget.textInputElement.question_text != null &&
              widget.textInputElement.question_text!.isNotEmpty)
            Padding(
              padding: const EdgeInsets.only(bottom: 16.0), // More space
              child: Text(
                widget.textInputElement.question_text!,
                style: textTheme.titleLarge?.copyWith(
                  // Larger question text
                  fontWeight: FontWeight.w600, // Bolder
                  color: Colors.black87,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          Padding(
            // Add padding around TextField
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            child: TextField(
              controller: _textController,
              style: textTheme.bodyLarge?.copyWith(
                fontSize: 18,
              ), // Larger input text
              decoration: InputDecoration(
                hintText: widget.textInputElement.placeholder,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(
                    10.0,
                  ), // Rounded border for TextField
                  borderSide: BorderSide(color: Colors.grey.shade400),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10.0),
                  borderSide: BorderSide(
                    color: Theme.of(context).primaryColor,
                    width: 2.0,
                  ),
                ),
                filled: true,
                fillColor: Colors.white,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16.0,
                  vertical: 14.0,
                ), // More padding inside TextField
                suffixIcon:
                    _isSubmitted
                        ? Icon(
                          _isCorrect
                              ? Icons.check_circle_outline_rounded
                              : Icons.highlight_off_rounded, // Different icons
                          color:
                              _isCorrect
                                  ? Colors.green.shade600
                                  : Colors.red.shade600,
                          size: 26, // Larger icon
                        )
                        : null,
              ),
              onSubmitted: _handleSubmit,
              textInputAction: TextInputAction.done,
              enabled: !_isSubmitted,
            ),
          ),
          if (_isSubmitted &&
              widget.textInputElement.explanation_on_correct != null &&
              widget.textInputElement.explanation_on_correct!.isNotEmpty &&
              _isCorrect)
            Padding(
              padding: const EdgeInsets.only(top: 12.0),
              child: Container(
                padding: const EdgeInsets.all(10.0),
                decoration: BoxDecoration(
                  color: Colors.green[50],
                  borderRadius: BorderRadius.circular(6.0),
                  border: Border.all(color: Colors.green[100]!),
                ),
                child: Text(
                  widget.textInputElement.explanation_on_correct!,
                  style: TextStyle(color: Colors.green[700], fontSize: 13),
                ),
              ),
            ),
          if (_isSubmitted &&
              widget.textInputElement.action_button_text.isNotEmpty)
            Padding(
              padding: const EdgeInsets.only(
                top: 24.0,
                left: 8.0,
                right: 8.0,
              ), // More space and horizontal padding
              child: SizedBox(
                width: double.infinity,
                height: 56, // Consistent button height
                child: ElevatedButton.icon(
                  icon: Icon(
                    widget.isLastSlideInLesson && _isCorrect
                        ? Icons.skip_next_rounded
                        : Icons.arrow_forward_ios_rounded, // Different icons
                    size: 22,
                  ),
                  label: Text(widget.textInputElement.action_button_text),
                  onPressed:
                      (_isCorrect ||
                              widget.textInputElement.feedback_incorrect ==
                                  null)
                          ? widget.onNextAction
                          : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor:
                        _isCorrect
                            ? Theme.of(context).primaryColor
                            : Colors.grey.shade500,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 14.0),
                    textStyle: const TextStyle(
                      fontSize: 17.0,
                      fontWeight: FontWeight.bold,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(
                        12.0,
                      ), // Softer radius
                    ),
                    elevation: _isCorrect && _isSubmitted ? 3 : 1,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}
