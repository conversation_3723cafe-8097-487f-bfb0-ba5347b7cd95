import 'package:flutter/material.dart';
import 'package:resonance/widgets/interactive_widget_scaffold.dart';

class InteractiveEvolutionaryPathwayExplorer extends StatefulWidget {
  const InteractiveEvolutionaryPathwayExplorer({super.key});

  @override
  State<InteractiveEvolutionaryPathwayExplorer> createState() => _InteractiveEvolutionaryPathwayExplorerState();
}

class _InteractiveEvolutionaryPathwayExplorerState extends State<InteractiveEvolutionaryPathwayExplorer> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  double _evolutionProgress = 0.0; // 0.0 to 1.0, representing evolutionary progress
  String _evolutionDescription = 'Humans rely on traditional food sources. No photosynthetic adaptations.';

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 4),
    )..repeat(reverse: true);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  String _getEvolutionDescription() {
    if (_evolutionProgress < 0.2) {
      return 'Humans rely on traditional food sources. No photosynthetic adaptations. Minor genetic predispositions might exist.';
    } else if (_evolutionProgress < 0.5) {
      return 'Early evolutionary changes: Development of specialized skin cells for light absorption, increased chlorophyll production in certain tissues, and a reduced need for complex digestive systems.';
    } else if (_evolutionProgress < 0.8) {
      return 'Advanced evolutionary changes: Significant physiological restructuring, including larger surface areas for light exposure (e.g., broader limbs, thinner skin), highly efficient internal light-harvesting organs, and a symbiotic relationship with photosynthetic microorganisms.';
    } else {
      return 'Divergent species: Humans have evolved into a new species, Homo chloroflora, fully capable of photosynthesis. Their bodies are optimized for light absorption, and their societal structures revolve around maximizing solar energy intake.';
    }
  }

  IconData _getEvolutionIcon() {
    if (_evolutionProgress < 0.2) {
      return Icons.accessibility_new;
    } else if (_evolutionProgress < 0.5) {
      return Icons.self_improvement;
    } else if (_evolutionProgress < 0.8) {
      return Icons.nature_people;
    } else {
      return Icons.eco;
    }
  }

  Color _getEvolutionColor() {
    if (_evolutionProgress < 0.2) {
      return Colors.blueGrey;
    } else if (_evolutionProgress < 0.5) {
      return Colors.lightGreen.shade300;
    } else if (_evolutionProgress < 0.8) {
      return Colors.green.shade600;
    } else {
      return Colors.green.shade900;
    }
  }

  @override
  Widget build(BuildContext context) {
    return InteractiveWidgetScaffold(
      title: 'Evolutionary Pathway Explorer',
      description: 'Explore evolutionary pathways for human photosynthesis.',
      interactiveContent: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            AnimatedBuilder(
              animation: _controller,
              builder: (context, child) {
                return Transform.scale(
                  scale: 1.0 + (_controller.value * 0.1 * _evolutionProgress),
                  child: Icon(
                    _getEvolutionIcon(),
                    size: 100,
                    color: _getEvolutionColor(),
                  ),
                );
              },
            ),
            const SizedBox(height: 20),
            Text(
              'Evolutionary Progress: ${(_evolutionProgress * 100).toStringAsFixed(0)}%',
              style: const TextStyle(fontSize: 18),
            ),
            Slider(
              value: _evolutionProgress,
              min: 0.0,
              max: 1.0,
              divisions: 10,
              label: (_evolutionProgress * 100).toStringAsFixed(0),
              onChanged: (value) {
                setState(() {
                  _evolutionProgress = value;
                });
              },
            ),
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: Text(
                'Adjust the slider to explore hypothetical evolutionary stages if humans developed photosynthesis. Observe the icon and color changes, and read the description to understand the biological and societal implications.',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 16),
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Text(
                _getEvolutionDescription(),
                textAlign: TextAlign.center,
                style: const TextStyle(fontSize: 16, fontStyle: FontStyle.italic),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
