import 'package:flutter/material.dart';
import 'dart:math';

class InteractiveDerivativeInterpreter extends StatefulWidget {
  const InteractiveDerivativeInterpreter({super.key});

  @override
  State<InteractiveDerivativeInterpreter> createState() => _InteractiveDerivativeInterpreterState();
}

class _InteractiveDerivativeInterpreterState extends State<InteractiveDerivativeInterpreter> {
  double _xValue = 0.0;
  final TextEditingController _functionController = TextEditingController(text: 'x*x'); // Default function x^2

  double _function(double x) {
    try {
      if (_functionController.text.contains('x*x')) {
        return x * x;
      } else if (_functionController.text.contains('sin(x)')) {
        return sin(x);
      } else if (_functionController.text.contains('e^x')) {
        return exp(x);
      }
      return double.nan;
    } catch (e) {
      return double.nan;
    }
  }

  double _derivative(double x) {
    if (_functionController.text.contains('x*x')) {
      return 2 * x;
    } else if (_functionController.text.contains('sin(x)')) {
      return cos(x);
    } else if (_functionController.text.contains('e^x')) {
      return exp(x);
    }
    return double.nan;
  }

  String _interpretDerivative(double x, double derivativeValue) {
    if (derivativeValue.isNaN) {
      return 'Derivative is undefined at this point.';
    } else if (derivativeValue > 0.1) {
      return 'The function is increasing rapidly at x = ${x.toStringAsFixed(2)}.';
    } else if (derivativeValue < -0.1) {
      return 'The function is decreasing rapidly at x = ${x.toStringAsFixed(2)}.';
    } else if (derivativeValue >= -0.1 && derivativeValue <= 0.1) {
      return 'The function is momentarily flat (or has a local extremum) at x = ${x.toStringAsFixed(2)}.';
    }
    return 'Interpretation not available.';
  }

  @override
  Widget build(BuildContext context) {
    double derivativeValue = _derivative(_xValue);
    String interpretation = _interpretDerivative(_xValue, derivativeValue);

    return Card(
      margin: const EdgeInsets.all(16.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Interactive Derivative Interpreter',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            TextField(
              controller: _functionController,
              decoration: InputDecoration(
                labelText: 'Enter Function f(x) (e.g., x*x, sin(x), e^x)',
                border: OutlineInputBorder(),
              ),
              onChanged: (value) {
                setState(() {});
              },
            ),
            const SizedBox(height: 20),
            Text(
              'Current x: ${_xValue.toStringAsFixed(2)}',
              style: TextStyle(fontSize: 16),
            ),
            Slider(
              value: _xValue,
              min: -5.0,
              max: 5.0,
              divisions: 100,
              onChanged: (newValue) {
                setState(() {
                  _xValue = newValue;
                });
              },
            ),
            const SizedBox(height: 10),
            Text(
              'f\'(${_xValue.toStringAsFixed(2)}): ${derivativeValue.isNaN ? 'Undefined' : derivativeValue.toStringAsFixed(2)}',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: Colors.blue),
            ),
            const SizedBox(height: 20),
            Text(
              'Interpretation: $interpretation',
              style: TextStyle(fontSize: 16, fontStyle: FontStyle.italic),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            CustomPaint(
              size: Size(300, 200),
              painter: _DerivativeInterpreterGraphPainter(_function, _xValue, _derivative),
            ),
          ],
        ),
      ),
    );
  }
}

class _DerivativeInterpreterGraphPainter extends CustomPainter {
  final Function(double) _function;
  final double _x;
  final Function(double) _derivative;

  _DerivativeInterpreterGraphPainter(this._function, this._x, this._derivative);

  @override
  void paint(Canvas canvas, Size size) {
    final Paint paint = Paint()
      ..color = Colors.blue
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    final Paint pointPaint = Paint()
      ..color = Colors.red
      ..strokeWidth = 8
      ..style = PaintingStyle.fill;

    final Paint tangentLinePaint = Paint()
      ..color = Colors.green
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    final double minX = -5.0;
    final double maxX = 5.0;
    final double minY = -10.0; // Adjusted for common functions
    final double maxY = 25.0;

    final double rangeX = maxX - minX;
    final double rangeY = maxY - minY;

    final double scaleX = size.width / rangeX;
    final double scaleY = size.height / rangeY;

    Offset toCanvas(double x, double y) {
      final double canvasX = (x - minX) * scaleX;
      final double canvasY = size.height - (y - minY) * scaleY;
      return Offset(canvasX, canvasY);
    }

    // Draw axes
    canvas.drawLine(toCanvas(minX, 0), toCanvas(maxX, 0), paint..color = Colors.grey); // X-axis
    canvas.drawLine(toCanvas(0, minY), toCanvas(0, maxY), paint..color = Colors.grey); // Y-axis

    // Draw function graph
    final Path path = Path();
    bool firstPoint = true;
    for (double i = minX; i <= maxX; i += 0.1) {
      double xVal = i;
      double yVal = _function(xVal);
      if (!yVal.isNaN && yVal.isFinite) {
        if (firstPoint) {
          path.moveTo(toCanvas(xVal, yVal).dx, toCanvas(xVal, yVal).dy);
          firstPoint = false;
        } else {
          path.lineTo(toCanvas(xVal, yVal).dx, toCanvas(xVal, yVal).dy);
        }
      } else {
        firstPoint = true;
      }
    }
    canvas.drawPath(path, paint);

    // Draw point (x, f(x))
    double y = _function(_x);
    if (!y.isNaN && y.isFinite) {
      canvas.drawCircle(toCanvas(_x, y), 4, pointPaint);

      // Draw tangent line
      double slope = _derivative(_x);
      if (!slope.isNaN && slope.isFinite) {
        double xTangent1 = minX;
        double yTangent1 = slope * (xTangent1 - _x) + y;
        double xTangent2 = maxX;
        double yTangent2 = slope * (xTangent2 - _x) + y;

        canvas.drawLine(toCanvas(xTangent1, yTangent1), toCanvas(xTangent2, yTangent2), tangentLinePaint);
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    _DerivativeInterpreterGraphPainter oldPainter = oldDelegate as _DerivativeInterpreterGraphPainter;
    return oldPainter._x != _x;
  }
}
