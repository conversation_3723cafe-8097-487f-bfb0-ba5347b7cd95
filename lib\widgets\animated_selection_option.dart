import 'package:flutter/material.dart';

class AnimatedSelectionOption extends StatefulWidget {
  final String text;
  final bool isSelected;
  final VoidCallback onTap;
  final IconData? icon;
  final int index; // For staggered animation

  const AnimatedSelectionOption({
    Key? key,
    required this.text,
    required this.isSelected,
    required this.onTap,
    this.icon,
    required this.index,
  }) : super(key: key);

  @override
  _AnimatedSelectionOptionState createState() => _AnimatedSelectionOptionState();
}

class _AnimatedSelectionOptionState extends State<AnimatedSelectionOption>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: Duration(milliseconds: 300),
    );

    _scaleAnimation = Tween<double>(begin: 0.95, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.easeOutCubic,
      ),
    );

    _opacityAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.easeOut,
      ),
    );

    // Staggered animation based on index
    Future.delayed(Duration(milliseconds: 100 * widget.index), () {
      if (mounted) {
        _controller.forward();
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return FadeTransition(
          opacity: _opacityAnimation,
          child: Transform.scale(
            scale: _scaleAnimation.value,
            child: child,
          ),
        );
      },
      child: GestureDetector(
        onTap: () {
          widget.onTap();
          // Add a little bounce animation when tapped
          _controller.reset();
          _controller.forward();
        },
        child: AnimatedContainer(
          duration: Duration(milliseconds: 300),
          curve: Curves.easeOutCubic,
          margin: EdgeInsets.only(bottom: 8),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(19),
            color: widget.isSelected
                ? Color.fromRGBO(100, 45, 176, 1) // Purple when selected
                : Color.fromRGBO(243, 243, 243, 1), // Light grey when not selected
            boxShadow: widget.isSelected
                ? [
                    BoxShadow(
                      color: Color.fromRGBO(100, 45, 176, 0.25),
                      offset: Offset(0, 2),
                      blurRadius: 8,
                      spreadRadius: 0,
                    )
                  ]
                : null,
          ),
          padding: EdgeInsets.symmetric(horizontal: 16, vertical: 16),
          child: Row(
            children: [
              if (widget.icon != null) ...[
                Icon(
                  widget.icon,
                  color: widget.isSelected
                      ? Colors.white
                      : Color.fromRGBO(16, 17, 20, 1),
                  size: 20,
                ),
                SizedBox(width: 8),
              ],
              Expanded(
                child: Text(
                  widget.text,
                  style: TextStyle(
                    color: widget.isSelected
                        ? Colors.white
                        : Color.fromRGBO(16, 17, 20, 1),
                    fontFamily: 'WorkSans',
                    fontSize: 16,
                    fontWeight: widget.isSelected ? FontWeight.w500 : FontWeight.w400,
                  ),
                ),
              ),
              AnimatedOpacity(
                opacity: widget.isSelected ? 1.0 : 0.0,
                duration: Duration(milliseconds: 300),
                child: Icon(
                  Icons.check_circle_outline,
                  color: Colors.white,
                  size: 20,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
