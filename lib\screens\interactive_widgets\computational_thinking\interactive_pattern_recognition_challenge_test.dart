import 'package:flutter/material.dart';
import 'dart:math';

class InteractivePatternRecognitionChallengeTest extends StatefulWidget {
  const InteractivePatternRecognitionChallengeTest({super.key});

  @override
  State<InteractivePatternRecognitionChallengeTest> createState() => _InteractivePatternRecognitionChallengeTestState();
}

class _InteractivePatternRecognitionChallengeTestState extends State<InteractivePatternRecognitionChallengeTest> {
  int _currentQuestionIndex = 0;
  final TextEditingController _answerController = TextEditingController();
  String? _feedbackMessage;
  bool _isCorrect = false;

  final List<Map<String, dynamic>> _questions = [
    {
      'sequence': [3, 6, 9, 12],
      'question': 'What is the next number in the sequence?',
      'correctAnswer': '15',
      'explanation': 'The pattern is adding 3 to the previous number.',
    },
    {
      'sequence': [1, 2, 4, 8],
      'question': 'What is the next number in the sequence?',
      'correctAnswer': '16',
      'explanation': 'The pattern is multiplying the previous number by 2.',
    },
    {
      'sequence': [10, 8, 6, 4],
      'question': 'What is the next number in the sequence?',
      'correctAnswer': '2',
      'explanation': 'The pattern is subtracting 2 from the previous number.',
    },
    {
      'sequence': ['A', 'C', 'E', 'G'],
      'question': 'What is the next letter in the sequence?',
      'correctAnswer': 'I',
      'explanation': 'The pattern is skipping one letter in the alphabet (A, B, C, D, E, F, G, H, I).',
    },
  ];

  @override
  void initState() {
    super.initState();
    _answerController.addListener(_onAnswerChanged);
  }

  @override
  void dispose() {
    _answerController.removeListener(_onAnswerChanged);
    _answerController.dispose();
    super.dispose();
  }

  void _onAnswerChanged() {
    setState(() {
      _feedbackMessage = null; // Clear feedback when user types
    });
  }

  void _checkAnswer() {
    final currentQuestion = _questions[_currentQuestionIndex];
    setState(() {
      if (_answerController.text.trim().toLowerCase() == currentQuestion['correctAnswer'].toLowerCase()) {
        _feedbackMessage = 'Correct! ${currentQuestion['explanation']}';
        _isCorrect = true;
      } else {
        _feedbackMessage = 'Incorrect. ${currentQuestion['explanation']}';
        _isCorrect = false;
      }
    });
  }

  void _nextQuestion() {
    setState(() {
      if (_currentQuestionIndex < _questions.length - 1) {
        _currentQuestionIndex++;
        _answerController.clear();
        _feedbackMessage = null;
        _isCorrect = false;
      } else {
        _feedbackMessage = 'Test Completed! You\'ve finished the pattern recognition challenges.';
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final currentQuestion = _questions[_currentQuestionIndex];

    return Card(
      margin: const EdgeInsets.all(16.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Interactive Pattern Recognition Challenge (Module Test)',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            Text(
              'Question ${_currentQuestionIndex + 1}/${_questions.length}:',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
            ),
            const SizedBox(height: 10),
            Text(
              'Sequence: ${currentQuestion['sequence'].map((e) => e.toString()).join(', ')}',
              style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold, color: Colors.blue),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 10),
            Text(
              currentQuestion['question'],
              style: TextStyle(fontSize: 16),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            TextField(
              controller: _answerController,
              decoration: InputDecoration(
                labelText: 'Your Answer',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 10),
            ElevatedButton(
              onPressed: _checkAnswer,
              child: const Text('Check Answer'),
            ),
            if (_feedbackMessage != null)
              Padding(
                padding: const EdgeInsets.all(10.0),
                child: Text(
                  _feedbackMessage!,
                  style: TextStyle(
                    color: _isCorrect ? Colors.green : Colors.red,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            const SizedBox(height: 10),
            ElevatedButton(
              onPressed: _nextQuestion,
              child: Text(_currentQuestionIndex < _questions.length - 1 ? 'Next Question' : 'Finish Test'),
            ),
          ],
        ),
      ),
    );
  }
}
