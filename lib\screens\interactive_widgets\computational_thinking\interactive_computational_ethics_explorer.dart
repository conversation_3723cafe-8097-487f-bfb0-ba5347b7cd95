import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_markdown/flutter_markdown.dart';

class InteractiveComputationalEthicsExplorer extends ConsumerStatefulWidget {
  const InteractiveComputationalEthicsExplorer({super.key});

  @override
  ConsumerState<InteractiveComputationalEthicsExplorer> createState() => _InteractiveComputationalEthicsExplorerState();
}

class _InteractiveComputationalEthicsExplorerState extends ConsumerState<InteractiveComputationalEthicsExplorer> {
  String _scenario = '';
  String _ethicalDilemma = '';
  String _stakeholders = '';
  String _computationalAspects = '';
  String _ethicalFrameworks = '';
  String _proposedSolution = '';

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Interactive Computational Ethics Explorer',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 20),
          const MarkdownBody(
            data: '''
            This tool helps you explore ethical considerations in computational contexts.
            Define a scenario, identify the ethical dilemma, list stakeholders,
            analyze computational aspects, apply ethical frameworks, and propose a solution.
            ''',
          ),
          const SizedBox(height: 20),
          _buildSectionTitle('1. Scenario Description'),
          TextField(
            maxLines: 3,
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
              hintText: 'Describe a real-world scenario with computational implications (e.g., "AI in hiring decisions")',
            ),
            onChanged: (value) {
              setState(() {
                _scenario = value;
              });
            },
          ),
          const SizedBox(height: 20),
          _buildSectionTitle('2. Ethical Dilemma'),
          TextField(
            maxLines: 3,
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
              hintText: 'What is the core ethical conflict or challenge?',
            ),
            onChanged: (value) {
              setState(() {
                _ethicalDilemma = value;
              });
            },
          ),
          const SizedBox(height: 20),
          _buildSectionTitle('3. Stakeholders'),
          TextField(
            maxLines: 3,
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
              hintText: 'Who are the individuals or groups affected by this scenario?',
            ),
            onChanged: (value) {
              setState(() {
                _stakeholders = value;
              });
            },
          ),
          const SizedBox(height: 20),
          _buildSectionTitle('4. Computational Aspects'),
          TextField(
            maxLines: 5,
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
              hintText: 'What are the technical details, algorithms, or data involved?',
            ),
            onChanged: (value) {
              setState(() {
                _computationalAspects = value;
              });
            },
          ),
          const SizedBox(height: 20),
          _buildSectionTitle('5. Ethical Frameworks'),
          TextField(
            maxLines: 5,
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
              hintText: 'Which ethical theories apply? (e.g., "Utilitarianism, Deontology, Virtue Ethics")',
            ),
            onChanged: (value) {
              setState(() {
                _ethicalFrameworks = value;
              });
            },
          ),
          const SizedBox(height: 20),
          _buildSectionTitle('6. Proposed Solution/Mitigation'),
          TextField(
            maxLines: 5,
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
              hintText: 'How can the ethical dilemma be addressed or mitigated?',
            ),
            onChanged: (value) {
              setState(() {
                _proposedSolution = value;
              });
            },
          ),
          const SizedBox(height: 30),
          _buildEthicsSummaryCard(context),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Text(
        title,
        style: const TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
      ),
    );
  }

  Widget _buildEthicsSummaryCard(BuildContext context) {
    return Card(
      elevation: 4,
      margin: const EdgeInsets.symmetric(vertical: 20),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Your Ethical Analysis:',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold, color: Colors.redAccent),
            ),
            const Divider(),
            _buildSummaryItem('Scenario:', _scenario),
            _buildSummaryItem('Ethical Dilemma:', _ethicalDilemma),
            _buildSummaryItem('Stakeholders:', _stakeholders),
            _buildSummaryItem('Computational Aspects:', _computationalAspects),
            _buildSummaryItem('Ethical Frameworks:', _ethicalFrameworks),
            _buildSummaryItem('Proposed Solution:', _proposedSolution),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryItem(String title, String content) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 4),
          Text(
            content.isNotEmpty ? content : 'Not yet entered.',
            style: TextStyle(fontSize: 16, fontStyle: content.isEmpty ? FontStyle.italic : FontStyle.normal),
          ),
        ],
      ),
    );
  }
}
