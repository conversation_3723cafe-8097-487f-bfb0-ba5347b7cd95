import 'package:flutter/material.dart';
import 'package:flutter/services.dart'; // For input formatters

class InteractiveMomentumCalculator extends StatefulWidget {
  const InteractiveMomentumCalculator({super.key});

  @override
  State<InteractiveMomentumCalculator> createState() => _InteractiveMomentumCalculatorState();
}

class _InteractiveMomentumCalculatorState extends State<InteractiveMomentumCalculator> {
  final TextEditingController _massController = TextEditingController();
  final TextEditingController _velocityController = TextEditingController();
  double? _momentum;

  void _calculateMomentum() {
    setState(() {
      final double? mass = double.tryParse(_massController.text);
      final double? velocity = double.tryParse(_velocityController.text);

      if (mass != null && velocity != null) {
        _momentum = mass * velocity;
      } else {
        _momentum = null; // Clear momentum if input is invalid
      }
    });
  }

  @override
  void dispose() {
    _massController.dispose();
    _velocityController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Momentum Calculator'),
        backgroundColor: Colors.teal,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            TextField(
              controller: _massController,
              keyboardType: const TextInputType.numberWithOptions(decimal: true),
              inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d*'))],
              decoration: const InputDecoration(
                labelText: 'Mass (kg)',
                border: OutlineInputBorder(),
              ),
              onChanged: (_) => _calculateMomentum(),
            ),
            const SizedBox(height: 20),
            TextField(
              controller: _velocityController,
              keyboardType: const TextInputType.numberWithOptions(decimal: true),
              inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d*'))],
              decoration: const InputDecoration(
                labelText: 'Velocity (m/s)',
                border: OutlineInputBorder(),
              ),
              onChanged: (_) => _calculateMomentum(),
            ),
            const SizedBox(height: 30),
            Text(
              _momentum == null
                  ? 'Enter mass and velocity to calculate momentum.'
                  : 'Momentum (p): ${_momentum!.toStringAsFixed(2)} kg·m/s',
              style: const TextStyle(fontSize: 22, fontWeight: FontWeight.bold, color: Colors.teal),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
