import 'package:flutter/material.dart';
import 'package:resonance/widgets/interactive_widget_scaffold.dart';

class InteractiveNutritionalRequirementsAnalyzer extends StatefulWidget {
  const InteractiveNutritionalRequirementsAnalyzer({super.key});

  @override
  State<InteractiveNutritionalRequirementsAnalyzer> createState() => _InteractiveNutritionalRequirementsAnalyzerState();
}

class _InteractiveNutritionalRequirementsAnalyzerState extends State<InteractiveNutritionalRequirementsAnalyzer> {
  double _photosynthesisContribution = 0.0; // 0.0 to 1.0, representing contribution from photosynthesis
  String _nutritionalNeeds = 'Full reliance on external food sources for all nutrients.';

  void _updateNutritionalNeeds() {
    setState(() {
      if (_photosynthesisContribution < 0.2) {
        _nutritionalNeeds = 'Full reliance on external food sources for all nutrients. Photosynthesis provides minimal supplemental energy.';
      } else if (_photosynthesisContribution < 0.5) {
        _nutritionalNeeds = 'Reduced need for carbohydrates and fats from external sources. Protein and micronutrient intake still crucial.';
      } else if (_photosynthesisContribution < 0.8) {
        _nutritionalNeeds = 'Significant reduction in overall food intake. Focus shifts to obtaining essential amino acids, vitamins, and minerals from highly concentrated sources.';
      } else {
        _nutritionalNeeds = 'Minimal external nutritional requirements. Photosynthesis provides most energy. Focus on trace minerals and specific compounds not synthesized internally.';
      }
    });
  }

  @override
  void initState() {
    super.initState();
    _updateNutritionalNeeds();
  }

  @override
  Widget build(BuildContext context) {
    return InteractiveWidgetScaffold(
      title: 'Nutritional Requirements Analyzer',
      description: 'Analyze nutritional requirements with photosynthesis.',
      interactiveContent: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.food_bank, size: 100, color: Colors.orange.shade700),
            const SizedBox(height: 20),
            Text(
              'Photosynthesis Contribution: ${(_photosynthesisContribution * 100).toStringAsFixed(0)}%',
              style: const TextStyle(fontSize: 18),
            ),
            Slider(
              value: _photosynthesisContribution,
              min: 0.0,
              max: 1.0,
              divisions: 10,
              label: (_photosynthesisContribution * 100).toStringAsFixed(0),
              onChanged: (value) {
                _photosynthesisContribution = value;
                _updateNutritionalNeeds();
              },
            ),
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: Text(
                'Adjust the slider to simulate the hypothetical contribution of photosynthesis to human energy needs. Read the description below to understand how nutritional requirements might change.',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 16),
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Text(
                _nutritionalNeeds,
                textAlign: TextAlign.center,
                style: const TextStyle(fontSize: 16, fontStyle: FontStyle.italic),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
