import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that allows users to explore basic integration rules.
class InteractiveBasicIntegrationRulesToolWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveBasicIntegrationRulesToolWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  /// Factory constructor to create the widget from JSON data
  factory InteractiveBasicIntegrationRulesToolWidget.fromData(Map<String, dynamic> data) {
    return InteractiveBasicIntegrationRulesToolWidget(
      data: data,
    );
  }

  @override
  State<InteractiveBasicIntegrationRulesToolWidget> createState() => _InteractiveBasicIntegrationRulesToolWidgetState();
}

class _InteractiveBasicIntegrationRulesToolWidgetState extends State<InteractiveBasicIntegrationRulesToolWidget> {
  late double _powerN; // For f(x) = x^n

  // Colors
  late Color _primaryColor;
  late Color _secondaryColor;
  late Color _backgroundColor;
  late Color _textColor;

  @override
  void initState() {
    super.initState();
    _powerN = widget.data['initial_power_n']?.toDouble() ?? 2.0;

    _primaryColor = _parseColor(widget.data['primary_color']) ?? Colors.blue;
    _secondaryColor = _parseColor(widget.data['secondary_color']) ?? Colors.orange;
    _backgroundColor = _parseColor(widget.data['background_color']) ?? Colors.white;
    _textColor = _parseColor(widget.data['text_color']) ?? Colors.black87;
  }

  Color? _parseColor(String? hexString) {
    if (hexString == null) return null;
    hexString = hexString.replaceAll('#', '');
    if (hexString.length == 6) {
      hexString = 'FF$hexString';
    }
    try {
      return Color(int.parse(hexString, radix: 16));
    } catch (e) {
      return null;
    }
  }

  // Function to display
  String _functionExpression() {
    if (_powerN == -1) return 'f(x) = 1/x';
    return 'f(x) = x^${_powerN.toStringAsFixed(0)}';
  }

  // Antiderivative using power rule for integration
  String _antiderivativeExpression() {
    if (_powerN == -1) return 'F(x) = ln|x| + C';
    final newPower = _powerN + 1;
    if (newPower == 0) return 'F(x) = C'; // Should not happen for power rule if n != -1
    if (newPower == 1) return 'F(x) = x + C';
    return 'F(x) = (1/${newPower.toStringAsFixed(0)})x^${newPower.toStringAsFixed(0)} + C';
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _backgroundColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.data['title'] ?? 'Basic Integration Rules Tool',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),
          const SizedBox(height: 8),
          if (widget.data['description'] != null)
            Text(
              widget.data['description'],
              style: TextStyle(
                fontSize: 14,
                color: _textColor.withOpacity(0.8),
              ),
            ),
          const SizedBox(height: 16),

          Text(
            'Rule: Power Rule for Integration (∫xⁿ dx = xⁿ⁺¹/(n+1) + C, n ≠ -1)',
            style: TextStyle(color: _textColor, fontSize: 16, fontStyle: FontStyle.italic),
          ),
          const SizedBox(height: 16),

          Text(
            'Choose Power (n): ${_powerN.toStringAsFixed(0)}',
            style: TextStyle(color: _primaryColor, fontSize: 16, fontWeight: FontWeight.bold),
          ),
          Slider(
            value: _powerN,
            min: -2.0, // Avoid n=-1 for this rule
            max: 5.0,
            divisions: 7,
            label: _powerN.toStringAsFixed(0),
            activeColor: _primaryColor,
            onChanged: (value) {
              setState(() {
                _powerN = value;
              });
            },
          ),
          const SizedBox(height: 16),

          Center(
            child: Column(
              children: [
                Text(
                  'Original Function:',
                  style: TextStyle(color: _textColor, fontSize: 16),
                ),
                Text(
                  _functionExpression(),
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: _primaryColor,
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  'Antiderivative:',
                  style: TextStyle(color: _textColor, fontSize: 16),
                ),
                Text(
                  _antiderivativeExpression(),
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: _secondaryColor,
                  ),
                ),
              ],
            ),
          ),

          // Widget name tag
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'InteractiveBasicIntegrationRulesTool',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }
}
