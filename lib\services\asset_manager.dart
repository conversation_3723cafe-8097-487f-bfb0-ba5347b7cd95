import 'dart:io';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:path_provider/path_provider.dart';
import '../models/course_models.dart';

/// Service for managing course assets (images, animations, videos)
class AssetManager {
  static final AssetManager _instance = AssetManager._internal();

  // Singleton instance
  factory AssetManager() {
    return _instance;
  }

  AssetManager._internal();

  // Cache for loaded assets
  final Map<String, String> _cachedAssets = {};

  // API keys for image services
  final String _giphyApiKey = 'AP703A14Crc2Dwv1zrF022Q2iLatsCZM';
  final String _unsplashApiKey = '*******************************************';

  /// Get the local path for an asset, downloading if needed
  Future<String> getAssetPath(
    String categoryId,
    String courseId,
    String assetPath,
    String? remoteUrl,
  ) async {
    // Create a unique cache key
    final cacheKey = '$categoryId/$courseId/$assetPath';

    // Check if already cached
    if (_cachedAssets.containsKey(cacheKey)) {
      return _cachedAssets[cacheKey]!;
    }

    // For web platform or if no remote URL, use bundled assets
    if (kIsWeb || remoteUrl == null || remoteUrl.isEmpty) {
      final bundledPath = 'assets/$categoryId/$courseId/$assetPath';
      _cachedAssets[cacheKey] = bundledPath;
      return bundledPath;
    }

    try {
      // Determine local storage path
      final appDir = await getApplicationDocumentsDirectory();
      final localDir = Directory(
        '${appDir.path}/courses/$categoryId/$courseId',
      );
      final localPath = '${localDir.path}/$assetPath';
      final file = File(localPath);

      // Check if file exists locally
      if (await file.exists()) {
        _cachedAssets[cacheKey] = localPath;
        return localPath;
      }

      // Create directory if it doesn't exist
      if (!await localDir.exists()) {
        await localDir.create(recursive: true);
      }

      // Download the file
      final response = await http.get(Uri.parse(remoteUrl));
      if (response.statusCode == 200) {
        await file.writeAsBytes(response.bodyBytes);
        _cachedAssets[cacheKey] = localPath;
        return localPath;
      } else {
        throw Exception('Failed to download asset: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error getting asset path: $e');
      // Fallback to bundled asset
      final bundledPath = 'assets/placeholder.png';
      _cachedAssets[cacheKey] = bundledPath;
      return bundledPath;
    }
  }

  /// Preload assets for a module
  Future<void> preloadModuleAssets(
    String categoryId,
    String courseId,
    Module module,
  ) async {
    final assetPaths = <String, String>{};

    // Extract all asset paths from the module
    for (final lessonDef in module.lessons) {
      // lessons are LessonDefinition
      for (final contentScreen in lessonDef.contentBlocks) {
        // contentBlocks are ContentScreen
        final visual = contentScreen.content.visual;
        // For now, only preloading LocalAssetVisual as it's a direct path
        // Giphy/Unsplash are API calls at render time.
        // Other visual types might need specific handling if they have downloadable remote URLs.
        if (visual is LocalAssetVisual) {
          // Assuming LocalAssetVisual's 'value' is the path and it doesn't have a separate remoteUrl for preloading.
          // If LocalAssetVisual could also have a remoteUrl for downloading to local cache, that logic would be added here.
          // For now, we assume local assets are bundled and getAssetPath will resolve them.
          // If a remoteUrl were present on LocalAssetVisual for optional download:
          // if (visual.remoteUrl != null) {
          //   assetPaths[visual.value] = visual.remoteUrl!;
          // }
        }
        // Example for other types if they had remote URLs for preloading:
        // else if (visual is SomeOtherVisualWithRemoteUrl) {
        //   if (visual.remoteDownloadUrl != null) {
        //     assetPaths[visual.localPathKey] = visual.remoteDownloadUrl!;
        //   }
        // }
      }
    }

    // Download all assets in parallel
    // This part assumes assetPaths map is populated with {localPathKey: remoteUrl}
    // If LocalAssetVisuals are purely local, this map might be empty or logic needs adjustment.
    // For now, if assetPaths is empty, Future.wait will do nothing.
    await Future.wait(
      assetPaths.entries.map(
        (entry) => getAssetPath(categoryId, courseId, entry.key, entry.value),
      ),
    );
  }

  /// Search for an image on Unsplash
  Future<String?> searchUnsplashImage(String query) async {
    try {
      final response = await http.get(
        Uri.parse(
          'https://api.unsplash.com/search/photos?query=$query&per_page=1',
        ),
        headers: {'Authorization': 'Client-ID $_unsplashApiKey'},
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['results'] != null && data['results'].isNotEmpty) {
          return data['results'][0]['urls']['regular'];
        }
      }
      return null;
    } catch (e) {
      debugPrint('Error searching Unsplash: $e');
      return null;
    }
  }

  /// Search for a GIF on Giphy
  Future<String?> searchGiphyGif(String query) async {
    try {
      final response = await http.get(
        Uri.parse(
          'https://api.giphy.com/v1/gifs/search?api_key=$_giphyApiKey&q=$query&limit=1',
        ),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['data'] != null && data['data'].isNotEmpty) {
          return data['data'][0]['images']['original']['url'];
        }
      }
      return null;
    } catch (e) {
      debugPrint('Error searching Giphy: $e');
      return null;
    }
  }

  /// Clear the asset cache
  void clearCache() {
    _cachedAssets.clear();
  }

  /// Get cached asset count for debugging
  int get cachedAssetCount => _cachedAssets.length;

  /// Check if asset is cached
  bool isAssetCached(String categoryId, String courseId, String assetPath) {
    final cacheKey = '$categoryId/$courseId/$assetPath';
    return _cachedAssets.containsKey(cacheKey);
  }
}
