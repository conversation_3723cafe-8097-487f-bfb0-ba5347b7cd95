import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'screens/leagues/leagues_screen.dart';
import 'services/course_service.dart';
import 'services/interactive_widget_service.dart';
import 'services/service_provider.dart';
import 'utils/page_transitions.dart';
import 'screens/interactive_widgets/interactive_widgets_showcase.dart';
import 'screens/main_app_scaffold.dart';

void main() async {
  // Ensure Flutter is initialized
  WidgetsFlutterBinding.ensureInitialized();

  // Hide system UI during splash for a more immersive experience
  SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersive);

  // Set system UI to be transparent
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      systemNavigationBarColor: Colors.black,
    ),
  );

  // Initialize services
  final courseService = CourseService();
  await courseService.initialize();

  // Complete the calculus course
  courseService.completeCourse('calculus');

  final interactiveWidgetService = InteractiveWidgetService();
  await interactiveWidgetService.initialize();

  // Print the number of widgets loaded for debugging
  debugPrint(
    'Loaded ${interactiveWidgetService.getInteractiveWidgets().length} interactive widgets',
  );

  // Run the app with a custom splash screen
  runApp(
    MyApp(
      courseService: courseService,
      interactiveWidgetService: interactiveWidgetService,
    ),
  );
}

class MyApp extends StatelessWidget {
  final CourseService courseService;
  final InteractiveWidgetService interactiveWidgetService;

  const MyApp({
    super.key,
    required this.courseService,
    required this.interactiveWidgetService,
  });

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    return ServiceProvider(
      courseService: courseService,
      interactiveWidgetService: interactiveWidgetService,
      child: MaterialApp(
        title: 'Resonance',
        debugShowCheckedModeBanner: false,
        routes: {
          '/leagues': (context) => const LeaguesScreen(),
          '/interactive_widgets':
              (context) => const InteractiveWidgetsShowcase(),
        },
        theme: ThemeData(
          primarySwatch: Colors.blue,
          scaffoldBackgroundColor: Colors.grey[100], // Light background
          fontFamily: 'WorkSans', // Using WorkSans as our custom font
          appBarTheme: AppBarTheme(
            backgroundColor: Colors.grey[100], // Match scaffold background
            elevation: 0, // No shadow
            iconTheme: IconThemeData(
              color: Colors.grey[800],
            ), // Drawer icon color
            titleTextStyle: TextStyle(
              color: Colors.grey[800],
              fontSize: 18,
              fontWeight: FontWeight.w500,
              fontFamily: 'WorkSans',
            ),
          ),
          // We're using a custom navigation bar, so we don't need the theme here
          chipTheme: ChipThemeData(
            backgroundColor: Colors.green[100],
            labelStyle: TextStyle(
              color: Colors.green[800],
              fontWeight: FontWeight.bold,
              fontFamily: 'WorkSans',
            ),
            padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 2.0),
          ),
          // Define TabBar theme for the indicator and labels
          tabBarTheme: TabBarTheme(
            indicator: UnderlineTabIndicator(
              borderSide: BorderSide(color: Colors.blue[700]!, width: 3.0),
              insets: const EdgeInsets.symmetric(
                horizontal: 16.0,
              ), // Adjust indicator width/padding
            ),
            labelColor: Colors.blue[700],
            unselectedLabelColor: Colors.grey[600],
            labelStyle: const TextStyle(
              fontWeight: FontWeight.bold,
              fontFamily: 'WorkSans',
            ),
            unselectedLabelStyle: const TextStyle(
              fontWeight: FontWeight.normal,
              fontFamily: 'WorkSans',
            ),
          ),
          useMaterial3: true,
        ),
        home: const CustomSplashScreen(),
      ),
    );
  }
}

// Custom splash screen with RESONANCE text
class CustomSplashScreen extends StatefulWidget {
  const CustomSplashScreen({super.key});

  @override
  State<CustomSplashScreen> createState() => _CustomSplashScreenState();
}

class _CustomSplashScreenState extends State<CustomSplashScreen> {
  @override
  void initState() {
    super.initState();
    // Navigate to welcome screen after a short delay
    // In a real app, you would check if the user is logged in here
    // and navigate to the appropriate screen
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        // Navigate to the main app scaffold with courses screen selected
        AppNavigator.pushReplacement(
          context,
          const MainAppScaffold(
            initialIndex: 1,
          ), // 1 is the index for Courses screen
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text(
              'Resonance.',
              style: TextStyle(
                color: Colors.white,
                fontSize: 37,
                fontWeight: FontWeight.bold,
                letterSpacing: 3.0,
              ),
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }
}
