import 'package:flutter/material.dart';

class SelectionOption extends StatelessWidget {
  final String text;
  final bool isSelected;
  final VoidCallback onTap;
  final IconData? icon;

  const SelectionOption({
    Key? key,
    required this.text,
    required this.isSelected,
    required this.onTap,
    this.icon,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: EdgeInsets.only(bottom: 8),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(19),
          color: isSelected 
              ? Color.fromRGBO(100, 45, 176, 1) // Purple when selected
              : Color.fromRGBO(243, 243, 243, 1), // Light grey when not selected
          boxShadow: isSelected 
              ? [
                  BoxShadow(
                    color: Color.fromRGBO(100, 45, 176, 0.25),
                    offset: Offset(0, 0),
                    blurRadius: 4,
                  )
                ] 
              : null,
        ),
        padding: EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        child: Row(
          children: [
            if (icon != null) ...[
              Icon(
                icon,
                color: isSelected ? Colors.white : Color.fromRGBO(16, 17, 20, 1),
                size: 20,
              ),
              SizedBox(width: 8),
            ],
            Expanded(
              child: Text(
                text,
                style: TextStyle(
                  color: isSelected 
                      ? Colors.white 
                      : Color.fromRGBO(16, 17, 20, 1),
                  fontFamily: 'WorkSans',
                  fontSize: 16,
                  fontWeight: isSelected ? FontWeight.w500 : FontWeight.w400,
                ),
              ),
            ),
            if (isSelected)
              Icon(
                Icons.check_circle_outline,
                color: Colors.white,
                size: 20,
              ),
          ],
        ),
      ),
    );
  }
}
