import 'package:flutter/material.dart';
import 'dart:math';
import 'package:resonance/widgets/interactive_widget_scaffold.dart';

class InteractiveAtmosphericEffectsVisualizer extends StatefulWidget {
  const InteractiveAtmosphericEffectsVisualizer({super.key});

  @override
  State<InteractiveAtmosphericEffectsVisualizer> createState() =>
      _InteractiveAtmosphericEffectsVisualizerState();
}

class _InteractiveAtmosphericEffectsVisualizerState
    extends State<InteractiveAtmosphericEffectsVisualizer> {
  final TextEditingController _gravityController = TextEditingController(text: '9.81');
  final TextEditingController _temperatureController = TextEditingController(text: '288.15'); // Kelvin
  final TextEditingController _gasDensityController = TextEditingController(text: '1.225'); // kg/m^3

  double _gravity = 9.81; // m/s^2
  double _temperature = 288.15; // Kelvin (15 C)
  double _gasDensity = 1.225; // kg/m^3 (standard air density at sea level)

  List<double> _pressureProfile = [];
  List<double> _densityProfile = [];

  @override
  void initState() {
    super.initState();
    _calculateAtmosphericProfiles();
  }

  void _calculateAtmosphericProfiles() {
    _pressureProfile.clear();
    _densityProfile.clear();

    // Simplified barometric formula for isothermal atmosphere
    // P(h) = P0 * exp(-Mgh / (RT))
    // rho(h) = rho0 * exp(-Mgh / (RT))
    // Where M = molar mass of air (0.029 kg/mol), R = ideal gas constant (8.314 J/(mol·K))
    double P0 = 101325; // Pa (sea level pressure)
    double M = 0.029; // kg/mol
    double R = 8.314; // J/(mol·K)

    for (int h = 0; h <= 100; h++) { // Altitude from 0 to 10 km (simplified to 100 units for visualization)
      double altitudeKm = h / 10.0; // Scale to 10 km max altitude
      double pressure = P0 * exp(-M * _gravity * (altitudeKm * 1000) / (R * _temperature));
      double density = _gasDensity * exp(-M * _gravity * (altitudeKm * 1000) / (R * _temperature));
      _pressureProfile.add(pressure);
      _densityProfile.add(density);
    }
  }

  void _visualizeEffects() {
    setState(() {
      _gravity = double.tryParse(_gravityController.text) ?? _gravity;
      _temperature = double.tryParse(_temperatureController.text) ?? _temperature;
      _gasDensity = double.tryParse(_gasDensityController.text) ?? _gasDensity;
      _calculateAtmosphericProfiles();
    });
  }

  @override
  void dispose() {
    _gravityController.dispose();
    _temperatureController.dispose();
    _gasDensityController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return InteractiveWidgetScaffold(
      title: 'Atmospheric Effects Visualizer',
      interactiveWidget: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _gravityController,
                    keyboardType: TextInputType.number,
                    decoration: const InputDecoration(
                      labelText: 'Gravity (m/s²)',
                      border: OutlineInputBorder(),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextField(
                    controller: _temperatureController,
                    keyboardType: TextInputType.number,
                    decoration: const InputDecoration(
                      labelText: 'Temperature (K)',
                      border: OutlineInputBorder(),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _gasDensityController,
                    keyboardType: TextInputType.number,
                    decoration: const InputDecoration(
                      labelText: 'Gas Density (kg/m³)',
                      border: OutlineInputBorder(),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(child: Container()), // Empty space
              ],
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _visualizeEffects,
              child: const Text('Visualize Effects'),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: CustomPaint(
                painter: AtmospherePainter(
                  pressureProfile: _pressureProfile,
                  densityProfile: _densityProfile,
                ),
                child: Container(),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class AtmospherePainter extends CustomPainter {
  final List<double> pressureProfile;
  final List<double> densityProfile;

  AtmospherePainter({
    required this.pressureProfile,
    required this.densityProfile,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final pressurePaint = Paint()
      ..color = Colors.blue
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;

    final densityPaint = Paint()
      ..color = Colors.green
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;

    // Draw pressure profile
    if (pressureProfile.isNotEmpty) {
      double maxPressure = pressureProfile.reduce(max);
      double minPressure = pressureProfile.reduce(min);
      double pressureRange = maxPressure - minPressure;

      final pressurePath = Path();
      pressurePath.moveTo(
        0,
        size.height - (pressureProfile[0] - minPressure) / pressureRange * size.height,
      );
      for (int i = 1; i < pressureProfile.length; i++) {
        double x = i / (pressureProfile.length - 1) * size.width;
        double y = size.height - (pressureProfile[i] - minPressure) / pressureRange * size.height;
        pressurePath.lineTo(x, y);
      }
      canvas.drawPath(pressurePath, pressurePaint);
    }

    // Draw density profile
    if (densityProfile.isNotEmpty) {
      double maxDensity = densityProfile.reduce(max);
      double minDensity = densityProfile.reduce(min);
      double densityRange = maxDensity - minDensity;

      final densityPath = Path();
      densityPath.moveTo(
        0,
        size.height - (densityProfile[0] - minDensity) / densityRange * size.height,
      );
      for (int i = 1; i < densityProfile.length; i++) {
        double x = i / (densityProfile.length - 1) * size.width;
        double y = size.height - (densityProfile[i] - minDensity) / densityRange * size.height;
        densityPath.lineTo(x, y);
      }
      canvas.drawPath(densityPath, densityPaint);
    }

    // Add labels (simplified)
    TextPainter(
      text: const TextSpan(text: 'Pressure', style: TextStyle(color: Colors.blue, fontSize: 10)),
      textDirection: TextDirection.ltr,
    )..layout(minWidth: 0, maxWidth: size.width)
        ..paint(canvas, Offset(size.width - 60, 0));

    TextPainter(
      text: const TextSpan(text: 'Density', style: TextStyle(color: Colors.green, fontSize: 10)),
      textDirection: TextDirection.ltr,
    )..layout(minWidth: 0, maxWidth: size.width)
        ..paint(canvas, Offset(size.width - 60, 20));
  }

  @override
  bool shouldRepaint(covariant AtmospherePainter oldDelegate) {
    return oldDelegate.pressureProfile != pressureProfile ||
        oldDelegate.densityProfile != densityProfile;
  }
}
