import 'package:flutter/material.dart';
import 'package:resonance_app/widgets/interactive_widget_scaffold.dart';
import 'dart:math';

class InteractiveDiodeCharacteristicsTool extends StatefulWidget {
  const InteractiveDiodeCharacteristicsTool({super.key});

  @override
  State<InteractiveDiodeCharacteristicsTool> createState() => _InteractiveDiodeCharacteristicsToolState();
}

class _InteractiveDiodeCharacteristicsToolState extends State<InteractiveDiodeCharacteristicsTool> {
  double voltage = 0.0; // Volts
  double saturationCurrent = 1e-12; // Amperes (Is)
  double thermalVoltage = 0.025; // Volts (Vt at room temp)
  double idealityFactor = 1.0; // n

  double current = 0.0; // Amperes

  @override
  void initState() {
    super.initState();
    _calculateDiodeCurrent();
  }

  void _calculateDiodeCurrent() {
    setState(() {
      // Shockley Diode Equation: I = Is * (exp(V / (n * Vt)) - 1)
      current = saturationCurrent * (exp(voltage / (idealityFactor * thermalVoltage)) - 1);
    });
  }

  @override
  Widget build(BuildContext context) {
    return InteractiveWidgetScaffold(
      title: 'Interactive Diode Characteristics Tool',
      description: 'Explore the current-voltage characteristics of a diode using the Shockley Diode Equation.',
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text(
              'Applied Voltage (V): ${voltage.toStringAsFixed(2)} V',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            Slider(
              value: voltage,
              min: -1.0,
              max: 0.8,
              divisions: 180,
              label: voltage.toStringAsFixed(2),
              onChanged: (value) {
                setState(() {
                  voltage = value;
                  _calculateDiodeCurrent();
                });
              },
            ),
            const SizedBox(height: 16.0),
            Text(
              'Saturation Current (Is): ${saturationCurrent.toStringAsExponential(2)} A',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            Slider(
              value: log(saturationCurrent), // Use log for slider to handle wide range
              min: log(1e-15),
              max: log(1e-9),
              divisions: 60,
              label: saturationCurrent.toStringAsExponential(2),
              onChanged: (value) {
                setState(() {
                  saturationCurrent = exp(value);
                  _calculateDiodeCurrent();
                });
              },
            ),
            const SizedBox(height: 16.0),
            Text(
              'Ideality Factor (n): ${idealityFactor.toStringAsFixed(1)}',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            Slider(
              value: idealityFactor,
              min: 1.0,
              max: 2.0,
              divisions: 10,
              label: idealityFactor.toStringAsFixed(1),
              onChanged: (value) {
                setState(() {
                  idealityFactor = value;
                  _calculateDiodeCurrent();
                });
              },
            ),
            const SizedBox(height: 24.0),
            Text(
              'Diode Current (I): ${current.toStringAsExponential(2)} A',
              style: Theme.of(context).textTheme.headlineSmall,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
