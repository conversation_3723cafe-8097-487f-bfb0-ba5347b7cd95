import 'package:flutter/material.dart';
import 'package:resonance/widgets/interactive_widget_scaffold.dart';

class InteractiveCulturalDevelopmentSimulator extends StatefulWidget {
  const InteractiveCulturalDevelopmentSimulator({super.key});

  @override
  State<InteractiveCulturalDevelopmentSimulator> createState() => _InteractiveCulturalDevelopmentSimulatorState();
}

class _InteractiveCulturalDevelopmentSimulatorState extends State<InteractiveCulturalDevelopmentSimulator> {
  double _moonInfluenceFactor = 0.5; // 0.0 to 1.0, representing influence of second moon
  String _culturalImpact = 'Minimal impact on cultural development.';

  void _updateCulturalImpact() {
    setState(() {
      if (_moonInfluenceFactor < 0.2) {
        _culturalImpact = 'Minimal impact on cultural development. Traditions and societal structures remain largely unchanged.';
      } else if (_moonInfluenceFactor < 0.5) {
        _culturalImpact = 'Moderate impact: New myths, calendars, and artistic expressions emerge. Some shifts in daily routines and festivals.';
      } else if (_moonInfluenceFactor < 0.8) {
        _culturalImpact = 'Significant impact: Profound changes in religion, philosophy, and social organization. New architectural styles and navigation techniques develop.';
      } else {
        _culturalImpact = 'Extreme impact: Radical transformation of human civilization. Dual-moon worship, complex celestial navigation, and a completely re-imagined sense of time and space.';
      }
    });
  }

  @override
  void initState() {
    super.initState();
    _updateCulturalImpact();
  }

  @override
  Widget build(BuildContext context) {
    return InteractiveWidgetScaffold(
      title: 'Cultural Development Simulator',
      description: 'Simulate cultural and societal development with two moons.',
      interactiveContent: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.public, size: 100, color: Colors.brown),
            const SizedBox(height: 20),
            Text(
              'Second Moon Cultural Influence: ${(_moonInfluenceFactor * 100).toStringAsFixed(0)}%',
              style: const TextStyle(fontSize: 18),
            ),
            Slider(
              value: _moonInfluenceFactor,
              min: 0.0,
              max: 1.0,
              divisions: 10,
              label: (_moonInfluenceFactor * 100).toStringAsFixed(0),
              onChanged: (value) {
                _moonInfluenceFactor = value;
                _updateCulturalImpact();
              },
            ),
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: Text(
                'Adjust the slider to simulate the hypothetical cultural influence of a second moon. Read the description below to understand the potential impacts on human civilization, including myths, calendars, and societal structures.',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 16),
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Text(
                _culturalImpact,
                textAlign: TextAlign.center,
                style: const TextStyle(fontSize: 16, fontStyle: FontStyle.italic),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
