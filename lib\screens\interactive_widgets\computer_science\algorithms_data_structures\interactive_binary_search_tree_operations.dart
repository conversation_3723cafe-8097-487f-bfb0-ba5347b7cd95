import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:resonance_app/services/interactive_widget_service.dart';
import 'package:resonance_app/widgets/animated_button.dart';
import 'dart:math';

class InteractiveBinarySearchTreeOperations extends StatefulWidget {
  final String widgetId;

  const InteractiveBinarySearchTreeOperations({Key? key, required this.widgetId}) : super(key: key);

  @override
  _InteractiveBinarySearchTreeOperationsState createState() => _InteractiveBinarySearchTreeOperationsState();
}

class _InteractiveBinarySearchTreeOperationsState extends State<InteractiveBinarySearchTreeOperations> {
  TreeNode? _root;
  TextEditingController _insertController = TextEditingController();
  TextEditingController _searchController = TextEditingController();
  TextEditingController _deleteController = TextEditingController();
  String _message = '';
  double _animationSpeed = 500; // milliseconds

  @override
  void initState() {
    super.initState();
    _loadState();
    _buildInitialTree();
  }

  void _loadState() async {
    final service = Provider.of<InteractiveWidgetService>(context, listen: false);
    final savedState = await service.getWidgetState(widget.widgetId);
    if (savedState != null) {
      setState(() {
        _root = _deserializeTree(savedState['tree']);
        _message = savedState['message'] ?? '';
        _animationSpeed = savedState['animationSpeed'] ?? 500;
      });
    }
  }

  void _saveState() {
    final service = Provider.of<InteractiveWidgetService>(context, listen: false);
    service.saveWidgetState(widget.widgetId, {
      'tree': _serializeTree(_root),
      'message': _message,
      'animationSpeed': _animationSpeed,
    });
  }

  Map<String, dynamic>? _serializeTree(TreeNode? node) {
    if (node == null) return null;
    return {
      'value': node.value,
      'left': _serializeTree(node.left),
      'right': _serializeTree(node.right),
    };
  }

  TreeNode? _deserializeTree(Map<String, dynamic>? data) {
    if (data == null) return null;
    final node = TreeNode(data['value']);
    node.left = _deserializeTree(data['left']);
    node.right = _deserializeTree(data['right']);
    return node;
  }

  void _buildInitialTree() {
    setState(() {
      _root = null;
      _insertNode(50);
      _insertNode(30);
      _insertNode(70);
      _insertNode(20);
      _insertNode(40);
      _insertNode(60);
      _insertNode(80);
      _message = 'Initial tree built.';
    });
    _saveState();
  }

  void _insertNode(int value) {
    if (_root == null) {
      _root = TreeNode(value);
      return;
    }
    TreeNode? current = _root;
    while (current != null) {
      if (value < current.value) {
        if (current.left == null) {
          current.left = TreeNode(value);
          return;
        }
        current = current.left;
      } else if (value > current.value) {
        if (current.right == null) {
          current.right = TreeNode(value);
          return;
        }
        current = current.right;
      } else {
        // Value already exists
        return;
      }
    }
  }

  void _handleInsert() {
    final value = int.tryParse(_insertController.text);
    if (value != null) {
      setState(() {
        _insertNode(value);
        _message = 'Inserted $value into the tree.';
      });
      _insertController.clear();
      _saveState();
    } else {
      setState(() {
        _message = 'Please enter a valid number to insert.';
      });
    }
  }

  void _handleSearch() {
    final value = int.tryParse(_searchController.text);
    if (value != null) {
      setState(() {
        final found = _searchNode(_root, value);
        _message = found ? 'Value $value found in the tree.' : 'Value $value not found in the tree.';
      });
      _searchController.clear();
      _saveState();
    } else {
      setState(() {
        _message = 'Please enter a valid number to search.';
      });
    }
  }

  bool _searchNode(TreeNode? node, int value) {
    if (node == null) return false;
    if (node.value == value) return true;
    if (value < node.value) {
      return _searchNode(node.left, value);
    } else {
      return _searchNode(node.right, value);
    }
  }

  void _handleDelete() {
    final value = int.tryParse(_deleteController.text);
    if (value != null) {
      setState(() {
        _root = _deleteNode(_root, value);
        _message = 'Deleted $value from the tree.';
      });
      _deleteController.clear();
      _saveState();
    } else {
      setState(() {
        _message = 'Please enter a valid number to delete.';
      });
    }
  }

  TreeNode? _deleteNode(TreeNode? node, int value) {
    if (node == null) return node;

    if (value < node.value) {
      node.left = _deleteNode(node.left, value);
    } else if (value > node.value) {
      node.right = _deleteNode(node.right, value);
    } else {
      if (node.left == null) {
        return node.right;
      } else if (node.right == null) {
        return node.left;
      }
      node.value = _minValue(node.right!);
      node.right = _deleteNode(node.right, node.value);
    }
    return node;
  }

  int _minValue(TreeNode node) {
    int minValue = node.value;
    while (node.left != null) {
      minValue = node.left!.value;
      node = node.left!;
    }
    return minValue;
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Interactive Binary Search Tree Operations',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _insertController,
                  keyboardType: TextInputType.number,
                  decoration: const InputDecoration(
                    labelText: 'Insert Value',
                    border: OutlineInputBorder(),
                  ),
                ),
              ),
              const SizedBox(width: 10),
              AnimatedButton(
                onTap: _handleInsert,
                text: 'Insert',
                color: Colors.blue,
              ),
            ],
          ),
          const SizedBox(height: 10),
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _searchController,
                  keyboardType: TextInputType.number,
                  decoration: const InputDecoration(
                    labelText: 'Search Value',
                    border: OutlineInputBorder(),
                  ),
                ),
              ),
              const SizedBox(width: 10),
              AnimatedButton(
                onTap: _handleSearch,
                text: 'Search',
                color: Colors.green,
              ),
            ],
          ),
          const SizedBox(height: 10),
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _deleteController,
                  keyboardType: TextInputType.number,
                  decoration: const InputDecoration(
                    labelText: 'Delete Value',
                    border: OutlineInputBorder(),
                  ),
                ),
              ),
              const SizedBox(width: 10),
              AnimatedButton(
                onTap: _handleDelete,
                text: 'Delete',
                color: Colors.red,
              ),
            ],
          ),
          const SizedBox(height: 10),
          AnimatedButton(
            onTap: _buildInitialTree,
            text: 'Reset Tree',
            color: Colors.orange,
          ),
          const SizedBox(height: 20),
          Text(
            'Tree Visualization (Simplified):',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 10),
          Expanded(
            child: SingleChildScrollView(
              child: _root == null
                  ? const Center(child: Text('Tree is empty.'))
                  : TreeView(root: _root!),
            ),
          ),
          const SizedBox(height: 20),
          Text(
            'Message: $_message',
            style: Theme.of(context).textTheme.bodyLarge,
          ),
        ],
      ),
    );
  }
}

class TreeNode {
  int value;
  TreeNode? left;
  TreeNode? right;

  TreeNode(this.value);
}

class TreeView extends StatelessWidget {
  final TreeNode root;

  const TreeView({Key? key, required this.root}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        return CustomPaint(
          painter: TreePainter(root),
          size: Size(constraints.maxWidth, _calculateTreeHeight(root) * 50.0 + 50), // Adjust height dynamically
        );
      },
    );
  }

  double _calculateTreeHeight(TreeNode? node) {
    if (node == null) return 0;
    return 1 + max(_calculateTreeHeight(node.left), _calculateTreeHeight(node.right));
  }
}

class TreePainter extends CustomPainter {
  final TreeNode root;
  final double nodeRadius = 15;
  final double horizontalSpacing = 40;
  final double verticalSpacing = 50;

  TreePainter(this.root);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.black
      ..strokeWidth = 2;

    _drawNode(canvas, root, size.width / 2, nodeRadius + 10, size.width / 4);
  }

  void _drawNode(Canvas canvas, TreeNode node, double x, double y, double horizontalOffset) {
    // Draw node circle
    canvas.drawCircle(Offset(x, y), nodeRadius, Paint()..color = Colors.blue);
    // Draw node text
    TextPainter textPainter = TextPainter(
      text: TextSpan(
        text: node.value.toString(),
        style: const TextStyle(color: Colors.white, fontSize: 12, fontWeight: FontWeight.bold),
      ),
      textDirection: TextDirection.ltr,
    );
    textPainter.layout();
    textPainter.paint(canvas, Offset(x - textPainter.width / 2, y - textPainter.height / 2));

    // Draw lines to children
    if (node.left != null) {
      canvas.drawLine(Offset(x, y + nodeRadius), Offset(x - horizontalOffset, y + verticalSpacing - nodeRadius), Paint()..color = Colors.black..strokeWidth = 2);
      _drawNode(canvas, node.left!, x - horizontalOffset, y + verticalSpacing, horizontalOffset / 2);
    }
    if (node.right != null) {
      canvas.drawLine(Offset(x, y + nodeRadius), Offset(x + horizontalOffset, y + verticalSpacing - nodeRadius), Paint()..color = Colors.black..strokeWidth = 2);
      _drawNode(canvas, node.right!, x + horizontalOffset, y + verticalSpacing, horizontalOffset / 2);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}
