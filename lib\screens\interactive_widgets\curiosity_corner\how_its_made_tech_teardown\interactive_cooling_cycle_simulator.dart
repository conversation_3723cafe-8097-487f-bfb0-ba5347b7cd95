import 'package:flutter/material.dart';
import 'dart:math';

class InteractiveCoolingCycleSimulator extends StatefulWidget {
  const InteractiveCoolingCycleSimulator({super.key});

  @override
  InteractiveCoolingCycleSimulatorState createState() => InteractiveCoolingCycleSimulatorState();
}

class InteractiveCoolingCycleSimulatorState extends State<InteractiveCoolingCycleSimulator> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;
  double _temperature = 25.0; // Initial temperature in Celsius
  bool _isCooling = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 5),
    );
    _animation = Tween<double>(begin: 0.0, end: 1.0).animate(_controller)
      ..addListener(() {
        setState(() {
          if (_isCooling) {
            _temperature = 25.0 - (25.0 - 4.0) * _animation.value; // Cools from 25C to 4C
          } else {
            _temperature = 4.0 + (25.0 - 4.0) * _animation.value; // Warms from 4C to 25C
          }
        });
      })
      ..addStatusListener((status) {
        if (status == AnimationStatus.completed) {
          setState(() {
            _isCooling = !_isCooling; // Toggle cooling/warming
            _controller.reset();
            _controller.forward();
          });
        }
      });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _toggleSimulation() {
    if (_controller.isAnimating) {
      _controller.stop();
    } else {
      _controller.forward();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Interactive Cooling Cycle Simulator',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 20),
            CustomPaint(
              painter: CoolingCyclePainter(_temperature),
              size: const Size(200, 200),
            ),
            const SizedBox(height: 20),
            Text(
              'Current Temperature: ${_temperature.toStringAsFixed(1)}°C',
              style: const TextStyle(fontSize: 18),
            ),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: _toggleSimulation,
              child: Text(_controller.isAnimating ? 'Pause Simulation' : 'Start Simulation'),
            ),
          ],
        ),
      ),
    );
  }
}

class CoolingCyclePainter extends CustomPainter {
  final double temperature;

  CoolingCyclePainter(this.temperature);

  @override
  void paint(Canvas canvas, Size size) {
    final Paint paint = Paint();
    final double centerX = size.width / 2;
    final double centerY = size.height / 2;
    final double radius = min(centerX, centerY) * 0.8;

    // Background for the "refrigerator"
    paint.color = Colors.blueGrey[100]!;
    canvas.drawRect(Rect.fromLTWH(0, 0, size.width, size.height), paint);

    // Representing the cooling coil/evaporator
    paint.color = Colors.blue[700]!;
    paint.style = PaintingStyle.stroke;
    paint.strokeWidth = 5;
    canvas.drawPath(_drawCoilPath(size), paint);

    // Representing the compressor
    paint.color = Colors.grey[700]!;
    paint.style = PaintingStyle.fill;
    canvas.drawCircle(Offset(centerX - radius * 0.6, centerY + radius * 0.6), 20, paint);
    final TextPainter compressorText = TextPainter(
      text: const TextSpan(text: 'Compressor', style: TextStyle(color: Colors.white, fontSize: 10)),
      textDirection: TextDirection.ltr,
    );
    compressorText.layout();
    compressorText.paint(canvas, Offset(centerX - radius * 0.6 - compressorText.width / 2, centerY + radius * 0.6 - compressorText.height / 2));

    // Representing the condenser
    paint.color = Colors.red[700]!;
    canvas.drawRect(Rect.fromLTWH(centerX + radius * 0.2, centerY - radius * 0.8, radius * 0.4, radius * 0.4), paint);
    final TextPainter condenserText = TextPainter(
      text: const TextSpan(text: 'Condenser', style: TextStyle(color: Colors.white, fontSize: 10)),
      textDirection: TextDirection.ltr,
    );
    condenserText.layout();
    condenserText.paint(canvas, Offset(centerX + radius * 0.2 + radius * 0.2 - condenserText.width / 2, centerY - radius * 0.8 + radius * 0.2 - condenserText.height / 2));


    // Temperature indicator
    final double tempNormalized = (temperature - 4.0) / (25.0 - 4.0); // Normalize temperature between 0 and 1
    final Color tempColor = Color.lerp(Colors.blue, Colors.red, tempNormalized)!;

    paint.color = tempColor;
    canvas.drawCircle(Offset(centerX, centerY), radius * 0.3, paint);

    final TextPainter tempPainter = TextPainter(
      text: TextSpan(
        text: '${temperature.toStringAsFixed(1)}°C',
        style: TextStyle(
          color: Colors.white,
          fontSize: 24,
          fontWeight: FontWeight.bold,
        ),
      ),
      textDirection: TextDirection.ltr,
    );
    tempPainter.layout();
    tempPainter.paint(canvas, Offset(centerX - tempPainter.width / 2, centerY - tempPainter.height / 2));
  }

  Path _drawCoilPath(Size size) {
    final Path path = Path();
    final double startX = size.width * 0.2;
    final double endX = size.width * 0.8;
    final double startY = size.height * 0.2;
    final double endY = size.height * 0.8;
    final double coilWidth = (endX - startX) / 5;

    path.moveTo(startX, startY);
    path.lineTo(endX, startY);
    path.lineTo(endX, endY);
    path.lineTo(startX, endY);
    path.lineTo(startX, startY);

    // Simple coil representation
    for (int i = 0; i < 5; i++) {
      path.lineTo(startX + coilWidth * i, startY + (i % 2 == 0 ? 0 : 20));
      path.lineTo(startX + coilWidth * (i + 1), startY + (i % 2 == 0 ? 20 : 0));
    }

    return path;
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}
