import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;

// API Key - In a real app, store this securely and not in source code.
const String unsplashAccessKey = '*******************************************';

class UnsplashVisualWidget extends StatefulWidget {
  final String searchTerm;
  final double? height;

  const UnsplashVisualWidget({
    super.key,
    required this.searchTerm,
    this.height,
  });

  @override
  State<UnsplashVisualWidget> createState() => _UnsplashVisualWidgetState();
}

class _UnsplashVisualWidgetState extends State<UnsplashVisualWidget> {
  Future<String?>? _imageUrlFuture;

  @override
  void initState() {
    super.initState();
    _imageUrlFuture = _fetchImageUrl(widget.searchTerm);
  }

  @override
  void didUpdateWidget(covariant UnsplashVisualWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.searchTerm != oldWidget.searchTerm) {
      _imageUrlFuture = _fetchImageUrl(widget.searchTerm);
    }
  }

  Future<String?> _fetchImageUrl(String searchTerm) async {
    if (unsplashAccessKey.isEmpty) {
      print('Unsplash API key is missing.');
      return null;
    }
    final Uri url = Uri.parse(
      'https://api.unsplash.com/search/photos?query=${Uri.encodeComponent(searchTerm)}&per_page=1&client_id=$unsplashAccessKey',
    );

    try {
      final response = await http.get(url);
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['results'] != null && data['results'].isNotEmpty) {
          // Using 'small' version for quicker loading, can also use 'regular' or 'full'
          return data['results'][0]['urls']?['small'];
        }
      } else {
        print('Unsplash API error: ${response.statusCode} ${response.body}');
      }
    } catch (e) {
      print('Error fetching Unsplash image: $e');
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: widget.height ?? MediaQuery.of(context).size.height * 0.35,
      width: double.infinity, // Ensure it tries to fill width
      padding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 8.0),
      child: FutureBuilder<String?>(
        future: _imageUrlFuture,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          } else if (snapshot.hasError || snapshot.data == null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.image_not_supported_outlined,
                    size: 40,
                    color: Colors.grey,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Could not load image for "${widget.searchTerm}"',
                    textAlign: TextAlign.center,
                    style: const TextStyle(fontSize: 12, color: Colors.grey),
                  ),
                ],
              ),
            );
          } else {
            return Image.network(
              snapshot.data!,
              fit: BoxFit.contain,
              width: double.infinity, // Fill width within constraints
              loadingBuilder: (
                BuildContext context,
                Widget child,
                ImageChunkEvent? loadingProgress,
              ) {
                if (loadingProgress == null) return child;
                return Center(
                  child: CircularProgressIndicator(
                    value:
                        loadingProgress.expectedTotalBytes != null
                            ? loadingProgress.cumulativeBytesLoaded /
                                loadingProgress.expectedTotalBytes!
                            : null,
                  ),
                );
              },
              errorBuilder: (context, error, stackTrace) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.broken_image_outlined,
                        size: 40,
                        color: Colors.redAccent,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Error displaying image for "${widget.searchTerm}"',
                        textAlign: TextAlign.center,
                        style: const TextStyle(
                          fontSize: 12,
                          color: Colors.redAccent,
                        ),
                      ),
                    ],
                  ),
                );
              },
            );
          }
        },
      ),
    );
  }
}
