import 'package:flutter/material.dart';

class InteractivePoliticalArgumentDissector extends StatelessWidget {
  const InteractivePoliticalArgumentDissector({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Political Argument Dissector'),
        backgroundColor: Colors.redAccent,
      ),
      body: const Center(
        child: Text(
          'Interactive Political Argument Dissector Widget',
          style: TextStyle(fontSize: 24),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }
}
