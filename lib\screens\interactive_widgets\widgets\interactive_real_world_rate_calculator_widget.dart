import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that calculates and visualizes real-world rates of change.
class InteractiveRealWorldRateCalculatorWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveRealWorldRateCalculatorWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  /// Factory constructor to create the widget from JSON data
  factory InteractiveRealWorldRateCalculatorWidget.fromData(Map<String, dynamic> data) {
    return InteractiveRealWorldRateCalculatorWidget(
      data: data,
    );
  }

  @override
  State<InteractiveRealWorldRateCalculatorWidget> createState() => _InteractiveRealWorldRateCalculatorWidgetState();
}

class _InteractiveRealWorldRateCalculatorWidgetState extends State<InteractiveRealWorldRateCalculatorWidget> {
  late double _currentTime; // Time (t)

  // Example: Volume of water in a tank (V) as a function of time (t)
  // Let V(t) = 10t^2 (volume increases quadratically with time)
  double _volumeFunction(double t) {
    return 10 * t * t;
  }

  // Rate of change of volume (dV/dt = 20t)
  double _rateOfChangeFunction(double t) {
    return 20 * t;
  }

  // Colors
  late Color _primaryColor;
  late Color _secondaryColor;
  late Color _backgroundColor;
  late Color _textColor;

  @override
  void initState() {
    super.initState();
    _currentTime = widget.data['initial_time']?.toDouble() ?? 1.0;

    _primaryColor = _parseColor(widget.data['primary_color']) ?? Colors.blue;
    _secondaryColor = _parseColor(widget.data['secondary_color']) ?? Colors.orange;
    _backgroundColor = _parseColor(widget.data['background_color']) ?? Colors.white;
    _textColor = _parseColor(widget.data['text_color']) ?? Colors.black87;
  }

  Color? _parseColor(String? hexString) {
    if (hexString == null) return null;
    hexString = hexString.replaceAll('#', '');
    if (hexString.length == 6) {
      hexString = 'FF$hexString';
    }
    try {
      return Color(int.parse(hexString, radix: 16));
    } catch (e) {
      return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    final currentVolume = _volumeFunction(_currentTime);
    final currentRateOfChange = _rateOfChangeFunction(_currentTime);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _backgroundColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.data['title'] ?? 'Water Tank Filling Rate',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),
          const SizedBox(height: 8),
          if (widget.data['description'] != null)
            Text(
              widget.data['description'],
              style: TextStyle(
                fontSize: 14,
                color: _textColor.withOpacity(0.8),
              ),
            ),
          const SizedBox(height: 16),

          Text(
            'Scenario: Water filling a tank. Volume V(t) = 10t²',
            style: TextStyle(color: _textColor, fontSize: 16, fontStyle: FontStyle.italic),
          ),
          Text(
            'Rate of Change: dV/dt = 20t',
            style: TextStyle(color: _textColor, fontSize: 16, fontStyle: FontStyle.italic),
          ),
          const SizedBox(height: 16),

          Text(
            'Current Time (t): ${_currentTime.toStringAsFixed(1)}',
            style: TextStyle(color: _primaryColor, fontSize: 16, fontWeight: FontWeight.bold),
          ),
          Slider(
            value: _currentTime,
            min: 0.0,
            max: 10.0,
            divisions: 100,
            label: _currentTime.toStringAsFixed(1),
            activeColor: _primaryColor,
            onChanged: (value) {
              setState(() {
                _currentTime = value;
              });
            },
          ),
          const SizedBox(height: 16),

          Center(
            child: Column(
              children: [
                Text(
                  'Volume at t=${_currentTime.toStringAsFixed(1)}: ${currentVolume.toStringAsFixed(2)} units³',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: _primaryColor,
                  ),
                ),
                Text(
                  'Rate of Change at t=${_currentTime.toStringAsFixed(1)}: ${currentRateOfChange.toStringAsFixed(2)} units³/time',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: _secondaryColor,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),

          Container(
            height: 250,
            child: CustomPaint(
              painter: RealWorldRatePainter(
                volumeFunction: _volumeFunction,
                rateOfChangeFunction: _rateOfChangeFunction,
                currentTime: _currentTime,
                primaryColor: _primaryColor,
                secondaryColor: _secondaryColor,
                textColor: _textColor,
              ),
              child: Container(),
            ),
          ),

          // Widget name tag
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'InteractiveRealWorldRateCalculator',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }
}

class RealWorldRatePainter extends CustomPainter {
  final Function(double) volumeFunction;
  final Function(double) rateOfChangeFunction;
  final double currentTime;
  final Color primaryColor;
  final Color secondaryColor;
  final Color textColor;

  RealWorldRatePainter({
    required this.volumeFunction,
    required this.rateOfChangeFunction,
    required this.currentTime,
    required this.primaryColor,
    required this.secondaryColor,
    required this.textColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint();
    final textPainter = TextPainter(textDirection: TextDirection.ltr);
    final textStyle = TextStyle(color: textColor, fontSize: 10);

    // Define plot area padding
    const double padding = 30.0;
    final double plotWidth = size.width - 2 * padding;
    final double plotHeight = size.height - 2 * padding;

    // Determine x (time) and y (volume) ranges for plotting
    final double minPlotT = 0.0;
    final double maxPlotT = 10.0;
    final double minPlotV = 0.0;
    final double maxPlotV = 10 * math.pow(maxPlotT, 2); // Max volume at max time

    // Scale factors
    final double xScale = plotWidth / (maxPlotT - minPlotT);
    final double yScale = plotHeight / (maxPlotV - minPlotV);

    // Helper to convert plot coordinates to canvas coordinates
    Offset toCanvas(double t, double v) {
      final canvasX = padding + (t - minPlotT) * xScale;
      final canvasY = size.height - padding - (v - minPlotV) * yScale;
      return Offset(canvasX, canvasY);
    }

    // Draw axes
    paint.color = Colors.grey;
    paint.strokeWidth = 1.0;
    // T-axis (X-axis)
    canvas.drawLine(toCanvas(minPlotT, 0), toCanvas(maxPlotT, 0), paint);
    // V-axis (Y-axis)
    canvas.drawLine(toCanvas(0, minPlotV), toCanvas(0, maxPlotV), paint);

    // Draw axis labels
    textPainter.text = TextSpan(text: 'Time (t)', style: textStyle);
    textPainter.layout();
    textPainter.paint(canvas, Offset(size.width / 2 - textPainter.width / 2, size.height - padding + 5));

    textPainter.text = TextSpan(text: 'Volume (V)', style: textStyle);
    textPainter.layout();
    canvas.save();
    canvas.translate(padding - 15, size.height / 2 + textPainter.width / 2);
    canvas.rotate(-math.pi / 2);
    textPainter.paint(canvas, Offset.zero);
    canvas.restore();

    // Draw volume function curve
    paint.color = primaryColor;
    paint.style = PaintingStyle.stroke;
    paint.strokeWidth = 2.0;

    final path = Path();
    bool firstPoint = true;
    for (double t = minPlotT; t <= maxPlotT; t += (maxPlotT - minPlotT) / 100) {
      final v = volumeFunction(t);
      final point = toCanvas(t, v);
      if (firstPoint) {
        path.moveTo(point.dx, point.dy);
        firstPoint = false;
      } else {
        path.lineTo(point.dx, point.dy);
      }
    }
    canvas.drawPath(path, paint);

    // Draw current point (t, V(t))
    final currentVolumePoint = toCanvas(currentTime, volumeFunction(currentTime));
    paint.color = primaryColor;
    paint.style = PaintingStyle.fill;
    canvas.drawCircle(currentVolumePoint, 5, paint);

    // Draw tangent line at current time (representing rate of change)
    final currentRate = rateOfChangeFunction(currentTime);
    final tangentY1 = currentRate * (minPlotT - currentTime) + volumeFunction(currentTime);
    final tangentY2 = currentRate * (maxPlotT - currentTime) + volumeFunction(currentTime);

    paint.color = secondaryColor;
    paint.strokeWidth = 2.0;
    canvas.drawLine(toCanvas(minPlotT, tangentY1), toCanvas(maxPlotT, tangentY2), paint);

    // Draw labels for current point and rate
    textPainter.text = TextSpan(text: 't = ${currentTime.toStringAsFixed(1)}', style: textStyle.copyWith(color: primaryColor));
    textPainter.layout();
    textPainter.paint(canvas, Offset(currentVolumePoint.dx - textPainter.width / 2, size.height - padding + 5));

    textPainter.text = TextSpan(text: 'V(t) = ${volumeFunction(currentTime).toStringAsFixed(1)}', style: textStyle.copyWith(color: primaryColor));
    textPainter.layout();
    textPainter.paint(canvas, Offset(padding + 5, currentVolumePoint.dy - textPainter.height / 2));

    textPainter.text = TextSpan(text: 'Rate = ${currentRate.toStringAsFixed(1)}', style: textStyle.copyWith(color: secondaryColor));
    textPainter.layout();
    // Position rate label near the tangent line
    final midX = (minPlotT + maxPlotT) / 2;
    final midY = currentRate * (midX - currentTime) + volumeFunction(currentTime);
    final midCanvasPoint = toCanvas(midX, midY);
    textPainter.paint(canvas, Offset(midCanvasPoint.dx - textPainter.width / 2, midCanvasPoint.dy - textPainter.height - 5));
  }

  @override
  bool shouldRepaint(covariant RealWorldRatePainter oldDelegate) {
    return oldDelegate.currentTime != currentTime ||
           oldDelegate.primaryColor != primaryColor ||
           oldDelegate.secondaryColor != secondaryColor ||
           oldDelegate.textColor != textColor;
  }
}
