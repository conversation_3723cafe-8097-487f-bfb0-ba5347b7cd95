class UserPreferences {
  // Page 2: Learning goal
  String? learningGoal;
  
  // Page 3: Learning focus
  String? learningFocus;
  
  // Page 5: Topic to explore
  String? topicToExplore;
  
  // Page 5: User level
  String? userLevel;
  
  // Page 7: Daily goal (minutes)
  int? dailyGoalMinutes;
  
  // Page 7: Learning time preference
  String? learningTimePreference;
  
  // Page 8: Notification preference
  bool? allowNotifications;
  
  UserPreferences({
    this.learningGoal,
    this.learningFocus,
    this.topicToExplore,
    this.userLevel,
    this.dailyGoalMinutes,
    this.learningTimePreference,
    this.allowNotifications,
  });
  
  // Method to check if a specific page has selections made
  bool hasSelectionForPage(int pageNumber) {
    switch (pageNumber) {
      case 2:
        return learningGoal != null;
      case 3:
        return learningFocus != null;
      case 5:
        return topicToExplore != null && userLevel != null;
      case 7:
        return dailyGoalMinutes != null && learningTimePreference != null;
      case 8:
        return allowNotifications != null;
      default:
        return true; // For pages without selections
    }
  }
  
  // Convert to a map for storage
  Map<String, dynamic> toMap() {
    return {
      'learningGoal': learningGoal,
      'learningFocus': learningFocus,
      'topicToExplore': topicToExplore,
      'userLevel': userLevel,
      'dailyGoalMinutes': dailyGoalMinutes,
      'learningTimePreference': learningTimePreference,
      'allowNotifications': allowNotifications,
    };
  }
  
  // Create from a map (for loading from storage)
  factory UserPreferences.fromMap(Map<String, dynamic> map) {
    return UserPreferences(
      learningGoal: map['learningGoal'],
      learningFocus: map['learningFocus'],
      topicToExplore: map['topicToExplore'],
      userLevel: map['userLevel'],
      dailyGoalMinutes: map['dailyGoalMinutes'],
      learningTimePreference: map['learningTimePreference'],
      allowNotifications: map['allowNotifications'],
    );
  }
  
  @override
  String toString() {
    return 'UserPreferences(learningGoal: $learningGoal, learningFocus: $learningFocus, '
        'topicToExplore: $topicToExplore, userLevel: $userLevel, '
        'dailyGoalMinutes: $dailyGoalMinutes, learningTimePreference: $learningTimePreference, '
        'allowNotifications: $allowNotifications)';
  }
}
