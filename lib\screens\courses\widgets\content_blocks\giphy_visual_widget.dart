import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;

// API Key - In a real app, store this securely and not in source code.
const String giphyApiKey = 'AP703A14Crc2Dwv1zrF022Q2iLatsCZM';

class GiphyVisualWidget extends StatefulWidget {
  final String searchTerm;
  final double? height;

  const GiphyVisualWidget({super.key, required this.searchTerm, this.height});

  @override
  State<GiphyVisualWidget> createState() => _GiphyVisualWidgetState();
}

class _GiphyVisualWidgetState extends State<GiphyVisualWidget> {
  Future<String?>? _gifUrlFuture;

  @override
  void initState() {
    super.initState();
    _gifUrlFuture = _fetchGifUrl(widget.searchTerm);
  }

  @override
  void didUpdateWidget(covariant GiphyVisualWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.searchTerm != oldWidget.searchTerm) {
      _gifUrlFuture = _fetchGifUrl(widget.searchTerm);
    }
  }

  Future<String?> _fetchGifUrl(String searchTerm) async {
    if (giphyApiKey.isEmpty) {
      print('Giphy API key is missing.');
      return null;
    }
    final Uri url = Uri.parse(
      'https://api.giphy.com/v1/gifs/search?api_key=$giphyApiKey&q=${Uri.encodeComponent(searchTerm)}&limit=1&offset=0&rating=g&lang=en',
    );

    try {
      final response = await http.get(url);
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['data'] != null && data['data'].isNotEmpty) {
          // Using a fixed size version, like 'fixed_height_downsampled' or 'original'
          // Check Giphy API docs for available renditions
          return data['data'][0]['images']['fixed_height_downsampled']?['url'] ??
              data['data'][0]['images']['original']?['url'];
        }
      } else {
        print('Giphy API error: ${response.statusCode} ${response.body}');
      }
    } catch (e) {
      print('Error fetching Giphy GIF: $e');
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: widget.height ?? MediaQuery.of(context).size.height * 0.35,
      width: double.infinity, // Ensure it tries to fill width
      padding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 8.0),
      child: FutureBuilder<String?>(
        future: _gifUrlFuture,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          } else if (snapshot.hasError || snapshot.data == null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.image_not_supported_outlined,
                    size: 40,
                    color: Colors.grey,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Could not load GIF for "${widget.searchTerm}"',
                    textAlign: TextAlign.center,
                    style: const TextStyle(fontSize: 12, color: Colors.grey),
                  ),
                ],
              ),
            );
          } else {
            return Image.network(
              snapshot.data!,
              fit: BoxFit.contain,
              width: double.infinity, // Fill width within constraints
              loadingBuilder: (
                BuildContext context,
                Widget child,
                ImageChunkEvent? loadingProgress,
              ) {
                if (loadingProgress == null) return child;
                return Center(
                  child: CircularProgressIndicator(
                    value:
                        loadingProgress.expectedTotalBytes != null
                            ? loadingProgress.cumulativeBytesLoaded /
                                loadingProgress.expectedTotalBytes!
                            : null,
                  ),
                );
              },
              errorBuilder: (context, error, stackTrace) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.broken_image_outlined,
                        size: 40,
                        color: Colors.redAccent,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Error displaying GIF for "${widget.searchTerm}"',
                        textAlign: TextAlign.center,
                        style: const TextStyle(
                          fontSize: 12,
                          color: Colors.redAccent,
                        ),
                      ),
                    ],
                  ),
                );
              },
            );
          }
        },
      ),
    );
  }
}
