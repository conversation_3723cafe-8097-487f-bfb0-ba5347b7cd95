import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class InteractiveMomentOfInertiaCalculator extends StatefulWidget {
  const InteractiveMomentOfInertiaCalculator({super.key});

  @override
  State<InteractiveMomentOfInertiaCalculator> createState() => _InteractiveMomentOfInertiaCalculatorState();
}

class _InteractiveMomentOfInertiaCalculatorState extends State<InteractiveMomentOfInertiaCalculator> {
  final TextEditingController _massController = TextEditingController();
  final TextEditingController _radiusController = TextEditingController();
  String _result = '';

  @override
  void dispose() {
    _massController.dispose();
    _radiusController.dispose();
    super.dispose();
  }

  void _calculateMomentOfInertia() {
    final double? mass = double.tryParse(_massController.text);
    final double? radius = double.tryParse(_radiusController.text);

    if (mass == null || radius == null || mass <= 0 || radius <= 0) {
      setState(() {
        _result = 'Please enter valid positive numbers for mass and radius.';
      });
      return;
    }

    // For a solid cylinder or disk rotating about its central axis: I = 0.5 * m * r^2
    // For a thin hoop or ring: I = m * r^2
    // For a solid sphere: I = 0.4 * m * r^2
    // For simplicity, let's use the formula for a solid cylinder/disk for this calculator.
    final double momentOfInertia = 0.5 * mass * radius * radius;

    setState(() {
      _result = 'Moment of Inertia (I): ${momentOfInertia.toStringAsFixed(4)} kg·m²';
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Moment of Inertia Calculator'),
        backgroundColor: Colors.deepPurple,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'Calculate the moment of inertia for a solid cylinder or disk.',
              style: TextStyle(fontSize: 16.0, fontStyle: FontStyle.italic),
            ),
            const SizedBox(height: 20),
            TextField(
              controller: _massController,
              keyboardType: const TextInputType.numberWithOptions(decimal: true),
              inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*'))],
              decoration: const InputDecoration(
                labelText: 'Mass (m) in kg',
                border: OutlineInputBorder(),
                hintText: 'Enter mass',
              ),
            ),
            const SizedBox(height: 15),
            TextField(
              controller: _radiusController,
              keyboardType: const TextInputType.numberWithOptions(decimal: true),
              inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*'))],
              decoration: const InputDecoration(
                labelText: 'Radius (r) in meters',
                border: OutlineInputBorder(),
                hintText: 'Enter radius',
              ),
            ),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: _calculateMomentOfInertia,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.deepPurple,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 15),
                textStyle: const TextStyle(fontSize: 18),
              ),
              child: const Text('Calculate Moment of Inertia'),
            ),
            const SizedBox(height: 20),
            Card(
              elevation: 4,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Result:',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 10),
                    Text(
                      _result,
                      style: const TextStyle(fontSize: 20, color: Colors.blueAccent),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 20),
            const Text(
              'Formula used: I = 0.5 * m * r² (for a solid cylinder or disk)',
              style: TextStyle(fontSize: 14, fontStyle: FontStyle.italic, color: Colors.grey),
            ),
          ],
        ),
      ),
    );
  }
}
