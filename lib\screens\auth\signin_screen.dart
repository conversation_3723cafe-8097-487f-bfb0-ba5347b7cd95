import 'package:flutter/material.dart';
import 'signup_screen.dart';

class SigninWidget extends StatefulWidget {
  @override
  _SigninWidgetState createState() => _SigninWidgetState();
}

class _SigninWidgetState extends State<SigninWidget> {
  // Controllers for text fields
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _isPasswordVisible = false;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white, // White background for the sign-in screen
      body: SingleChildScrollView(
        // Allows content to scroll if keyboard appears or content is long
        child: Column(
          children: <Widget>[
            // --- Header Section with Background Image/Gradient ---
            Container(
              height: 256, // Height from original code
              width: double.infinity, // Span full width
              child: Stack(
                children: <Widget>[
                  // Background Logo Image (Rotated)
                  Positioned(
                    top: -382.56, // Original position
                    left: -290, // Original position
                    child: Transform.rotate(
                      angle: 0.6319 * (3.14159 / 180),
                      child: Container(
                        width: 946.34, // Original size
                        height: 946.34, // Original size
                        decoration: BoxDecoration(
                          image: DecorationImage(
                            image: AssetImage('assets/images/logo.png'),
                            fit: BoxFit.fitWidth,
                            // Add opacity if needed
                            colorFilter: ColorFilter.mode(
                              Colors.white.withOpacity(
                                0.15,
                              ), // Make logo subtle
                              BlendMode.dstATop,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                  // White Fade Gradient Overlay
                  Positioned.fill(
                    child: Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topCenter, // Simpler alignment
                          end: Alignment.bottomCenter,
                          colors: [
                            Colors.white.withOpacity(0), // Transparent at top
                            Colors.white, // Solid white at bottom
                          ],
                          stops: [0.0, 0.9], // Adjust stops for fade effect
                        ),
                      ),
                    ),
                  ),
                  // Header Text (Simplified structure)
                  Positioned(
                    // Position text towards the bottom of the header area
                    bottom: 20, // Adjust as needed
                    left: 14,
                    right: 14,
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: <Widget>[
                        Text(
                          'Sign In To Resonance.',
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            color: Color.fromRGBO(16, 17, 20, 1), // Dark text
                            fontFamily: 'WorkSans',
                            fontSize: 30,
                            fontWeight: FontWeight.w600, // e.g., SemiBold
                            height: 1.27,
                          ),
                        ),
                        SizedBox(height: 8),
                        Text(
                          'Go Deeper Than Knowing.',
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            color: Color.fromRGBO(57, 59, 66, 1), // Grey text
                            fontFamily: 'WorkSans',
                            fontSize: 16,
                            fontWeight: FontWeight.w400, // e.g., Regular
                            height: 1.5,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            // --- Form Section ---
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment:
                    CrossAxisAlignment.stretch, // Make buttons stretch
                children: <Widget>[
                  SizedBox(
                    height: 24,
                  ), // Spacing between header and form (adjust as needed)
                  // --- Email Field ---
                  Text(
                    'Email Address',
                    style: TextStyle(
                      color: Color.fromRGBO(16, 17, 20, 1),
                      fontFamily: 'WorkSans',
                      fontSize: 14,
                      fontWeight: FontWeight.w500, // e.g., Medium
                    ),
                  ),
                  SizedBox(height: 8),
                  TextFormField(
                    controller: _emailController,
                    keyboardType: TextInputType.emailAddress,
                    // Use InputDecoration for styling
                    decoration: InputDecoration(
                      hintText: '<EMAIL>', // Placeholder text
                      hintStyle: TextStyle(
                        color: Colors.grey[400],
                        fontFamily: 'WorkSans',
                      ),
                      filled: true,
                      fillColor: Color.fromRGBO(
                        243,
                        243,
                        243,
                        1,
                      ), // Light grey fill
                      prefixIcon: Icon(
                        Icons.email_outlined,
                        color: Colors.grey[600],
                      ), // Email Icon
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(19),
                        borderSide: BorderSide.none, // Remove default border
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(19),
                        // Border when not focused (subtle grey)
                        borderSide: BorderSide(
                          color: Colors.grey.shade300,
                          width: 1,
                        ),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(19),
                        // Border when focused (purple from original code)
                        borderSide: BorderSide(
                          color: Color.fromRGBO(100, 45, 176, 1),
                          width: 1.5,
                        ),
                      ),
                      contentPadding: EdgeInsets.symmetric(
                        vertical: 16,
                        horizontal: 16,
                      ),
                    ),
                    style: TextStyle(
                      // Style for the text the user types
                      color: Color.fromRGBO(57, 59, 66, 1),
                      fontFamily: 'WorkSans',
                      fontSize: 16,
                    ),
                  ),

                  SizedBox(height: 24),

                  // --- Password Field ---
                  Text(
                    'Password',
                    style: TextStyle(
                      color: Color.fromRGBO(16, 17, 20, 1),
                      fontFamily: 'WorkSans',
                      fontSize: 14,
                      fontWeight: FontWeight.w500, // e.g., Medium
                    ),
                  ),
                  SizedBox(height: 8),
                  TextFormField(
                    controller: _passwordController,
                    obscureText: !_isPasswordVisible, // Toggle visibility
                    decoration: InputDecoration(
                      hintText: 'Enter your password',
                      hintStyle: TextStyle(
                        color: Colors.grey[400],
                        fontFamily: 'WorkSans',
                      ),
                      filled: true,
                      fillColor: Color.fromRGBO(243, 243, 243, 1),
                      prefixIcon: Icon(
                        Icons.lock_outline,
                        color: Colors.grey[600],
                      ), // Lock Icon
                      // Replaced the second null with a visibility toggle icon
                      suffixIcon: IconButton(
                        icon: Icon(
                          _isPasswordVisible
                              ? Icons.visibility_off_outlined
                              : Icons.visibility_outlined,
                          color: Colors.grey[600],
                        ),
                        onPressed: () {
                          setState(() {
                            _isPasswordVisible = !_isPasswordVisible;
                          });
                        },
                      ),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(19),
                        borderSide: BorderSide.none,
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(19),
                        // Border when not focused (subtle grey)
                        borderSide: BorderSide(
                          color: Colors.grey.shade300,
                          width: 1,
                        ),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(19),
                        // Border when focused (no extra border needed like email)
                        borderSide: BorderSide(
                          color: Color.fromRGBO(100, 45, 176, 1),
                          width: 1.5,
                        ),
                      ),
                      contentPadding: EdgeInsets.symmetric(
                        vertical: 16,
                        horizontal: 16,
                      ),
                    ),
                    style: TextStyle(
                      // Style for the text the user types
                      color: Color.fromRGBO(57, 59, 66, 1),
                      fontFamily: 'WorkSans',
                      fontSize: 16,
                    ),
                  ),

                  SizedBox(height: 24),

                  // --- Sign In Button ---
                  ElevatedButton.icon(
                    onPressed: () {
                      // TODO: Implement Sign In Logic
                      print('Email: ${_emailController.text}');
                      print('Password: ${_passwordController.text}');
                    },
                    icon: Icon(Icons.arrow_forward, size: 18),
                    label: Text('Sign In'),
                    style: ElevatedButton.styleFrom(
                      foregroundColor: Colors.white, // Text color
                      backgroundColor: Color.fromRGBO(
                        16,
                        17,
                        20,
                        1,
                      ), // Button background (dark)
                      padding: EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(19),
                      ),
                      textStyle: TextStyle(
                        fontFamily: 'WorkSans',
                        fontSize: 16,
                        fontWeight: FontWeight.w500, // e.g., Medium
                      ),
                    ),
                  ),

                  SizedBox(height: 40),

                  // --- Social Login Buttons ---
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center, // Center icons
                    children: <Widget>[
                      // Google Icon Button
                      _buildSocialIconButton(
                        // Using a Text 'G' as a placeholder, replace with actual logo/icon
                        iconWidget: Text(
                          'G',
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Colors.red, // Google-like color
                          ),
                        ),
                        onPressed: () {
                          // TODO: Implement Google Sign In
                          print('Google Sign In Tapped');
                        },
                      ),

                      SizedBox(width: 16), // Spacing between icons
                      // Apple Icon Button
                      _buildSocialIconButton(
                        iconWidget: Icon(
                          Icons.apple,
                          color: Colors.black,
                          size: 24,
                        ), // Apple Icon
                        onPressed: () {
                          // TODO: Implement Apple Sign In
                          print('Apple Sign In Tapped');
                        },
                      ),

                      SizedBox(width: 16),

                      // Facebook Icon Button
                      _buildSocialIconButton(
                        iconWidget: Icon(
                          Icons.facebook,
                          color: Colors.blue.shade800,
                          size: 24,
                        ), // Facebook Icon
                        onPressed: () {
                          // TODO: Implement Facebook Sign In
                          print('Facebook Sign In Tapped');
                        },
                      ),
                    ],
                  ),

                  SizedBox(height: 48),

                  // --- Sign Up / Forgot Password Links ---
                  Column(
                    children: <Widget>[
                      // Sign Up Text Button
                      Row(
                        // Use Row to center easily
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            "Don't have an account? ",
                            style: TextStyle(
                              color: Colors.grey[600], // Fixed undefined color
                              fontFamily: 'WorkSans',
                              fontSize: 14,
                            ),
                          ),
                          InkWell(
                            onTap: () {
                              // Navigate to Sign Up Screen
                              Navigator.of(context).pushReplacement(
                                MaterialPageRoute(
                                  builder: (_) => SignupWidget(),
                                ),
                              );
                            },
                            child: Text(
                              'Sign Up.',
                              style: TextStyle(
                                color: Color.fromRGBO(
                                  100,
                                  45,
                                  176,
                                  1,
                                ), // Use a distinct color (e.g., purple)
                                fontFamily: 'WorkSans',
                                fontSize: 14,
                                fontWeight: FontWeight.w600, // Make link bolder
                              ),
                            ),
                          ),
                        ],
                      ),

                      SizedBox(height: 16),

                      // Forgot Password Text Button
                      InkWell(
                        onTap: () {
                          // TODO: Navigate to Forgot Password Screen
                          print('Forgot Password Tapped');
                        },
                        child: Text(
                          'Forgot Password?',
                          style: TextStyle(
                            color: Color.fromRGBO(
                              255,
                              127,
                              54,
                              1,
                            ), // Original orange color
                            fontFamily: 'WorkSans',
                            fontSize: 14,
                            fontWeight: FontWeight.w500, // e.g., Medium
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 20), // Bottom padding
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Helper widget to build consistently styled social icon buttons
  Widget _buildSocialIconButton({
    required Widget iconWidget,
    required VoidCallback onPressed,
  }) {
    return InkWell(
      // Use InkWell for tap feedback
      onTap: onPressed,
      borderRadius: BorderRadius.circular(19), // Make ripple effect circular
      child: Container(
        padding: const EdgeInsets.all(12), // Padding inside the border
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(19),
          border: Border.all(
            color: Color.fromRGBO(185, 187, 190, 1), // Original border color
            width: 1,
          ),
          color: Colors.white, // Ensure white background inside border
        ),
        child: iconWidget, // The actual icon (or Text placeholder)
      ),
    );
  }
}
