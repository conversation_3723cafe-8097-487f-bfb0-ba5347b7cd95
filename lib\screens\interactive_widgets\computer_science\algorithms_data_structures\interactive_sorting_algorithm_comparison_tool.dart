import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:resonance_app/services/interactive_widget_service.dart';
import 'package:resonance_app/widgets/animated_button.dart';

class InteractiveSortingAlgorithmComparisonTool extends StatefulWidget {
  final String widgetId;

  const InteractiveSortingAlgorithmComparisonTool({Key? key, required this.widgetId}) : super(key: key);

  @override
  _InteractiveSortingAlgorithmComparisonToolState createState() => _InteractiveSortingAlgorithmComparisonToolState();
}

class _InteractiveSortingAlgorithmComparisonToolState extends State<InteractiveSortingAlgorithmComparisonTool> {
  String _selectedAlgorithm1 = 'Bubble Sort';
  String _selectedAlgorithm2 = 'Merge Sort';
  String _comparisonResult = '';

  final List<String> _algorithms = [
    'Bubble Sort',
    'Insertion Sort',
    'Merge Sort',
    'Quick Sort',
  ];

  @override
  void initState() {
    super.initState();
    _loadState();
  }

  void _loadState() async {
    final service = Provider.of<InteractiveWidgetService>(context, listen: false);
    final savedState = await service.getWidgetState(widget.widgetId);
    if (savedState != null) {
      setState(() {
        _selectedAlgorithm1 = savedState['selectedAlgorithm1'] ?? 'Bubble Sort';
        _selectedAlgorithm2 = savedState['selectedAlgorithm2'] ?? 'Merge Sort';
        _comparisonResult = savedState['comparisonResult'] ?? '';
      });
    }
  }

  void _saveState() {
    final service = Provider.of<InteractiveWidgetService>(context, listen: false);
    service.saveWidgetState(widget.widgetId, {
      'selectedAlgorithm1': _selectedAlgorithm1,
      'selectedAlgorithm2': _selectedAlgorithm2,
      'comparisonResult': _comparisonResult,
    });
  }

  void _compareAlgorithms() {
    String result = '';
    if (_selectedAlgorithm1 == _selectedAlgorithm2) {
      result = 'Please select two different algorithms for comparison.';
    } else {
      result = _getComparisonDetails(_selectedAlgorithm1, _selectedAlgorithm2);
    }
    setState(() {
      _comparisonResult = result;
    });
    _saveState();
  }

  String _getComparisonDetails(String algo1, String algo2) {
    Map<String, Map<String, String>> properties = {
      'Bubble Sort': {
        'Time Complexity (Worst/Average)': 'O(n^2)',
        'Time Complexity (Best)': 'O(n)',
        'Space Complexity': 'O(1)',
        'Stability': 'Stable',
        'Use Case': 'Educational, small datasets',
      },
      'Insertion Sort': {
        'Time Complexity (Worst/Average)': 'O(n^2)',
        'Time Complexity (Best)': 'O(n)',
        'Space Complexity': 'O(1)',
        'Stability': 'Stable',
        'Use Case': 'Small datasets, nearly sorted data',
      },
      'Merge Sort': {
        'Time Complexity (Worst/Average/Best)': 'O(n log n)',
        'Space Complexity': 'O(n)',
        'Stability': 'Stable',
        'Use Case': 'External sorting, linked lists',
      },
      'Quick Sort': {
        'Time Complexity (Worst)': 'O(n^2)',
        'Time Complexity (Average/Best)': 'O(n log n)',
        'Space Complexity': 'O(log n) (recursive stack)',
        'Stability': 'Unstable',
        'Use Case': 'General-purpose sorting, in-place sorting',
      },
    };

    String details = 'Comparison between $algo1 and $algo2:\n\n';
    details += '$algo1 Properties:\n';
    properties[algo1]?.forEach((key, value) {
      details += '  $key: $value\n';
    });
    details += '\n$algo2 Properties:\n';
    properties[algo2]?.forEach((key, value) {
      details += '  $key: $value\n';
    });

    return details;
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Interactive Sorting Algorithm Comparison Tool',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 20),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              DropdownButton<String>(
                value: _selectedAlgorithm1,
                onChanged: (String? newValue) {
                  setState(() {
                    _selectedAlgorithm1 = newValue!;
                  });
                },
                items: _algorithms.map<DropdownMenuItem<String>>((String value) {
                  return DropdownMenuItem<String>(
                    value: value,
                    child: Text(value),
                  );
                }).toList(),
              ),
              const Text('vs.'),
              DropdownButton<String>(
                value: _selectedAlgorithm2,
                onChanged: (String? newValue) {
                  setState(() {
                    _selectedAlgorithm2 = newValue!;
                  });
                },
                items: _algorithms.map<DropdownMenuItem<String>>((String value) {
                  return DropdownMenuItem<String>(
                    value: value,
                    child: Text(value),
                  );
                }).toList(),
              ),
            ],
          ),
          const SizedBox(height: 20),
          Center(
            child: AnimatedButton(
              onTap: _compareAlgorithms,
              text: 'Compare',
              color: Colors.purple,
            ),
          ),
          const SizedBox(height: 20),
          Expanded(
            child: SingleChildScrollView(
              child: Text(
                _comparisonResult,
                style: Theme.of(context).textTheme.bodyLarge,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
