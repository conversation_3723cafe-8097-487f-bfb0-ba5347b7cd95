import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:resonance_app/services/interactive_widget_service.dart';
import 'package:resonance_app/widgets/animated_button.dart';

class InteractiveStackQueueSimulator extends StatefulWidget {
  final String widgetId;

  const InteractiveStackQueueSimulator({Key? key, required this.widgetId}) : super(key: key);

  @override
  _InteractiveStackQueueSimulatorState createState() => _InteractiveStackQueueSimulatorState();
}

class _InteractiveStackQueueSimulatorState extends State<InteractiveStackQueueSimulator> {
  List<int> _stack = [];
  List<int> _queue = [];
  TextEditingController _inputController = TextEditingController();
  String _message = '';

  @override
  void initState() {
    super.initState();
    _loadState();
  }

  void _loadState() async {
    final service = Provider.of<InteractiveWidgetService>(context, listen: false);
    final savedState = await service.getWidgetState(widget.widgetId);
    if (savedState != null) {
      setState(() {
        _stack = List<int>.from(savedState['stack'] ?? []);
        _queue = List<int>.from(savedState['queue'] ?? []);
      });
    }
  }

  void _saveState() {
    final service = Provider.of<InteractiveWidgetService>(context, listen: false);
    service.saveWidgetState(widget.widgetId, {
      'stack': _stack,
      'queue': _queue,
    });
  }

  void _pushToStack() {
    final value = int.tryParse(_inputController.text);
    if (value != null) {
      setState(() {
        _stack.add(value);
        _message = 'Pushed $value to stack.';
      });
      _inputController.clear();
      _saveState();
    } else {
      setState(() {
        _message = 'Please enter a valid number.';
      });
    }
  }

  void _popFromStack() {
    if (_stack.isNotEmpty) {
      final value = _stack.removeLast();
      setState(() {
        _message = 'Popped $value from stack.';
      });
      _saveState();
    } else {
      setState(() {
        _message = 'Stack is empty.';
      });
    }
  }

  void _enqueue() {
    final value = int.tryParse(_inputController.text);
    if (value != null) {
      setState(() {
        _queue.add(value);
        _message = 'Enqueued $value to queue.';
      });
      _inputController.clear();
      _saveState();
    } else {
      setState(() {
        _message = 'Please enter a valid number.';
      });
    }
  }

  void _dequeue() {
    if (_queue.isNotEmpty) {
      final value = _queue.removeAt(0);
      setState(() {
        _message = 'Dequeued $value from queue.';
      });
      _saveState();
    } else {
      setState(() {
        _message = 'Queue is empty.';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Interactive Stack and Queue Simulator',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 20),
          TextField(
            controller: _inputController,
            keyboardType: TextInputType.number,
            decoration: const InputDecoration(
              labelText: 'Enter a number',
              border: OutlineInputBorder(),
            ),
          ),
          const SizedBox(height: 10),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              AnimatedButton(
                onTap: _pushToStack,
                text: 'Push to Stack',
                color: Colors.blue,
              ),
              AnimatedButton(
                onTap: _popFromStack,
                text: 'Pop from Stack',
                color: Colors.red,
              ),
            ],
          ),
          const SizedBox(height: 10),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              AnimatedButton(
                onTap: _enqueue,
                text: 'Enqueue',
                color: Colors.green,
              ),
              AnimatedButton(
                onTap: _dequeue,
                text: 'Dequeue',
                color: Colors.orange,
              ),
            ],
          ),
          const SizedBox(height: 20),
          Text(
            'Stack: ${_stack.reversed.join(', ')}',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 10),
          Text(
            'Queue: ${_queue.join(', ')}',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 20),
          Text(
            'Message: $_message',
            style: Theme.of(context).textTheme.bodyLarge,
          ),
        ],
      ),
    );
  }
}
