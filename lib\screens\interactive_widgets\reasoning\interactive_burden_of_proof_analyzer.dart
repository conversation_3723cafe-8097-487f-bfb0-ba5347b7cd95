import 'package:flutter/material.dart';
import 'package:resonance/widgets/interactive_widget.dart';

class InteractiveBurdenOfProofAnalyzer extends InteractiveWidget {
  const InteractiveBurdenOfProofAnalyzer({super.key});

  @override
  InteractiveWidgetMetaData get metaData => InteractiveWidgetMetaData(
        title: 'Interactive Burden of Proof Analyzer',
        category: 'Reasoning',
        course: 'Reasoning: Frontiers',
        module: 'Informal Logic and Argumentation Theory',
        slug: 'interactive-burden-of-proof-analyzer',
        description:
            'An interactive tool to analyze and understand the concept of burden of proof in arguments.',
        difficulty: InteractiveWidgetDifficulty.medium,
        tags: const ['logic', 'argumentation', 'burden of proof'],
      );

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(metaData.title),
        backgroundColor: Colors.deepPurple,
      ),
      body: const Center(
        child: Padding(
          padding: EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'Welcome to the Interactive Burden of Proof Analyzer!',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 20),
              Text(
                'This widget will help you analyze and understand the concept of burden of proof in various argumentative contexts.',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 16),
              ),
              SizedBox(height: 40),
              // Placeholder for interactive content
              CircularProgressIndicator(),
              SizedBox(height: 20),
              Text(
                'Content coming soon!',
                style: TextStyle(fontSize: 18, fontStyle: FontStyle.italic),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
