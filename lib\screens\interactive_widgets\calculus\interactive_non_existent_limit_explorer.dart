import 'package:flutter/material.dart';
import 'dart:math';

class InteractiveNonExistentLimitExplorer extends StatefulWidget {
  const InteractiveNonExistentLimitExplorer({super.key});

  @override
  State<InteractiveNonExistentLimitExplorer> createState() => _InteractiveNonExistentLimitExplorerState();
}

class _InteractiveNonExistentLimitExplorerState extends State<InteractiveNonExistentLimitExplorer> {
  double _xValue = 0.0;
  final double _targetX = 0.0; // The x-value where the limit does not exist

  double _function(double x) {
    // Example function with a jump discontinuity at x=0: piecewise function
    if (x < _targetX) {
      return x + 1;
    } else if (x >= _targetX) {
      return x - 1;
    }
    return double.nan; // Should not be reached
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Interactive Non-Existent Limit Explorer',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 20),
            Text(
              'Function: f(x) = { x + 1 if x < 0, x - 1 if x ≥ 0 }',
              style: TextStyle(fontSize: 16),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 10),
            Text(
              'Explore the limit as x approaches ${_targetX.toStringAsFixed(1)}',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 20),
            Slider(
              value: _xValue,
              min: _targetX - 3.0,
              max: _targetX + 3.0,
              divisions: 100,
              onChanged: (newValue) {
                setState(() {
                  _xValue = newValue;
                });
              },
            ),
            const SizedBox(height: 10),
            Text(
              'Current x: ${_xValue.toStringAsFixed(3)}',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 10),
            Text(
              'f(${_xValue.toStringAsFixed(3)}): ${_function(_xValue).toStringAsFixed(3)}',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 20),
            CustomPaint(
              size: Size(300, 200),
              painter: _NonExistentLimitGraphPainter(_function, _targetX, _xValue),
            ),
            const SizedBox(height: 20),
            Text(
              'As x approaches ${_targetX.toStringAsFixed(1)} from the left, f(x) approaches 1.0',
              style: TextStyle(fontSize: 14, color: Colors.blue),
              textAlign: TextAlign.center,
            ),
            Text(
              'As x approaches ${_targetX.toStringAsFixed(1)} from the right, f(x) approaches -1.0',
              style: TextStyle(fontSize: 14, color: Colors.red),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 10),
            const Text(
              'Since the left-hand limit and right-hand limit are not equal, the limit does NOT exist at x = 0.',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: Colors.purple),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

class _NonExistentLimitGraphPainter extends CustomPainter {
  final Function(double) _function;
  final double _targetX;
  final double _currentX;

  _NonExistentLimitGraphPainter(this._function, this._targetX, this._currentX);

  @override
  void paint(Canvas canvas, Size size) {
    final Paint paint = Paint()
      ..color = Colors.blue
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    final Paint pointPaint = Paint()
      ..color = Colors.red
      ..strokeWidth = 8
      ..style = PaintingStyle.fill;

    final Paint targetLinePaint = Paint()
      ..color = Colors.green
      ..strokeWidth = 1
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    final double minX = _targetX - 3.0;
    final double maxX = _targetX + 3.0;
    final double minY = -3.0; // Adjusted for this specific function
    final double maxY = 3.0;

    final double rangeX = maxX - minX;
    final double rangeY = maxY - minY;

    final double scaleX = size.width / rangeX;
    final double scaleY = size.height / rangeY;

    Offset toCanvas(double x, double y) {
      final double canvasX = (x - minX) * scaleX;
      final double canvasY = size.height - (y - minY) * scaleY;
      return Offset(canvasX, canvasY);
    }

    // Draw axes
    canvas.drawLine(toCanvas(minX, 0), toCanvas(maxX, 0), paint..color = Colors.grey); // X-axis
    canvas.drawLine(toCanvas(0, minY), toCanvas(0, maxY), paint..color = Colors.grey); // Y-axis

    // Draw function graph (left part)
    final Path pathLeft = Path();
    for (double i = minX; i < _targetX; i += 0.05) {
      double x = i;
      double y = _function(x);
      if (!y.isNaN && y.isFinite) {
        if (pathLeft.isEmpty) {
          pathLeft.moveTo(toCanvas(x, y).dx, toCanvas(x, y).dy);
        } else {
          pathLeft.lineTo(toCanvas(x, y).dx, toCanvas(x, y).dy);
        }
      }
    }
    canvas.drawPath(pathLeft, paint);

    // Draw function graph (right part)
    final Path pathRight = Path();
    for (double i = _targetX; i <= maxX; i += 0.05) {
      double x = i;
      double y = _function(x);
      if (!y.isNaN && y.isFinite) {
        if (pathRight.isEmpty) {
          pathRight.moveTo(toCanvas(x, y).dx, toCanvas(x, y).dy);
        } else {
          pathRight.lineTo(toCanvas(x, y).dx, toCanvas(x, y).dy);
        }
      }
    }
    canvas.drawPath(pathRight, paint);

    // Draw open circle at (0, 1) for the left part
    canvas.drawCircle(toCanvas(_targetX, _function(_targetX - 0.001)), 4, Paint()..color = Colors.blue..style = PaintingStyle.stroke..strokeWidth = 2);
    // Draw filled circle at (0, -1) for the right part
    canvas.drawCircle(toCanvas(_targetX, _function(_targetX)), 4, Paint()..color = Colors.blue..style = PaintingStyle.fill);


    // Draw target X line
    canvas.drawLine(toCanvas(_targetX, minY), toCanvas(_targetX, maxY), targetLinePaint);
    TextPainter(
      text: TextSpan(text: 'x=${_targetX.toStringAsFixed(1)}', style: TextStyle(color: Colors.green, fontSize: 10)),
      textDirection: TextDirection.ltr,
    )..layout()..paint(canvas, toCanvas(_targetX + 0.1, minY + 0.1));

    // Draw current x and f(x) point
    double currentY = _function(_currentX);
    if (!currentY.isNaN && currentY.isFinite) {
      canvas.drawCircle(toCanvas(_currentX, currentY), 4, pointPaint);
      // Draw lines to axes
      canvas.drawLine(toCanvas(_currentX, currentY), toCanvas(_currentX, 0), targetLinePaint..color = Colors.red.withOpacity(0.5));
      canvas.drawLine(toCanvas(_currentX, currentY), toCanvas(0, currentY), targetLinePaint..color = Colors.red.withOpacity(0.5));

      TextPainter(
        text: TextSpan(text: '(${_currentX.toStringAsFixed(2)}, ${currentY.toStringAsFixed(2)})', style: TextStyle(color: Colors.red, fontSize: 10)),
        textDirection: TextDirection.ltr,
      )..layout()..paint(canvas, toCanvas(_currentX + 0.1, currentY + 0.1));
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    _NonExistentLimitGraphPainter oldPainter = oldDelegate as _NonExistentLimitGraphPainter;
    return oldPainter._currentX != _currentX;
  }
}
