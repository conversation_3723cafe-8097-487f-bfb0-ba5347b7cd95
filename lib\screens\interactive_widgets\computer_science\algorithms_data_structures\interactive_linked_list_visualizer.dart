import 'package:flutter/material.dart';
import 'package:resonance_app/models/interactive_widget_model.dart';

class Node {
  int value;
  Node? next;

  Node(this.value, [this.next]);
}

class InteractiveLinkedListVisualizer extends StatefulWidget {
  final InteractiveWidgetModel widgetModel;

  const InteractiveLinkedListVisualizer({Key? key, required this.widgetModel}) : super(key: key);

  @override
  _InteractiveLinkedListVisualizerState createState() => _InteractiveLinkedListVisualizerState();
}

class _InteractiveLinkedListVisualizerState extends State<InteractiveLinkedListVisualizer> {
  Node? _head;
  final TextEditingController _addController = TextEditingController();
  final TextEditingController _removeController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _initializeList();
  }

  void _initializeList() {
    _head = Node(10, Node(20, Node(30, Node(40, Node(50)))));
  }

  void _addNode(int value) {
    setState(() {
      if (_head == null) {
        _head = Node(value);
      } else {
        Node? current = _head;
        while (current!.next != null) {
          current = current.next;
        }
        current.next = Node(value);
      }
    });
  }

  void _removeNode(int value) {
    setState(() {
      if (_head == null) return;

      if (_head!.value == value) {
        _head = _head!.next;
        return;
      }

      Node? current = _head;
      while (current!.next != null && current.next!.value != value) {
        current = current.next;
      }

      if (current.next != null) {
        current.next = current.next!.next;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.widgetModel.name,
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 10),
          Text(
            widget.widgetModel.description,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          const SizedBox(height: 20),
          _buildLinkedListVisualizer(),
          const SizedBox(height: 20),
          _buildControls(),
        ],
      ),
    );
  }

  Widget _buildLinkedListVisualizer() {
    List<Widget> nodes = [];
    Node? current = _head;
    while (current != null) {
      nodes.add(_buildNodeWidget(current.value));
      if (current.next != null) {
        nodes.add(
          Icon(Icons.arrow_right_alt, size: 40, color: Colors.grey[700]),
        );
      }
      current = current.next;
    }

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: nodes,
      ),
    );
  }

  Widget _buildNodeWidget(int value) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 5),
      padding: const EdgeInsets.all(15),
      decoration: BoxDecoration(
        color: Colors.lightBlue.withOpacity(0.3),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.lightBlue),
      ),
      child: Text(
        value.toString(),
        style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
      ),
    );
  }

  Widget _buildControls() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: _addController,
                keyboardType: TextInputType.number,
                decoration: const InputDecoration(
                  labelText: 'Value to Add',
                  border: OutlineInputBorder(),
                ),
              ),
            ),
            const SizedBox(width: 10),
            ElevatedButton(
              onPressed: () {
                final value = int.tryParse(_addController.text);
                if (value != null) {
                  _addNode(value);
                  _addController.clear();
                }
              },
              child: const Text('Add Node'),
            ),
          ],
        ),
        const SizedBox(height: 10),
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: _removeController,
                keyboardType: TextInputType.number,
                decoration: const InputDecoration(
                  labelText: 'Value to Remove',
                  border: OutlineInputBorder(),
                ),
              ),
            ),
            const SizedBox(width: 10),
            ElevatedButton(
              onPressed: () {
                final value = int.tryParse(_removeController.text);
                if (value != null) {
                  _removeNode(value);
                  _removeController.clear();
                }
              },
              child: const Text('Remove Node'),
            ),
          ],
        ),
      ],
    );
  }
}
