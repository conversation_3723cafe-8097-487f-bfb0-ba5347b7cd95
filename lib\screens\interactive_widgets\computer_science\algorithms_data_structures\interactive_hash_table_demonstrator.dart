import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:resonance_app/services/interactive_widget_service.dart';
import 'package:resonance_app/widgets/animated_button.dart';

class InteractiveHashTableDemonstrator extends StatefulWidget {
  final String widgetId;

  const InteractiveHashTableDemonstrator({Key? key, required this.widgetId}) : super(key: key);

  @override
  _InteractiveHashTableDemonstratorState createState() => _InteractiveHashTableDemonstratorState();
}

class _InteractiveHashTableDemonstratorState extends State<InteractiveHashTableDemonstrator> {
  Map<String, String> _hashTable = {};
  TextEditingController _keyController = TextEditingController();
  TextEditingController _valueController = TextEditingController();
  String _message = '';

  @override
  void initState() {
    super.initState();
    _loadState();
  }

  void _loadState() async {
    final service = Provider.of<InteractiveWidgetService>(context, listen: false);
    final savedState = await service.getWidgetState(widget.widgetId);
    if (savedState != null) {
      setState(() {
        _hashTable = Map<String, String>.from(savedState['hashTable'] ?? {});
      });
    }
  }

  void _saveState() {
    final service = Provider.of<InteractiveWidgetService>(context, listen: false);
    service.saveWidgetState(widget.widgetId, {
      'hashTable': _hashTable,
    });
  }

  void _addEntry() {
    final key = _keyController.text;
    final value = _valueController.text;
    if (key.isNotEmpty && value.isNotEmpty) {
      setState(() {
        _hashTable[key] = value;
        _message = 'Added: $key -> $value';
      });
      _keyController.clear();
      _valueController.clear();
      _saveState();
    } else {
      setState(() {
        _message = 'Please enter both key and value.';
      });
    }
  }

  void _retrieveValue() {
    final key = _keyController.text;
    if (key.isNotEmpty) {
      final value = _hashTable[key];
      setState(() {
        _message = value != null ? 'Value for "$key": $value' : 'Key "$key" not found.';
      });
      _keyController.clear();
    } else {
      setState(() {
        _message = 'Please enter a key to retrieve.';
      });
    }
  }

  void _removeEntry() {
    final key = _keyController.text;
    if (key.isNotEmpty) {
      if (_hashTable.containsKey(key)) {
        setState(() {
          _hashTable.remove(key);
          _message = 'Removed key: "$key"';
        });
        _saveState();
      } else {
        setState(() {
          _message = 'Key "$key" not found.';
        });
      }
      _keyController.clear();
    } else {
      setState(() {
        _message = 'Please enter a key to remove.';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Interactive Hash Table Demonstrator',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 20),
          TextField(
            controller: _keyController,
            decoration: const InputDecoration(
              labelText: 'Enter Key',
              border: OutlineInputBorder(),
            ),
          ),
          const SizedBox(height: 10),
          TextField(
            controller: _valueController,
            decoration: const InputDecoration(
              labelText: 'Enter Value',
              border: OutlineInputBorder(),
            ),
          ),
          const SizedBox(height: 10),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              AnimatedButton(
                onTap: _addEntry,
                text: 'Add Entry',
                color: Colors.blue,
              ),
              AnimatedButton(
                onTap: _retrieveValue,
                text: 'Retrieve Value',
                color: Colors.green,
              ),
              AnimatedButton(
                onTap: _removeEntry,
                text: 'Remove Entry',
                color: Colors.red,
              ),
            ],
          ),
          const SizedBox(height: 20),
          Text(
            'Hash Table Contents:',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 10),
          Expanded(
            child: ListView.builder(
              itemCount: _hashTable.length,
              itemBuilder: (context, index) {
                final key = _hashTable.keys.elementAt(index);
                final value = _hashTable[key];
                return Card(
                  margin: const EdgeInsets.symmetric(vertical: 4),
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Text('$key: $value'),
                  ),
                );
              },
            ),
          ),
          const SizedBox(height: 20),
          Text(
            'Message: $_message',
            style: Theme.of(context).textTheme.bodyLarge,
          ),
        ],
      ),
    );
  }
}
