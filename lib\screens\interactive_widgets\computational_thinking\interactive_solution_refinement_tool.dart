import 'package:flutter/material.dart';

class InteractiveSolutionRefinementTool extends StatefulWidget {
  const InteractiveSolutionRefinementTool({super.key});

  @override
  State<InteractiveSolutionRefinementTool> createState() => _InteractiveSolutionRefinementToolState();
}

class _InteractiveSolutionRefinementToolState extends State<InteractiveSolutionRefinementTool> {
  final TextEditingController _initialSolutionController = TextEditingController();
  final TextEditingController _refinementSuggestionController = TextEditingController();
  List<String> _refinementSteps = [];

  void _addRefinement() {
    setState(() {
      String suggestion = _refinementSuggestionController.text.trim();
      if (suggestion.isNotEmpty) {
        _refinementSteps.add(suggestion);
        _refinementSuggestionController.clear();
      }
    });
  }

  void _clearRefinements() {
    setState(() {
      _refinementSteps.clear();
      _initialSolutionController.clear();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Interactive Solution Refinement Tool',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            TextField(
              controller: _initialSolutionController,
              decoration: InputDecoration(
                labelText: 'Enter Initial Solution/Idea',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
            const SizedBox(height: 10),
            TextField(
              controller: _refinementSuggestionController,
              decoration: InputDecoration(
                labelText: 'Suggest a Refinement (e.g., "Improve efficiency", "Handle edge cases")',
                border: OutlineInputBorder(),
              ),
              onSubmitted: (_) => _addRefinement(),
            ),
            const SizedBox(height: 10),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ElevatedButton(
                  onPressed: _addRefinement,
                  child: const Text('Add Refinement'),
                ),
                ElevatedButton(
                  onPressed: _clearRefinements,
                  child: const Text('Clear All'),
                  style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
                ),
              ],
            ),
            const SizedBox(height: 20),
            if (_refinementSteps.isNotEmpty)
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Refinement Steps:',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 10),
                  ListView.builder(
                    shrinkWrap: true,
                    itemCount: _refinementSteps.length,
                    itemBuilder: (context, index) {
                      return Card(
                        margin: const EdgeInsets.symmetric(vertical: 4.0),
                        child: ListTile(
                          leading: CircleAvatar(child: Text('${index + 1}')),
                          title: Text(_refinementSteps[index]),
                          trailing: IconButton(
                            icon: const Icon(Icons.delete, color: Colors.red),
                            onPressed: () {
                              setState(() {
                                _refinementSteps.removeAt(index);
                              });
                            },
                          ),
                        ),
                      );
                    },
                  ),
                ],
              )
            else
              const Text(
                'Enter an initial solution and suggest refinements to improve it.',
                style: TextStyle(fontStyle: FontStyle.italic, color: Colors.grey),
              ),
          ],
        ),
      ),
    );
  }
}
