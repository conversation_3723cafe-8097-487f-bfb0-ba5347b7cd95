import 'package:flutter/material.dart';
import 'package:resonance/utils/page_transitions.dart';
import 'package:resonance/widgets/animated_button.dart';

class InteractiveBodePlotGenerator extends StatefulWidget {
  const InteractiveBodePlotGenerator({super.key});

  @override
  State<InteractiveBodePlotGenerator> createState() => _InteractiveBodePlotGeneratorState();
}

class _InteractiveBodePlotGeneratorState extends State<InteractiveBodePlotGenerator> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Interactive Bode Plot Generator'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            Navigator.of(context).pop();
          },
        ),
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Text(
                'This is the Interactive Bode Plot Generator.',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 18),
              ),
              const SizedBox(height: 30),
              AnimatedButton(
                onTap: () {
                  // Add functionality for the button here
                },
                text: 'Generate Bode Plot',
                color: Colors.blueAccent,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
