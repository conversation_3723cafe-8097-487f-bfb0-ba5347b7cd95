import 'package:flutter/material.dart';

class InteractiveEnergyEfficiencyCalculator extends StatefulWidget {
  const InteractiveEnergyEfficiencyCalculator({super.key});

  @override
  InteractiveEnergyEfficiencyCalculatorState createState() => InteractiveEnergyEfficiencyCalculatorState();
}

class InteractiveEnergyEfficiencyCalculatorState extends State<InteractiveEnergyEfficiencyCalculator> {
  double _volume = 200.0; // Liters
  double _insulationThickness = 5.0; // cm
  double _doorSealQuality = 0.8; // 0.0 (bad) to 1.0 (excellent)
  double _energyConsumption = 0.0; // kWh/year

  @override
  void initState() {
    super.initState();
    _calculateEnergyConsumption();
  }

  void _calculateEnergyConsumption() {
    setState(() {
      // Simplified model for energy consumption
      // Factors: volume, insulation, door seal quality
      // Higher volume -> more energy
      // Thicker insulation -> less energy
      // Better door seal -> less energy
      _energyConsumption = (_volume * 0.5) / (_insulationThickness * 0.1 + _doorSealQuality * 10);
      _energyConsumption = _energyConsumption.clamp(50.0, 1000.0); // Clamp to reasonable range
    });
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Refrigerator Energy Efficiency Calculator',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 20),
            Text('Volume: ${_volume.toStringAsFixed(0)} Liters'),
            Slider(
              value: _volume,
              min: 100,
              max: 500,
              divisions: 400,
              label: _volume.toStringAsFixed(0),
              onChanged: (double value) {
                setState(() {
                  _volume = value;
                  _calculateEnergyConsumption();
                });
              },
            ),
            const SizedBox(height: 10),
            Text('Insulation Thickness: ${_insulationThickness.toStringAsFixed(1)} cm'),
            Slider(
              value: _insulationThickness,
              min: 1,
              max: 10,
              divisions: 90,
              label: _insulationThickness.toStringAsFixed(1),
              onChanged: (double value) {
                setState(() {
                  _insulationThickness = value;
                  _calculateEnergyConsumption();
                });
              },
            ),
            const SizedBox(height: 10),
            Text('Door Seal Quality: ${(_doorSealQuality * 100).toStringAsFixed(0)}%'),
            Slider(
              value: _doorSealQuality,
              min: 0.1,
              max: 1.0,
              divisions: 90,
              label: (_doorSealQuality * 100).toStringAsFixed(0),
              onChanged: (double value) {
                setState(() {
                  _doorSealQuality = value;
                  _calculateEnergyConsumption();
                });
              },
            ),
            const SizedBox(height: 20),
            Text(
              'Estimated Annual Energy Consumption: ${_energyConsumption.toStringAsFixed(2)} kWh/year',
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            const Text(
              'Adjust the sliders to see how different factors affect a refrigerator\'s energy efficiency.',
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 14, fontStyle: FontStyle.italic),
            ),
          ],
        ),
      ),
    );
  }
}
