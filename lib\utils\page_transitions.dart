import 'package:flutter/material.dart';

/// A custom page route that provides smooth fade transitions between pages
class SmoothPageRoute<T> extends PageRoute<T> {
  final Widget page;
  final Duration duration;
  @override
  final RouteSettings settings;
  final bool fadeIn;

  SmoothPageRoute({
    required this.page,
    required this.settings,
    this.fadeIn = true,
    this.duration = const Duration(milliseconds: 200), // Faster transition
  });

  @override
  Color? get barrierColor => null;

  @override
  bool get barrierDismissible => false;

  @override
  String? get barrierLabel => null;

  @override
  Widget buildPage(
    BuildContext context,
    Animation<double> animation,
    Animation<double> secondaryAnimation,
  ) {
    return page;
  }

  @override
  Widget buildTransitions(
    BuildContext context,
    Animation<double> animation,
    Animation<double> secondaryAnimation,
    Widget child,
  ) {
    // Simple fade transition
    return FadeTransition(
      opacity: animation.drive(
        Tween(begin: 0.0, end: 1.0).chain(CurveTween(curve: Curves.easeOut)),
      ),
      child: child,
    );
  }

  @override
  bool get maintainState => true;

  @override
  Duration get transitionDuration => duration;
}

/// Navigation utility class to centralize navigation methods
class AppNavigator {
  /// Navigate to a new screen with a fade transition
  static Future<T?> push<T>(BuildContext context, Widget page) {
    return Navigator.of(context).push<T>(
      SmoothPageRoute<T>(
        page: page,
        settings: RouteSettings(name: page.runtimeType.toString()),
      ),
    );
  }

  /// Replace the current screen with a new one using a fade transition
  static Future<T?> pushReplacement<T, TO>(BuildContext context, Widget page) {
    return Navigator.of(context).pushReplacement<T, TO>(
      SmoothPageRoute<T>(
        page: page,
        settings: RouteSettings(name: page.runtimeType.toString()),
      ),
    );
  }

  /// Push a new screen and remove all previous screens
  static Future<T?> pushAndRemoveUntil<T>(BuildContext context, Widget page) {
    return Navigator.of(context).pushAndRemoveUntil<T>(
      SmoothPageRoute<T>(
        page: page,
        settings: RouteSettings(name: page.runtimeType.toString()),
      ),
      (route) => false,
    );
  }

  /// Pop the current screen
  static void pop<T>(BuildContext context, [T? result]) {
    Navigator.of(context).pop<T>(result);
  }
}

/// A custom page transition for the assessment flow
class AssessmentPageTransition extends StatelessWidget {
  final Widget child;
  final Animation<double> animation;
  final bool forward;

  const AssessmentPageTransition({
    super.key,
    required this.child,
    required this.animation,
    this.forward = true,
  });

  @override
  Widget build(BuildContext context) {
    // Define the slide animation
    final slideAnimation = Tween<Offset>(
      begin: Offset(forward ? 1.0 : -1.0, 0.0),
      end: Offset.zero,
    ).animate(CurvedAnimation(parent: animation, curve: Curves.easeOutCubic));

    // Define the fade animation
    final fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: animation,
        curve: Interval(0.0, 0.7, curve: Curves.easeOut),
      ),
    );

    // Define the scale animation
    final scaleAnimation = Tween<double>(
      begin: 0.95,
      end: 1.0,
    ).animate(CurvedAnimation(parent: animation, curve: Curves.easeOutCubic));

    return FadeTransition(
      opacity: fadeAnimation,
      child: SlideTransition(
        position: slideAnimation,
        child: ScaleTransition(scale: scaleAnimation, child: child),
      ),
    );
  }
}
