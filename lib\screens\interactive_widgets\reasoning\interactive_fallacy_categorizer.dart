import 'package:flutter/material.dart';
import 'package:resonance/widgets/animated_button.dart';

class InteractiveFallacyCategorizer extends StatefulWidget {
  const InteractiveFallacyCategorizer({super.key});

  @override
  State<InteractiveFallacyCategorizer> createState() => _InteractiveFallacyCategorizerState();
}

class _InteractiveFallacyCategorizerState extends State<InteractiveFallacyCategorizer> {
  int _currentQuestionIndex = 0;
  String? _selectedCategory;
  String _feedback = '';
  int _score = 0;

  final List<Map<String, dynamic>> _questions = [
    {
      'argument': 'My doctor told me to eat more vegetables, but he\'s overweight, so I won\'t listen to him.',
      'correctCategory': 'Ad Hominem',
      'explanation': 'This is an Ad Hominem fallacy because it attacks the doctor\'s character (being overweight) instead of addressing the validity of his advice.',
    },
    {
      'argument': 'If we allow same-sex marriage, then soon people will want to marry animals, and then chaos will ensue!',
      'correctCategory': 'Slippery Slope',
      'explanation': 'This is a Slippery Slope fallacy, asserting that one action will inevitably lead to a series of increasingly negative consequences without sufficient evidence.',
    },
    {
      'argument': 'Everyone knows that ghosts exist, so they must be real.',
      'correctCategory': 'Bandwagon (Ad Populum)',
      'explanation': 'This is a Bandwagon fallacy, arguing that something is true because many people believe it, rather than providing logical evidence.',
    },
    {
      'argument': 'You either support the new tax law or you hate our country.',
      'correctCategory': 'False Dilemma (Black or White)',
      'explanation': 'This is a False Dilemma, presenting only two extreme options as the only possibilities, when other alternatives may exist.',
    },
    {
      'argument': 'The reason I am a good student is because I get good grades.',
      'correctCategory': 'Circular Reasoning (Begging the Question)',
      'explanation': 'This is Circular Reasoning because the premise (getting good grades) is simply a restatement of the conclusion (being a good student), offering no independent support.',
    },
  ];

  final List<String> _categories = [
    'Ad Hominem',
    'Straw Man',
    'Appeal to Authority',
    'False Dilemma (Black or White)',
    'Slippery Slope',
    'Hasty Generalization',
    'Red Herring',
    'Bandwagon (Ad Populum)',
    'Appeal to Emotion',
    'Circular Reasoning (Begging the Question)',
  ];

  void _checkAnswer() {
    if (_selectedCategory == null) {
      setState(() {
        _feedback = 'Please select a fallacy category.';
      });
      return;
    }

    if (_selectedCategory == _questions[_currentQuestionIndex]['correctCategory']) {
      setState(() {
        _feedback = 'Correct! ${_questions[_currentQuestionIndex]['explanation']}';
        _score++;
      });
    } else {
      setState(() {
        _feedback = 'Incorrect. The correct category was: "${_questions[_currentQuestionIndex]['correctCategory']}". ${_questions[_currentQuestionIndex]['explanation']}';
      });
    }
  }

  void _nextQuestion() {
    setState(() {
      if (_currentQuestionIndex < _questions.length - 1) {
        _currentQuestionIndex++;
        _selectedCategory = null;
        _feedback = '';
      } else {
        _feedback = 'Challenge Completed! Your score: $_score / ${_questions.length}';
      }
    });
  }

  void _resetChallenge() {
    setState(() {
      _currentQuestionIndex = 0;
      _selectedCategory = null;
      _feedback = '';
      _score = 0;
    });
  }

  @override
  Widget build(BuildContext context) {
    final currentQuestion = _questions[_currentQuestionIndex];

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text(
            'Fallacy Categorizer',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.primary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 20),
          Card(
            elevation: 4,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            color: Theme.of(context).colorScheme.surfaceVariant,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Argument:',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                  const SizedBox(height: 10),
                  Text(
                    currentQuestion['argument'],
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                  const SizedBox(height: 15),
                  Text(
                    'Select the fallacy category:',
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                  ..._categories.map((category) {
                    return RadioListTile<String>(
                      title: Text(
                        category,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                      value: category,
                      groupValue: _selectedCategory,
                      onChanged: (value) {
                        setState(() {
                          _selectedCategory = value;
                          _feedback = ''; // Clear feedback on new selection
                        });
                      },
                      activeColor: Theme.of(context).colorScheme.secondary,
                    );
                  }).toList(),
                ],
              ),
            ),
          ),
          const SizedBox(height: 20),
          AnimatedButton(
            onTap: _checkAnswer,
            label: 'Check Answer',
            color: Theme.of(context).colorScheme.secondary,
            labelColor: Theme.of(context).colorScheme.onSecondary,
          ),
          if (_feedback.isNotEmpty) ...[
            const SizedBox(height: 10),
            Text(
              _feedback,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: _feedback.startsWith('Correct') ? Colors.green : Colors.red,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
          ],
          const SizedBox(height: 20),
          AnimatedButton(
            onTap: _nextQuestion,
            label: _currentQuestionIndex < _questions.length - 1 ? 'Next Argument' : 'Finish Challenge',
            color: Theme.of(context).colorScheme.tertiary,
            labelColor: Theme.of(context).colorScheme.onTertiary,
          ),
          if (_currentQuestionIndex == _questions.length - 1 && _feedback.contains('Completed'))
            Padding(
              padding: const EdgeInsets.only(top: 20.0),
              child: AnimatedButton(
                onTap: _resetChallenge,
                label: 'Restart Challenge',
                color: Theme.of(context).colorScheme.primary,
                labelColor: Theme.of(context).colorScheme.onPrimary,
              ),
            ),
        ],
      ),
    );
  }
}
