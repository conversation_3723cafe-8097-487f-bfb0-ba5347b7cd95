import 'package:flutter/material.dart';
import 'package:resonance/widgets/interactive_widget_scaffold.dart';

class InteractiveTimelineBranchingVisualizer extends StatefulWidget {
  const InteractiveTimelineBranchingVisualizer({super.key});

  @override
  State<InteractiveTimelineBranchingVisualizer> createState() => _InteractiveTimelineBranchingVisualizerState();
}

class _InteractiveTimelineBranchingVisualizerState extends State<InteractiveTimelineBranchingVisualizer> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  double _branchingFactor = 0.0; // 0.0 to 1.0, representing the likelihood/impact of branching

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 5),
    )..repeat(reverse: true);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return InteractiveWidgetScaffold(
      title: 'Timeline Branching Visualizer',
      description: 'Visualize branching timelines in paradox-free time travel.',
      interactiveContent: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CustomPaint(
              size: const Size(300, 200),
              painter: _TimelinePainter(
                animation: _controller,
                branchingFactor: _branchingFactor,
              ),
            ),
            const SizedBox(height: 20),
            Text(
              'Branching Likelihood/Impact: ${(_branchingFactor * 100).toStringAsFixed(0)}%',
              style: const TextStyle(fontSize: 18),
            ),
            Slider(
              value: _branchingFactor,
              min: 0.0,
              max: 1.0,
              divisions: 10,
              label: (_branchingFactor * 100).toStringAsFixed(0),
              onChanged: (value) {
                setState(() {
                  _branchingFactor = value;
                });
              },
            ),
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: Text(
                'Adjust the slider to visualize how time travel might create branching timelines in a paradox-free universe. A higher branching factor indicates more significant or frequent divergences from the original timeline.',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 16),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _TimelinePainter extends CustomPainter {
  final Animation<double> animation;
  final double branchingFactor;

  _TimelinePainter({required this.animation, required this.branchingFactor}) : super(repaint: animation);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.blueGrey
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;

    final center = Offset(size.width / 2, size.height / 2);

    // Main timeline
    canvas.drawLine(Offset(0, center.dy), Offset(size.width, center.dy), paint);

    // Branching points
    final numBranches = (branchingFactor * 5).round();
    for (int i = 0; i < numBranches; i++) {
      final branchX = size.width * (0.2 + i * 0.15);
      final branchYOffset = (animation.value * 20 * branchingFactor) * (i % 2 == 0 ? 1 : -1);

      // Draw a small circle at the branching point
      canvas.drawCircle(Offset(branchX, center.dy), 5.0, paint..style = PaintingStyle.fill);

      // Draw a branch line
      canvas.drawLine(
        Offset(branchX, center.dy),
        Offset(branchX + 30, center.dy + branchYOffset),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}
