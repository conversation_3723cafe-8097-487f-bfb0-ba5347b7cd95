import 'package:flutter/material.dart';

class AssessmentProgressBar extends StatelessWidget {
  final int currentPage;
  final int totalPages;
  final String title;

  const AssessmentProgressBar({
    Key? key,
    required this.currentPage,
    required this.totalPages,
    required this.title,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Header with title and progress indicator
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // Back button or icon
            Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(18),
                color: Color.fromRGBO(243, 243, 243, 1),
              ),
              width: 36,
              height: 36,
              child: IconButton(
                icon: Icon(
                  Icons.arrow_back,
                  size: 18,
                  color: Color.fromRGBO(16, 17, 20, 1),
                ),
                onPressed: () {
                  Navigator.of(context).pop();
                },
              ),
            ),
            
            // Title
            Text(
              title,
              style: TextStyle(
                color: Color.fromRGBO(16, 17, 20, 1),
                fontFamily: 'WorkSans',
                fontSize: 20,
                fontWeight: FontWeight.w500,
              ),
            ),
            
            // Progress indicator
            Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                color: Color.fromRGBO(240, 247, 254, 1),
              ),
              padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              child: Text(
                '$currentPage of $totalPages',
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: Color.fromRGBO(100, 45, 176, 1), // Purple color
                  fontFamily: 'WorkSans',
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
        
        // Progress bar
        Container(
          margin: EdgeInsets.only(top: 16),
          width: double.infinity,
          height: 4,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(2),
            color: Color.fromRGBO(243, 243, 243, 1),
          ),
          child: Stack(
            children: [
              FractionallySizedBox(
                widthFactor: currentPage / totalPages,
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(2),
                    color: Color.fromRGBO(100, 45, 176, 1), // Purple color
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
