import 'package:flutter/material.dart';
import 'dart:math';

class InteractiveDerivativeDefinitionVisualizer extends StatefulWidget {
  const InteractiveDerivativeDefinitionVisualizer({super.key});

  @override
  State<InteractiveDerivativeDefinitionVisualizer> createState() => _InteractiveDerivativeDefinitionVisualizerState();
}

class _InteractiveDerivativeDefinitionVisualizerState extends State<InteractiveDerivativeDefinitionVisualizer> {
  double _xValue = 1.0;
  double _hValue = 1.0; // Represents delta x or h
  final TextEditingController _functionController = TextEditingController(text: 'x*x'); // Default function x^2

  double _function(double x) {
    try {
      // Basic parser for simple functions. For a robust solution, a math expression parser library would be needed.
      // This example handles x*x, x+2, sin(x)
      if (_functionController.text.contains('x*x')) {
        return x * x;
      } else if (_functionController.text.contains('x+2')) {
        return x + 2;
      } else if (_functionController.text.contains('sin(x)')) {
        return sin(x);
      }
      return double.nan; // Indicate unsupported function
    } catch (e) {
      return double.nan;
    }
  }

  double _calculateSecantSlope() {
    double x = _xValue;
    double h = _hValue;
    double y1 = _function(x);
    double y2 = _function(x + h);

    if (y1.isNaN || y2.isNaN || h == 0) {
      return double.nan;
    }
    return (y2 - y1) / h;
  }

  @override
  Widget build(BuildContext context) {
    double secantSlope = _calculateSecantSlope();

    return Card(
      margin: const EdgeInsets.all(16.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Interactive Derivative Definition Visualizer',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            TextField(
              controller: _functionController,
              decoration: InputDecoration(
                labelText: 'Enter Function f(x) (e.g., x*x, x+2, sin(x))',
                border: OutlineInputBorder(),
              ),
              onChanged: (value) {
                setState(() {}); // Rebuild on function change
              },
            ),
            const SizedBox(height: 20),
            Text(
              'Point x: ${_xValue.toStringAsFixed(2)}',
              style: TextStyle(fontSize: 16),
            ),
            Slider(
              value: _xValue,
              min: -3.0,
              max: 3.0,
              divisions: 60,
              onChanged: (newValue) {
                setState(() {
                  _xValue = newValue;
                });
              },
            ),
            const SizedBox(height: 10),
            Text(
              'h (Δx): ${_hValue.toStringAsFixed(2)}',
              style: TextStyle(fontSize: 16),
            ),
            Slider(
              value: _hValue,
              min: 0.01,
              max: 2.0,
              divisions: 199,
              onChanged: (newValue) {
                setState(() {
                  _hValue = newValue;
                });
              },
            ),
            const SizedBox(height: 10),
            Text(
              'Slope of Secant Line: ${secantSlope.isNaN ? 'Undefined' : secantSlope.toStringAsFixed(3)}',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: Colors.blue),
            ),
            const SizedBox(height: 20),
            CustomPaint(
              size: Size(300, 200),
              painter: _DerivativeDefinitionGraphPainter(_function, _xValue, _hValue),
            ),
            const SizedBox(height: 20),
            const Text(
              'As h approaches 0, the secant line approaches the tangent line, and its slope approaches the derivative.',
              style: TextStyle(fontSize: 14, fontStyle: FontStyle.italic),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

class _DerivativeDefinitionGraphPainter extends CustomPainter {
  final Function(double) _function;
  final double _x;
  final double _h;

  _DerivativeDefinitionGraphPainter(this._function, this._x, this._h);

  @override
  void paint(Canvas canvas, Size size) {
    final Paint paint = Paint()
      ..color = Colors.blue
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    final Paint pointPaint = Paint()
      ..color = Colors.red
      ..strokeWidth = 8
      ..style = PaintingStyle.fill;

    final Paint secantLinePaint = Paint()
      ..color = Colors.green
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    final double minX = -5.0;
    final double maxX = 5.0;
    final double minY = -10.0; // Adjusted for x^2 function
    final double maxY = 25.0;

    final double rangeX = maxX - minX;
    final double rangeY = maxY - minY;

    final double scaleX = size.width / rangeX;
    final double scaleY = size.height / rangeY;

    Offset toCanvas(double x, double y) {
      final double canvasX = (x - minX) * scaleX;
      final double canvasY = size.height - (y - minY) * scaleY;
      return Offset(canvasX, canvasY);
    }

    // Draw axes
    canvas.drawLine(toCanvas(minX, 0), toCanvas(maxX, 0), paint..color = Colors.grey); // X-axis
    canvas.drawLine(toCanvas(0, minY), toCanvas(0, maxY), paint..color = Colors.grey); // Y-axis

    // Draw function graph
    final Path path = Path();
    bool firstPoint = true;
    for (double i = minX; i <= maxX; i += 0.1) {
      double xVal = i;
      double yVal = _function(xVal);
      if (!yVal.isNaN && yVal.isFinite) {
        if (firstPoint) {
          path.moveTo(toCanvas(xVal, yVal).dx, toCanvas(xVal, yVal).dy);
          firstPoint = false;
        } else {
          path.lineTo(toCanvas(xVal, yVal).dx, toCanvas(xVal, yVal).dy);
        }
      } else {
        firstPoint = true;
      }
    }
    canvas.drawPath(path, paint);

    // Draw points (x, f(x)) and (x+h, f(x+h))
    double y_x = _function(_x);
    double y_xh = _function(_x + _h);

    if (!y_x.isNaN && y_x.isFinite) {
      canvas.drawCircle(toCanvas(_x, y_x), 4, pointPaint);
      TextPainter(
        text: TextSpan(text: '(${_x.toStringAsFixed(1)}, ${y_x.toStringAsFixed(1)})', style: TextStyle(color: Colors.red, fontSize: 10)),
        textDirection: TextDirection.ltr,
      )..layout()..paint(canvas, toCanvas(_x + 0.1, y_x + 0.1));
    }
    if (!y_xh.isNaN && y_xh.isFinite) {
      canvas.drawCircle(toCanvas(_x + _h, y_xh), 4, pointPaint);
      TextPainter(
        text: TextSpan(text: '(${(_x + _h).toStringAsFixed(1)}, ${y_xh.toStringAsFixed(1)})', style: TextStyle(color: Colors.red, fontSize: 10)),
        textDirection: TextDirection.ltr,
      )..layout()..paint(canvas, toCanvas(_x + _h + 0.1, y_xh + 0.1));
    }

    // Draw secant line
    if (!y_x.isNaN && !y_xh.isNaN && _h != 0) {
      canvas.drawLine(toCanvas(_x, y_x), toCanvas(_x + _h, y_xh), secantLinePaint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    _DerivativeDefinitionGraphPainter oldPainter = oldDelegate as _DerivativeDefinitionGraphPainter;
    return oldPainter._x != _x || oldPainter._h != _h;
  }
}
