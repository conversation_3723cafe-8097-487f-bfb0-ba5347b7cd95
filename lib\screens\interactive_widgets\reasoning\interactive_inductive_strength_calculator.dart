import 'package:flutter/material.dart';
import 'package:resonance/widgets/animated_button.dart';

class InteractiveInductiveStrengthCalculator extends StatefulWidget {
  const InteractiveInductiveStrengthCalculator({super.key});

  @override
  State<InteractiveInductiveStrengthCalculator> createState() => _InteractiveInductiveStrengthCalculatorState();
}

class _InteractiveInductiveStrengthCalculatorState extends State<InteractiveInductiveStrengthCalculator> {
  final TextEditingController _premisesController = TextEditingController();
  final TextEditingController _conclusionController = TextEditingController();
  String _strengthResult = '';

  void _calculateStrength() {
    setState(() {
      final String premises = _premisesController.text;
      final String conclusion = _conclusionController.text;

      if (premises.isEmpty || conclusion.isEmpty) {
        _strengthResult = 'Please enter both premises and a conclusion.';
        return;
      }

      // Placeholder for actual inductive strength calculation logic
      // This would involve analyzing the relationship between premises and conclusion
      // to determine the probability of the conclusion given the premises.
      _strengthResult = 'Calculating inductive strength for:\n\n'
          'Premises: "$premises"\n'
          'Conclusion: "$conclusion"\n\n'
          'This is a placeholder for a sophisticated algorithm that would assess '
          'the evidential support the premises provide for the conclusion. '
          'Factors like sample size, representativeness, background knowledge, '
          'and counter-evidence would be considered to determine strength.';

      // Example of a very basic, non-functional strength assessment
      if (premises.contains('all') && conclusion.contains('all')) {
        _strengthResult += '\n\n(Hint: Universal generalizations from limited premises are often weak inductive arguments.)';
      } else if (premises.split(',').length > 3 && conclusion.contains('likely')) {
        _strengthResult += '\n\n(Hint: A larger and more diverse set of premises can increase inductive strength.)';
      }
    });
  }

  @override
  void dispose() {
    _premisesController.dispose();
    _conclusionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text(
            'Inductive Strength Calculator',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.primary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 20),
          TextField(
            controller: _premisesController,
            maxLines: 4,
            decoration: InputDecoration(
              labelText: 'Enter Premises (comma-separated)',
              hintText: 'e.g., "Every raven observed so far is black, This bird is a raven"',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              filled: true,
              fillColor: Theme.of(context).cardColor,
            ),
          ),
          const SizedBox(height: 10),
          TextField(
            controller: _conclusionController,
            maxLines: 2,
            decoration: InputDecoration(
              labelText: 'Enter Conclusion',
              hintText: 'e.g., "Therefore, this bird is black."',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              filled: true,
              fillColor: Theme.of(context).cardColor,
            ),
          ),
          const SizedBox(height: 20),
          AnimatedButton(
            onTap: _calculateStrength,
            label: 'Calculate Strength',
            color: Theme.of(context).colorScheme.secondary,
            labelColor: Theme.of(context).colorScheme.onSecondary,
          ),
          const SizedBox(height: 20),
          Expanded(
            child: SingleChildScrollView(
              child: Card(
                elevation: 4,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                color: Theme.of(context).colorScheme.surfaceVariant,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Inductive Strength:',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                      const SizedBox(height: 10),
                      Text(
                        _strengthResult.isEmpty ? 'Enter premises and a conclusion and click "Calculate Strength" to see the result.' : _strengthResult,
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
