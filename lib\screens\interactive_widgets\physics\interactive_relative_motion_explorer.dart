import 'package:flutter/material.dart';
import 'dart:math';

class InteractiveRelativeMotionExplorer extends StatefulWidget {
  const InteractiveRelativeMotionExplorer({super.key});

  @override
  State<InteractiveRelativeMotionExplorer> createState() => _InteractiveRelativeMotionExplorerState();
}

class _InteractiveRelativeMotionExplorerState extends State<InteractiveRelativeMotionExplorer> {
  final TextEditingController _velocityAController = TextEditingController(text: '10');
  final TextEditingController _angleAController = TextEditingController(text: '0');
  final TextEditingController _velocityBController = TextEditingController(text: '5');
  final TextEditingController _angleBController = TextEditingController(text: '90');

  double _velocityA = 10.0; // m/s
  double _angleA = 0.0; // degrees
  double _velocityB = 5.0; // m/s
  double _angleB = 90.0; // degrees

  double _relativeVelocityMagnitude = 0.0;
  double _relativeVelocityAngle = 0.0;

  @override
  void dispose() {
    _velocityAController.dispose();
    _angleAController.dispose();
    _velocityBController.dispose();
    _angleBController.dispose();
    super.dispose();
  }

  void _calculateRelativeMotion() {
    setState(() {
      _velocityA = double.tryParse(_velocityAController.text) ?? 10.0;
      _angleA = double.tryParse(_angleAController.text) ?? 0.0;
      _velocityB = double.tryParse(_velocityBController.text) ?? 5.0;
      _angleB = double.tryParse(_angleBController.text) ?? 90.0;

      final double angleARad = _angleA * pi / 180.0;
      final double angleBRad = _angleB * pi / 180.0;

      final double vxA = _velocityA * cos(angleARad);
      final double vyA = _velocityA * sin(angleARad);

      final double vxB = _velocityB * cos(angleBRad);
      final double vyB = _velocityB * sin(angleBRad);

      // Relative velocity of B with respect to A (V_B - V_A)
      final double relativeVx = vxB - vxA;
      final double relativeVy = vyB - vyA;

      _relativeVelocityMagnitude = sqrt(relativeVx * relativeVx + relativeVy * relativeVy);
      _relativeVelocityAngle = atan2(relativeVy, relativeVx) * 180 / pi;

      if (_relativeVelocityAngle < 0) {
        _relativeVelocityAngle += 360;
      }
    });
  }

  @override
  void initState() {
    super.initState();
    _calculateRelativeMotion(); // Calculate initial relative motion on load
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Interactive Relative Motion Explorer'),
        backgroundColor: Colors.teal,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text(
              'Object A Velocity',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            TextField(
              controller: _velocityAController,
              keyboardType: TextInputType.number,
              decoration: const InputDecoration(
                labelText: 'Magnitude (m/s)',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 10),
            TextField(
              controller: _angleAController,
              keyboardType: TextInputType.number,
              decoration: const InputDecoration(
                labelText: 'Angle (degrees from positive x-axis)',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 20),
            Text(
              'Object B Velocity',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            TextField(
              controller: _velocityBController,
              keyboardType: TextInputType.number,
              decoration: const InputDecoration(
                labelText: 'Magnitude (m/s)',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 10),
            TextField(
              controller: _angleBController,
              keyboardType: TextInputType.number,
              decoration: const InputDecoration(
                labelText: 'Angle (degrees from positive x-axis)',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: _calculateRelativeMotion,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.teal,
                padding: const EdgeInsets.symmetric(vertical: 15),
              ),
              child: const Text(
                'Calculate Relative Motion',
                style: TextStyle(fontSize: 18, color: Colors.white),
              ),
            ),
            const SizedBox(height: 20),
            Text(
              'Relative Velocity (B with respect to A):',
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            Text(
              'Magnitude: ${_relativeVelocityMagnitude.toStringAsFixed(2)} m/s',
              style: const TextStyle(fontSize: 16),
            ),
            Text(
              'Angle: ${_relativeVelocityAngle.toStringAsFixed(2)}°',
              style: const TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 20),
            Expanded(
              child: CustomPaint(
                painter: RelativeMotionPainter(
                  velocityA: _velocityA,
                  angleA: _angleA,
                  velocityB: _velocityB,
                  angleB: _angleB,
                  relativeVelocityMagnitude: _relativeVelocityMagnitude,
                  relativeVelocityAngle: _relativeVelocityAngle,
                ),
                child: Container(),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class RelativeMotionPainter extends CustomPainter {
  final double velocityA;
  final double angleA;
  final double velocityB;
  final double angleB;
  final double relativeVelocityMagnitude;
  final double relativeVelocityAngle;

  RelativeMotionPainter({
    required this.velocityA,
    required this.angleA,
    required this.velocityB,
    required this.angleB,
    required this.relativeVelocityMagnitude,
    required this.relativeVelocityAngle,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final double centerX = size.width / 2;
    final double centerY = size.height / 2;
    const double scaleFactor = 5.0; // Pixels per m/s

    // Draw origin
    canvas.drawCircle(Offset(centerX, centerY), 5, Paint()..color = Colors.black);
    canvas.drawCircle(Offset(centerX, centerY), 2, Paint()..color = Colors.white);

    // Draw axes
    final Paint axisPaint = Paint()
      ..color = Colors.grey
      ..strokeWidth = 1.0;
    canvas.drawLine(Offset(0, centerY), Offset(size.width, centerY), axisPaint); // X-axis
    canvas.drawLine(Offset(centerX, 0), Offset(centerX, size.height), axisPaint); // Y-axis

    // Helper to draw vector
    void drawVector(Canvas c, Offset start, double magnitude, double angleDegrees, Color color, String label) {
      final double angleRad = angleDegrees * pi / 180.0;
      final double endX = start.dx + magnitude * scaleFactor * cos(angleRad);
      final double endY = start.dy - magnitude * scaleFactor * sin(angleRad); // Y-axis is inverted in canvas

      final Paint vectorPaint = Paint()
        ..color = color
        ..strokeWidth = 3.0
        ..strokeCap = StrokeCap.round;

      c.drawLine(start, Offset(endX, endY), vectorPaint);

      // Draw arrow head
      final double arrowSize = 8.0;
      final Path arrowPath = Path();
      arrowPath.moveTo(endX, endY);
      arrowPath.lineTo(endX - arrowSize * cos(angleRad - pi / 6), endY + arrowSize * sin(angleRad - pi / 6));
      arrowPath.lineTo(endX - arrowSize * cos(angleRad + pi / 6), endY + arrowSize * sin(angleRad + pi / 6));
      arrowPath.close();
      c.drawPath(arrowPath, vectorPaint);

      // Draw text label
      final TextPainter textPainter = TextPainter(
        text: TextSpan(
          text: label,
          style: TextStyle(color: color, fontSize: 12),
        ),
        textDirection: TextDirection.ltr,
      );
      textPainter.layout();
      textPainter.paint(c, Offset(endX + 5, endY + 5));
    }

    // Draw Vector A (V_A)
    drawVector(canvas, Offset(centerX, centerY), velocityA, angleA, Colors.blue, 'V_A');

    // Draw Vector B (V_B)
    drawVector(canvas, Offset(centerX, centerY), velocityB, angleB, Colors.green, 'V_B');

    // Draw Relative Vector (V_B - V_A)
    // This vector starts from the tip of V_A and ends at the tip of V_B
    final double angleARad = angleA * pi / 180.0;
    final double startRelativeX = centerX + velocityA * scaleFactor * cos(angleARad);
    final double startRelativeY = centerY - velocityA * scaleFactor * sin(angleARad);

    drawVector(canvas, Offset(startRelativeX, startRelativeY), relativeVelocityMagnitude, relativeVelocityAngle, Colors.red, 'V_B/A');
  }

  @override
  bool shouldRepaint(covariant RelativeMotionPainter oldDelegate) {
    return oldDelegate.velocityA != velocityA ||
        oldDelegate.angleA != angleA ||
        oldDelegate.velocityB != velocityB ||
        oldDelegate.angleB != angleB ||
        oldDelegate.relativeVelocityMagnitude != relativeVelocityMagnitude ||
        oldDelegate.relativeVelocityAngle != relativeVelocityAngle;
  }
}
