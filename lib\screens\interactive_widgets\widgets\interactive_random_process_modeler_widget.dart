import 'package:flutter/material.dart';
import 'dart:math';

/// A widget that allows users to model and visualize random processes.
class InteractiveRandomProcessModelerWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveRandomProcessModelerWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  /// Factory constructor to create the widget from JSON data
  factory InteractiveRandomProcessModelerWidget.fromData(Map<String, dynamic> data) {
    return InteractiveRandomProcessModelerWidget(
      data: data,
    );
  }

  @override
  State<InteractiveRandomProcessModelerWidget> createState() => _InteractiveRandomProcessModelerWidgetState();
}

class _InteractiveRandomProcessModelerWidgetState extends State<InteractiveRandomProcessModelerWidget> with SingleTickerProviderStateMixin {
  late int _numberOfTrials;
  late double _probabilityOfHeads; // For coin flip example
  
  int _headsCount = 0;
  int _tailsCount = 0;
  List<bool> _outcomes = []; // true for heads, false for tails

  late AnimationController _animationController;
  late Animation<double> _animation;
  
  final Random _random = Random();

  // Colors
  late Color _primaryColor;
  late Color _secondaryColor;
  late Color _backgroundColor;
  late Color _textColor;

  @override
  void initState() {
    super.initState();
    _numberOfTrials = widget.data['number_of_trials'] ?? 100;
    _probabilityOfHeads = widget.data['probability_of_heads']?.toDouble() ?? 0.5;

    _primaryColor = _parseColor(widget.data['primary_color']) ?? Colors.blue;
    _secondaryColor = _parseColor(widget.data['secondary_color']) ?? Colors.orange;
    _backgroundColor = _parseColor(widget.data['background_color']) ?? Colors.white;
    _textColor = _parseColor(widget.data['text_color']) ?? Colors.black87;

    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );

    _runSimulation(); // Run initial simulation
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Color? _parseColor(String? hexString) {
    if (hexString == null) return null;
    hexString = hexString.replaceAll('#', '');
    if (hexString.length == 6) {
      hexString = 'FF$hexString';
    }
    try {
      return Color(int.parse(hexString, radix: 16));
    } catch (e) {
      return null;
    }
  }

  void _runSimulation() {
    setState(() {
      _headsCount = 0;
      _tailsCount = 0;
      _outcomes = [];
      for (int i = 0; i < _numberOfTrials; i++) {
        bool isHeads = _random.nextDouble() < _probabilityOfHeads;
        _outcomes.add(isHeads);
        if (isHeads) {
          _headsCount++;
        } else {
          _tailsCount++;
        }
      }
    });
    _animationController.forward(from: 0.0);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _backgroundColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.data['title'] ?? 'Random Process Modeler (Coin Flip)',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),
          const SizedBox(height: 8),
          if (widget.data['description'] != null)
            Text(
              widget.data['description'],
              style: TextStyle(
                fontSize: 14,
                color: _textColor.withOpacity(0.8),
              ),
            ),
          const SizedBox(height: 16),

          Text(
            'Number of Flips: $_numberOfTrials',
            style: TextStyle(color: _textColor),
          ),
          Slider(
            value: _numberOfTrials.toDouble(),
            min: 10,
            max: 1000,
            divisions: (1000 - 10) ~/ 10,
            label: _numberOfTrials.toString(),
            activeColor: _primaryColor,
            onChanged: (value) {
              setState(() {
                _numberOfTrials = value.toInt();
              });
            },
            onChangeEnd: (value) {
              _runSimulation();
            },
          ),
          const SizedBox(height: 8),

          Text(
            'Probability of Heads: ${_probabilityOfHeads.toStringAsFixed(2)}',
            style: TextStyle(color: _textColor),
          ),
          Slider(
            value: _probabilityOfHeads,
            min: 0.0,
            max: 1.0,
            divisions: 100,
            label: _probabilityOfHeads.toStringAsFixed(2),
            activeColor: _secondaryColor,
            onChanged: (value) {
              setState(() {
                _probabilityOfHeads = value;
              });
            },
            onChangeEnd: (value) {
              _runSimulation();
            },
          ),
          const SizedBox(height: 16),

          Center(
            child: ElevatedButton.icon(
              onPressed: _runSimulation,
              icon: const Icon(Icons.refresh),
              label: const Text('Run Simulation'),
              style: ElevatedButton.styleFrom(
                backgroundColor: _primaryColor,
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              ),
            ),
          ),
          const SizedBox(height: 16),

          AnimatedBuilder(
            animation: _animation,
            builder: (context, child) {
              final currentHeads = (_headsCount * _animation.value).toInt();
              final currentTails = (_tailsCount * _animation.value).toInt();
              final total = currentHeads + currentTails;

              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Results:',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: _textColor,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Heads: $currentHeads (${total > 0 ? (currentHeads / total * 100).toStringAsFixed(1) : 0}%)',
                    style: TextStyle(color: _primaryColor),
                  ),
                  Text(
                    'Tails: $currentTails (${total > 0 ? (currentTails / total * 100).toStringAsFixed(1) : 0}%)',
                    style: TextStyle(color: _secondaryColor),
                  ),
                  const SizedBox(height: 16),
                  Container(
                    height: 100,
                    child: CustomPaint(
                      painter: CoinFlipBarPainter(
                        headsCount: currentHeads,
                        tailsCount: currentTails,
                        headsColor: _primaryColor,
                        tailsColor: _secondaryColor,
                      ),
                      child: Container(),
                    ),
                  ),
                ],
              );
            },
          ),

          // Widget name tag
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'InteractiveRandomProcessModeler',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }
}

class CoinFlipBarPainter extends CustomPainter {
  final int headsCount;
  final int tailsCount;
  final Color headsColor;
  final Color tailsColor;

  CoinFlipBarPainter({
    required this.headsCount,
    required this.tailsCount,
    required this.headsColor,
    required this.tailsColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final total = headsCount + tailsCount;
    if (total == 0) return;

    final headsRatio = headsCount / total;
    final tailsRatio = tailsCount / total;

    final headsWidth = size.width * headsRatio;
    final tailsWidth = size.width * tailsRatio;

    final headsPaint = Paint()..color = headsColor;
    final tailsPaint = Paint()..color = tailsColor;

    // Draw heads bar
    canvas.drawRect(
      Rect.fromLTWH(0, 0, headsWidth, size.height),
      headsPaint,
    );

    // Draw tails bar
    canvas.drawRect(
      Rect.fromLTWH(headsWidth, 0, tailsWidth, size.height),
      tailsPaint,
    );
  }

  @override
  bool shouldRepaint(covariant CoinFlipBarPainter oldDelegate) {
    return oldDelegate.headsCount != headsCount ||
           oldDelegate.tailsCount != tailsCount;
  }
}
