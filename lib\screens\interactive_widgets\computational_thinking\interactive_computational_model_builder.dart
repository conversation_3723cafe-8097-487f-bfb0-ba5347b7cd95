import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_markdown/flutter_markdown.dart';

class InteractiveComputationalModelBuilder extends ConsumerStatefulWidget {
  const InteractiveComputationalModelBuilder({super.key});

  @override
  ConsumerState<InteractiveComputationalModelBuilder> createState() => _InteractiveComputationalModelBuilderState();
}

class _InteractiveComputationalModelBuilderState extends ConsumerState<InteractiveComputationalModelBuilder> {
  String _modelName = '';
  String _inputs = '';
  String _outputs = '';
  String _rules = '';
  String _assumptions = '';
  String _modelDescription = '';

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Interactive Computational Model Builder',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 20),
          const MarkdownBody(
            data: '''
            This tool helps you design computational models for various phenomena.
            Define the model's name, its inputs, expected outputs, the rules governing its behavior,
            and any underlying assumptions.
            ''',
          ),
          const SizedBox(height: 20),
          _buildSectionTitle('1. Model Name'),
          TextField(
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
              hintText: 'e.g., "Traffic Flow Simulation Model"',
            ),
            onChanged: (value) {
              setState(() {
                _modelName = value;
              });
            },
          ),
          const SizedBox(height: 20),
          _buildSectionTitle('2. Inputs (What data does the model take?)'),
          TextField(
            maxLines: 3,
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
              hintText: 'e.g., "Number of cars, Road capacity, Traffic light timings"',
            ),
            onChanged: (value) {
              setState(() {
                _inputs = value;
              });
            },
          ),
          const SizedBox(height: 20),
          _buildSectionTitle('3. Outputs (What results does the model produce?)'),
          TextField(
            maxLines: 3,
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
              hintText: 'e.g., "Average travel time, Congestion points, Optimal traffic light timings"',
            ),
            onChanged: (value) {
              setState(() {
                _outputs = value;
              });
            },
          ),
          const SizedBox(height: 20),
          _buildSectionTitle('4. Rules/Logic (How does the model process inputs to produce outputs?)'),
          TextField(
            maxLines: 7,
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
              hintText: 'Describe the algorithms, equations, or logical rules the model follows.',
            ),
            onChanged: (value) {
              setState(() {
                _rules = value;
              });
            },
          ),
          const SizedBox(height: 20),
          _buildSectionTitle('5. Assumptions (What simplifications or conditions does the model rely on?)'),
          TextField(
            maxLines: 5,
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
              hintText: 'e.g., "Cars follow traffic rules, No accidents, Constant vehicle speed"',
            ),
            onChanged: (value) {
              setState(() {
                _assumptions = value;
              });
            },
          ),
          const SizedBox(height: 30),
          _buildModelSummaryCard(context),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Text(
        title,
        style: const TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
      ),
    );
  }

  Widget _buildModelSummaryCard(BuildContext context) {
    _modelDescription = '''
**Model Name:** ${_modelName.isNotEmpty ? _modelName : 'N/A'}
**Inputs:** ${_inputs.isNotEmpty ? _inputs : 'N/A'}
**Outputs:** ${_outputs.isNotEmpty ? _outputs : 'N/A'}
**Rules/Logic:** ${_rules.isNotEmpty ? _rules : 'N/A'}
**Assumptions:** ${_assumptions.isNotEmpty ? _assumptions : 'N/A'}
''';

    return Card(
      elevation: 4,
      margin: const EdgeInsets.symmetric(vertical: 20),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Your Computational Model Summary:',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold, color: Colors.green),
            ),
            const Divider(),
            MarkdownBody(data: _modelDescription),
          ],
        ),
      ),
    );
  }
}
