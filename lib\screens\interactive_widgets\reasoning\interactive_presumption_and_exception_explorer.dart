import 'package:flutter/material.dart';
import 'package:resonance/widgets/interactive_widget.dart';

class InteractivePresumptionAndExceptionExplorer extends InteractiveWidget {
  const InteractivePresumptionAndExceptionExplorer({super.key});

  @override
  InteractiveWidgetMetaData get metaData => InteractiveWidgetMetaData(
        title: 'Interactive Presumption and Exception Explorer',
        category: 'Reasoning',
        course: 'Reasoning: Frontiers',
        module: 'Informal Logic and Argumentation Theory',
        slug: 'interactive-presumption-and-exception-explorer',
        description:
            'An interactive tool to explore the concepts of presumption and exception in argumentation.',
        difficulty: InteractiveWidgetDifficulty.medium,
        tags: const ['logic', 'argumentation', 'presumption', 'exception'],
      );

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(metaData.title),
        backgroundColor: Colors.deepPurple,
      ),
      body: const Center(
        child: Padding(
          padding: EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'Welcome to the Interactive Presumption and Exception Explorer!',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 20),
              Text(
                'This widget will help you understand and apply the concepts of presumption and exception in informal logic.',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 16),
              ),
              SizedBox(height: 40),
              // Placeholder for interactive content
              CircularProgressIndicator(),
              SizedBox(height: 20),
              Text(
                'Content coming soon!',
                style: TextStyle(fontSize: 18, fontStyle: FontStyle.italic),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
