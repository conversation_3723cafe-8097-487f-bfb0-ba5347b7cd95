import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:resonance_app/services/interactive_widget_service.dart';
import 'package:resonance_app/widgets/animated_button.dart';
import 'dart:math';

class InteractiveBalancedTreeExplorer extends StatefulWidget {
  final String widgetId;

  const InteractiveBalancedTreeExplorer({Key? key, required this.widgetId}) : super(key: key);

  @override
  _InteractiveBalancedTreeExplorerState createState() => _InteractiveBalancedTreeExplorerState();
}

class _InteractiveBalancedTreeExplorerState extends State<InteractiveBalancedTreeExplorer> {
  AVLTreeNode? _root;
  TextEditingController _insertController = TextEditingController();
  TextEditingController _deleteController = TextEditingController();
  String _message = '';
  double _animationSpeed = 500; // milliseconds

  @override
  void initState() {
    super.initState();
    _loadState();
    _buildInitialTree();
  }

  void _loadState() async {
    final service = Provider.of<InteractiveWidgetService>(context, listen: false);
    final savedState = await service.getWidgetState(widget.widgetId);
    if (savedState != null) {
      setState(() {
        _root = _deserializeTree(savedState['tree']);
        _message = savedState['message'] ?? '';
        _animationSpeed = savedState['animationSpeed'] ?? 500;
      });
    }
  }

  void _saveState() {
    final service = Provider.of<InteractiveWidgetService>(context, listen: false);
    service.saveWidgetState(widget.widgetId, {
      'tree': _serializeTree(_root),
      'message': _message,
      'animationSpeed': _animationSpeed,
    });
  }

  Map<String, dynamic>? _serializeTree(AVLTreeNode? node) {
    if (node == null) return null;
    return {
      'value': node.value,
      'height': node.height,
      'left': _serializeTree(node.left),
      'right': _serializeTree(node.right),
    };
  }

  AVLTreeNode? _deserializeTree(Map<String, dynamic>? data) {
    if (data == null) return null;
    final node = AVLTreeNode(data['value']);
    node.height = data['height'];
    node.left = _deserializeTree(data['left']);
    node.right = _deserializeTree(data['right']);
    return node;
  }

  void _buildInitialTree() {
    setState(() {
      _root = null;
      _insertValue(10);
      _insertValue(20);
      _insertValue(30);
      _insertValue(40);
      _insertValue(50);
      _insertValue(25);
      _message = 'Initial AVL tree built.';
    });
    _saveState();
  }

  void _insertValue(int value) {
    _root = _insert(_root, value);
  }

  AVLTreeNode? _insert(AVLTreeNode? node, int value) {
    if (node == null) return AVLTreeNode(value);

    if (value < node.value) {
      node.left = _insert(node.left, value);
    } else if (value > node.value) {
      node.right = _insert(node.right, value);
    } else {
      return node; // Duplicate values not allowed
    }

    node.height = 1 + max(_height(node.left), _height(node.right));

    int balance = _getBalance(node);

    // Left Left Case
    if (balance > 1 && value < node.left!.value) {
      return _rightRotate(node);
    }

    // Right Right Case
    if (balance < -1 && value > node.right!.value) {
      return _leftRotate(node);
    }

    // Left Right Case
    if (balance > 1 && value > node.left!.value) {
      node.left = _leftRotate(node.left!);
      return _rightRotate(node);
    }

    // Right Left Case
    if (balance < -1 && value < node.right!.value) {
      node.right = _rightRotate(node.right!);
      return _leftRotate(node);
    }

    return node;
  }

  void _handleInsert() {
    final value = int.tryParse(_insertController.text);
    if (value != null) {
      setState(() {
        _insertValue(value);
        _message = 'Inserted $value into the AVL tree.';
      });
      _insertController.clear();
      _saveState();
    } else {
      setState(() {
        _message = 'Please enter a valid number to insert.';
      });
    }
  }

  void _handleDelete() {
    final value = int.tryParse(_deleteController.text);
    if (value != null) {
      setState(() {
        _root = _delete(_root, value);
        _message = 'Deleted $value from the AVL tree.';
      });
      _deleteController.clear();
      _saveState();
    } else {
      setState(() {
        _message = 'Please enter a valid number to delete.';
      });
    }
  }

  AVLTreeNode? _delete(AVLTreeNode? root, int value) {
    if (root == null) return root;

    if (value < root.value) {
      root.left = _delete(root.left, value);
    } else if (value > root.value) {
      root.right = _delete(root.right, value);
    } else {
      if (root.left == null || root.right == null) {
        root = (root.left == null) ? root.right : root.left;
      } else {
        AVLTreeNode temp = _minValueNode(root.right!);
        root.value = temp.value;
        root.right = _delete(root.right, temp.value);
      }
    }

    if (root == null) return root;

    root.height = 1 + max(_height(root.left), _height(root.right));

    int balance = _getBalance(root);

    // Left Left Case
    if (balance > 1 && _getBalance(root.left) >= 0) {
      return _rightRotate(root);
    }

    // Left Right Case
    if (balance > 1 && _getBalance(root.left) < 0) {
      root.left = _leftRotate(root.left!);
      return _rightRotate(root);
    }

    // Right Right Case
    if (balance < -1 && _getBalance(root.right) <= 0) {
      return _leftRotate(root);
    }

    // Right Left Case
    if (balance < -1 && _getBalance(root.right) > 0) {
      root.right = _rightRotate(root.right!);
      return _leftRotate(root);
    }

    return root;
  }

  AVLTreeNode _minValueNode(AVLTreeNode node) {
    AVLTreeNode current = node;
    while (current.left != null) {
      current = current.left!;
    }
    return current;
  }

  int _height(AVLTreeNode? node) {
    return node?.height ?? 0;
  }

  int _getBalance(AVLTreeNode? node) {
    return node == null ? 0 : _height(node.left) - _height(node.right);
  }

  AVLTreeNode _rightRotate(AVLTreeNode y) {
    AVLTreeNode x = y.left!;
    AVLTreeNode T2 = x.right!;

    x.right = y;
    y.left = T2;

    y.height = 1 + max(_height(y.left), _height(y.right));
    x.height = 1 + max(_height(x.left), _height(x.right));

    return x;
  }

  AVLTreeNode _leftRotate(AVLTreeNode x) {
    AVLTreeNode y = x.right!;
    AVLTreeNode T2 = y.left!;

    y.left = x;
    x.right = T2;

    x.height = 1 + max(_height(x.left), _height(x.right));
    y.height = 1 + max(_height(y.left), _height(y.right));

    return y;
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Interactive Balanced Tree Explorer (AVL Tree)',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _insertController,
                  keyboardType: TextInputType.number,
                  decoration: const InputDecoration(
                    labelText: 'Insert Value',
                    border: OutlineInputBorder(),
                  ),
                ),
              ),
              const SizedBox(width: 10),
              AnimatedButton(
                onTap: _handleInsert,
                text: 'Insert',
                color: Colors.blue,
              ),
            ],
          ),
          const SizedBox(height: 10),
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _deleteController,
                  keyboardType: TextInputType.number,
                  decoration: const InputDecoration(
                    labelText: 'Delete Value',
                    border: OutlineInputBorder(),
                  ),
                ),
              ),
              const SizedBox(width: 10),
              AnimatedButton(
                onTap: _handleDelete,
                text: 'Delete',
                color: Colors.red,
              ),
            ],
          ),
          const SizedBox(height: 10),
          AnimatedButton(
            onTap: _buildInitialTree,
            text: 'Reset Tree',
            color: Colors.orange,
          ),
          const SizedBox(height: 20),
          Text(
            'Tree Visualization (AVL):',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 10),
          Expanded(
            child: SingleChildScrollView(
              child: _root == null
                  ? const Center(child: Text('Tree is empty.'))
                  : AVLTreeView(root: _root!),
            ),
          ),
          const SizedBox(height: 20),
          Text(
            'Message: $_message',
            style: Theme.of(context).textTheme.bodyLarge,
          ),
        ],
      ),
    );
  }
}

class AVLTreeNode {
  int value;
  AVLTreeNode? left;
  AVLTreeNode? right;
  int height;

  AVLTreeNode(this.value) : height = 1;
}

class AVLTreeView extends StatelessWidget {
  final AVLTreeNode root;

  const AVLTreeView({Key? key, required this.root}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        return CustomPaint(
          painter: AVLTreePainter(root),
          size: Size(constraints.maxWidth, _calculateTreeHeight(root) * 50.0 + 50), // Adjust height dynamically
        );
      },
    );
  }

  double _calculateTreeHeight(AVLTreeNode? node) {
    if (node == null) return 0;
    return 1 + max(_calculateTreeHeight(node.left), _calculateTreeHeight(node.right));
  }
}

class AVLTreePainter extends CustomPainter {
  final AVLTreeNode root;
  final double nodeRadius = 15;
  final double horizontalSpacing = 40;
  final double verticalSpacing = 50;

  AVLTreePainter(this.root);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.black
      ..strokeWidth = 2;

    _drawNode(canvas, root, size.width / 2, nodeRadius + 10, size.width / 4);
  }

  void _drawNode(Canvas canvas, AVLTreeNode node, double x, double y, double horizontalOffset) {
    // Draw node circle
    canvas.drawCircle(Offset(x, y), nodeRadius, Paint()..color = Colors.blue);
    // Draw node text
    TextPainter textPainter = TextPainter(
      text: TextSpan(
        text: node.value.toString(),
        style: const TextStyle(color: Colors.white, fontSize: 12, fontWeight: FontWeight.bold),
      ),
      textDirection: TextDirection.ltr,
    );
    textPainter.layout();
    textPainter.paint(canvas, Offset(x - textPainter.width / 2, y - textPainter.height / 2));

    // Draw lines to children
    if (node.left != null) {
      canvas.drawLine(Offset(x, y + nodeRadius), Offset(x - horizontalOffset, y + verticalSpacing - nodeRadius), Paint()..color = Colors.black..strokeWidth = 2);
      _drawNode(canvas, node.left!, x - horizontalOffset, y + verticalSpacing, horizontalOffset / 2);
    }
    if (node.right != null) {
      canvas.drawLine(Offset(x, y + nodeRadius), Offset(x + horizontalOffset, y + verticalSpacing - nodeRadius), Paint()..color = Colors.black..strokeWidth = 2);
      _drawNode(canvas, node.right!, x + horizontalOffset, y + verticalSpacing, horizontalOffset / 2);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}
