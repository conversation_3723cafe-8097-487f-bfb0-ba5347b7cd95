import 'package:flutter/material.dart';
import 'package:resonance/widgets/interactive_widget_scaffold.dart';

class InteractiveRelativisticEffectsCalculator extends StatefulWidget {
  const InteractiveRelativisticEffectsCalculator({super.key});

  @override
  State<InteractiveRelativisticEffectsCalculator> createState() => _InteractiveRelativisticEffectsCalculatorState();
}

class _InteractiveRelativisticEffectsCalculatorState extends State<InteractiveRelativisticEffectsCalculator> {
  double _velocity = 0.0; // as a fraction of c
  double _timeDilationFactor = 1.0;
  double _lengthContractionFactor = 1.0;
  double _massIncreaseFactor = 1.0;

  void _calculateRelativisticEffects() {
    if (_velocity >= 1.0) {
      _timeDilationFactor = double.infinity;
      _lengthContractionFactor = 0.0;
      _massIncreaseFactor = double.infinity;
    } else {
      _timeDilationFactor = 1 / (sqrt(1 - (_velocity * _velocity)));
      _lengthContractionFactor = sqrt(1 - (_velocity * _velocity));
      _massIncreaseFactor = 1 / (sqrt(1 - (_velocity * _velocity)));
    }
  }

  @override
  Widget build(BuildContext context) {
    return InteractiveWidgetScaffold(
      title: 'Relativistic Effects Calculator',
      description: 'Explore time dilation, length contraction, and mass increase at relativistic speeds.',
      interactiveContent: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'Velocity (as fraction of c): ${_velocity.toStringAsFixed(2)}',
              style: const TextStyle(fontSize: 18),
            ),
            Slider(
              value: _velocity,
              min: 0.0,
              max: 0.99,
              divisions: 99,
              label: _velocity.toStringAsFixed(2),
              onChanged: (value) {
                setState(() {
                  _velocity = value;
                  _calculateRelativisticEffects();
                });
              },
            ),
            const SizedBox(height: 20),
            Text(
              'Time Dilation Factor (γ): ${_timeDilationFactor.toStringAsFixed(3)}',
              style: const TextStyle(fontSize: 18),
            ),
            Text(
              'Length Contraction Factor (1/γ): ${_lengthContractionFactor.toStringAsFixed(3)}',
              style: const TextStyle(fontSize: 18),
            ),
            Text(
              'Mass Increase Factor (γ): ${_massIncreaseFactor.toStringAsFixed(3)}',
              style: const TextStyle(fontSize: 18),
            ),
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: Text(
                'Adjust the slider to change the velocity (as a fraction of the speed of light, c). Observe how time dilation, length contraction, and mass increase are affected as you approach c.',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 16),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

import 'dart:math';
