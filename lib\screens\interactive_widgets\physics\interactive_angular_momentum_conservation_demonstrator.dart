import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class InteractiveAngularMomentumConservationDemonstrator extends StatefulWidget {
  const InteractiveAngularMomentumConservationDemonstrator({super.key});

  @override
  State<InteractiveAngularMomentumConservationDemonstrator> createState() => _InteractiveAngularMomentumConservationDemonstratorState();
}

class _InteractiveAngularMomentumConservationDemonstratorState extends State<InteractiveAngularMomentumConservationDemonstrator> {
  final TextEditingController _initialMomentOfInertiaController = TextEditingController();
  final TextEditingController _initialAngularVelocityController = TextEditingController();
  final TextEditingController _finalMomentOfInertiaController = TextEditingController();
  String _result = '';

  @override
  void dispose() {
    _initialMomentOfInertiaController.dispose();
    _initialAngularVelocityController.dispose();
    _finalMomentOfInertiaController.dispose();
    super.dispose();
  }

  void _calculateFinalAngularVelocity() {
    final double? i1 = double.tryParse(_initialMomentOfInertiaController.text);
    final double? w1 = double.tryParse(_initialAngularVelocityController.text);
    final double? i2 = double.tryParse(_finalMomentOfInertiaController.text);

    if (i1 == null || w1 == null || i2 == null || i1 <= 0 || i2 <= 0) {
      setState(() {
        _result = 'Please enter valid positive numbers for all inputs.';
      });
      return;
    }

    // Conservation of Angular Momentum: L1 = L2 => I1 * w1 = I2 * w2
    // So, w2 = (I1 * w1) / I2
    final double w2 = (i1 * w1) / i2;

    setState(() {
      _result = 'Final Angular Velocity (ω₂): ${w2.toStringAsFixed(4)} rad/s';
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Angular Momentum Conservation'),
        backgroundColor: Colors.deepPurple,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'Demonstrate the conservation of angular momentum (I₁ω₁ = I₂ω₂).',
              style: TextStyle(fontSize: 16.0, fontStyle: FontStyle.italic),
            ),
            const SizedBox(height: 20),
            TextField(
              controller: _initialMomentOfInertiaController,
              keyboardType: const TextInputType.numberWithOptions(decimal: true),
              inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*'))],
              decoration: const InputDecoration(
                labelText: 'Initial Moment of Inertia (I₁) in kg·m²',
                border: OutlineInputBorder(),
                hintText: 'Enter I₁',
              ),
            ),
            const SizedBox(height: 15),
            TextField(
              controller: _initialAngularVelocityController,
              keyboardType: const TextInputType.numberWithOptions(decimal: true),
              inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*'))],
              decoration: const InputDecoration(
                labelText: 'Initial Angular Velocity (ω₁) in rad/s',
                border: OutlineInputBorder(),
                hintText: 'Enter ω₁',
              ),
            ),
            const SizedBox(height: 15),
            TextField(
              controller: _finalMomentOfInertiaController,
              keyboardType: const TextInputType.numberWithOptions(decimal: true),
              inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*'))],
              decoration: const InputDecoration(
                labelText: 'Final Moment of Inertia (I₂) in kg·m²',
                border: OutlineInputBorder(),
                hintText: 'Enter I₂',
              ),
            ),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: _calculateFinalAngularVelocity,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.deepPurple,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 15),
                textStyle: const TextStyle(fontSize: 18),
              ),
              child: const Text('Calculate Final Angular Velocity'),
            ),
            const SizedBox(height: 20),
            Card(
              elevation: 4,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Result:',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 10),
                    Text(
                      _result,
                      style: const TextStyle(fontSize: 20, color: Colors.blueAccent),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 20),
            const Text(
              'Formula used: I₁ω₁ = I₂ω₂ (Conservation of Angular Momentum)',
              style: TextStyle(fontSize: 14, fontStyle: FontStyle.italic, color: Colors.grey),
            ),
          ],
        ),
      ),
    );
  }
}
