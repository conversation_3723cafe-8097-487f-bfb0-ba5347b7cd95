import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_markdown/flutter_markdown.dart';

class InteractiveDataDrivenDecisionMaker extends ConsumerStatefulWidget {
  const InteractiveDataDrivenDecisionMaker({super.key});

  @override
  ConsumerState<InteractiveDataDrivenDecisionMaker> createState() => _InteractiveDataDrivenDecisionMakerState();
}

class _InteractiveDataDrivenDecisionMakerState extends ConsumerState<InteractiveDataDrivenDecisionMaker> {
  String _decisionScenario = '';
  String _availableData = '';
  String _analysisMethod = '';
  String _insights = '';
  String _decision = '';

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Interactive Data-Driven Decision Maker',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 20),
          const MarkdownBody(
            data: '''
            This interactive tool guides you through making decisions based on data.
            Define a scenario, list available data, choose an analysis method,
            extract insights, and formulate a data-driven decision.
            ''',
          ),
          const SizedBox(height: 20),
          _buildSectionTitle('1. Decision Scenario'),
          TextField(
            maxLines: 3,
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
              hintText: 'Describe the decision you need to make (e.g., "Should a company launch a new product?")',
            ),
            onChanged: (value) {
              setState(() {
                _decisionScenario = value;
              });
            },
          ),
          const SizedBox(height: 20),
          _buildSectionTitle('2. Available Data'),
          TextField(
            maxLines: 5,
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
              hintText: 'What data do you have? (e.g., "Market research, Sales figures, Customer feedback")',
            ),
            onChanged: (value) {
              setState(() {
                _availableData = value;
              });
            },
          ),
          const SizedBox(height: 20),
          _buildSectionTitle('3. Analysis Method'),
          TextField(
            maxLines: 3,
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
              hintText: 'How will you analyze the data? (e.g., "Statistical analysis, Trend identification, A/B testing")',
            ),
            onChanged: (value) {
              setState(() {
                _analysisMethod = value;
              });
            },
          ),
          const SizedBox(height: 20),
          _buildSectionTitle('4. Insights from Data'),
          TextField(
            maxLines: 5,
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
              hintText: 'What patterns, correlations, or key findings emerge from your analysis?',
            ),
            onChanged: (value) {
              setState(() {
                _insights = value;
              });
            },
          ),
          const SizedBox(height: 20),
          _buildSectionTitle('5. Data-Driven Decision'),
          TextField(
            maxLines: 5,
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
              hintText: 'Based on the insights, what is your final decision?',
            ),
            onChanged: (value) {
              setState(() {
                _decision = value;
              });
            },
          ),
          const SizedBox(height: 30),
          _buildDecisionSummaryCard(context),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Text(
        title,
        style: const TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
      ),
    );
  }

  Widget _buildDecisionSummaryCard(BuildContext context) {
    return Card(
      elevation: 4,
      margin: const EdgeInsets.symmetric(vertical: 20),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Your Data-Driven Decision Process:',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold, color: Colors.orange),
            ),
            const Divider(),
            _buildSummaryItem('Scenario:', _decisionScenario),
            _buildSummaryItem('Available Data:', _availableData),
            _buildSummaryItem('Analysis Method:', _analysisMethod),
            _buildSummaryItem('Insights:', _insights),
            _buildSummaryItem('Decision:', _decision),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryItem(String title, String content) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 4),
          Text(
            content.isNotEmpty ? content : 'Not yet entered.',
            style: TextStyle(fontSize: 16, fontStyle: content.isEmpty ? FontStyle.italic : FontStyle.normal),
          ),
        ],
      ),
    );
  }
}
