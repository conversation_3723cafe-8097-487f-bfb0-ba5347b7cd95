import 'package:flutter/material.dart';
import 'package:resonance/widgets/interactive_widget_scaffold.dart';

class InteractiveClimateImpactVisualizer extends StatefulWidget {
  const InteractiveClimateImpactVisualizer({super.key});

  @override
  State<InteractiveClimateImpactVisualizer> createState() => _InteractiveClimateImpactVisualizerState();
}

class _InteractiveClimateImpactVisualizerState extends State<InteractiveClimateImpactVisualizer> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  double _moonInfluence = 0.0; // 0.0 to 1.0, representing influence of second moon

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 5),
    )..repeat(reverse: true);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  Color _getClimateColor() {
    if (_moonInfluence < 0.2) {
      return Colors.blue; // Stable climate
    } else if (_moonInfluence < 0.5) {
      return Colors.lightBlue; // Mild changes
    } else if (_moonInfluence < 0.8) {
      return Colors.orange; // Significant changes
    } else {
      return Colors.red; // Extreme changes
    }
  }

  String _getClimateDescription() {
    if (_moonInfluence < 0.2) {
      return 'Climate remains relatively stable, similar to current conditions. Minor shifts in weather patterns.';
    } else if (_moonInfluence < 0.5) {
      return 'Noticeable shifts in ocean currents and atmospheric patterns, leading to more extreme weather events and regional temperature changes.';
    } else if (_moonInfluence < 0.8) {
      return 'Major disruptions to global climate, including significant changes in sea levels, more frequent and intense storms, and widespread temperature fluctuations.';
    } else {
      return 'Catastrophic climate changes, potentially leading to uninhabitable regions, massive shifts in ecosystems, and extreme global temperatures.';
    }
  }

  @override
  Widget build(BuildContext context) {
    return InteractiveWidgetScaffold(
      title: 'Climate Impact Visualizer',
      description: 'Visualize climate changes due to two moons.',
      interactiveContent: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            AnimatedBuilder(
              animation: _controller,
              builder: (context, child) {
                return Container(
                  width: 150,
                  height: 150,
                  decoration: BoxDecoration(
                    color: _getClimateColor().withOpacity(0.5 + (_controller.value * 0.5 * _moonInfluence)),
                    shape: BoxShape.circle,
                    border: Border.all(color: _getClimateColor(), width: 5.0),
                  ),
                  child: Icon(Icons.cloud, size: 80, color: Colors.white.withOpacity(0.8)),
                );
              },
            ),
            const SizedBox(height: 20),
            Text(
              'Second Moon Influence: ${(_moonInfluence * 100).toStringAsFixed(0)}%',
              style: const TextStyle(fontSize: 18),
            ),
            Slider(
              value: _moonInfluence,
              min: 0.0,
              max: 1.0,
              divisions: 10,
              label: (_moonInfluence * 100).toStringAsFixed(0),
              onChanged: (value) {
                setState(() {
                  _moonInfluence = value;
                });
              },
            ),
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: Text(
                'Adjust the slider to simulate the hypothetical influence of a second moon on Earth\'s climate. Observe the color change and read the description to understand the potential impacts.',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 16),
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Text(
                _getClimateDescription(),
                textAlign: TextAlign.center,
                style: const TextStyle(fontSize: 16, fontStyle: FontStyle.italic),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
