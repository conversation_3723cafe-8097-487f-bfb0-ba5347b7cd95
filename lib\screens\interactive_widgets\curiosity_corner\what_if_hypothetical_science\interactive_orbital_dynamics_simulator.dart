import 'package:flutter/material.dart';
import 'package:resonance/widgets/interactive_widget_scaffold.dart';
import 'dart:math';

class InteractiveOrbitalDynamicsSimulator extends StatefulWidget {
  const InteractiveOrbitalDynamicsSimulator({super.key});

  @override
  State<InteractiveOrbitalDynamicsSimulator> createState() => _InteractiveOrbitalDynamicsSimulatorState();
}

class _InteractiveOrbitalDynamicsSimulatorState extends State<InteractiveOrbitalDynamicsSimulator> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  double _moon1OrbitRadius = 100.0;
  double _moon2OrbitRadius = 150.0;
  double _moon1Speed = 1.0;
  double _moon2Speed = 0.7;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 10),
    )..repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return InteractiveWidgetScaffold(
      title: 'Orbital Dynamics Simulator',
      description: 'Simulate orbital dynamics with two moons around Earth.',
      interactiveContent: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CustomPaint(
              size: const Size(300, 300),
              painter: _OrbitalPainter(
                animation: _controller,
                moon1Radius: _moon1OrbitRadius,
                moon2Radius: _moon2OrbitRadius,
                moon1Speed: _moon1Speed,
                moon2Speed: _moon2Speed,
              ),
            ),
            const SizedBox(height: 20),
            Text(
              'Moon 1 Radius: ${_moon1OrbitRadius.toStringAsFixed(0)} units',
              style: const TextStyle(fontSize: 16),
            ),
            Slider(
              value: _moon1OrbitRadius,
              min: 50.0,
              max: 150.0,
              divisions: 100,
              label: _moon1OrbitRadius.toStringAsFixed(0),
              onChanged: (value) {
                setState(() {
                  _moon1OrbitRadius = value;
                });
              },
            ),
            Text(
              'Moon 2 Radius: ${_moon2OrbitRadius.toStringAsFixed(0)} units',
              style: const TextStyle(fontSize: 16),
            ),
            Slider(
              value: _moon2OrbitRadius,
              min: 100.0,
              max: 200.0,
              divisions: 100,
              label: _moon2OrbitRadius.toStringAsFixed(0),
              onChanged: (value) {
                setState(() {
                  _moon2OrbitRadius = value;
                });
              },
            ),
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: Text(
                'Adjust the sliders to change the orbital radii of the two moons. Observe how their paths and relative speeds change around the Earth (center).',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 16),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _OrbitalPainter extends CustomPainter {
  final Animation<double> animation;
  final double moon1Radius;
  final double moon2Radius;
  final double moon1Speed;
  final double moon2Speed;

  _OrbitalPainter({
    required this.animation,
    required this.moon1Radius,
    required this.moon2Radius,
    required this.moon1Speed,
    required this.moon2Speed,
  }) : super(repaint: animation);

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final earthPaint = Paint()..color = Colors.green;
    final moonPaint = Paint()..color = Colors.grey;
    final orbitPaint = Paint()
      ..color = Colors.blueGrey.withOpacity(0.5)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;

    // Draw Earth
    canvas.drawCircle(center, 20.0, earthPaint);

    // Draw Moon 1 orbit
    canvas.drawCircle(center, moon1Radius, orbitPaint);

    // Draw Moon 2 orbit
    canvas.drawCircle(center, moon2Radius, orbitPaint);

    // Calculate Moon 1 position
    final angle1 = 2 * pi * animation.value * moon1Speed;
    final moon1X = center.dx + moon1Radius * cos(angle1);
    final moon1Y = center.dy + moon1Radius * sin(angle1);
    canvas.drawCircle(Offset(moon1X, moon1Y), 8.0, moonPaint);

    // Calculate Moon 2 position
    final angle2 = 2 * pi * animation.value * moon2Speed;
    final moon2X = center.dx + moon2Radius * cos(angle2);
    final moon2Y = center.dy + moon2Radius * sin(angle2);
    canvas.drawCircle(Offset(moon2X, moon2Y), 10.0, moonPaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}
