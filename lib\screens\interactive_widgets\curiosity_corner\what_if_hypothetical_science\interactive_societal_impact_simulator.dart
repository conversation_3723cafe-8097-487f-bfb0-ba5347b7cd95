import 'package:flutter/material.dart';
import 'package:resonance/widgets/interactive_widget_scaffold.dart';

class InteractiveSocietalImpactSimulator extends StatefulWidget {
  const InteractiveSocietalImpactSimulator({super.key});

  @override
  State<InteractiveSocietalImpactSimulator> createState() => _InteractiveSocietalImpactSimulatorState();
}

class _InteractiveSocietalImpactSimulatorState extends State<InteractiveSocietalImpactSimulator> {
  double _photosynthesisReliance = 0.0; // 0.0 to 1.0, representing reliance on photosynthesis
  String _societalImpact = 'Society functions as normal, relying on traditional food sources.';

  void _updateSocietalImpact() {
    setState(() {
      if (_photosynthesisReliance < 0.2) {
        _societalImpact = 'Society functions as normal, relying on traditional food sources. Minor shifts in agriculture.';
      } else if (_photosynthesisReliance < 0.5) {
        _societalImpact = 'Moderate impact: Reduced need for agriculture, leading to shifts in land use and economic structures. More leisure time for individuals.';
      } else if (_photosynthesisReliance < 0.8) {
        _societalImpact = 'Significant impact: Agriculture becomes a niche activity. Urban planning changes to maximize sunlight exposure. New social hierarchies based on photosynthetic efficiency.';
      } else {
        _societalImpact = 'Extreme impact: Food scarcity is eliminated. Global economy shifts dramatically. Human settlements prioritize sunlight access. Potential for new forms of social organization and governance.';
      }
    });
  }

  @override
  void initState() {
    super.initState();
    _updateSocietalImpact();
  }

  @override
  Widget build(BuildContext context) {
    return InteractiveWidgetScaffold(
      title: 'Societal Impact Simulator',
      description: 'Simulate societal changes if humans photosynthesized.',
      interactiveContent: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.people, size: 100, color: Colors.blueGrey),
            const SizedBox(height: 20),
            Text(
              'Photosynthesis Reliance: ${(_photosynthesisReliance * 100).toStringAsFixed(0)}%',
              style: const TextStyle(fontSize: 18),
            ),
            Slider(
              value: _photosynthesisReliance,
              min: 0.0,
              max: 1.0,
              divisions: 10,
              label: (_photosynthesisReliance * 100).toStringAsFixed(0),
              onChanged: (value) {
                _photosynthesisReliance = value;
                _updateSocietalImpact();
              },
            ),
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: Text(
                'Adjust the slider to simulate the hypothetical reliance of human society on photosynthesis. Read the description below to understand the potential impacts on agriculture, economy, and social structures.',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 16),
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Text(
                _societalImpact,
                textAlign: TextAlign.center,
                style: const TextStyle(fontSize: 16, fontStyle: FontStyle.italic),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
