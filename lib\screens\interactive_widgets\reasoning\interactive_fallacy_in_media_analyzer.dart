import 'package:flutter/material.dart';
import 'package:resonance/widgets/animated_button.dart';

class InteractiveFallacyInMediaAnalyzer extends StatefulWidget {
  const InteractiveFallacyInMediaAnalyzer({super.key});

  @override
  State<InteractiveFallacyInMediaAnalyzer> createState() => _InteractiveFallacyInMediaAnalyzerState();
}

class _InteractiveFallacyInMediaAnalyzerState extends State<InteractiveFallacyInMediaAnalyzer> {
  String _mediaContent = '';
  String _analysisResult = '';

  final List<Map<String, String>> _fallacyKeywords = [
    {'name': 'Ad Hominem', 'keywords': 'attack, personal, character, motive'},
    {'name': 'Straw Man', 'keywords': 'misrepresent, distort, exaggerate, oversimplify'},
    {'name': 'Appeal to Authority', 'keywords': 'expert says, celebrity, doctor recommends'},
    {'name': 'False Dilemma', 'keywords': 'either/or, only two choices, no middle ground'},
    {'name': '<PERSON>lipper<PERSON>lope', 'keywords': 'inevitable, chain reaction, if A then B then C'},
    {'name': 'Hasty Generalization', 'keywords': 'all, every, small sample, few instances'},
    {'name': 'Red Herring', 'keywords': 'distraction, irrelevant, change topic'},
    {'name': 'Bandwagon', 'keywords': 'everyone does it, popular, majority believes'},
    {'name': 'Appeal to Emotion', 'keywords': 'pity, fear, anger, sympathy, sad, happy'},
    {'name': 'Circular Reasoning', 'keywords': 'because, therefore, assumes, rephrasing'},
  ];

  void _analyzeMediaContent() {
    setState(() {
      if (_mediaContent.isEmpty) {
        _analysisResult = 'Please enter some media content to analyze.';
        return;
      }

      String lowerCaseContent = _mediaContent.toLowerCase();
      List<String> identifiedFallacies = [];

      for (var fallacy in _fallacyKeywords) {
        List<String> keywords = fallacy['keywords']!.split(', ').map((e) => e.trim()).toList();
        bool matches = keywords.any((keyword) => lowerCaseContent.contains(keyword));
        if (matches) {
          identifiedFallacies.add(fallacy['name']!);
        }
      }

      if (identifiedFallacies.isEmpty) {
        _analysisResult = 'No common fallacies detected based on keywords. '
            'However, a deeper analysis might reveal subtle logical flaws.';
      } else {
        _analysisResult = 'Potential Fallacies Detected:\n\n' +
            identifiedFallacies.map((f) => '- $f').join('\n') +
            '\n\nThis tool provides a preliminary scan for common fallacies. '
            'Full analysis requires careful reading and understanding of context.';
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text(
            'Fallacy in Media Analyzer',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.primary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 20),
          TextField(
            onChanged: (value) {
              setState(() {
                _mediaContent = value;
              });
            },
            maxLines: 8,
            decoration: InputDecoration(
              labelText: 'Enter text from media (e.g., news article, advertisement, speech)',
              hintText: 'e.g., "Our product is the best because all smart people use it."',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              filled: true,
              fillColor: Theme.of(context).cardColor,
            ),
          ),
          const SizedBox(height: 20),
          AnimatedButton(
            onTap: _analyzeMediaContent,
            label: 'Analyze Media',
            color: Theme.of(context).colorScheme.secondary,
            labelColor: Theme.of(context).colorScheme.onSecondary,
          ),
          const SizedBox(height: 20),
          Expanded(
            child: SingleChildScrollView(
              child: Card(
                elevation: 4,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                color: Theme.of(context).colorScheme.surfaceVariant,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Analysis Result:',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                      const SizedBox(height: 10),
                      Text(
                        _analysisResult.isEmpty ? 'Enter media content and click "Analyze Media" to see the result.' : _analysisResult,
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
