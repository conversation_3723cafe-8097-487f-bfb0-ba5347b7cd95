import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that allows users to visualize how a function approaches a limit.
class InteractiveLimitApproachVisualizerWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveLimitApproachVisualizerWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  /// Factory constructor to create the widget from JSON data
  factory InteractiveLimitApproachVisualizerWidget.fromData(Map<String, dynamic> data) {
    return InteractiveLimitApproachVisualizerWidget(
      data: data,
    );
  }

  @override
  State<InteractiveLimitApproachVisualizerWidget> createState() => _InteractiveLimitApproachVisualizerWidgetState();
}

class _InteractiveLimitApproachVisualizerWidgetState extends State<InteractiveLimitApproachVisualizerWidget> {
  late double _limitPointX;
  late double _currentX;
  late double _approachFactor; // Controls how close currentX is to limitPointX

  // Function to visualize (e.g., f(x) = x^2)
  double _function(double x) {
    // Can be made configurable via widget.data
    return x * x;
  }

  // Colors
  late Color _primaryColor;
  late Color _secondaryColor;
  late Color _backgroundColor;
  late Color _textColor;

  @override
  void initState() {
    super.initState();
    _limitPointX = widget.data['limit_point_x']?.toDouble() ?? 2.0;
    _approachFactor = widget.data['initial_approach_factor']?.toDouble() ?? 0.0; // 0.0 means far, 1.0 means very close

    _primaryColor = _parseColor(widget.data['primary_color']) ?? Colors.blue;
    _secondaryColor = _parseColor(widget.data['secondary_color']) ?? Colors.orange;
    _backgroundColor = _parseColor(widget.data['background_color']) ?? Colors.white;
    _textColor = _parseColor(widget.data['text_color']) ?? Colors.black87;

    _updateCurrentX();
  }

  Color? _parseColor(String? hexString) {
    if (hexString == null) return null;
    hexString = hexString.replaceAll('#', '');
    if (hexString.length == 6) {
      hexString = 'FF$hexString';
    }
    try {
      return Color(int.parse(hexString, radix: 16));
    } catch (e) {
      return null;
    }
  }

  void _updateCurrentX() {
    // Calculate currentX based on approachFactor
    // When approachFactor is 0, currentX is far from limitPointX (e.g., limitPointX + 5)
    // When approachFactor is 1, currentX is very close to limitPointX (e.g., limitPointX + 0.001)
    // We can use a logarithmic scale for approach to make it more intuitive
    double offset = 5.0 * math.pow(0.1, _approachFactor * 2); // From 5.0 down to 0.00005
    _currentX = _limitPointX + offset; // Approach from the right
    // Could add option to approach from left or both sides
  }

  @override
  Widget build(BuildContext context) {
    final currentY = _function(_currentX);
    final limitY = _function(_limitPointX);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _backgroundColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.data['title'] ?? 'Limit Approach Visualizer',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),
          const SizedBox(height: 8),
          if (widget.data['description'] != null)
            Text(
              widget.data['description'],
              style: TextStyle(
                fontSize: 14,
                color: _textColor.withOpacity(0.8),
              ),
            ),
          const SizedBox(height: 16),

          Text(
            'Function: f(x) = x²',
            style: TextStyle(color: _textColor, fontSize: 16, fontStyle: FontStyle.italic),
          ),
          Text(
            'Approaching x = ${_limitPointX.toStringAsFixed(1)}',
            style: TextStyle(color: _textColor, fontSize: 16, fontStyle: FontStyle.italic),
          ),
          const SizedBox(height: 16),

          Text(
            'Approach Factor: ${(_approachFactor * 100).toStringAsFixed(0)}%',
            style: TextStyle(color: _textColor),
          ),
          Slider(
            value: _approachFactor,
            min: 0.0,
            max: 1.0,
            divisions: 100,
            label: (_approachFactor * 100).toStringAsFixed(0) + '%',
            activeColor: _primaryColor,
            onChanged: (value) {
              setState(() {
                _approachFactor = value;
                _updateCurrentX();
              });
            },
          ),
          const SizedBox(height: 16),

          Text(
            'Current x: ${_currentX.toStringAsFixed(4)}',
            style: TextStyle(color: _primaryColor, fontSize: 16, fontWeight: FontWeight.bold),
          ),
          Text(
            'Current f(x): ${currentY.toStringAsFixed(4)}',
            style: TextStyle(color: _primaryColor, fontSize: 16, fontWeight: FontWeight.bold),
          ),
          Text(
            'Limit f(x) as x→${_limitPointX.toStringAsFixed(1)}: ${limitY.toStringAsFixed(4)}',
            style: TextStyle(color: _secondaryColor, fontSize: 16, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),

          Container(
            height: 250,
            child: CustomPaint(
              painter: LimitApproachPainter(
                function: _function,
                limitPointX: _limitPointX,
                currentX: _currentX,
                primaryColor: _primaryColor,
                secondaryColor: _secondaryColor,
                textColor: _textColor,
              ),
              child: Container(),
            ),
          ),

          // Widget name tag
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'InteractiveLimitApproachVisualizer',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }
}

class LimitApproachPainter extends CustomPainter {
  final Function(double) function;
  final double limitPointX;
  final double currentX;
  final Color primaryColor;
  final Color secondaryColor;
  final Color textColor;

  LimitApproachPainter({
    required this.function,
    required this.limitPointX,
    required this.currentX,
    required this.primaryColor,
    required this.secondaryColor,
    required this.textColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint();
    final textPainter = TextPainter(textDirection: TextDirection.ltr);
    final textStyle = TextStyle(color: textColor, fontSize: 10);

    // Define plot area padding
    const double padding = 30.0;
    final double plotWidth = size.width - 2 * padding;
    final double plotHeight = size.height - 2 * padding;

    // Determine x and y ranges for plotting
    final double minPlotX = math.min(limitPointX - 5, currentX - 1);
    final double maxPlotX = math.max(limitPointX + 5, currentX + 1);
    final double minPlotY = -10.0; // Fixed for now, could be dynamic
    final double maxPlotY = 30.0; // Fixed for now, could be dynamic

    // Scale factors
    final double xScale = plotWidth / (maxPlotX - minPlotX);
    final double yScale = plotHeight / (maxPlotY - minPlotY);

    // Helper to convert plot coordinates to canvas coordinates
    Offset toCanvas(double x, double y) {
      final canvasX = padding + (x - minPlotX) * xScale;
      final canvasY = size.height - padding - (y - minPlotY) * yScale;
      return Offset(canvasX, canvasY);
    }

    // Draw axes
    paint.color = Colors.grey;
    paint.strokeWidth = 1.0;
    // X-axis
    canvas.drawLine(toCanvas(minPlotX, 0), toCanvas(maxPlotX, 0), paint);
    // Y-axis
    canvas.drawLine(toCanvas(0, minPlotY), toCanvas(0, maxPlotY), paint);

    // Draw function curve
    paint.color = primaryColor;
    paint.style = PaintingStyle.stroke;
    paint.strokeWidth = 2.0;

    final path = Path();
    bool firstPoint = true;
    for (double x = minPlotX; x <= maxPlotX; x += (maxPlotX - minPlotX) / 100) {
      final y = function(x);
      final point = toCanvas(x, y);
      if (firstPoint) {
        path.moveTo(point.dx, point.dy);
        firstPoint = false;
      } else {
        path.lineTo(point.dx, point.dy);
      }
    }
    canvas.drawPath(path, paint);

    // Draw limit point
    final limitPointY = function(limitPointX);
    final limitCanvasPoint = toCanvas(limitPointX, limitPointY);
    paint.color = secondaryColor;
    paint.style = PaintingStyle.fill;
    canvas.drawCircle(limitCanvasPoint, 5, paint);

    // Draw current point
    final currentCanvasPoint = toCanvas(currentX, function(currentX));
    paint.color = primaryColor;
    paint.style = PaintingStyle.fill;
    canvas.drawCircle(currentCanvasPoint, 5, paint);

    // Draw dashed lines from current point to axes
    paint.color = Colors.grey.withOpacity(0.7);
    paint.strokeWidth = 1.0;
    paint.style = PaintingStyle.stroke;
    final dashWidth = 5.0;
    final dashSpace = 5.0;
    double startX = currentCanvasPoint.dx;
    double startY = currentCanvasPoint.dy;

    // Vertical dashed line to X-axis
    while (startY < toCanvas(currentX, 0).dy) {
      canvas.drawLine(Offset(startX, startY), Offset(startX, startY + dashWidth), paint);
      startY += dashWidth + dashSpace;
    }
    // Horizontal dashed line to Y-axis
    while (startX > toCanvas(0, function(currentX)).dx) {
      canvas.drawLine(Offset(startX, currentCanvasPoint.dy), Offset(startX - dashWidth, currentCanvasPoint.dy), paint);
      startX -= dashWidth + dashSpace;
    }

    // Draw labels for limit point and current point
    textPainter.text = TextSpan(text: '(${limitPointX.toStringAsFixed(1)}, ${limitPointY.toStringAsFixed(1)})', style: textStyle.copyWith(color: secondaryColor));
    textPainter.layout();
    textPainter.paint(canvas, Offset(limitCanvasPoint.dx + 5, limitCanvasPoint.dy - textPainter.height / 2));

    textPainter.text = TextSpan(text: '(${currentX.toStringAsFixed(2)}, ${function(currentX).toStringAsFixed(2)})', style: textStyle.copyWith(color: primaryColor));
    textPainter.layout();
    textPainter.paint(canvas, Offset(currentCanvasPoint.dx + 5, currentCanvasPoint.dy - textPainter.height / 2));
  }

  @override
  bool shouldRepaint(covariant LimitApproachPainter oldDelegate) {
    return oldDelegate.limitPointX != limitPointX ||
           oldDelegate.currentX != currentX ||
           oldDelegate.primaryColor != primaryColor ||
           oldDelegate.secondaryColor != secondaryColor ||
           oldDelegate.textColor != textColor;
  }
}
