import 'package:flutter/material.dart';

class BattleScreen extends StatelessWidget {
  const BattleScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Safe<PERSON>rea(
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Battle icon
              Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  color: const Color.fromRGBO(124, 66, 210, 0.1),
                  borderRadius: BorderRadius.circular(60),
                ),
                child: const Icon(
                  Icons.bolt,
                  size: 60,
                  color: Color.fromRGBO(124, 66, 210, 1),
                ),
              ),
              
              const SizedBox(height: 32),
              
              // Coming soon text
              const Text(
                'Battle Mode',
                style: TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  fontFamily: 'WorkSans',
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Description
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 40),
                child: Text(
                  'Challenge other learners and test your knowledge in real-time battles. Coming soon!',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey[600],
                    fontFamily: 'WorkSans',
                  ),
                ),
              ),
              
              const SizedBox(height: 40),
              
              // Notification button
              ElevatedButton(
                onPressed: () {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('You\'ll be notified when Battle Mode launches!'),
                      duration: Duration(seconds: 2),
                    ),
                  );
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color.fromRGBO(124, 66, 210, 1),
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(30),
                  ),
                ),
                child: const Text(
                  'Notify Me',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    fontFamily: 'WorkSans',
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
