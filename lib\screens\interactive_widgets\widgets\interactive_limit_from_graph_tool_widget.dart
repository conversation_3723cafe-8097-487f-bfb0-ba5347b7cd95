import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that allows users to determine limits by inspecting a graph.
class InteractiveLimitFromGraphToolWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveLimitFromGraphToolWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  /// Factory constructor to create the widget from JSON data
  factory InteractiveLimitFromGraphToolWidget.fromData(Map<String, dynamic> data) {
    return InteractiveLimitFromGraphToolWidget(
      data: data,
    );
  }

  @override
  State<InteractiveLimitFromGraphToolWidget> createState() => _InteractiveLimitFromGraphToolWidgetState();
}

class _InteractiveLimitFromGraphToolWidgetState extends State<InteractiveLimitFromGraphToolWidget> {
  late double _targetX; // The x-value where the limit is to be found
  double _currentProbeX = 0.0; // User's probe x-value
  
  // Function to visualize (e.g., f(x) = (x^2 - 4) / (x - 2) which simplifies to x + 2 for x != 2)
  double _function(double x) {
    if ((x - 2).abs() < 0.0001) { // Handle the discontinuity at x=2
      return double.nan; // Indicate a hole
    }
    return x + 2;
  }

  // Colors
  late Color _primaryColor;
  late Color _secondaryColor;
  late Color _backgroundColor;
  late Color _textColor;

  @override
  void initState() {
    super.initState();
    _targetX = widget.data['target_x']?.toDouble() ?? 2.0;
    _currentProbeX = _targetX + 1.0; // Start probe slightly away

    _primaryColor = _parseColor(widget.data['primary_color']) ?? Colors.blue;
    _secondaryColor = _parseColor(widget.data['secondary_color']) ?? Colors.orange;
    _backgroundColor = _parseColor(widget.data['background_color']) ?? Colors.white;
    _textColor = _parseColor(widget.data['text_color']) ?? Colors.black87;
  }

  Color? _parseColor(String? hexString) {
    if (hexString == null) return null;
    hexString = hexString.replaceAll('#', '');
    if (hexString.length == 6) {
      hexString = 'FF$hexString';
    }
    try {
      return Color(int.parse(hexString, radix: 16));
    } catch (e) {
      return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    final currentProbeY = _function(_currentProbeX);
    final actualLimitY = _targetX + 2; // For f(x) = x+2

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _backgroundColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.data['title'] ?? 'Limit From Graph Tool',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),
          const SizedBox(height: 8),
          if (widget.data['description'] != null)
            Text(
              widget.data['description'],
              style: TextStyle(
                fontSize: 14,
                color: _textColor.withOpacity(0.8),
              ),
            ),
          const SizedBox(height: 16),

          Text(
            'Function: f(x) = (x² - 4) / (x - 2)',
            style: TextStyle(color: _textColor, fontSize: 16, fontStyle: FontStyle.italic),
          ),
          Text(
            'Find the limit as x approaches ${_targetX.toStringAsFixed(1)}',
            style: TextStyle(color: _textColor, fontSize: 16, fontStyle: FontStyle.italic),
          ),
          const SizedBox(height: 16),

          Text(
            'Probe x: ${_currentProbeX.toStringAsFixed(2)}',
            style: TextStyle(color: _primaryColor, fontSize: 16, fontWeight: FontWeight.bold),
          ),
          Text(
            'Probe f(x): ${currentProbeY.isNaN ? 'Undefined' : currentProbeY.toStringAsFixed(2)}',
            style: TextStyle(color: _primaryColor, fontSize: 16, fontWeight: FontWeight.bold),
          ),
          Slider(
            value: _currentProbeX,
            min: _targetX - 3.0,
            max: _targetX + 3.0,
            divisions: 600, // 6 units * 100 divisions/unit
            label: _currentProbeX.toStringAsFixed(2),
            activeColor: _primaryColor,
            onChanged: (value) {
              setState(() {
                _currentProbeX = value;
              });
            },
          ),
          const SizedBox(height: 16),

          Container(
            height: 250,
            child: CustomPaint(
              painter: LimitGraphPainter(
                function: _function,
                targetX: _targetX,
                currentProbeX: _currentProbeX,
                primaryColor: _primaryColor,
                secondaryColor: _secondaryColor,
                textColor: _textColor,
              ),
              child: Container(),
            ),
          ),
          const SizedBox(height: 16),

          Center(
            child: Text(
              'The limit as x→${_targetX.toStringAsFixed(1)} is: ${actualLimitY.toStringAsFixed(1)}',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: _secondaryColor,
              ),
            ),
          ),

          // Widget name tag
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'InteractiveLimitFromGraphTool',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }
}

class LimitGraphPainter extends CustomPainter {
  final Function(double) function;
  final double targetX;
  final double currentProbeX;
  final Color primaryColor;
  final Color secondaryColor;
  final Color textColor;

  LimitGraphPainter({
    required this.function,
    required this.targetX,
    required this.currentProbeX,
    required this.primaryColor,
    required this.secondaryColor,
    required this.textColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint();
    final textPainter = TextPainter(textDirection: TextDirection.ltr);
    final textStyle = TextStyle(color: textColor, fontSize: 10);

    // Define plot area padding
    const double padding = 30.0;
    final double plotWidth = size.width - 2 * padding;
    final double plotHeight = size.height - 2 * padding;

    // Determine x and y ranges for plotting
    final double minPlotX = targetX - 3.5;
    final double maxPlotX = targetX + 3.5;
    final double minPlotY = targetX + 2 - 3.5; // For f(x) = x+2
    final double maxPlotY = targetX + 2 + 3.5;

    // Scale factors
    final double xScale = plotWidth / (maxPlotX - minPlotX);
    final double yScale = plotHeight / (maxPlotY - minPlotY);

    // Helper to convert plot coordinates to canvas coordinates
    Offset toCanvas(double x, double y) {
      final canvasX = padding + (x - minPlotX) * xScale;
      final canvasY = size.height - padding - (y - minPlotY) * yScale;
      return Offset(canvasX, canvasY);
    }

    // Draw axes
    paint.color = Colors.grey;
    paint.strokeWidth = 1.0;
    // X-axis
    canvas.drawLine(toCanvas(minPlotX, 0), toCanvas(maxPlotX, 0), paint);
    // Y-axis
    canvas.drawLine(toCanvas(0, minPlotY), toCanvas(0, maxPlotY), paint);

    // Draw function curve
    paint.color = primaryColor;
    paint.style = PaintingStyle.stroke;
    paint.strokeWidth = 2.0;

    final path = Path();
    bool firstPoint = true;
    for (double x = minPlotX; x <= maxPlotX; x += (maxPlotX - minPlotX) / 200) {
      final y = function(x);
      if (!y.isNaN) { // Only draw if not a hole
        final point = toCanvas(x, y);
        if (firstPoint) {
          path.moveTo(point.dx, point.dy);
          firstPoint = false;
        } else {
          path.lineTo(point.dx, point.dy);
        }
      } else {
        firstPoint = true; // Reset for next segment after a hole
      }
    }
    canvas.drawPath(path, paint);

    // Draw hole at targetX
    final holeCenter = toCanvas(targetX, function(targetX + 0.0001)); // Get y-value near hole
    paint.color = Colors.white; // Color of the hole
    paint.style = PaintingStyle.fill;
    canvas.drawCircle(holeCenter, 4, paint);
    paint.color = primaryColor; // Border of the hole
    paint.style = PaintingStyle.stroke;
    paint.strokeWidth = 1.5;
    canvas.drawCircle(holeCenter, 4, paint);

    // Draw target limit point (filled circle at the actual limit value)
    final actualLimitY = targetX + 2; // For f(x) = x+2
    final limitCanvasPoint = toCanvas(targetX, actualLimitY);
    paint.color = secondaryColor;
    paint.style = PaintingStyle.fill;
    canvas.drawCircle(limitCanvasPoint, 5, paint);

    // Draw current probe point
    final currentProbeY = function(currentProbeX);
    if (!currentProbeY.isNaN) {
      final currentProbeCanvasPoint = toCanvas(currentProbeX, currentProbeY);
      paint.color = primaryColor;
      paint.style = PaintingStyle.fill;
      canvas.drawCircle(currentProbeCanvasPoint, 5, paint);

      // Draw dashed lines from current probe point to axes
      paint.color = Colors.grey.withOpacity(0.7);
      paint.strokeWidth = 1.0;
      paint.style = PaintingStyle.stroke;
      final dashWidth = 5.0;
      final dashSpace = 5.0;
      double startX = currentProbeCanvasPoint.dx;
      double startY = currentProbeCanvasPoint.dy;

      // Vertical dashed line to X-axis
      while (startY < toCanvas(currentProbeX, 0).dy) {
        canvas.drawLine(Offset(startX, startY), Offset(startX, startY + dashWidth), paint);
        startY += dashWidth + dashSpace;
      }
      // Horizontal dashed line to Y-axis
      while (startX > toCanvas(0, currentProbeY).dx) {
        canvas.drawLine(Offset(startX, currentProbeCanvasPoint.dy), Offset(startX - dashWidth, currentProbeCanvasPoint.dy), paint);
        startX -= dashWidth + dashSpace;
      }
    }

    // Draw labels for targetX and actualLimitY
    textPainter.text = TextSpan(text: 'x = ${targetX.toStringAsFixed(1)}', style: textStyle.copyWith(color: secondaryColor));
    textPainter.layout();
    textPainter.paint(canvas, Offset(toCanvas(targetX, 0).dx - textPainter.width / 2, size.height - padding + 5));

    textPainter.text = TextSpan(text: 'L = ${actualLimitY.toStringAsFixed(1)}', style: textStyle.copyWith(color: secondaryColor));
    textPainter.layout();
    textPainter.paint(canvas, Offset(padding + 5, toCanvas(0, actualLimitY).dy - textPainter.height / 2));
  }

  @override
  bool shouldRepaint(covariant LimitGraphPainter oldDelegate) {
    return oldDelegate.targetX != targetX ||
           oldDelegate.currentProbeX != currentProbeX ||
           oldDelegate.primaryColor != primaryColor ||
           oldDelegate.secondaryColor != secondaryColor ||
           oldDelegate.textColor != textColor;
  }
}
