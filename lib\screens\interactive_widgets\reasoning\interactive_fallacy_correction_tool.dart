import 'package:flutter/material.dart';
import 'package:resonance/widgets/animated_button.dart';

class InteractiveFallacyCorrectionTool extends StatefulWidget {
  const InteractiveFallacyCorrectionTool({super.key});

  @override
  State<InteractiveFallacyCorrectionTool> createState() => _InteractiveFallacyCorrectionToolState();
}

class _InteractiveFallacyCorrectionToolState extends State<InteractiveFallacyCorrectionTool> {
  final TextEditingController _fallaciousArgumentController = TextEditingController();
  final TextEditingController _correctedArgumentController = TextEditingController();
  String _feedback = '';

  final List<Map<String, String>> _fallacyExamples = [
    {
      'fallacious': 'You can\'t trust anything she says about economics; she\'s just a housewife.',
      'corrected': 'Her argument about economics is flawed because it lacks supporting data.',
      'fallacyType': 'Ad Hominem',
      'explanation': 'The original argument attacks the person (housewife) instead of the argument. The corrected version focuses on the argument\'s content.',
    },
    {
      'fallacious': 'My opponent wants to cut military spending, which means he wants to leave our country defenseless and vulnerable to attack!',
      'corrected': 'My opponent proposes cutting military spending. This could lead to reduced defense capabilities, which is a concern for national security.',
      'fallacyType': 'Straw Man',
      'explanation': 'The original argument distorts the opponent\'s position (cutting spending) into an extreme, easily attackable version (leaving the country defenseless). The corrected version accurately represents the position and its potential implications.',
    },
    {
      'fallacious': 'If we allow children to choose their bedtime, they will never sleep, and then they will fail school and become unproductive members of society.',
      'corrected': 'Allowing children to choose their bedtime might lead to inconsistent sleep schedules, which could negatively impact their academic performance and overall well-being.',
      'fallacyType': 'Slippery Slope',
      'explanation': 'The original argument presents an exaggerated chain of inevitable negative consequences. The corrected version identifies a plausible, but not inevitable, negative outcome.',
    },
    {
      'fallacious': 'You either love your country or you\'re a traitor.',
      'corrected': 'One can disagree with certain government policies while still loving their country. Patriotism can be expressed in many ways.',
      'fallacyType': 'False Dilemma',
      'explanation': 'The original argument presents only two extreme options. The corrected version acknowledges a broader spectrum of possibilities.',
    },
  ];

  int _currentExampleIndex = 0;

  @override
  void initState() {
    super.initState();
    _loadCurrentExample();
  }

  void _loadCurrentExample() {
    _fallaciousArgumentController.text = _fallacyExamples[_currentExampleIndex]['fallacious']!;
    _correctedArgumentController.text = ''; // Clear previous correction
    _feedback = '';
  }

  void _checkCorrection() {
    setState(() {
      final String expectedCorrection = _fallacyExamples[_currentExampleIndex]['corrected']!;
      final String userCorrection = _correctedArgumentController.text.trim();

      if (userCorrection.isEmpty) {
        _feedback = 'Please provide your corrected argument.';
        return;
      }

      // Simple check: for a real tool, this would involve more sophisticated NLP or manual review
      if (userCorrection.contains(_fallacyExamples[_currentExampleIndex]['fallacyType']!)) {
        _feedback = 'Your correction mentions the fallacy type, but focus on rephrasing the argument itself.';
      } else if (userCorrection.length > expectedCorrection.length * 0.7 && userCorrection.length < expectedCorrection.length * 1.3 && userCorrection.contains('focuses on') || userCorrection.contains('addresses')) {
        _feedback = 'Good attempt! Your correction is on the right track. '
            'Remember to remove the personal attack and focus on the argument\'s content. '
            'The original fallacy was: ${_fallacyExamples[_currentExampleIndex]['fallacyType']}.';
      } else if (userCorrection.toLowerCase().contains(expectedCorrection.toLowerCase().substring(0, expectedCorrection.length ~/ 2))) {
        _feedback = 'Close! Your correction is similar to the expected one. '
            'The original fallacy was: ${_fallacyExamples[_currentExampleIndex]['fallacyType']}.';
      } else {
        _feedback = 'Not quite. The original fallacy was: ${_fallacyExamples[_currentExampleIndex]['fallacyType']}. '
            'Explanation: ${_fallacyExamples[_currentExampleIndex]['explanation']}\n\n'
            'Expected Correction: "${expectedCorrection}"';
      }
    });
  }

  void _nextExample() {
    setState(() {
      if (_currentExampleIndex < _fallacyExamples.length - 1) {
        _currentExampleIndex++;
        _loadCurrentExample();
      } else {
        _feedback = 'All examples completed! You\'ve practiced correcting various fallacies.';
      }
    });
  }

  void _resetTool() {
    setState(() {
      _currentExampleIndex = 0;
      _loadCurrentExample();
    });
  }

  @override
  void dispose() {
    _fallaciousArgumentController.dispose();
    _correctedArgumentController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text(
            'Fallacy Correction Tool',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.primary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 20),
          Card(
            elevation: 4,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            color: Theme.of(context).colorScheme.surfaceVariant,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Fallacious Argument:',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                  const SizedBox(height: 10),
                  TextField(
                    controller: _fallaciousArgumentController,
                    readOnly: true,
                    maxLines: 3,
                    decoration: InputDecoration(
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      filled: true,
                      fillColor: Theme.of(context).cardColor,
                    ),
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                  const SizedBox(height: 20),
                  Text(
                    'Your Corrected Argument:',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                  const SizedBox(height: 10),
                  TextField(
                    controller: _correctedArgumentController,
                    maxLines: 5,
                    decoration: InputDecoration(
                      labelText: 'Rewrite the argument to remove the fallacy',
                      hintText: 'e.g., "Her argument about economics is flawed because it lacks supporting data."',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      filled: true,
                      fillColor: Theme.of(context).cardColor,
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 20),
          AnimatedButton(
            onTap: _checkCorrection,
            label: 'Check Correction',
            color: Theme.of(context).colorScheme.secondary,
            labelColor: Theme.of(context).colorScheme.onSecondary,
          ),
          if (_feedback.isNotEmpty) ...[
            const SizedBox(height: 10),
            Text(
              _feedback,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: _feedback.startsWith('Correct') ? Colors.green : Colors.red,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
          ],
          const SizedBox(height: 20),
          AnimatedButton(
            onTap: _nextExample,
            label: _currentExampleIndex < _fallacyExamples.length - 1 ? 'Next Example' : 'Finish Practice',
            color: Theme.of(context).colorScheme.tertiary,
            labelColor: Theme.of(context).colorScheme.onTertiary,
          ),
          if (_currentExampleIndex == _fallacyExamples.length - 1 && _feedback.contains('Completed'))
            Padding(
              padding: const EdgeInsets.only(top: 20.0),
              child: AnimatedButton(
                onTap: _resetTool,
                label: 'Restart Practice',
                color: Theme.of(context).colorScheme.primary,
                labelColor: Theme.of(context).colorScheme.onPrimary,
              ),
            ),
        ],
      ),
    );
  }
}
