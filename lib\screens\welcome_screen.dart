import 'package:flutter/material.dart';
import 'auth/signin_screen.dart';
import 'main_app_scaffold.dart';
import 'onboarding/assessment_controller.dart';
import '../services/service_provider.dart';

class WelcomescreenWidget extends StatefulWidget {
  @override
  _WelcomescreenWidgetState createState() => _WelcomescreenWidgetState();
}

class _WelcomescreenWidgetState extends State<WelcomescreenWidget> {
  @override
  Widget build(BuildContext context) {
    // Use Scaffold for basic screen structure and SafeArea handling
    return Scaffold(
      backgroundColor: Color.fromRGBO(
        16,
        17,
        20,
        1,
      ), // Background for the whole screen
      // Remove SafeArea to allow image to cover the entire screen including status bar
      body: Stack(
        fit: StackFit.expand, // Make stack fill the entire screen
        children: <Widget>[
          // Background Logo Image (Covering full screen)
          Positioned.fill(
            child: Container(
              decoration: BoxDecoration(
                image: DecorationImage(
                  image: AssetImage('assets/images/logo.png'),
                  fit: BoxFit.cover,
                ),
              ),
            ),
          ),

          // Gradient Overlay (Full screen) - to ensure text readability over the image
          Positioned.fill(
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Color.fromRGBO(17, 18, 20, 0.2), // Slight darkening at top
                    Color.fromRGBO(
                      17,
                      18,
                      20,
                      0.6,
                    ), // Medium darkening in middle
                    Color.fromRGBO(
                      17,
                      18,
                      20,
                      0.9,
                    ), // Strong darkening at bottom
                  ],
                  stops: [
                    0.0,
                    0.5,
                    0.8,
                  ], // Adjusted stops for better fade effect
                ),
              ),
            ),
          ),

          // Content Area (Positioned at bottom)
          Positioned(
            bottom:
                80, // Position from bottom instead of top for better adaptability
            left: 0,
            right: 0,
            child: Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: 24.0,
              ), // Consistent padding
              child: Column(
                mainAxisSize: MainAxisSize.min, // Column takes minimum space
                crossAxisAlignment:
                    CrossAxisAlignment.center, // Center items horizontally
                children: <Widget>[
                  // Welcome Text
                  Text(
                    'Welcome To Resonance.',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: Colors.white,
                      fontFamily:
                          'WorkSans', // Use the family name from pubspec.yaml
                      fontSize: 36,
                      fontWeight:
                          FontWeight
                              .w600, // Example: SemiBold (adjust as needed)
                      height: 1.22,
                    ),
                  ),

                  SizedBox(height: 32),

                  // Get Started Button (using InkWell for tap effect)
                  InkWell(
                    onTap: () {
                      // Get course service
                      final courseService =
                          ServiceProvider.of(context).courseService;

                      // Create a guest user and navigate directly to main app
                      // In a real app, you might want to show the assessment flow first
                      Navigator.of(context).pushReplacement(
                        MaterialPageRoute(
                          builder: (_) => const MainAppScaffold(),
                        ),
                      );
                    },
                    borderRadius: BorderRadius.circular(
                      21,
                    ), // Match Container's border radius
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(21),
                        color: Color.fromRGBO(124, 66, 210, 1),
                      ),
                      padding: EdgeInsets.symmetric(
                        horizontal: 32,
                        vertical: 16,
                      ),
                      child: Row(
                        mainAxisSize:
                            MainAxisSize.min, // Row takes minimum space
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: <Widget>[
                          Text(
                            'Get Started',
                            style: TextStyle(
                              color: Colors.white,
                              fontFamily:
                                  'WorkSans', // Use the family name from pubspec.yaml
                              fontSize: 18,
                              fontWeight:
                                  FontWeight
                                      .w500, // Example: Medium (adjust as needed)
                              height: 1,
                            ),
                          ),
                          SizedBox(width: 16),
                          // Replaced 'null' with an Icon
                          Icon(
                            Icons.arrow_forward,
                            color: Colors.white,
                            size: 20, // Adjust size as needed
                          ),
                        ],
                      ),
                    ),
                  ),

                  SizedBox(height: 40),

                  // Sign In Text (using InkWell for tap)
                  InkWell(
                    onTap: () {
                      // Navigate to Sign In screen
                      Navigator.of(
                        context,
                      ).push(MaterialPageRoute(builder: (_) => SigninWidget()));
                    },
                    child: Text(
                      'Already have account? Sign In',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        color: Colors.white.withAlpha(179),
                        fontFamily: 'WorkSans',
                        fontSize: 14,
                        fontWeight: FontWeight.w400,
                        height: 1,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          // The 'null' Positioned widgets were removed as they served no purpose.
        ],
      ),
    );
  }
}
