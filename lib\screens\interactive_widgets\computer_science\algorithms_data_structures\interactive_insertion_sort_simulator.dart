import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:resonance_app/services/interactive_widget_service.dart';
import 'package:resonance_app/widgets/animated_button.dart';
import 'dart:math';

class InteractiveInsertionSortSimulator extends StatefulWidget {
  final String widgetId;

  const InteractiveInsertionSortSimulator({Key? key, required this.widgetId}) : super(key: key);

  @override
  _InteractiveInsertionSortSimulatorState createState() => _InteractiveInsertionSortSimulatorState();
}

class _InteractiveInsertionSortSimulatorState extends State<InteractiveInsertionSortSimulator> {
  List<int> _numbers = [];
  int _currentIndex = -1;
  int _compareIndex = -1;
  bool _isSorting = false;
  String _message = '';
  int _arraySize = 10;
  double _animationSpeed = 500; // milliseconds

  @override
  void initState() {
    super.initState();
    _loadState();
    _generateRandomArray();
  }

  void _loadState() async {
    final service = Provider.of<InteractiveWidgetService>(context, listen: false);
    final savedState = await service.getWidgetState(widget.widgetId);
    if (savedState != null) {
      setState(() {
        _numbers = List<int>.from(savedState['numbers'] ?? []);
        _currentIndex = savedState['currentIndex'] ?? -1;
        _compareIndex = savedState['compareIndex'] ?? -1;
        _isSorting = savedState['isSorting'] ?? false;
        _message = savedState['message'] ?? '';
        _arraySize = savedState['arraySize'] ?? 10;
        _animationSpeed = savedState['animationSpeed'] ?? 500;
      });
    }
  }

  void _saveState() {
    final service = Provider.of<InteractiveWidgetService>(context, listen: false);
    service.saveWidgetState(widget.widgetId, {
      'numbers': _numbers,
      'currentIndex': _currentIndex,
      'compareIndex': _compareIndex,
      'isSorting': _isSorting,
      'message': _message,
      'arraySize': _arraySize,
      'animationSpeed': _animationSpeed,
    });
  }

  void _generateRandomArray() {
    setState(() {
      _numbers = List.generate(_arraySize, (index) => Random().nextInt(90) + 10); // Numbers between 10 and 99
      _currentIndex = -1;
      _compareIndex = -1;
      _isSorting = false;
      _message = 'New array generated.';
    });
    _saveState();
  }

  Future<void> _insertionSort() async {
    if (_isSorting) return;

    setState(() {
      _isSorting = true;
      _message = 'Sorting started...';
    });
    _saveState();

    int n = _numbers.length;
    for (int i = 1; i < n; i++) {
      int key = _numbers[i];
      int j = i - 1;

      setState(() {
        _currentIndex = i;
        _message = 'Picking element ${key} at index $i';
      });
      _saveState();
      await Future.delayed(Duration(milliseconds: _animationSpeed.toInt()));

      while (j >= 0 && _numbers[j] > key) {
        setState(() {
          _compareIndex = j;
          _numbers[j + 1] = _numbers[j];
          _message = 'Shifting ${_numbers[j]} to the right';
        });
        _saveState();
        await Future.delayed(Duration(milliseconds: _animationSpeed.toInt()));
        j--;
      }
      setState(() {
        _numbers[j + 1] = key;
        _message = 'Inserting ${key} at index ${j + 1}';
      });
      _saveState();
      await Future.delayed(Duration(milliseconds: _animationSpeed.toInt()));
    }

    setState(() {
      _currentIndex = -1;
      _compareIndex = -1;
      _isSorting = false;
      _message = 'Sorting completed!';
    });
    _saveState();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Interactive Insertion Sort Simulator',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                child: Slider(
                  value: _arraySize.toDouble(),
                  min: 5,
                  max: 20,
                  divisions: 15,
                  label: _arraySize.round().toString(),
                  onChanged: (double value) {
                    setState(() {
                      _arraySize = value.round();
                    });
                  },
                  onChangeEnd: (double value) {
                    _generateRandomArray();
                  },
                ),
              ),
              Text('Array Size: $_arraySize'),
            ],
          ),
          Row(
            children: [
              Expanded(
                child: Slider(
                  value: _animationSpeed,
                  min: 50,
                  max: 1000,
                  divisions: 19,
                  label: _animationSpeed.round().toString(),
                  onChanged: (double value) {
                    setState(() {
                      _animationSpeed = value;
                    });
                  },
                ),
              ),
              Text('Speed: ${_animationSpeed.round()}ms'),
            ],
          ),
          const SizedBox(height: 10),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              AnimatedButton(
                onTap: _generateRandomArray,
                text: 'Generate New Array',
                color: Colors.blue,
              ),
              AnimatedButton(
                onTap: _isSorting ? null : _insertionSort,
                text: _isSorting ? 'Sorting...' : 'Start Sort',
                color: _isSorting ? Colors.grey : Colors.green,
              ),
            ],
          ),
          const SizedBox(height: 20),
          Text(
            'Array:',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 10),
          Container(
            height: 100,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: _numbers.asMap().entries.map((entry) {
                int index = entry.key;
                int number = entry.value;
                Color color = Colors.grey;
                if (index == _currentIndex) {
                  color = Colors.blue; // Current element being inserted
                } else if (index == _compareIndex) {
                  color = Colors.red; // Element being compared/shifted
                } else if (index < _currentIndex) {
                  color = Colors.green; // Already sorted part
                }

                return Container(
                  margin: const EdgeInsets.symmetric(horizontal: 2),
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: color,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    number.toString(),
                    style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                  ),
                );
              }).toList(),
            ),
          ),
          const SizedBox(height: 20),
          Text(
            'Message: $_message',
            style: Theme.of(context).textTheme.bodyLarge,
          ),
        ],
      ),
    );
  }
}
