import 'package:flutter/material.dart';

class InteractiveProblemDecompositionTool extends StatefulWidget {
  const InteractiveProblemDecompositionTool({super.key});

  @override
  State<InteractiveProblemDecompositionTool> createState() => _InteractiveProblemDecompositionToolState();
}

class _InteractiveProblemDecompositionToolState extends State<InteractiveProblemDecompositionTool> {
  final TextEditingController _problemController = TextEditingController();
  List<String> _subproblems = [];

  void _decomposeProblem() {
    setState(() {
      String problem = _problemController.text.trim();
      if (problem.isNotEmpty) {
        // Simple decomposition logic: split by common conjunctions or phrases
        _subproblems = problem.split(RegExp(r' and | then | also |;')).map((s) => s.trim()).where((s) => s.isNotEmpty).toList();
        if (_subproblems.length == 1 && _subproblems[0] == problem) {
          // If no clear split, suggest a generic decomposition
          _subproblems = ['Identify inputs and outputs', 'Break into smaller steps', 'Consider edge cases'];
        }
      } else {
        _subproblems = [];
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Interactive Problem Decomposition Tool',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            TextField(
              controller: _problemController,
              decoration: InputDecoration(
                labelText: 'Enter a complex problem (e.g., "Plan a birthday party")',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
            const SizedBox(height: 10),
            ElevatedButton(
              onPressed: _decomposeProblem,
              child: const Text('Decompose Problem'),
            ),
            const SizedBox(height: 20),
            if (_subproblems.isNotEmpty)
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Sub-problems:',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 10),
                  ..._subproblems.asMap().entries.map((entry) {
                    int index = entry.key;
                    String subproblem = entry.value;
                    return Padding(
                      padding: const EdgeInsets.symmetric(vertical: 4.0),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text('${index + 1}. ', style: TextStyle(fontSize: 14)),
                          Expanded(
                            child: Text(subproblem, style: TextStyle(fontSize: 14)),
                          ),
                        ],
                      ),
                    );
                  }).toList(),
                ],
              )
            else
              const Text(
                'Enter a problem above to see its decomposition.',
                style: TextStyle(fontStyle: FontStyle.italic, color: Colors.grey),
              ),
          ],
        ),
      ),
    );
  }
}
