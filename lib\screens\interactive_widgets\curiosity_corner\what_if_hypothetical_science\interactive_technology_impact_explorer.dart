import 'package:flutter/material.dart';
import 'package:resonance/widgets/interactive_widget_scaffold.dart';

class InteractiveTechnologyImpactExplorer extends StatefulWidget {
  const InteractiveTechnologyImpactExplorer({super.key});

  @override
  State<InteractiveTechnologyImpactExplorer> createState() => _InteractiveTechnologyImpactExplorerState();
}

class _InteractiveTechnologyImpactExplorerState extends State<InteractiveTechnologyImpactExplorer> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  double _lightSpeedFactor = 1.0; // 1.0 means normal speed, lower means slower

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 3),
    )..repeat(reverse: true);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  String _getImpactDescription() {
    if (_lightSpeedFactor < 0.2) {
      return 'Extreme impact: Technology as we know it would be impossible. Communication would be severely limited, and advanced computing would be non-existent.';
    } else if (_lightSpeedFactor < 0.5) {
      return 'Significant impact: Basic electronics might function, but global communication would be very slow. High-speed computing and data transfer would be severely hampered.';
    } else if (_lightSpeedFactor < 0.8) {
      return 'Moderate impact: Some technologies would be slower, especially those relying on rapid data transfer over long distances. GPS and satellite communication would be noticeably affected.';
    } else {
      return 'Minimal impact: Most current technologies would function as expected, with only very subtle changes in high-precision systems.';
    }
  }

  @override
  Widget build(BuildContext context) {
    return InteractiveWidgetScaffold(
      title: 'Technology Impact Explorer',
      description: 'Explore how different light speeds affect technological development.',
      interactiveContent: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            AnimatedBuilder(
              animation: _controller,
              builder: (context, child) {
                return Transform.scale(
                  scale: 1.0 + (_controller.value * 0.1 * (1 - _lightSpeedFactor)),
                  child: Icon(Icons.lightbulb_outline, size: 100, color: Colors.amber),
                );
              },
            ),
            const SizedBox(height: 20),
            Text(
              'Light Speed Factor: ${_lightSpeedFactor.toStringAsFixed(2)}x',
              style: const TextStyle(fontSize: 18),
            ),
            Slider(
              value: _lightSpeedFactor,
              min: 0.01,
              max: 1.0,
              divisions: 99,
              label: _lightSpeedFactor.toStringAsFixed(2),
              onChanged: (value) {
                setState(() {
                  _lightSpeedFactor = value;
                  _controller.duration = Duration(milliseconds: (3000 * _lightSpeedFactor).round());
                  _controller.repeat(reverse: true);
                });
              },
            ),
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: Text(
                'Adjust the slider to simulate different light speeds. Observe the icon animation and read the description below to understand the hypothetical impact on technology.',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 16),
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Text(
                _getImpactDescription(),
                textAlign: TextAlign.center,
                style: const TextStyle(fontSize: 16, fontStyle: FontStyle.italic),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
