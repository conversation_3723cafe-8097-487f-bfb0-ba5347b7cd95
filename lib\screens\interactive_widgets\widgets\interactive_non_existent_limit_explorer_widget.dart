import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that allows users to explore functions where limits do not exist.
class InteractiveNonExistentLimitExplorerWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveNonExistentLimitExplorerWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  /// Factory constructor to create the widget from JSON data
  factory InteractiveNonExistentLimitExplorerWidget.fromData(Map<String, dynamic> data) {
    return InteractiveNonExistentLimitExplorerWidget(
      data: data,
    );
  }

  @override
  State<InteractiveNonExistentLimitExplorerWidget> createState() => _InteractiveNonExistentLimitExplorerWidgetState();
}

class _InteractiveNonExistentLimitExplorerWidgetState extends State<InteractiveNonExistentLimitExplorerWidget> {
  late double _discontinuityPointX;
  double _currentProbeX = 0.0; // User's probe x-value

  // Piecewise function with a jump discontinuity at x=0
  // f(x) = x + 1 for x < 0
  // f(x) = x + 3 for x >= 0
  double _function(double x) {
    if (x < _discontinuityPointX) {
      return x + 1;
    } else {
      return x + 3;
    }
  }

  // Colors
  late Color _primaryColor;
  late Color _secondaryColor;
  late Color _backgroundColor;
  late Color _textColor;

  @override
  void initState() {
    super.initState();
    _discontinuityPointX = widget.data['discontinuity_point_x']?.toDouble() ?? 0.0;
    _currentProbeX = _discontinuityPointX - 1.0; // Start probe from left

    _primaryColor = _parseColor(widget.data['primary_color']) ?? Colors.blue;
    _secondaryColor = _parseColor(widget.data['secondary_color']) ?? Colors.orange;
    _backgroundColor = _parseColor(widget.data['background_color']) ?? Colors.white;
    _textColor = _parseColor(widget.data['text_color']) ?? Colors.black87;
  }

  Color? _parseColor(String? hexString) {
    if (hexString == null) return null;
    hexString = hexString.replaceAll('#', '');
    if (hexString.length == 6) {
      hexString = 'FF$hexString';
    }
    try {
      return Color(int.parse(hexString, radix: 16));
    } catch (e) {
      return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    final currentProbeY = _function(_currentProbeX);
    final leftHandLimit = _discontinuityPointX + 1; // For x < 0
    final rightHandLimit = _discontinuityPointX + 3; // For x >= 0

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _backgroundColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.data['title'] ?? 'Non-Existent Limit Explorer',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),
          const SizedBox(height: 8),
          if (widget.data['description'] != null)
            Text(
              widget.data['description'],
              style: TextStyle(
                fontSize: 14,
                color: _textColor.withOpacity(0.8),
              ),
            ),
          const SizedBox(height: 16),

          Text(
            'Function: f(x) = { x + 1 (x < 0), x + 3 (x ≥ 0) }',
            style: TextStyle(color: _textColor, fontSize: 16, fontStyle: FontStyle.italic),
          ),
          Text(
            'Explore limit as x approaches ${_discontinuityPointX.toStringAsFixed(1)}',
            style: TextStyle(color: _textColor, fontSize: 16, fontStyle: FontStyle.italic),
          ),
          const SizedBox(height: 16),

          Text(
            'Probe x: ${_currentProbeX.toStringAsFixed(2)}',
            style: TextStyle(color: _primaryColor, fontSize: 16, fontWeight: FontWeight.bold),
          ),
          Text(
            'Probe f(x): ${currentProbeY.toStringAsFixed(2)}',
            style: TextStyle(color: _primaryColor, fontSize: 16, fontWeight: FontWeight.bold),
          ),
          Slider(
            value: _currentProbeX,
            min: _discontinuityPointX - 3.0,
            max: _discontinuityPointX + 3.0,
            divisions: 600,
            label: _currentProbeX.toStringAsFixed(2),
            activeColor: _primaryColor,
            onChanged: (value) {
              setState(() {
                _currentProbeX = value;
              });
            },
          ),
          const SizedBox(height: 16),

          Container(
            height: 250,
            child: CustomPaint(
              painter: NonExistentLimitPainter(
                function: _function,
                discontinuityPointX: _discontinuityPointX,
                currentProbeX: _currentProbeX,
                primaryColor: _primaryColor,
                secondaryColor: _secondaryColor,
                textColor: _textColor,
              ),
              child: Container(),
            ),
          ),
          const SizedBox(height: 16),

          Center(
            child: Column(
              children: [
                Text(
                  'Left-Hand Limit (x→${_discontinuityPointX.toStringAsFixed(1)}⁻): ${leftHandLimit.toStringAsFixed(1)}',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: _secondaryColor,
                  ),
                ),
                Text(
                  'Right-Hand Limit (x→${_discontinuityPointX.toStringAsFixed(1)}⁺): ${rightHandLimit.toStringAsFixed(1)}',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: _secondaryColor,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Limit Does Not Exist!',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: _incorrectColor, // Assuming _incorrectColor is defined or using a default
                  ),
                ),
              ],
            ),
          ),

          // Widget name tag
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'InteractiveNonExistentLimitExplorer',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }
}

class NonExistentLimitPainter extends CustomPainter {
  final Function(double) function;
  final double discontinuityPointX;
  final double currentProbeX;
  final Color primaryColor;
  final Color secondaryColor;
  final Color textColor;

  NonExistentLimitPainter({
    required this.function,
    required this.discontinuityPointX,
    required this.currentProbeX,
    required this.primaryColor,
    required this.secondaryColor,
    required this.textColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint();
    final textPainter = TextPainter(textDirection: TextDirection.ltr);
    final textStyle = TextStyle(color: textColor, fontSize: 10);

    // Define plot area padding
    const double padding = 30.0;
    final double plotWidth = size.width - 2 * padding;
    final double plotHeight = size.height - 2 * padding;

    // Determine x and y ranges for plotting
    final double minPlotX = discontinuityPointX - 3.5;
    final double maxPlotX = discontinuityPointX + 3.5;
    final double minPlotY = discontinuityPointX + 1 - 1.5; // Based on f(x) = x+1
    final double maxPlotY = discontinuityPointX + 3 + 1.5; // Based on f(x) = x+3

    // Scale factors
    final double xScale = plotWidth / (maxPlotX - minPlotX);
    final double yScale = plotHeight / (maxPlotY - minPlotY);

    // Helper to convert plot coordinates to canvas coordinates
    Offset toCanvas(double x, double y) {
      final canvasX = padding + (x - minPlotX) * xScale;
      final canvasY = size.height - padding - (y - minPlotY) * yScale;
      return Offset(canvasX, canvasY);
    }

    // Draw axes
    paint.color = Colors.grey;
    paint.strokeWidth = 1.0;
    // X-axis
    canvas.drawLine(toCanvas(minPlotX, 0), toCanvas(maxPlotX, 0), paint);
    // Y-axis
    canvas.drawLine(toCanvas(0, minPlotY), toCanvas(0, maxPlotY), paint);

    // Draw function curve (left part)
    paint.color = primaryColor;
    paint.style = PaintingStyle.stroke;
    paint.strokeWidth = 2.0;

    final pathLeft = Path();
    bool firstPointLeft = true;
    for (double x = minPlotX; x < discontinuityPointX; x += (maxPlotX - minPlotX) / 200) {
      final y = function(x);
      final point = toCanvas(x, y);
      if (firstPointLeft) {
        pathLeft.moveTo(point.dx, point.dy);
        firstPointLeft = false;
      } else {
        pathLeft.lineTo(point.dx, point.dy);
      }
    }
    canvas.drawPath(pathLeft, paint);

    // Draw function curve (right part)
    final pathRight = Path();
    bool firstPointRight = true;
    for (double x = discontinuityPointX; x <= maxPlotX; x += (maxPlotX - minPlotX) / 200) {
      final y = function(x);
      final point = toCanvas(x, y);
      if (firstPointRight) {
        pathRight.moveTo(point.dx, point.dy);
        firstPointRight = false;
      } else {
        pathRight.lineTo(point.dx, point.dy);
      }
    }
    canvas.drawPath(pathRight, paint);

    // Draw open circle at (discontinuityPointX, LHL)
    final lhlPoint = toCanvas(discontinuityPointX, discontinuityPointX + 1);
    paint.color = Colors.white;
    paint.style = PaintingStyle.fill;
    canvas.drawCircle(lhlPoint, 4, paint);
    paint.color = primaryColor;
    paint.style = PaintingStyle.stroke;
    paint.strokeWidth = 1.5;
    canvas.drawCircle(lhlPoint, 4, paint);

    // Draw filled circle at (discontinuityPointX, RHL)
    final rhlPoint = toCanvas(discontinuityPointX, discontinuityPointX + 3);
    paint.color = primaryColor;
    paint.style = PaintingStyle.fill;
    canvas.drawCircle(rhlPoint, 4, paint);

    // Draw current probe point
    final currentProbeCanvasPoint = toCanvas(currentProbeX, function(currentProbeX));
    paint.color = secondaryColor;
    paint.style = PaintingStyle.fill;
    canvas.drawCircle(currentProbeCanvasPoint, 5, paint);

    // Draw dashed lines from current probe point to axes
    paint.color = Colors.grey.withOpacity(0.7);
    paint.strokeWidth = 1.0;
    paint.style = PaintingStyle.stroke;
    final dashWidth = 5.0;
    final dashSpace = 5.0;
    double startX = currentProbeCanvasPoint.dx;
    double startY = currentProbeCanvasPoint.dy;

    // Vertical dashed line to X-axis
    while (startY < toCanvas(currentProbeX, 0).dy) {
      canvas.drawLine(Offset(startX, startY), Offset(startX, startY + dashWidth), paint);
      startY += dashWidth + dashSpace;
    }
    // Horizontal dashed line to Y-axis
    while (startX > toCanvas(0, function(currentProbeX)).dx) {
      canvas.drawLine(Offset(startX, currentProbeCanvasPoint.dy), Offset(startX - dashWidth, currentProbeCanvasPoint.dy), paint);
      startX -= dashWidth + dashSpace;
    }

    // Draw labels for discontinuity point and limits
    textPainter.text = TextSpan(text: 'x = ${discontinuityPointX.toStringAsFixed(1)}', style: textStyle.copyWith(color: textColor));
    textPainter.layout();
    textPainter.paint(canvas, Offset(toCanvas(discontinuityPointX, 0).dx - textPainter.width / 2, size.height - padding + 5));

    textPainter.text = TextSpan(text: 'LHL: ${(discontinuityPointX + 1).toStringAsFixed(1)}', style: textStyle.copyWith(color: secondaryColor));
    textPainter.layout();
    textPainter.paint(canvas, Offset(padding + 5, toCanvas(0, discontinuityPointX + 1).dy - textPainter.height / 2));

    textPainter.text = TextSpan(text: 'RHL: ${(discontinuityPointX + 3).toStringAsFixed(1)}', style: textStyle.copyWith(color: secondaryColor));
    textPainter.layout();
    textPainter.paint(canvas, Offset(padding + 5, toCanvas(0, discontinuityPointX + 3).dy - textPainter.height / 2));
  }

  @override
  bool shouldRepaint(covariant NonExistentLimitPainter oldDelegate) {
    return oldDelegate.discontinuityPointX != discontinuityPointX ||
           oldDelegate.currentProbeX != currentProbeX ||
           oldDelegate.primaryColor != primaryColor ||
           oldDelegate.secondaryColor != secondaryColor ||
           oldDelegate.textColor != textColor;
  }
}
