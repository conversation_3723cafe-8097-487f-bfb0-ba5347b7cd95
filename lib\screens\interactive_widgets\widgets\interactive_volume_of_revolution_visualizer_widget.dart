import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that visualizes the volume of a solid of revolution.
class InteractiveVolumeOfRevolutionVisualizerWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveVolumeOfRevolutionVisualizerWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  /// Factory constructor to create the widget from JSON data
  factory InteractiveVolumeOfRevolutionVisualizerWidget.fromData(Map<String, dynamic> data) {
    return InteractiveVolumeOfRevolutionVisualizerWidget(
      data: data,
    );
  }

  @override
  State<InteractiveVolumeOfRevolutionVisualizerWidget> createState() => _InteractiveVolumeOfRevolutionVisualizerWidgetState();
}

class _InteractiveVolumeOfRevolutionVisualizerWidgetState extends State<InteractiveVolumeOfRevolutionVisualizerWidget> {
  late double _minX;
  late double _maxX;
  late String _axisOfRevolution; // 'x' or 'y'

  // Function to revolve (e.g., f(x) = x)
  double _function(double x) {
    return x;
  }

  // Colors
  late Color _primaryColor; // For the function curve
  late Color _secondaryColor; // For the solid of revolution
  late Color _backgroundColor;
  late Color _textColor;

  @override
  void initState() {
    super.initState();
    _minX = widget.data['min_x']?.toDouble() ?? 0.0;
    _maxX = widget.data['max_x']?.toDouble() ?? 3.0;
    _axisOfRevolution = widget.data['axis_of_revolution'] ?? 'x';

    _primaryColor = _parseColor(widget.data['primary_color']) ?? Colors.blue;
    _secondaryColor = _parseColor(widget.data['secondary_color']) ?? Colors.orange;
    _backgroundColor = _parseColor(widget.data['background_color']) ?? Colors.white;
    _textColor = _parseColor(widget.data['text_color']) ?? Colors.black87;
  }

  Color? _parseColor(String? hexString) {
    if (hexString == null) return null;
    hexString = hexString.replaceAll('#', '');
    if (hexString.length == 6) {
      hexString = 'FF$hexString';
    }
    try {
      return Color(int.parse(hexString, radix: 16));
    } catch (e) {
      return null;
    }
  }

  double _calculateVolume() {
    // For f(x) = x revolved around x-axis from a to b
    // Volume = pi * integral from a to b of [f(x)]^2 dx
    // Here, f(x)^2 = x^2. Integral of x^2 is x^3 / 3
    // Volume = pi * [ (b^3 / 3) - (a^3 / 3) ]
    if (_axisOfRevolution == 'x') {
      return math.pi * ((math.pow(_maxX, 3) / 3.0) - (math.pow(_minX, 3) / 3.0));
    }
    // For y-axis revolution, would need inverse function and integrate with respect to y
    return 0.0; // Placeholder for y-axis revolution
  }

  @override
  Widget build(BuildContext context) {
    final volume = _calculateVolume();

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _backgroundColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.data['title'] ?? 'Volume of Revolution Visualizer',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),
          const SizedBox(height: 8),
          if (widget.data['description'] != null)
            Text(
              widget.data['description'],
              style: TextStyle(
                fontSize: 14,
                color: _textColor.withOpacity(0.8),
              ),
            ),
          const SizedBox(height: 16),

          Text(
            'Function: f(x) = x',
            style: TextStyle(color: _primaryColor, fontSize: 16, fontStyle: FontStyle.italic),
          ),
          Text(
            'Interval: [${_minX.toStringAsFixed(0)}, ${_maxX.toStringAsFixed(0)}]',
            style: TextStyle(color: _textColor, fontSize: 16, fontStyle: FontStyle.italic),
          ),
          Text(
            'Axis of Revolution: ${_axisOfRevolution.toUpperCase()}-axis',
            style: TextStyle(color: _textColor, fontSize: 16, fontStyle: FontStyle.italic),
          ),
          const SizedBox(height: 16),

          Center(
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _secondaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: _secondaryColor),
              ),
              child: Text(
                'Calculated Volume: ${volume.toStringAsFixed(2)} units³',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: _secondaryColor,
                ),
              ),
            ),
          ),
          const SizedBox(height: 16),

          Container(
            height: 250,
            child: CustomPaint(
              painter: VolumeOfRevolutionPainter(
                function: _function,
                minX: _minX,
                maxX: _maxX,
                primaryColor: _primaryColor,
                secondaryColor: _secondaryColor,
                textColor: _textColor,
              ),
              child: Container(),
            ),
          ),

          // Widget name tag
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'InteractiveVolumeOfRevolutionVisualizer',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }
}

class VolumeOfRevolutionPainter extends CustomPainter {
  final Function(double) function;
  final double minX;
  final double maxX;
  final Color primaryColor;
  final Color secondaryColor;
  final Color textColor;

  VolumeOfRevolutionPainter({
    required this.function,
    required this.minX,
    required this.maxX,
    required this.primaryColor,
    required this.secondaryColor,
    required this.textColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint();
    final textPainter = TextPainter(textDirection: TextDirection.ltr);
    final textStyle = TextStyle(color: textColor, fontSize: 10);

    // Define plot area padding
    const double padding = 30.0;
    final double plotWidth = size.width - 2 * padding;
    final double plotHeight = size.height - 2 * padding;

    // Determine y range for plotting
    final double minY = 0.0;
    final double maxY = function(maxX) * 1.2; // For f(x)=x, max at x=3 is 3, so ~3.6

    // Scale factors
    final double xScale = plotWidth / (maxX - minX);
    final double yScale = plotHeight / (maxY - minY);

    // Helper to convert plot coordinates to canvas coordinates
    Offset toCanvas(double x, double y) {
      final canvasX = padding + (x - minX) * xScale;
      final canvasY = size.height - padding - (y - minY) * yScale;
      return Offset(canvasX, canvasY);
    }

    // Draw axes
    paint.color = Colors.grey;
    paint.strokeWidth = 1.0;
    // X-axis
    canvas.drawLine(toCanvas(minX, 0), toCanvas(maxX, 0), paint);
    // Y-axis
    canvas.drawLine(toCanvas(0, minY), toCanvas(0, maxY), paint);

    // Draw function curve
    paint.color = primaryColor;
    paint.style = PaintingStyle.stroke;
    paint.strokeWidth = 2.0;

    final path = Path();
    bool firstPoint = true;
    for (double x = minX; x <= maxX; x += (maxX - minX) / 100) {
      final y = function(x);
      final point = toCanvas(x, y);
      if (firstPoint) {
        path.moveTo(point.dx, point.dy);
        firstPoint = false;
      } else {
        path.lineTo(point.dx, point.dy);
      }
    }
    canvas.drawPath(path, paint);

    // Draw shaded area (region to be revolved)
    paint.color = primaryColor.withOpacity(0.2);
    paint.style = PaintingStyle.fill;
    final areaPath = Path()
      ..moveTo(toCanvas(minX, 0).dx, toCanvas(minX, 0).dy);
    for (double x = minX; x <= maxX; x += (maxX - minX) / 100) {
      areaPath.lineTo(toCanvas(x, function(x)).dx, toCanvas(x, function(x)).dy);
    }
    areaPath.lineTo(toCanvas(maxX, 0).dx, toCanvas(maxX, 0).dy);
    areaPath.close();
    canvas.drawPath(areaPath, paint);

    // Draw 3D representation (simplified)
    // Draw ellipses for the "ends" of the solid
    paint.color = secondaryColor.withOpacity(0.5);
    paint.style = PaintingStyle.fill;
    // Left ellipse
    final leftRadius = function(minX) * yScale;
    canvas.drawOval(Rect.fromCenter(center: toCanvas(minX, 0), width: leftRadius * 2, height: leftRadius * 0.5), paint);
    // Right ellipse
    final rightRadius = function(maxX) * yScale;
    canvas.drawOval(Rect.fromCenter(center: toCanvas(maxX, 0), width: rightRadius * 2, height: rightRadius * 0.5), paint);

    paint.color = secondaryColor;
    paint.style = PaintingStyle.stroke;
    paint.strokeWidth = 1.0;
    canvas.drawOval(Rect.fromCenter(center: toCanvas(minX, 0), width: leftRadius * 2, height: leftRadius * 0.5), paint);
    canvas.drawOval(Rect.fromCenter(center: toCanvas(maxX, 0), width: rightRadius * 2, height: rightRadius * 0.5), paint);

    // Draw "side" lines of the solid (simplified for cone)
    canvas.drawLine(toCanvas(minX, 0), toCanvas(maxX, function(maxX)), paint);
    canvas.drawLine(toCanvas(minX, 0), toCanvas(maxX, -function(maxX)), paint); // Mirror for 3D effect
  }

  @override
  bool shouldRepaint(covariant VolumeOfRevolutionPainter oldDelegate) {
    return oldDelegate.minX != minX ||
           oldDelegate.maxX != maxX ||
           oldDelegate.primaryColor != primaryColor ||
           oldDelegate.secondaryColor != secondaryColor ||
           oldDelegate.textColor != textColor;
  }
}
