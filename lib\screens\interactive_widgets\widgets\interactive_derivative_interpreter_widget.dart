import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that helps users interpret the meaning of the derivative in context.
class InteractiveDerivativeInterpreterWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveDerivativeInterpreterWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  /// Factory constructor to create the widget from JSON data
  factory InteractiveDerivativeInterpreterWidget.fromData(Map<String, dynamic> data) {
    return InteractiveDerivativeInterpreterWidget(
      data: data,
    );
  }

  @override
  State<InteractiveDerivativeInterpreterWidget> createState() => _InteractiveDerivativeInterpreterWidgetState();
}

class _InteractiveDerivativeInterpreterWidgetState extends State<InteractiveDerivativeInterpreterWidget> {
  late double _currentTime; // The time (x-value) to probe

  // Position function (e.g., s(t) = t^2)
  double _positionFunction(double t) {
    return t * t;
  }

  // Velocity function (derivative of position, v(t) = 2t for s(t) = t^2)
  double _velocityFunction(double t) {
    return 2 * t;
  }

  // Colors
  late Color _primaryColor;
  late Color _secondaryColor;
  late Color _backgroundColor;
  late Color _textColor;

  @override
  void initState() {
    super.initState();
    _currentTime = widget.data['initial_time']?.toDouble() ?? 1.0;

    _primaryColor = _parseColor(widget.data['primary_color']) ?? Colors.blue;
    _secondaryColor = _parseColor(widget.data['secondary_color']) ?? Colors.orange;
    _backgroundColor = _parseColor(widget.data['background_color']) ?? Colors.white;
    _textColor = _parseColor(widget.data['text_color']) ?? Colors.black87;
  }

  Color? _parseColor(String? hexString) {
    if (hexString == null) return null;
    hexString = hexString.replaceAll('#', '');
    if (hexString.length == 6) {
      hexString = 'FF$hexString';
    }
    try {
      return Color(int.parse(hexString, radix: 16));
    } catch (e) {
      return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    final currentPosition = _positionFunction(_currentTime);
    final currentVelocity = _velocityFunction(_currentTime);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _backgroundColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.data['title'] ?? 'Derivative Interpreter (Position-Velocity)',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),
          const SizedBox(height: 8),
          if (widget.data['description'] != null)
            Text(
              widget.data['description'],
              style: TextStyle(
                fontSize: 14,
                color: _textColor.withOpacity(0.8),
              ),
            ),
          const SizedBox(height: 16),

          Text(
            'Position Function: s(t) = t²',
            style: TextStyle(color: _textColor, fontSize: 16, fontStyle: FontStyle.italic),
          ),
          Text(
            'Velocity Function: v(t) = s\'(t) = 2t',
            style: TextStyle(color: _textColor, fontSize: 16, fontStyle: FontStyle.italic),
          ),
          const SizedBox(height: 16),

          Text(
            'Current Time (t): ${_currentTime.toStringAsFixed(1)}',
            style: TextStyle(color: _primaryColor, fontSize: 16, fontWeight: FontWeight.bold),
          ),
          Slider(
            value: _currentTime,
            min: 0.0,
            max: 5.0,
            divisions: 50,
            label: _currentTime.toStringAsFixed(1),
            activeColor: _primaryColor,
            onChanged: (value) {
              setState(() {
                _currentTime = value;
              });
            },
          ),
          const SizedBox(height: 16),

          Center(
            child: Column(
              children: [
                Text(
                  'Position at t=${_currentTime.toStringAsFixed(1)}: ${currentPosition.toStringAsFixed(2)}',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: _primaryColor,
                  ),
                ),
                Text(
                  'Instantaneous Velocity at t=${_currentTime.toStringAsFixed(1)}: ${currentVelocity.toStringAsFixed(2)}',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: _secondaryColor,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),

          Container(
            height: 250,
            child: CustomPaint(
              painter: PositionVelocityPainter(
                positionFunction: _positionFunction,
                velocityFunction: _velocityFunction,
                currentTime: _currentTime,
                primaryColor: _primaryColor,
                secondaryColor: _secondaryColor,
                textColor: _textColor,
              ),
              child: Container(),
            ),
          ),

          // Widget name tag
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'InteractiveDerivativeInterpreter',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }
}

class PositionVelocityPainter extends CustomPainter {
  final Function(double) positionFunction;
  final Function(double) velocityFunction;
  final double currentTime;
  final Color primaryColor;
  final Color secondaryColor;
  final Color textColor;

  PositionVelocityPainter({
    required this.positionFunction,
    required this.velocityFunction,
    required this.currentTime,
    required this.primaryColor,
    required this.secondaryColor,
    required this.textColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint();
    final textPainter = TextPainter(textDirection: TextDirection.ltr);
    final textStyle = TextStyle(color: textColor, fontSize: 10);

    // Define plot area padding
    const double padding = 30.0;
    final double plotWidth = size.width - 2 * padding;
    final double plotHeight = size.height - 2 * padding;

    // Determine x (time) and y (position) ranges for plotting
    final double minPlotT = 0.0;
    final double maxPlotT = 5.0;
    final double minPlotS = 0.0;
    final double maxPlotS = 25.0; // For t^2, max at t=5 is 25

    // Scale factors
    final double xScale = plotWidth / (maxPlotT - minPlotT);
    final double yScale = plotHeight / (maxPlotS - minPlotS);

    // Helper to convert plot coordinates to canvas coordinates
    Offset toCanvas(double t, double s) {
      final canvasX = padding + (t - minPlotT) * xScale;
      final canvasY = size.height - padding - (s - minPlotS) * yScale;
      return Offset(canvasX, canvasY);
    }

    // Draw axes
    paint.color = Colors.grey;
    paint.strokeWidth = 1.0;
    // T-axis (X-axis)
    canvas.drawLine(toCanvas(minPlotT, 0), toCanvas(maxPlotT, 0), paint);
    // S-axis (Y-axis)
    canvas.drawLine(toCanvas(0, minPlotS), toCanvas(0, maxPlotS), paint);

    // Draw axis labels
    textPainter.text = TextSpan(text: 'Time (t)', style: textStyle);
    textPainter.layout();
    textPainter.paint(canvas, Offset(size.width / 2 - textPainter.width / 2, size.height - padding + 5));

    textPainter.text = TextSpan(text: 'Position (s)', style: textStyle);
    textPainter.layout();
    canvas.save();
    canvas.translate(padding - 15, size.height / 2 + textPainter.width / 2);
    canvas.rotate(-math.pi / 2);
    textPainter.paint(canvas, Offset.zero);
    canvas.restore();

    // Draw position function curve
    paint.color = primaryColor;
    paint.style = PaintingStyle.stroke;
    paint.strokeWidth = 2.0;

    final path = Path();
    bool firstPoint = true;
    for (double t = minPlotT; t <= maxPlotT; t += (maxPlotT - minPlotT) / 100) {
      final s = positionFunction(t);
      final point = toCanvas(t, s);
      if (firstPoint) {
        path.moveTo(point.dx, point.dy);
        firstPoint = false;
      } else {
        path.lineTo(point.dx, point.dy);
      }
    }
    canvas.drawPath(path, paint);

    // Draw current point (t, s(t))
    final currentPositionPoint = toCanvas(currentTime, positionFunction(currentTime));
    paint.color = primaryColor;
    paint.style = PaintingStyle.fill;
    canvas.drawCircle(currentPositionPoint, 5, paint);

    // Draw tangent line at current time (representing velocity)
    final currentVelocity = velocityFunction(currentTime);
    final tangentY1 = currentVelocity * (minPlotT - currentTime) + positionFunction(currentTime);
    final tangentY2 = currentVelocity * (maxPlotT - currentTime) + positionFunction(currentTime);

    paint.color = secondaryColor;
    paint.strokeWidth = 2.0;
    canvas.drawLine(toCanvas(minPlotT, tangentY1), toCanvas(maxPlotT, tangentY2), paint);

    // Draw labels for current point and velocity
    textPainter.text = TextSpan(text: 't = ${currentTime.toStringAsFixed(1)}', style: textStyle.copyWith(color: primaryColor));
    textPainter.layout();
    textPainter.paint(canvas, Offset(currentPositionPoint.dx - textPainter.width / 2, size.height - padding + 5));

    textPainter.text = TextSpan(text: 's(t) = ${positionFunction(currentTime).toStringAsFixed(1)}', style: textStyle.copyWith(color: primaryColor));
    textPainter.layout();
    textPainter.paint(canvas, Offset(padding + 5, currentPositionPoint.dy - textPainter.height / 2));

    textPainter.text = TextSpan(text: 'v(t) = ${currentVelocity.toStringAsFixed(1)}', style: textStyle.copyWith(color: secondaryColor));
    textPainter.layout();
    // Position velocity label near the tangent line
    final midX = (minPlotT + maxPlotT) / 2;
    final midY = currentVelocity * (midX - currentTime) + positionFunction(currentTime);
    final midCanvasPoint = toCanvas(midX, midY);
    textPainter.paint(canvas, Offset(midCanvasPoint.dx - textPainter.width / 2, midCanvasPoint.dy - textPainter.height - 5));
  }

  @override
  bool shouldRepaint(covariant PositionVelocityPainter oldDelegate) {
    return oldDelegate.currentTime != currentTime ||
           oldDelegate.primaryColor != primaryColor ||
           oldDelegate.secondaryColor != secondaryColor ||
           oldDelegate.textColor != textColor;
  }
}
