import 'package:flutter/material.dart';
import 'package:resonance/widgets/interactive_widget_scaffold.dart';

class InteractiveEthicalDilemmaSolver extends StatefulWidget {
  const InteractiveEthicalDilemmaSolver({super.key});

  @override
  State<InteractiveEthicalDilemmaSolver> createState() => _InteractiveEthicalDilemmaSolverState();
}

class _InteractiveEthicalDilemmaSolverState extends State<InteractiveEthicalDilemmaSolver> {
  String _dilemma = 'Should I save one person in the past, potentially erasing my own existence?';
  String _solutionApproach = 'Utilitarianism';
  String _analysisResult = 'No clear answer without more context.';

  final TextEditingController _dilemmaController = TextEditingController(text: 'Should I save one person in the past, potentially erasing my own existence?');

  @override
  void initState() {
    super.initState();
    _dilemmaController.addListener(_updateDilemma);
    _analyzeDilemma();
  }

  @override
  void dispose() {
    _dilemmaController.removeListener(_updateDilemma);
    _dilemmaController.dispose();
    super.dispose();
  }

  void _updateDilemma() {
    setState(() {
      _dilemma = _dilemmaController.text;
      _analyzeDilemma();
    });
  }

  void _analyzeDilemma() {
    // Simplified logic for demonstration
    setState(() {
      if (_dilemma.toLowerCase().contains('save one person') && _dilemma.toLowerCase().contains('erasing my own existence')) {
        _analysisResult = 'This is a classic "Grandfather Paradox" type dilemma. In a paradox-free time travel model, this action might be impossible, or it would create a new timeline where you never existed, but the original timeline remains untouched.';
      } else if (_dilemma.toLowerCase().contains('prevent a disaster') && _dilemma.toLowerCase().contains('unforeseen consequences')) {
        _analysisResult = 'A utilitarian approach would weigh the greatest good for the greatest number. However, unforeseen consequences make this difficult to calculate. A deontological approach might focus on the duty to prevent harm, regardless of outcome.';
      } else if (_dilemma.toLowerCase().contains('steal future tech') && _dilemma.toLowerCase().contains('benefit humanity')) {
        _analysisResult = 'A utilitarian perspective might support this if the benefits outweigh the risks. A deontological view might argue against theft, regardless of the outcome. Virtue ethics would consider what a virtuous time traveler would do.';
      }
      else {
        _analysisResult = 'Consider applying different ethical frameworks: Utilitarianism (greatest good), Deontology (duty/rules), or Virtue Ethics (character).';
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return InteractiveWidgetScaffold(
      title: 'Ethical Dilemma Solver',
      description: 'Solve ethical dilemmas arising from time travel scenarios.',
      interactiveContent: Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              TextField(
                controller: _dilemmaController,
                decoration: const InputDecoration(
                  labelText: 'Ethical Dilemma',
                  border: OutlineInputBorder(),
                  hintText: 'e.g., "Should I save one person in the past, potentially erasing my own existence?"',
                ),
                maxLines: 3,
                onChanged: (text) {
                  _dilemma = text;
                  _analyzeDilemma();
                },
              ),
              const SizedBox(height: 20),
              DropdownButton<String>(
                value: _solutionApproach,
                onChanged: (String? newValue) {
                  setState(() {
                    _solutionApproach = newValue!;
                    _analyzeDilemma();
                  });
                },
                items: <String>['Utilitarianism', 'Deontology', 'Virtue Ethics', 'No specific approach']
                    .map<DropdownMenuItem<String>>((String value) {
                  return DropdownMenuItem<String>(
                    value: value,
                    child: Text(value),
                  );
                }).toList(),
              ),
              const SizedBox(height: 30),
              Text(
                'Analysis Result:',
                style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 10),
              Text(
                _analysisResult,
                textAlign: TextAlign.center,
                style: const TextStyle(fontSize: 16, fontStyle: FontStyle.italic, color: Colors.purple),
              ),
              const Padding(
                padding: EdgeInsets.all(16.0),
                child: Text(
                  'Enter an ethical dilemma related to time travel and select an ethical framework to analyze it. This tool provides a simplified analysis based on common philosophical approaches to ethics.',
                  textAlign: TextAlign.center,
                  style: TextStyle(fontSize: 16),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
