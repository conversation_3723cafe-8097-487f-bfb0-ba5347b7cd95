import 'package:flutter/material.dart';
import 'dart:math';

class InteractiveLimitTableGenerator extends StatefulWidget {
  const InteractiveLimitTableGenerator({super.key});

  @override
  State<InteractiveLimitTableGenerator> createState() => _InteractiveLimitTableGeneratorState();
}

class _InteractiveLimitTableGeneratorState extends State<InteractiveLimitTableGenerator> {
  final double _targetX = 2.0; // The x-value to approach
  final TextEditingController _functionController = TextEditingController(text: '(x*x - 4) / (x - 2)');
  String _errorMessage = '';

  double _evaluateFunction(double x, String functionString) {
    try {
      // Basic parser for simple functions. For a robust solution, a math expression parser library would be needed.
      // This example handles (x*x - 4) / (x - 2) and x + 2
      if (functionString.contains('(x*x - 4) / (x - 2)')) {
        if (x == _targetX) return double.nan;
        return (x * x - 4) / (x - 2);
      } else if (functionString.contains('x + 2')) {
        return x + 2;
      }
      // Add more function parsing logic here as needed
      return double.nan; // Indicate unsupported function
    } catch (e) {
      _errorMessage = 'Error evaluating function: ${e.toString()}';
      return double.nan;
    }
  }

  List<Map<String, String>> _generateTable() {
    List<Map<String, String>> tableData = [];
    _errorMessage = '';

    // Values approaching from the left
    for (int i = 3; i >= 0; i--) {
      double x = _targetX - pow(10, -i).toDouble();
      double y = _evaluateFunction(x, _functionController.text);
      tableData.add({
        'x': x.toStringAsFixed(i + 2),
        'f(x)': y.isNaN ? 'Undefined' : y.toStringAsFixed(i + 2),
      });
    }

    // Target point
    double yTarget = _evaluateFunction(_targetX, _functionController.text);
    tableData.add({
      'x': _targetX.toStringAsFixed(1),
      'f(x)': yTarget.isNaN ? 'Undefined' : yTarget.toStringAsFixed(1),
    });

    // Values approaching from the right
    for (int i = 0; i <= 3; i++) {
      double x = _targetX + pow(10, -i).toDouble();
      double y = _evaluateFunction(x, _functionController.text);
      tableData.add({
        'x': x.toStringAsFixed(i + 2),
        'f(x)': y.isNaN ? 'Undefined' : y.toStringAsFixed(i + 2),
      });
    }
    return tableData;
  }

  @override
  Widget build(BuildContext context) {
    List<Map<String, String>> table = _generateTable();

    return Card(
      margin: const EdgeInsets.all(16.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Interactive Limit Table Generator',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 20),
            TextField(
              controller: _functionController,
              decoration: InputDecoration(
                labelText: 'Enter Function f(x) (e.g., (x*x - 4) / (x - 2) or x + 2)',
                border: OutlineInputBorder(),
              ),
              onChanged: (value) {
                setState(() {
                  _errorMessage = ''; // Clear error on change
                });
              },
            ),
            const SizedBox(height: 10),
            Text(
              'Approaching x = ${_targetX.toStringAsFixed(1)}',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 20),
            if (_errorMessage.isNotEmpty)
              Padding(
                padding: const EdgeInsets.only(bottom: 10.0),
                child: Text(
                  _errorMessage,
                  style: TextStyle(color: Colors.red),
                ),
              ),
            Table(
              border: TableBorder.all(color: Colors.grey),
              columnWidths: const {
                0: FlexColumnWidth(1),
                1: FlexColumnWidth(1),
              },
              children: [
                TableRow(
                  decoration: BoxDecoration(color: Colors.blueGrey[100]),
                  children: const [
                    Padding(
                      padding: EdgeInsets.all(8.0),
                      child: Text('x', style: TextStyle(fontWeight: FontWeight.bold)),
                    ),
                    Padding(
                      padding: EdgeInsets.all(8.0),
                      child: Text('f(x)', style: TextStyle(fontWeight: FontWeight.bold)),
                    ),
                  ],
                ),
                ...table.map((row) {
                  return TableRow(
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Text(row['x']!),
                      ),
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Text(row['f(x)']!),
                      ),
                    ],
                  );
                }).toList(),
              ],
            ),
            const SizedBox(height: 20),
            Text(
              'As x approaches ${_targetX.toStringAsFixed(1)}, f(x) approaches ${(_targetX + 2).toStringAsFixed(1)}',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: Colors.purple),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
