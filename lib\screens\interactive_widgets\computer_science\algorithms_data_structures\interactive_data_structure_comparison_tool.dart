import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:resonance_app/services/interactive_widget_service.dart';
import 'package:resonance_app/widgets/animated_button.dart';

class InteractiveDataStructureComparisonTool extends StatefulWidget {
  final String widgetId;

  const InteractiveDataStructureComparisonTool({Key? key, required this.widgetId}) : super(key: key);

  @override
  _InteractiveDataStructureComparisonToolState createState() => _InteractiveDataStructureComparisonToolState();
}

class _InteractiveDataStructureComparisonToolState extends State<InteractiveDataStructureComparisonTool> {
  String _selectedStructure1 = 'Array';
  String _selectedStructure2 = 'Linked List';
  String _comparisonResult = '';

  final List<String> _dataStructures = [
    'Array',
    'Linked List',
    'Stack',
    'Queue',
    'Hash Table',
    'Tree',
    'Graph',
  ];

  @override
  void initState() {
    super.initState();
    _loadState();
  }

  void _loadState() async {
    final service = Provider.of<InteractiveWidgetService>(context, listen: false);
    final savedState = await service.getWidgetState(widget.widgetId);
    if (savedState != null) {
      setState(() {
        _selectedStructure1 = savedState['selectedStructure1'] ?? 'Array';
        _selectedStructure2 = savedState['selectedStructure2'] ?? 'Linked List';
        _comparisonResult = savedState['comparisonResult'] ?? '';
      });
    }
  }

  void _saveState() {
    final service = Provider.of<InteractiveWidgetService>(context, listen: false);
    service.saveWidgetState(widget.widgetId, {
      'selectedStructure1': _selectedStructure1,
      'selectedStructure2': _selectedStructure2,
      'comparisonResult': _comparisonResult,
    });
  }

  void _compareStructures() {
    String result = '';
    if (_selectedStructure1 == _selectedStructure2) {
      result = 'Please select two different data structures for comparison.';
    } else {
      result = _getComparisonDetails(_selectedStructure1, _selectedStructure2);
    }
    setState(() {
      _comparisonResult = result;
    });
    _saveState();
  }

  String _getComparisonDetails(String s1, String s2) {
    Map<String, Map<String, String>> properties = {
      'Array': {
        'Access': 'O(1) (by index)',
        'Insertion/Deletion': 'O(n) (middle), O(1) (end)',
        'Memory': 'Contiguous',
        'Use Case': 'Fixed-size collections, random access',
      },
      'Linked List': {
        'Access': 'O(n)',
        'Insertion/Deletion': 'O(1) (if node known), O(n) (search)',
        'Memory': 'Non-contiguous',
        'Use Case': 'Dynamic size, frequent insertions/deletions',
      },
      'Stack': {
        'Access': 'O(1) (top)',
        'Insertion/Deletion': 'O(1) (push/pop)',
        'Memory': 'LIFO principle',
        'Use Case': 'Function call stack, undo/redo',
      },
      'Queue': {
        'Access': 'O(1) (front)',
        'Insertion/Deletion': 'O(1) (enqueue/dequeue)',
        'Memory': 'FIFO principle',
        'Use Case': 'Task scheduling, breadth-first search',
      },
      'Hash Table': {
        'Access': 'Average O(1), Worst O(n)',
        'Insertion/Deletion': 'Average O(1), Worst O(n)',
        'Memory': 'Key-value pairs, uses hash function',
        'Use Case': 'Fast lookups, dictionaries',
      },
      'Tree': {
        'Access': 'O(log n) (balanced), O(n) (unbalanced)',
        'Insertion/Deletion': 'O(log n) (balanced), O(n) (unbalanced)',
        'Memory': 'Hierarchical structure',
        'Use Case': 'Hierarchical data, searching, sorting',
      },
      'Graph': {
        'Access': 'O(V+E)',
        'Insertion/Deletion': 'O(1) (add vertex/edge)',
        'Memory': 'Nodes and edges',
        'Use Case': 'Networks, relationships, pathfinding',
      },
    };

    String details = 'Comparison between $s1 and $s2:\n\n';
    details += '$s1 Properties:\n';
    properties[s1]?.forEach((key, value) {
      details += '  $key: $value\n';
    });
    details += '\n$s2 Properties:\n';
    properties[s2]?.forEach((key, value) {
      details += '  $key: $value\n';
    });

    return details;
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Interactive Data Structure Comparison Tool',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 20),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              DropdownButton<String>(
                value: _selectedStructure1,
                onChanged: (String? newValue) {
                  setState(() {
                    _selectedStructure1 = newValue!;
                  });
                },
                items: _dataStructures.map<DropdownMenuItem<String>>((String value) {
                  return DropdownMenuItem<String>(
                    value: value,
                    child: Text(value),
                  );
                }).toList(),
              ),
              const Text('vs.'),
              DropdownButton<String>(
                value: _selectedStructure2,
                onChanged: (String? newValue) {
                  setState(() {
                    _selectedStructure2 = newValue!;
                  });
                },
                items: _dataStructures.map<DropdownMenuItem<String>>((String value) {
                  return DropdownMenuItem<String>(
                    value: value,
                    child: Text(value),
                  );
                }).toList(),
              ),
            ],
          ),
          const SizedBox(height: 20),
          Center(
            child: AnimatedButton(
              onTap: _compareStructures,
              text: 'Compare',
              color: Colors.purple,
            ),
          ),
          const SizedBox(height: 20),
          Expanded(
            child: SingleChildScrollView(
              child: Text(
                _comparisonResult,
                style: Theme.of(context).textTheme.bodyLarge,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
