import 'package:flutter/material.dart';
import 'dart:math';
import 'package:resonance/widgets/interactive_widget_scaffold.dart';

class InteractiveObjectTrajectoryCalculator extends StatefulWidget {
  const InteractiveObjectTrajectoryCalculator({super.key});

  @override
  State<InteractiveObjectTrajectoryCalculator> createState() =>
      _InteractiveObjectTrajectoryCalculatorState();
}

class _InteractiveObjectTrajectoryCalculatorState
    extends State<InteractiveObjectTrajectoryCalculator>
    with SingleTickerProviderStateMixin {
  late TextEditingController _velocityController;
  late TextEditingController _angleController;
  late AnimationController _animationController;
  late Animation<double> _animation;

  double _initialVelocity = 50.0; // m/s
  double _launchAngle = 45.0; // degrees
  double _gravity = 9.81; // m/s^2

  Offset _objectPosition = Offset.zero;
  List<Offset> _trajectoryPoints = [];

  @override
  void initState() {
    super.initState();
    _velocityController = TextEditingController(text: _initialVelocity.toString());
    _angleController = TextEditingController(text: _launchAngle.toString());

    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 5), // Placeholder duration
    );

    _animationController.addListener(() {
      setState(() {
        _updateObjectPosition(_animation.value);
      });
    });

    _calculateTrajectory();
  }

  void _calculateTrajectory() {
    _trajectoryPoints.clear();
    double angleRad = _launchAngle * pi / 180;
    double timeOfFlight = (2 * _initialVelocity * sin(angleRad)) / _gravity;
    double maxTime = timeOfFlight * 1.2; // Extend duration slightly for visual
    
    _animationController.duration = Duration(milliseconds: (maxTime * 1000).toInt());
    _animation = Tween<double>(begin: 0.0, end: maxTime).animate(_animationController);

    for (double t = 0; t <= maxTime; t += maxTime / 100) {
      double x = _initialVelocity * cos(angleRad) * t;
      double y = _initialVelocity * sin(angleRad) * t - 0.5 * _gravity * t * t;
      _trajectoryPoints.add(Offset(x, y));
    }
    // Ensure the last point is added
    if (!_trajectoryPoints.contains(Offset(_initialVelocity * cos(angleRad) * timeOfFlight, 0.0))) {
      _trajectoryPoints.add(Offset(_initialVelocity * cos(angleRad) * timeOfFlight, 0.0));
    }
  }

  void _updateObjectPosition(double t) {
    double angleRad = _launchAngle * pi / 180;
    double x = _initialVelocity * cos(angleRad) * t;
    double y = _initialVelocity * sin(angleRad) * t - 0.5 * _gravity * t * t;
    _objectPosition = Offset(x, y);
  }

  void _simulate() {
    setState(() {
      _initialVelocity = double.tryParse(_velocityController.text) ?? _initialVelocity;
      _launchAngle = double.tryParse(_angleController.text) ?? _launchAngle;
      _calculateTrajectory();
      _animationController.reset();
      _animationController.forward();
    });
  }

  @override
  void dispose() {
    _velocityController.dispose();
    _angleController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return InteractiveWidgetScaffold(
      title: 'Object Trajectory Calculator',
      interactiveWidget: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _velocityController,
                    keyboardType: TextInputType.number,
                    decoration: const InputDecoration(
                      labelText: 'Initial Velocity (m/s)',
                      border: OutlineInputBorder(),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextField(
                    controller: _angleController,
                    keyboardType: TextInputType.number,
                    decoration: const InputDecoration(
                      labelText: 'Launch Angle (degrees)',
                      border: OutlineInputBorder(),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _simulate,
              child: const Text('Simulate Trajectory'),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: LayoutBuilder(
                builder: (context, constraints) {
                  return CustomPaint(
                    painter: TrajectoryPainter(
                      trajectoryPoints: _trajectoryPoints,
                      objectPosition: _objectPosition,
                      maxHeight: constraints.maxHeight,
                      maxWidth: constraints.maxWidth,
                    ),
                    child: Container(),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class TrajectoryPainter extends CustomPainter {
  final List<Offset> trajectoryPoints;
  final Offset objectPosition;
  final double maxHeight;
  final double maxWidth;

  TrajectoryPainter({
    required this.trajectoryPoints,
    required this.objectPosition,
    required this.maxHeight,
    required this.maxWidth,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.blue
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;

    final objectPaint = Paint()
      ..color = Colors.red
      ..style = PaintingStyle.fill;

    // Find max X and max Y for scaling
    double maxX = 0;
    double maxY = 0;
    for (var p in trajectoryPoints) {
      if (p.dx > maxX) maxX = p.dx;
      if (p.dy > maxY) maxY = p.dy;
    }
    // Ensure Y is always positive for drawing (canvas origin is top-left)
    maxY = maxY.abs();

    // Add some padding to the max values for better visualization
    maxX *= 1.1;
    maxY *= 1.1;

    // Determine scaling factors
    double scaleX = size.width / maxX;
    double scaleY = size.height / maxY;
    double scale = min(scaleX, scaleY); // Use the smaller scale to fit both dimensions

    // Adjust scale if trajectory is very flat or very high
    if (maxX == 0 || maxY == 0) {
      scale = 1.0; // Avoid division by zero or infinite scale
    } else {
      // Prioritize fitting the height if it's a tall trajectory, width if it's wide
      if (maxY / maxX > size.height / size.width) {
        scale = size.height / maxY;
      } else {
        scale = size.width / maxX;
      }
    }
    
    // Offset to draw from bottom-left
    final double offsetX = 0;
    final double offsetY = size.height;

    // Draw trajectory
    final path = Path();
    if (trajectoryPoints.isNotEmpty) {
      path.moveTo(offsetX + trajectoryPoints[0].dx * scale, offsetY - trajectoryPoints[0].dy * scale);
      for (int i = 1; i < trajectoryPoints.length; i++) {
        path.lineTo(offsetX + trajectoryPoints[i].dx * scale, offsetY - trajectoryPoints[i].dy * scale);
      }
    }
    canvas.drawPath(path, paint);

    // Draw object
    canvas.drawCircle(
      Offset(offsetX + objectPosition.dx * scale, offsetY - objectPosition.dy * scale),
      5.0, // Object radius
      objectPaint,
    );
  }

  @override
  bool shouldRepaint(covariant TrajectoryPainter oldDelegate) {
    return oldDelegate.trajectoryPoints != trajectoryPoints ||
        oldDelegate.objectPosition != objectPosition;
  }
}
