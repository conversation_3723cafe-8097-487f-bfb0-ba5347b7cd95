import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:resonance_app/services/interactive_widget_service.dart';
import 'package:resonance_app/widgets/animated_button.dart';
import 'dart:math';

class InteractiveBinarySearchSimulator extends StatefulWidget {
  final String widgetId;

  const InteractiveBinarySearchSimulator({Key? key, required this.widgetId}) : super(key: key);

  @override
  _InteractiveBinarySearchSimulatorState createState() => _InteractiveBinarySearchSimulatorState();
}

class _InteractiveBinarySearchSimulatorState extends State<InteractiveBinarySearchSimulator> {
  List<int> _numbers = [];
  int _target = -1;
  int _lowIndex = -1;
  int _highIndex = -1;
  int _midIndex = -1;
  bool _found = false;
  bool _isSearching = false;
  String _message = '';
  int _arraySize = 10;
  double _animationSpeed = 500; // milliseconds
  TextEditingController _targetController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadState();
    _generateSortedArray();
  }

  void _loadState() async {
    final service = Provider.of<InteractiveWidgetService>(context, listen: false);
    final savedState = await service.getWidgetState(widget.widgetId);
    if (savedState != null) {
      setState(() {
        _numbers = List<int>.from(savedState['numbers'] ?? []);
        _target = savedState['target'] ?? -1;
        _lowIndex = savedState['lowIndex'] ?? -1;
        _highIndex = savedState['highIndex'] ?? -1;
        _midIndex = savedState['midIndex'] ?? -1;
        _found = savedState['found'] ?? false;
        _isSearching = savedState['isSearching'] ?? false;
        _message = savedState['message'] ?? '';
        _arraySize = savedState['arraySize'] ?? 10;
        _animationSpeed = savedState['animationSpeed'] ?? 500;
        _targetController.text = _target == -1 ? '' : _target.toString();
      });
    }
  }

  void _saveState() {
    final service = Provider.of<InteractiveWidgetService>(context, listen: false);
    service.saveWidgetState(widget.widgetId, {
      'numbers': _numbers,
      'target': _target,
      'lowIndex': _lowIndex,
      'highIndex': _highIndex,
      'midIndex': _midIndex,
      'found': _found,
      'isSearching': _isSearching,
      'message': _message,
      'arraySize': _arraySize,
      'animationSpeed': _animationSpeed,
    });
  }

  void _generateSortedArray() {
    setState(() {
      _numbers = List.generate(_arraySize, (index) => Random().nextInt(90) + 10);
      _numbers.sort(); // Binary search requires a sorted array
      _lowIndex = -1;
      _highIndex = -1;
      _midIndex = -1;
      _found = false;
      _isSearching = false;
      _message = 'New sorted array generated.';
    });
    _saveState();
  }

  Future<void> _binarySearch() async {
    final value = int.tryParse(_targetController.text);
    if (value == null) {
      setState(() {
        _message = 'Please enter a valid number for the target.';
      });
      return;
    }
    _target = value;

    if (_isSearching) return;

    setState(() {
      _isSearching = true;
      _found = false;
      _lowIndex = 0;
      _highIndex = _numbers.length - 1;
      _midIndex = -1;
      _message = 'Searching for $_target...';
    });
    _saveState();

    while (_lowIndex <= _highIndex) {
      _midIndex = (_lowIndex + _highIndex) ~/ 2;

      setState(() {
        _message = 'Checking middle element at index $_midIndex: ${_numbers[_midIndex]}';
      });
      _saveState();
      await Future.delayed(Duration(milliseconds: _animationSpeed.toInt()));

      if (_numbers[_midIndex] == _target) {
        setState(() {
          _found = true;
          _message = 'Target $_target found at index $_midIndex!';
        });
        _saveState();
        break;
      } else if (_numbers[_midIndex] < _target) {
        setState(() {
          _message = '${_numbers[_midIndex]} < $_target. Searching right half.';
        });
        _saveState();
        await Future.delayed(Duration(milliseconds: _animationSpeed.toInt()));
        _lowIndex = _midIndex + 1;
      } else {
        setState(() {
          _message = '${_numbers[_midIndex]} > $_target. Searching left half.';
        });
        _saveState();
        await Future.delayed(Duration(milliseconds: _animationSpeed.toInt()));
        _highIndex = _midIndex - 1;
      }
    }

    if (!_found) {
      setState(() {
        _message = 'Target $_target not found in the array.';
      });
      _saveState();
    }

    setState(() {
      _isSearching = false;
      _lowIndex = -1;
      _highIndex = -1;
      _midIndex = -1;
    });
    _saveState();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Interactive Binary Search Simulator',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                child: Slider(
                  value: _arraySize.toDouble(),
                  min: 5,
                  max: 20,
                  divisions: 15,
                  label: _arraySize.round().toString(),
                  onChanged: (double value) {
                    setState(() {
                      _arraySize = value.round();
                    });
                  },
                  onChangeEnd: (double value) {
                    _generateSortedArray();
                  },
                ),
              ),
              Text('Array Size: $_arraySize'),
            ],
          ),
          Row(
            children: [
              Expanded(
                child: Slider(
                  value: _animationSpeed,
                  min: 50,
                  max: 1000,
                  divisions: 19,
                  label: _animationSpeed.round().toString(),
                  onChanged: (double value) {
                    setState(() {
                      _animationSpeed = value;
                    });
                  },
                ),
              ),
              Text('Speed: ${_animationSpeed.round()}ms'),
            ],
          ),
          const SizedBox(height: 10),
          TextField(
            controller: _targetController,
            keyboardType: TextInputType.number,
            decoration: const InputDecoration(
              labelText: 'Enter target number',
              border: OutlineInputBorder(),
            ),
          ),
          const SizedBox(height: 10),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              AnimatedButton(
                onTap: _generateSortedArray,
                text: 'Generate New Sorted Array',
                color: Colors.blue,
              ),
              AnimatedButton(
                onTap: _isSearching ? null : _binarySearch,
                text: _isSearching ? 'Searching...' : 'Start Search',
                color: _isSearching ? Colors.grey : Colors.green,
              ),
            ],
          ),
          const SizedBox(height: 20),
          Text(
            'Array:',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 10),
          Container(
            height: 100,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: _numbers.asMap().entries.map((entry) {
                int index = entry.key;
                int number = entry.value;
                Color color = Colors.grey;
                if (index == _midIndex) {
                  color = _found ? Colors.green : Colors.red; // Found or current middle
                } else if (index >= _lowIndex && index <= _highIndex && _isSearching) {
                  color = Colors.blue; // Current search range
                } else if (_found && number == _target) {
                  color = Colors.green; // Already found and highlighted
                }

                return Container(
                  margin: const EdgeInsets.symmetric(horizontal: 2),
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: color,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    number.toString(),
                    style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                  ),
                );
              }).toList(),
            ),
          ),
          const SizedBox(height: 20),
          Text(
            'Message: $_message',
            style: Theme.of(context).textTheme.bodyLarge,
          ),
        ],
      ),
    );
  }
}
