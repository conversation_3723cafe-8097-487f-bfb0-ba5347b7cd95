import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that calculates and visualizes the area between two curves.
class InteractiveAreaBetweenCurvesCalculatorWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveAreaBetweenCurvesCalculatorWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  /// Factory constructor to create the widget from JSON data
  factory InteractiveAreaBetweenCurvesCalculatorWidget.fromData(Map<String, dynamic> data) {
    return InteractiveAreaBetweenCurvesCalculatorWidget(
      data: data,
    );
  }

  @override
  State<InteractiveAreaBetweenCurvesCalculatorWidget> createState() => _InteractiveAreaBetweenCurvesCalculatorWidgetState();
}

class _InteractiveAreaBetweenCurvesCalculatorWidgetState extends State<InteractiveAreaBetweenCurvesCalculatorWidget> {
  late double _minX;
  late double _maxX;

  // Function 1 (e.g., f(x) = x)
  double _function1(double x) {
    return x;
  }

  // Function 2 (e.g., g(x) = x^2)
  double _function2(double x) {
    return x * x;
  }

  // Antiderivative of (f(x) - g(x)) = (x - x^2) -> (x^2/2 - x^3/3)
  double _antiderivativeDifference(double x) {
    return (x * x / 2.0) - (x * x * x / 3.0);
  }

  // Colors
  late Color _primaryColor; // For function 1
  late Color _secondaryColor; // For function 2
  late Color _backgroundColor;
  late Color _textColor;
  late Color _areaColor;

  @override
  void initState() {
    super.initState();
    _minX = widget.data['min_x']?.toDouble() ?? 0.0;
    _maxX = widget.data['max_x']?.toDouble() ?? 1.0;

    _primaryColor = _parseColor(widget.data['primary_color']) ?? Colors.blue;
    _secondaryColor = _parseColor(widget.data['secondary_color']) ?? Colors.orange;
    _backgroundColor = _parseColor(widget.data['background_color']) ?? Colors.white;
    _textColor = _parseColor(widget.data['text_color']) ?? Colors.black87;
    _areaColor = _parseColor(widget.data['area_color']) ?? Colors.green;
  }

  Color? _parseColor(String? hexString) {
    if (hexString == null) return null;
    hexString = hexString.replaceAll('#', '');
    if (hexString.length == 6) {
      hexString = 'FF$hexString';
    }
    try {
      return Color(int.parse(hexString, radix: 16));
    } catch (e) {
      return null;
    }
  }

  double _calculateAreaBetweenCurves() {
    // Area = ∫[a to b] (f(x) - g(x)) dx
    // Assuming f(x) >= g(x) on the interval
    return _antiderivativeDifference(_maxX) - _antiderivativeDifference(_minX);
  }

  @override
  Widget build(BuildContext context) {
    final area = _calculateAreaBetweenCurves();

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _backgroundColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.data['title'] ?? 'Area Between Curves Calculator',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),
          const SizedBox(height: 8),
          if (widget.data['description'] != null)
            Text(
              widget.data['description'],
              style: TextStyle(
                fontSize: 14,
                color: _textColor.withOpacity(0.8),
              ),
            ),
          const SizedBox(height: 16),

          Text(
            'Function 1: f(x) = x',
            style: TextStyle(color: _primaryColor, fontSize: 16, fontStyle: FontStyle.italic),
          ),
          Text(
            'Function 2: g(x) = x²',
            style: TextStyle(color: _secondaryColor, fontSize: 16, fontStyle: FontStyle.italic),
          ),
          Text(
            'Interval: [${_minX.toStringAsFixed(0)}, ${_maxX.toStringAsFixed(0)}]',
            style: TextStyle(color: _textColor, fontSize: 16, fontStyle: FontStyle.italic),
          ),
          const SizedBox(height: 16),

          Center(
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _areaColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: _areaColor),
              ),
              child: Text(
                'Area Between Curves: ${area.toStringAsFixed(4)}',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: _areaColor,
                ),
              ),
            ),
          ),
          const SizedBox(height: 16),

          Container(
            height: 250,
            child: CustomPaint(
              painter: AreaBetweenCurvesPainter(
                function1: _function1,
                function2: _function2,
                minX: _minX,
                maxX: _maxX,
                primaryColor: _primaryColor,
                secondaryColor: _secondaryColor,
                areaColor: _areaColor,
                textColor: _textColor,
              ),
              child: Container(),
            ),
          ),

          // Widget name tag
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'InteractiveAreaBetweenCurvesCalculator',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }
}

class AreaBetweenCurvesPainter extends CustomPainter {
  final Function(double) function1;
  final Function(double) function2;
  final double minX;
  final double maxX;
  final Color primaryColor;
  final Color secondaryColor;
  final Color areaColor;
  final Color textColor;

  AreaBetweenCurvesPainter({
    required this.function1,
    required this.function2,
    required this.minX,
    required this.maxX,
    required this.primaryColor,
    required this.secondaryColor,
    required this.areaColor,
    required this.textColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint();
    final textPainter = TextPainter(textDirection: TextDirection.ltr);
    final textStyle = TextStyle(color: textColor, fontSize: 10);

    // Define plot area padding
    const double padding = 30.0;
    final double plotWidth = size.width - 2 * padding;
    final double plotHeight = size.height - 2 * padding;

    // Determine y range for plotting
    final double minY = -1.0; // Extend slightly below 0
    final double maxY = 1.5; // Max value of x on [0,1] is 1, x^2 is 1.

    // Scale factors
    final double xScale = plotWidth / (maxX - minX);
    final double yScale = plotHeight / (maxY - minY);

    // Helper to convert plot coordinates to canvas coordinates
    Offset toCanvas(double x, double y) {
      final canvasX = padding + (x - minX) * xScale;
      final canvasY = size.height - padding - (y - minY) * yScale;
      return Offset(canvasX, canvasY);
    }

    // Draw axes
    paint.color = Colors.grey;
    paint.strokeWidth = 1.0;
    // X-axis
    canvas.drawLine(toCanvas(minX, 0), toCanvas(maxX, 0), paint);
    // Y-axis
    canvas.drawLine(toCanvas(0, minY), toCanvas(0, maxY), paint);

    // Draw function 1 curve
    paint.color = primaryColor;
    paint.style = PaintingStyle.stroke;
    paint.strokeWidth = 2.0;

    final path1 = Path();
    bool firstPoint1 = true;
    for (double x = minX; x <= maxX; x += (maxX - minX) / 100) {
      final y = function1(x);
      final point = toCanvas(x, y);
      if (firstPoint1) {
        path1.moveTo(point.dx, point.dy);
        firstPoint1 = false;
      } else {
        path1.lineTo(point.dx, point.dy);
      }
    }
    canvas.drawPath(path1, paint);

    // Draw function 2 curve
    paint.color = secondaryColor;
    paint.style = PaintingStyle.stroke;
    paint.strokeWidth = 2.0;

    final path2 = Path();
    bool firstPoint2 = true;
    for (double x = minX; x <= maxX; x += (maxX - minX) / 100) {
      final y = function2(x);
      final point = toCanvas(x, y);
      if (firstPoint2) {
        path2.moveTo(point.dx, point.dy);
        firstPoint2 = false;
      } else {
        path2.lineTo(point.dx, point.dy);
      }
    }
    canvas.drawPath(path2, paint);

    // Draw shaded area between curves
    paint.color = areaColor.withOpacity(0.4);
    paint.style = PaintingStyle.fill;
    final areaPath = Path();
    for (double x = minX; x <= maxX; x += (maxX - minX) / 100) {
      final y1 = function1(x);
      final y2 = function2(x);
      if (x == minX) {
        areaPath.moveTo(toCanvas(x, y1).dx, toCanvas(x, y1).dy);
      } else {
        areaPath.lineTo(toCanvas(x, y1).dx, toCanvas(x, y1).dy);
      }
    }
    for (double x = maxX; x >= minX; x -= (maxX - minX) / 100) {
      final y2 = function2(x);
      areaPath.lineTo(toCanvas(x, y2).dx, toCanvas(x, y2).dy);
    }
    areaPath.close();
    canvas.drawPath(areaPath, paint);

    paint.color = areaColor;
    paint.style = PaintingStyle.stroke;
    paint.strokeWidth = 1.0;
    canvas.drawPath(areaPath, paint);

    // Draw labels for minX and maxX
    textPainter.text = TextSpan(text: minX.toStringAsFixed(0), style: textStyle);
    textPainter.layout();
    textPainter.paint(canvas, Offset(toCanvas(minX, 0).dx - textPainter.width / 2, size.height - padding + 5));

    textPainter.text = TextSpan(text: maxX.toStringAsFixed(0), style: textStyle);
    textPainter.layout();
    textPainter.paint(canvas, Offset(toCanvas(maxX, 0).dx - textPainter.width / 2, size.height - padding + 5));
  }

  @override
  bool shouldRepaint(covariant AreaBetweenCurvesPainter oldDelegate) {
    return oldDelegate.minX != minX ||
           oldDelegate.maxX != maxX ||
           oldDelegate.primaryColor != primaryColor ||
           oldDelegate.secondaryColor != secondaryColor ||
           oldDelegate.areaColor != areaColor ||
           oldDelegate.textColor != textColor;
  }
}
