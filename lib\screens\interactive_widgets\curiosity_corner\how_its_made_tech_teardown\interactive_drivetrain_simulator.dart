import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:resonance_app/services/service_provider.dart';
import 'package:resonance_app/utils/page_transitions.dart';

class InteractiveDrivetrainSimulator extends StatefulWidget {
  const InteractiveDrivetrainSimulator({super.key});

  @override
  State<InteractiveDrivetrainSimulator> createState() => _InteractiveDrivetrainSimulatorState();
}

class _InteractiveDrivetrainSimulatorState extends State<InteractiveDrivetrainSimulator> with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 1),
    )..repeat(reverse: true);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Interactive Drivetrain Simulator'),
        backgroundColor: Theme.of(context).primaryColor,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Simulate the operation of an automobile drivetrain!',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 20),
            Expanded(
              child: Center(
                child: FadeTransition(
                  opacity: _controller,
                  child: const Text(
                    'Placeholder for interactive drivetrain simulation.',
                    textAlign: TextAlign.center,
                    style: TextStyle(fontSize: 18, fontStyle: FontStyle.italic),
                  ),
                ),
              ),
            ),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('Go Back'),
            ),
          ],
        ),
      ),
    );
  }
}
