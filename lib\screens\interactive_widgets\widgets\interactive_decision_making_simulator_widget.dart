import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that allows users to simulate and explore decision-making under uncertainty.
class InteractiveDecisionMakingSimulatorWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveDecisionMakingSimulatorWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  /// Factory constructor to create the widget from JSON data
  factory InteractiveDecisionMakingSimulatorWidget.fromData(Map<String, dynamic> data) {
    return InteractiveDecisionMakingSimulatorWidget(
      data: data,
    );
  }

  @override
  State<InteractiveDecisionMakingSimulatorWidget> createState() => _InteractiveDecisionMakingSimulatorWidgetState();
}

class _InteractiveDecisionMakingSimulatorWidgetState extends State<InteractiveDecisionMakingSimulatorWidget> {
  // Option 1 parameters
  late double _option1Outcome1Value;
  late double _option1Outcome1Probability;
  late double _option1Outcome2Value;
  late double _option1Outcome2Probability;

  // Option 2 parameters
  late double _option2Outcome1Value;
  late double _option2Outcome1Probability;
  late double _option2Outcome2Value;
  late double _option2Outcome2Probability;

  double _expectedValueOption1 = 0.0;
  double _expectedValueOption2 = 0.0;

  // Colors
  late Color _primaryColor;
  late Color _secondaryColor;
  late Color _backgroundColor;
  late Color _textColor;

  @override
  void initState() {
    super.initState();
    _option1Outcome1Value = widget.data['option1_outcome1_value']?.toDouble() ?? 100.0;
    _option1Outcome1Probability = widget.data['option1_outcome1_probability']?.toDouble() ?? 0.7;
    _option1Outcome2Value = widget.data['option1_outcome2_value']?.toDouble() ?? 20.0;
    _option1Outcome2Probability = widget.data['option1_outcome2_probability']?.toDouble() ?? 0.3;

    _option2Outcome1Value = widget.data['option2_outcome1_value']?.toDouble() ?? 150.0;
    _option2Outcome1Probability = widget.data['option2_outcome1_probability']?.toDouble() ?? 0.4;
    _option2Outcome2Value = widget.data['option2_outcome2_value']?.toDouble() ?? 50.0;
    _option2Outcome2Probability = widget.data['option2_outcome2_probability']?.toDouble() ?? 0.6;

    _primaryColor = _parseColor(widget.data['primary_color']) ?? Colors.blue;
    _secondaryColor = _parseColor(widget.data['secondary_color']) ?? Colors.orange;
    _backgroundColor = _parseColor(widget.data['background_color']) ?? Colors.white;
    _textColor = _parseColor(widget.data['text_color']) ?? Colors.black87;

    _calculateExpectedValues();
  }

  Color? _parseColor(String? hexString) {
    if (hexString == null) return null;
    hexString = hexString.replaceAll('#', '');
    if (hexString.length == 6) {
      hexString = 'FF$hexString';
    }
    try {
      return Color(int.parse(hexString, radix: 16));
    } catch (e) {
      return null;
    }
  }

  void _calculateExpectedValues() {
    setState(() {
      _expectedValueOption1 = (_option1Outcome1Value * _option1Outcome1Probability) +
                              (_option1Outcome2Value * _option1Outcome2Probability);
      _expectedValueOption2 = (_option2Outcome1Value * _option2Outcome1Probability) +
                              (_option2Outcome2Value * _option2Outcome2Probability);
    });
  }

  Widget _buildOptionControls(String title, double outcome1Value, double outcome1Prob, double outcome2Value, double outcome2Prob, Function(double) onUpdateOutcome1Value, Function(double) onUpdateOutcome1Prob, Function(double) onUpdateOutcome2Value, Function(double) onUpdateOutcome2Prob, Color color) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        const SizedBox(height: 8),
        Text('Outcome 1 Value: ${outcome1Value.toStringAsFixed(1)}', style: TextStyle(color: _textColor)),
        Slider(
          value: outcome1Value,
          min: 0, max: 200, divisions: 200,
          label: outcome1Value.toStringAsFixed(1),
          activeColor: color,
          onChanged: onUpdateOutcome1Value,
        ),
        Text('Outcome 1 Probability: ${outcome1Prob.toStringAsFixed(2)}', style: TextStyle(color: _textColor)),
        Slider(
          value: outcome1Prob,
          min: 0, max: 1, divisions: 100,
          label: outcome1Prob.toStringAsFixed(2),
          activeColor: color,
          onChanged: (value) {
            onUpdateOutcome1Prob(value);
            onUpdateOutcome2Prob(1.0 - value); // Ensure probabilities sum to 1
          },
        ),
        Text('Outcome 2 Value: ${outcome2Value.toStringAsFixed(1)}', style: TextStyle(color: _textColor)),
        Slider(
          value: outcome2Value,
          min: 0, max: 200, divisions: 200,
          label: outcome2Value.toStringAsFixed(1),
          activeColor: color,
          onChanged: onUpdateOutcome2Value,
        ),
        Text('Outcome 2 Probability: ${outcome2Prob.toStringAsFixed(2)}', style: TextStyle(color: _textColor)),
        Slider(
          value: outcome2Prob,
          min: 0, max: 1, divisions: 100,
          label: outcome2Prob.toStringAsFixed(2),
          activeColor: color,
          onChanged: (value) {
            onUpdateOutcome2Prob(value);
            onUpdateOutcome1Prob(1.0 - value); // Ensure probabilities sum to 1
          },
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _backgroundColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.data['title'] ?? 'Decision Making Simulator',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),
          const SizedBox(height: 8),
          if (widget.data['description'] != null)
            Text(
              widget.data['description'],
              style: TextStyle(
                fontSize: 14,
                color: _textColor.withOpacity(0.8),
              ),
            ),
          const SizedBox(height: 16),

          _buildOptionControls(
            'Option 1',
            _option1Outcome1Value, _option1Outcome1Probability,
            _option1Outcome2Value, _option1Outcome2Probability,
            (value) => setState(() { _option1Outcome1Value = value; _calculateExpectedValues(); }),
            (value) => setState(() { _option1Outcome1Probability = value; _calculateExpectedValues(); }),
            (value) => setState(() { _option1Outcome2Value = value; _calculateExpectedValues(); }),
            (value) => setState(() { _option1Outcome2Probability = value; _calculateExpectedValues(); }),
            _primaryColor,
          ),
          const SizedBox(height: 16),

          _buildOptionControls(
            'Option 2',
            _option2Outcome1Value, _option2Outcome1Probability,
            _option2Outcome2Value, _option2Outcome2Probability,
            (value) => setState(() { _option2Outcome1Value = value; _calculateExpectedValues(); }),
            (value) => setState(() { _option2Outcome1Probability = value; _calculateExpectedValues(); }),
            (value) => setState(() { _option2Outcome2Value = value; _calculateExpectedValues(); }),
            (value) => setState(() { _option2Outcome2Probability = value; _calculateExpectedValues(); }),
            _secondaryColor,
          ),
          const SizedBox(height: 16),

          Center(
            child: Column(
              children: [
                Text(
                  'Expected Value Option 1: ${_expectedValueOption1.toStringAsFixed(2)}',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: _primaryColor,
                  ),
                ),
                Text(
                  'Expected Value Option 2: ${_expectedValueOption2.toStringAsFixed(2)}',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: _secondaryColor,
                  ),
                ),
                const SizedBox(height: 16),
                ElevatedButton.icon(
                  onPressed: _calculateExpectedValues,
                  icon: const Icon(Icons.calculate),
                  label: const Text('Recalculate Expected Values'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _primaryColor,
                    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),

          Container(
            height: 150,
            child: CustomPaint(
              painter: ExpectedValueBarPainter(
                expectedValue1: _expectedValueOption1,
                expectedValue2: _expectedValueOption2,
                color1: _primaryColor,
                color2: _secondaryColor,
                textColor: _textColor,
              ),
              child: Container(),
            ),
          ),

          // Widget name tag
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'InteractiveDecisionMakingSimulator',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }
}

class ExpectedValueBarPainter extends CustomPainter {
  final double expectedValue1;
  final double expectedValue2;
  final Color color1;
  final Color color2;
  final Color textColor;

  ExpectedValueBarPainter({
    required this.expectedValue1,
    required this.expectedValue2,
    required this.color1,
    required this.color2,
    required this.textColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint();
    final textPainter = TextPainter(textDirection: TextDirection.ltr);
    final textStyle = TextStyle(color: textColor, fontSize: 12);

    final double maxValue = math.max(expectedValue1.abs(), expectedValue2.abs()) * 1.2;
    if (maxValue == 0) return;

    final double barWidth = size.width / 3; // Two bars + space
    final double zeroY = size.height / 2; // Y-axis for zero

    // Draw zero line
    paint.color = Colors.grey;
    paint.strokeWidth = 1.0;
    canvas.drawLine(Offset(0, zeroY), Offset(size.width, zeroY), paint);

    // Draw bar for Option 1
    final bar1Height = (expectedValue1 / maxValue) * (size.height / 2 - 10);
    paint.color = color1.withOpacity(0.7);
    canvas.drawRect(
      Rect.fromLTWH(
        barWidth / 2,
        expectedValue1 >= 0 ? zeroY - bar1Height : zeroY,
        barWidth,
        bar1Height.abs(),
      ),
      paint,
    );
    paint.color = color1;
    paint.style = PaintingStyle.stroke;
    canvas.drawRect(
      Rect.fromLTWH(
        barWidth / 2,
        expectedValue1 >= 0 ? zeroY - bar1Height : zeroY,
        barWidth,
        bar1Height.abs(),
      ),
      paint,
    );

    // Draw label for Option 1
    textPainter.text = TextSpan(text: 'Option 1', style: textStyle);
    textPainter.layout();
    textPainter.paint(canvas, Offset(barWidth / 2 + barWidth / 2 - textPainter.width / 2, size.height - textPainter.height));

    // Draw bar for Option 2
    final bar2Height = (expectedValue2 / maxValue) * (size.height / 2 - 10);
    paint.color = color2.withOpacity(0.7);
    paint.style = PaintingStyle.fill;
    canvas.drawRect(
      Rect.fromLTWH(
        barWidth / 2 + barWidth + barWidth / 2,
        expectedValue2 >= 0 ? zeroY - bar2Height : zeroY,
        barWidth,
        bar2Height.abs(),
      ),
      paint,
    );
    paint.color = color2;
    paint.style = PaintingStyle.stroke;
    canvas.drawRect(
      Rect.fromLTWH(
        barWidth / 2 + barWidth + barWidth / 2,
        expectedValue2 >= 0 ? zeroY - bar2Height : zeroY,
        barWidth,
        bar2Height.abs(),
      ),
      paint,
    );

    // Draw label for Option 2
    textPainter.text = TextSpan(text: 'Option 2', style: textStyle);
    textPainter.layout();
    textPainter.paint(canvas, Offset(barWidth / 2 + barWidth + barWidth / 2 + barWidth / 2 - textPainter.width / 2, size.height - textPainter.height));
  }

  @override
  bool shouldRepaint(covariant ExpectedValueBarPainter oldDelegate) {
    return oldDelegate.expectedValue1 != expectedValue1 ||
           oldDelegate.expectedValue2 != expectedValue2;
  }
}
