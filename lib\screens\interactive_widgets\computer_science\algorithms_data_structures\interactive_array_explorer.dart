import 'package:flutter/material.dart';
import 'package:resonance_app/models/interactive_widget_model.dart';

class InteractiveArrayExplorer extends StatefulWidget {
  final InteractiveWidgetModel widgetModel;

  const InteractiveArrayExplorer({Key? key, required this.widgetModel}) : super(key: key);

  @override
  _InteractiveArrayExplorerState createState() => _InteractiveArrayExplorerState();
}

class _InteractiveArrayExplorerState extends State<InteractiveArrayExplorer> {
  List<int> _array = [10, 20, 30, 40, 50];
  int? _selectedIndex;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.widgetModel.name,
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 10),
          Text(
            widget.widgetModel.description,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          const SizedBox(height: 20),
          _buildArrayVisualizer(),
          const SizedBox(height: 20),
          _buildControls(),
        ],
      ),
    );
  }

  Widget _buildArrayVisualizer() {
    return Container(
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('Array Elements:', style: TextStyle(fontWeight: FontWeight.bold)),
          const SizedBox(height: 10),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: _array.asMap().entries.map((entry) {
                int index = entry.key;
                int value = entry.value;
                bool isSelected = _selectedIndex == index;
                return GestureDetector(
                  onTap: () {
                    setState(() {
                      _selectedIndex = isSelected ? null : index;
                    });
                  },
                  child: Container(
                    margin: const EdgeInsets.symmetric(horizontal: 5),
                    padding: const EdgeInsets.all(15),
                    decoration: BoxDecoration(
                      color: isSelected ? Colors.blueAccent.withOpacity(0.7) : Colors.blueGrey.withOpacity(0.3),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: isSelected ? Colors.blueAccent : Colors.blueGrey),
                    ),
                    child: Column(
                      children: [
                        Text('Index: $index', style: const TextStyle(fontSize: 12)),
                        Text('Value: $value', style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
                      ],
                    ),
                  ),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildControls() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('Selected Element Details:', style: TextStyle(fontWeight: FontWeight.bold)),
        const SizedBox(height: 10),
        if (_selectedIndex != null)
          Text('Index: $_selectedIndex, Value: ${_array[_selectedIndex!]}')
        else
          const Text('No element selected.'),
        const SizedBox(height: 20),
        ElevatedButton(
          onPressed: () {
            setState(() {
              _array.add(_array.length * 10 + 10); // Add a new element
            });
          },
          child: const Text('Add Element'),
        ),
        const SizedBox(height: 10),
        ElevatedButton(
          onPressed: _selectedIndex != null
              ? () {
                  setState(() {
                    _array.removeAt(_selectedIndex!);
                    _selectedIndex = null; // Deselect after removal
                  });
                }
              : null,
          child: const Text('Remove Selected Element'),
        ),
      ],
    );
  }
}
