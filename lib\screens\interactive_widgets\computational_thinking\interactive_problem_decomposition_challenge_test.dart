import 'package:flutter/material.dart';

class InteractiveProblemDecompositionChallengeTest extends StatefulWidget {
  const InteractiveProblemDecompositionChallengeTest({super.key});

  @override
  State<InteractiveProblemDecompositionChallengeTest> createState() => _InteractiveProblemDecompositionChallengeTestState();
}

class _InteractiveProblemDecompositionChallengeTestState extends State<InteractiveProblemDecompositionChallengeTest> {
  int _currentQuestionIndex = 0;
  final TextEditingController _answerController = TextEditingController();
  String? _feedbackMessage;
  bool _isCorrect = false;

  final List<Map<String, dynamic>> _questions = [
    {
      'problem': 'Design a program to calculate the average of a list of numbers.',
      'correctDecomposition': [
        'Get the list of numbers as input',
        'Calculate the sum of the numbers',
        'Count how many numbers are in the list',
        'Divide the sum by the count to get the average',
        'Display the average'
      ],
      'hint': 'Think about the steps involved in calculating an average.',
    },
    {
      'problem': 'Organize a school fundraising event.',
      'correctDecomposition': [
        'Define fundraising goal and budget',
        'Choose event type and date',
        'Secure venue and necessary permits',
        'Promote the event to attract participants',
        'Manage logistics on event day',
        'Collect and count funds',
        'Thank participants and report results'
      ],
      'hint': 'Break down the event planning into logical phases.',
    },
    {
      'problem': 'Write a recipe for baking a cake.',
      'correctDecomposition': [
        'List all ingredients and their quantities',
        'Outline step-by-step instructions for mixing ingredients',
        'Specify baking temperature and time',
        'Provide instructions for cooling and decorating',
        'Include serving suggestions'
      ],
      'hint': 'Consider the sequence of actions and necessary components for baking.',
    },
  ];

  @override
  void initState() {
    super.initState();
    _answerController.addListener(_onAnswerChanged);
  }

  @override
  void dispose() {
    _answerController.removeListener(_onAnswerChanged);
    _answerController.dispose();
    super.dispose();
  }

  void _onAnswerChanged() {
    setState(() {
      _feedbackMessage = null; // Clear feedback when user types
    });
  }

  void _checkAnswer() {
    final currentQuestion = _questions[_currentQuestionIndex];
    List<String> userDecomposition = _answerController.text
        .split('\n')
        .map((s) => s.trim())
        .where((s) => s.isNotEmpty)
        .toList();

    // Simple check: compare sorted lists of subproblems
    bool match = _compareDecompositions(userDecomposition, currentQuestion['correctDecomposition']);

    setState(() {
      _isCorrect = match;
      if (_isCorrect) {
        _feedbackMessage = 'Correct! Your decomposition is logical.';
      } else {
        _feedbackMessage = 'Incorrect. Make sure you\'ve broken down the problem into clear, distinct steps.';
      }
    });
  }

  bool _compareDecompositions(List<String> user, List<String> correct) {
    if (user.length != correct.length) return false;
    List<String> sortedUser = List.from(user)..sort();
    List<String> sortedCorrect = List.from(correct)..sort();
    for (int i = 0; i < sortedUser.length; i++) {
      if (sortedUser[i].toLowerCase() != sortedCorrect[i].toLowerCase()) {
        return false;
      }
    }
    return true;
  }

  void _nextQuestion() {
    setState(() {
      if (_currentQuestionIndex < _questions.length - 1) {
        _currentQuestionIndex++;
        _answerController.clear();
        _feedbackMessage = null;
        _isCorrect = false;
      } else {
        _feedbackMessage = 'Test Completed! You\'ve finished the decomposition challenges.';
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final currentQuestion = _questions[_currentQuestionIndex];

    return Card(
      margin: const EdgeInsets.all(16.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Interactive Problem Decomposition Challenge (Module Test)',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            Text(
              'Problem: "${currentQuestion['problem']}"',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 10),
            const Text(
              'Decompose this problem into a list of smaller, manageable steps (one step per line):',
              style: TextStyle(fontSize: 14),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 10),
            TextField(
              controller: _answerController,
              maxLines: 8,
              decoration: InputDecoration(
                hintText: 'Enter steps here...',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 10),
            ElevatedButton(
              onPressed: _checkAnswer,
              child: const Text('Check Decomposition'),
            ),
            if (_feedbackMessage != null)
              Padding(
                padding: const EdgeInsets.all(10.0),
                child: Text(
                  _feedbackMessage!,
                  style: TextStyle(
                    color: _isCorrect ? Colors.green : Colors.red,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            const SizedBox(height: 10),
            ElevatedButton(
              onPressed: _nextQuestion,
              child: Text(_currentQuestionIndex < _questions.length - 1 ? 'Next Problem' : 'Finish Test'),
            ),
          ],
        ),
      ),
    );
  }
}
