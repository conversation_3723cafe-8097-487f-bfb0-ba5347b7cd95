import 'package:flutter/material.dart';
import 'dart:math';
import 'package:resonance/widgets/interactive_widget_scaffold.dart';

class InteractiveLightSpeedAdjuster extends StatefulWidget {
  const InteractiveLightSpeedAdjuster({super.key});

  @override
  State<InteractiveLightSpeedAdjuster> createState() =>
      _InteractiveLightSpeedAdjusterState();
}

class _InteractiveLightSpeedAdjusterState
    extends State<InteractiveLightSpeedAdjuster> with SingleTickerProviderStateMixin {
  final TextEditingController _lightSpeedController = TextEditingController(text: '299792458.0'); // m/s
  late AnimationController _animationController;
  late Animation<double> _animation;

  double _currentLightSpeed = 299792458.0; // m/s (speed of light in vacuum)
  double _objectDistance = 100.0; // meters
  double _objectSpeed = 0.0; // m/s

  double _perceivedTimeDelay = 0.0;
  double _visualDistortionFactor = 1.0;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 2),
    );
    _animation = Tween<double>(begin: 0.0, end: 1.0).animate(_animationController);

    _animationController.addListener(() {
      setState(() {
        _calculateEffects();
      });
    });
    _calculateEffects();
  }

  void _calculateEffects() {
    // Simplified effects:
    // Perceived time delay: distance / light_speed
    // Visual distortion: based on object speed relative to light speed (Lorentz factor)
    _perceivedTimeDelay = _objectDistance / _currentLightSpeed;

    // Lorentz factor: 1 / sqrt(1 - v^2/c^2)
    // If object speed is significant compared to light speed, distortion occurs.
    if (_objectSpeed >= _currentLightSpeed) {
      _visualDistortionFactor = 0.0; // Approaching infinite distortion
    } else {
      _visualDistortionFactor = sqrt(1 - (_objectSpeed * _objectSpeed) / (_currentLightSpeed * _currentLightSpeed));
    }
  }

  void _adjustSpeed() {
    setState(() {
      _currentLightSpeed = double.tryParse(_lightSpeedController.text) ?? _currentLightSpeed;
      _calculateEffects();
      _animationController.reset();
      _animationController.forward();
    });
  }

  @override
  void dispose() {
    _lightSpeedController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return InteractiveWidgetScaffold(
      title: 'Light Speed Adjuster',
      interactiveWidget: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            TextField(
              controller: _lightSpeedController,
              keyboardType: TextInputType.number,
              decoration: const InputDecoration(
                labelText: 'Light Speed (m/s)',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            Slider(
              value: _objectSpeed,
              min: 0.0,
              max: _currentLightSpeed * 0.99, // Max object speed close to light speed
              onChanged: (value) {
                setState(() {
                  _objectSpeed = value;
                  _calculateEffects();
                });
              },
              label: 'Object Speed: ${_objectSpeed.toStringAsFixed(0)} m/s',
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _adjustSpeed,
              child: const Text('Adjust Speed & Simulate'),
            ),
            const SizedBox(height: 16),
            Text(
              'Perceived Time Delay for 100m: ${_perceivedTimeDelay.toStringAsExponential(2)} seconds',
              style: const TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 8),
            Text(
              'Visual Distortion Factor (Lorentz): ${_visualDistortionFactor.toStringAsFixed(3)}',
              style: const TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: CustomPaint(
                painter: LightSpeedPainter(
                  animationValue: _animation.value,
                  lightSpeed: _currentLightSpeed,
                  objectSpeed: _objectSpeed,
                  distortionFactor: _visualDistortionFactor,
                ),
                child: Container(),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class LightSpeedPainter extends CustomPainter {
  final double animationValue;
  final double lightSpeed;
  final double objectSpeed;
  final double distortionFactor;

  LightSpeedPainter({
    required this.animationValue,
    required this.lightSpeed,
    required this.objectSpeed,
    required this.distortionFactor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final lightPaint = Paint()
      ..color = Colors.yellow
      ..style = PaintingStyle.fill;

    final objectPaint = Paint()
      ..color = Colors.blue
      ..style = PaintingStyle.fill;

    final textPainter = TextPainter(
      textDirection: TextDirection.ltr,
      textAlign: TextAlign.center,
    );

    // Simulate light traveling across the screen
    double lightX = size.width * animationValue;
    canvas.drawCircle(Offset(lightX, size.height / 4), 10.0, lightPaint);

    textPainter.text = TextSpan(
      text: 'Light Pulse',
      style: TextStyle(color: Colors.yellow[800], fontSize: 12),
    );
    textPainter.layout();
    textPainter.paint(canvas, Offset(lightX - textPainter.width / 2, size.height / 4 + 15));

    // Simulate object moving (with visual distortion effect)
    double objectX = size.width * animationValue * (objectSpeed / lightSpeed);
    double objectWidth = 20.0 * distortionFactor; // Simulate length contraction
    canvas.drawRect(
      Rect.fromCenter(
        center: Offset(objectX, size.height * 3 / 4),
        width: objectWidth,
        height: 20.0,
      ),
      objectPaint,
    );

    textPainter.text = TextSpan(
      text: 'Object',
      style: TextStyle(color: Colors.blue[800], fontSize: 12),
    );
    textPainter.layout();
    textPainter.paint(canvas, Offset(objectX - textPainter.width / 2, size.height * 3 / 4 + 15));

    // Draw a reference line
    final linePaint = Paint()
      ..color = Colors.grey
      ..strokeWidth = 1.0;
    canvas.drawLine(Offset(0, size.height / 2), Offset(size.width, size.height / 2), linePaint);
  }

  @override
  bool shouldRepaint(covariant LightSpeedPainter oldDelegate) {
    return oldDelegate.animationValue != animationValue ||
        oldDelegate.lightSpeed != lightSpeed ||
        oldDelegate.objectSpeed != objectSpeed ||
        oldDelegate.distortionFactor != distortionFactor;
  }
}
