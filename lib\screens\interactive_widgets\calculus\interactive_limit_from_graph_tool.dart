import 'package:flutter/material.dart';
import 'dart:math';

class InteractiveLimitFromGraphTool extends StatefulWidget {
  const InteractiveLimitFromGraphTool({super.key});

  @override
  State<InteractiveLimitFromGraphTool> createState() => _InteractiveLimitFromGraphToolState();
}

class _InteractiveLimitFromGraphToolState extends State<InteractiveLimitFromGraphTool> {
  double _xValue = 0.0;
  final double _targetX = 2.0; // The x-value to find the limit at

  double _function(double x) {
    // Example function with a hole at x=2: (x^2 - 4) / (x - 2)
    // which simplifies to x + 2 for x != 2
    if (x == _targetX) {
      return double.nan; // Represents a hole or discontinuity
    }
    return (x * x - 4) / (x - 2);
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Interactive Limit from Graph Tool',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 20),
            Text(
              'Function: f(x) = (x² - 4) / (x - 2)',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 10),
            Text(
              'Find the limit as x approaches $_targetX',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 20),
            Slider(
              value: _xValue,
              min: _targetX - 3.0,
              max: _targetX + 3.0,
              divisions: 100,
              onChanged: (newValue) {
                setState(() {
                  _xValue = newValue;
                });
              },
            ),
            const SizedBox(height: 10),
            Text(
              'Current x: ${_xValue.toStringAsFixed(3)}',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 10),
            Text(
              'f(${_xValue.toStringAsFixed(3)}): ${_function(_xValue).toStringAsFixed(3)}',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 20),
            CustomPaint(
              size: Size(300, 200),
              painter: _LimitFromGraphPainter(_function, _targetX, _xValue),
            ),
            const SizedBox(height: 20),
            Text(
              'As x approaches ${_targetX.toStringAsFixed(1)}, f(x) approaches ${(_targetX + 2).toStringAsFixed(1)}',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: Colors.purple),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

class _LimitFromGraphPainter extends CustomPainter {
  final Function(double) _function;
  final double _targetX;
  final double _currentX;

  _LimitFromGraphPainter(this._function, this._targetX, this._currentX);

  @override
  void paint(Canvas canvas, Size size) {
    final Paint paint = Paint()
      ..color = Colors.blue
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    final Paint pointPaint = Paint()
      ..color = Colors.red
      ..strokeWidth = 8
      ..style = PaintingStyle.fill;

    final Paint targetLinePaint = Paint()
      ..color = Colors.green
      ..strokeWidth = 1
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    final double minX = _targetX - 3.0;
    final double maxX = _targetX + 3.0;
    final double minY = _function(_targetX + 0.001) - 3.0; // Estimate y range
    final double maxY = _function(_targetX + 0.001) + 3.0;

    final double rangeX = maxX - minX;
    final double rangeY = maxY - minY;

    final double scaleX = size.width / rangeX;
    final double scaleY = size.height / rangeY;

    Offset toCanvas(double x, double y) {
      final double canvasX = (x - minX) * scaleX;
      final double canvasY = size.height - (y - minY) * scaleY;
      return Offset(canvasX, canvasY);
    }

    // Draw axes (simplified, assuming origin is not necessarily centered)
    canvas.drawLine(toCanvas(minX, 0), toCanvas(maxX, 0), paint..color = Colors.grey); // X-axis
    canvas.drawLine(toCanvas(0, minY), toCanvas(0, maxY), paint..color = Colors.grey); // Y-axis

    // Draw function graph
    final Path path = Path();
    bool firstPoint = true;
    for (double i = minX; i <= maxX; i += 0.05) {
      double x = i;
      double y = _function(x);

      if (!y.isNaN && y.isFinite) {
        if (firstPoint) {
          path.moveTo(toCanvas(x, y).dx, toCanvas(x, y).dy);
          firstPoint = false;
        } else {
          path.lineTo(toCanvas(x, y).dx, toCanvas(x, y).dy);
        }
      } else {
        firstPoint = true; // Reset for discontinuities
      }
    }
    canvas.drawPath(path, paint);

    // Draw hole at targetX if function is undefined there
    if (_function(_targetX).isNaN) {
      canvas.drawCircle(toCanvas(_targetX, _targetX + 2), 4, Paint()..color = Colors.orange..style = PaintingStyle.stroke..strokeWidth = 2);
    }

    // Draw target X line
    canvas.drawLine(toCanvas(_targetX, minY), toCanvas(_targetX, maxY), targetLinePaint);
    TextPainter(
      text: TextSpan(text: 'x=${_targetX.toStringAsFixed(1)}', style: TextStyle(color: Colors.green, fontSize: 10)),
      textDirection: TextDirection.ltr,
    )..layout()..paint(canvas, toCanvas(_targetX + 0.1, minY + 0.1));

    // Draw current x and f(x) point
    double currentY = _function(_currentX);
    if (!currentY.isNaN && currentY.isFinite) {
      canvas.drawCircle(toCanvas(_currentX, currentY), 4, pointPaint);
      // Draw lines to axes
      canvas.drawLine(toCanvas(_currentX, currentY), toCanvas(_currentX, 0), targetLinePaint..color = Colors.red.withOpacity(0.5));
      canvas.drawLine(toCanvas(_currentX, currentY), toCanvas(0, currentY), targetLinePaint..color = Colors.red.withOpacity(0.5));

      TextPainter(
        text: TextSpan(text: '(${_currentX.toStringAsFixed(2)}, ${currentY.toStringAsFixed(2)})', style: TextStyle(color: Colors.red, fontSize: 10)),
        textDirection: TextDirection.ltr,
      )..layout()..paint(canvas, toCanvas(_currentX + 0.1, currentY + 0.1));
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    _LimitFromGraphPainter oldPainter = oldDelegate as _LimitFromGraphPainter;
    return oldPainter._currentX != _currentX;
  }
}
