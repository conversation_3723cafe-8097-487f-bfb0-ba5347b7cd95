import 'package:flutter/material.dart';
import 'package:resonance_app/widgets/interactive_widget_scaffold.dart';

class InteractiveResistorCalculator extends StatefulWidget {
  const InteractiveResistorCalculator({super.key});

  @override
  State<InteractiveResistorCalculator> createState() => _InteractiveResistorCalculatorState();
}

class _InteractiveResistorCalculatorState extends State<InteractiveResistorCalculator> {
  double voltage = 0.0;
  double current = 0.0;
  double resistance = 0.0;

  void calculateResistance() {
    setState(() {
      if (current != 0) {
        resistance = voltage / current;
      } else {
        resistance = 0; // Avoid division by zero
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return InteractiveWidgetScaffold(
      title: 'Interactive Resistor Calculator',
      description: 'Calculate resistance using Ohm\'s Law (V = IR).',
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            TextField(
              keyboardType: TextInputType.number,
              decoration: const InputDecoration(
                labelText: 'Voltage (V)',
                border: OutlineInputBorder(),
              ),
              onChanged: (value) {
                setState(() {
                  voltage = double.tryParse(value) ?? 0.0;
                });
              },
            ),
            const SizedBox(height: 16.0),
            TextField(
              keyboardType: TextInputType.number,
              decoration: const InputDecoration(
                labelText: 'Current (A)',
                border: OutlineInputBorder(),
              ),
              onChanged: (value) {
                setState(() {
                  current = double.tryParse(value) ?? 0.0;
                });
              },
            ),
            const SizedBox(height: 24.0),
            ElevatedButton(
              onPressed: calculateResistance,
              child: const Text('Calculate Resistance'),
            ),
            const SizedBox(height: 24.0),
            Text(
              'Resistance (R): ${resistance.toStringAsFixed(2)} Ohms',
              style: Theme.of(context).textTheme.headlineSmall,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
