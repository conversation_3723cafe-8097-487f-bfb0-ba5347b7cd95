import 'package:flutter/material.dart';
import 'dart:math';

class InteractiveLimitDefinitionExplorer extends StatefulWidget {
  const InteractiveLimitDefinitionExplorer({super.key});

  @override
  State<InteractiveLimitDefinitionExplorer> createState() => _InteractiveLimitDefinitionExplorerState();
}

class _InteractiveLimitDefinitionExplorerState extends State<InteractiveLimitDefinitionExplorer> {
  double _epsilon = 0.5; // Epsilon value for the y-axis
  double _delta = 0.1; // Delta value for the x-axis (calculated)
  final double _limitL = 4.0; // The limit L
  final double _limitPointA = 2.0; // The point a (x approaches a)

  double _function(double x) {
    // Example function: f(x) = x + 2
    return x + 2;
  }

  @override
  void initState() {
    super.initState();
    _calculateDelta();
  }

  void _calculateDelta() {
    // For f(x) = x + 2, |f(x) - L| < epsilon becomes |(x + 2) - 4| < epsilon
    // |x - 2| < epsilon. So, delta = epsilon.
    // In a more complex function, delta would be derived from epsilon.
    // For simplicity, we'll set delta = epsilon for this linear function.
    setState(() {
      _delta = _epsilon;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Interactive Limit Definition Explorer (ε-δ)',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 20),
            Text(
              'Function: f(x) = x + 2',
              style: TextStyle(fontSize: 16),
            ),
            Text(
              'Limit L = $_limitL at x = $_limitPointA',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Text('ε (Epsilon):'),
                Slider(
                  value: _epsilon,
                  min: 0.01,
                  max: 1.0,
                  divisions: 99,
                  onChanged: (newValue) {
                    setState(() {
                      _epsilon = newValue;
                      _calculateDelta();
                    });
                  },
                ),
                Text(_epsilon.toStringAsFixed(2)),
              ],
            ),
            const SizedBox(height: 10),
            Text(
              'Calculated δ (Delta): ${_delta.toStringAsFixed(2)}',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 20),
            CustomPaint(
              size: Size(300, 250),
              painter: _LimitDefinitionGraphPainter(
                _function,
                _limitPointA,
                _limitL,
                _epsilon,
                _delta,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _LimitDefinitionGraphPainter extends CustomPainter {
  final Function(double) _function;
  final double _limitPointA;
  final double _limitL;
  final double _epsilon;
  final double _delta;

  _LimitDefinitionGraphPainter(
    this._function,
    this._limitPointA,
    this._limitL,
    this._epsilon,
    this._delta,
  );

  @override
  void paint(Canvas canvas, Size size) {
    final Paint paint = Paint()
      ..color = Colors.blue
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    final Paint limitLinePaint = Paint()
      ..color = Colors.green
      ..strokeWidth = 1
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    final Paint epsilonBandPaint = Paint()
      ..color = Colors.green.withOpacity(0.2)
      ..style = PaintingStyle.fill;

    final Paint deltaBandPaint = Paint()
      ..color = Colors.red.withOpacity(0.2)
      ..style = PaintingStyle.fill;

    // Define the visible range for the graph
    final double minX = _limitPointA - 3.0;
    final double maxX = _limitPointA + 3.0;
    final double minY = _limitL - 3.0;
    final double maxY = _limitL + 3.0;

    final double rangeX = maxX - minX;
    final double rangeY = maxY - minY;

    final double scaleX = size.width / rangeX;
    final double scaleY = size.height / rangeY;

    // Helper to convert math coordinates to canvas coordinates
    Offset toCanvas(double x, double y) {
      final double canvasX = (x - minX) * scaleX;
      final double canvasY = size.height - (y - minY) * scaleY;
      return Offset(canvasX, canvasY);
    }

    // Draw axes
    canvas.drawLine(toCanvas(minX, 0), toCanvas(maxX, 0), paint..color = Colors.grey); // X-axis
    canvas.drawLine(toCanvas(0, minY), toCanvas(0, maxY), paint..color = Colors.grey); // Y-axis

    // Draw function graph
    final Path path = Path();
    bool firstPoint = true;
    for (double i = minX; i <= maxX; i += 0.05) {
      double x = i;
      double y = _function(x);

      if (!y.isNaN && y.isFinite) {
        if (firstPoint) {
          path.moveTo(toCanvas(x, y).dx, toCanvas(x, y).dy);
          firstPoint = false;
        } else {
          path.lineTo(toCanvas(x, y).dx, toCanvas(x, y).dy);
        }
      } else {
        firstPoint = true; // Reset for discontinuities
      }
    }
    canvas.drawPath(path, paint);

    // Draw L (limit) line and epsilon band
    canvas.drawLine(toCanvas(minX, _limitL), toCanvas(maxX, _limitL), limitLinePaint);
    canvas.drawRect(
      Rect.fromPoints(
        toCanvas(minX, _limitL + _epsilon),
        toCanvas(maxX, _limitL - _epsilon),
      ),
      epsilonBandPaint,
    );
    TextPainter(
      text: TextSpan(text: 'L', style: TextStyle(color: Colors.green, fontSize: 12)),
      textDirection: TextDirection.ltr,
    )..layout()..paint(canvas, toCanvas(minX + 0.1, _limitL + 0.1));
    TextPainter(
      text: TextSpan(text: 'L+ε', style: TextStyle(color: Colors.green, fontSize: 10)),
      textDirection: TextDirection.ltr,
    )..layout()..paint(canvas, toCanvas(minX + 0.1, _limitL + _epsilon + 0.1));
    TextPainter(
      text: TextSpan(text: 'L-ε', style: TextStyle(color: Colors.green, fontSize: 10)),
      textDirection: TextDirection.ltr,
    )..layout()..paint(canvas, toCanvas(minX + 0.1, _limitL - _epsilon + 0.1));

    // Draw a (limit point on x-axis) line and delta band
    canvas.drawLine(toCanvas(_limitPointA, minY), toCanvas(_limitPointA, maxY), limitLinePaint..color = Colors.red);
    canvas.drawRect(
      Rect.fromPoints(
        toCanvas(_limitPointA - _delta, minY),
        toCanvas(_limitPointA + _delta, maxY),
      ),
      deltaBandPaint,
    );
    TextPainter(
      text: TextSpan(text: 'a', style: TextStyle(color: Colors.red, fontSize: 12)),
      textDirection: TextDirection.ltr,
    )..layout()..paint(canvas, toCanvas(_limitPointA + 0.1, minY + 0.1));
    TextPainter(
      text: TextSpan(text: 'a-δ', style: TextStyle(color: Colors.red, fontSize: 10)),
      textDirection: TextDirection.ltr,
    )..layout()..paint(canvas, toCanvas(_limitPointA - _delta + 0.1, minY + 0.1));
    TextPainter(
      text: TextSpan(text: 'a+δ', style: TextStyle(color: Colors.red, fontSize: 10)),
      textDirection: TextDirection.ltr,
    )..layout()..paint(canvas, toCanvas(_limitPointA + _delta + 0.1, minY + 0.1));
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    _LimitDefinitionGraphPainter oldPainter = oldDelegate as _LimitDefinitionGraphPainter;
    return oldPainter._epsilon != _epsilon || oldPainter._delta != _delta;
  }
}
