import 'package:flutter/material.dart';
import 'package:resonance/widgets/animated_button.dart';

class InteractiveFallaciousArgumentDetector extends StatefulWidget {
  const InteractiveFallaciousArgumentDetector({super.key});

  @override
  State<InteractiveFallaciousArgumentDetector> createState() => _InteractiveFallaciousArgumentDetectorState();
}

class _InteractiveFallaciousArgumentDetectorState extends State<InteractiveFallaciousArgumentDetector> {
  int _currentArgumentIndex = 0;
  bool? _isFallacious;
  String _feedback = '';
  int _score = 0;

  final List<Map<String, dynamic>> _arguments = [
    {
      'argument': 'If you don\'t support my proposal, you must be against progress.',
      'isFallacious': true,
      'fallacyType': 'False Dilemma',
      'explanation': 'This is a False Dilemma because it presents only two extreme options when other possibilities exist.',
    },
    {
      'argument': 'All cats I have seen have four legs. Therefore, all cats have four legs.',
      'isFallacious': false,
      'fallacyType': 'None (Strong Inductive Argument)',
      'explanation': 'This is a strong inductive argument based on consistent observation. While not deductively certain, it\'s a reasonable generalization.',
    },
    {
      'argument': 'My opponent\'s plan for the economy is terrible; he was caught speeding last year.',
      'isFallacious': true,
      'fallacyType': 'Ad Hominem',
      'explanation': 'This is an Ad Hominem fallacy, attacking the person\'s character (speeding) instead of addressing the merits of their economic plan.',
    },
    {
      'argument': 'We should not allow children to play video games. If they play video games, they will become violent, and then society will collapse.',
      'isFallacious': true,
      'fallacyType': 'Slippery Slope',
      'explanation': 'This is a Slippery Slope fallacy, claiming that one action will inevitably lead to a series of increasingly negative consequences without sufficient evidence.',
    },
    {
      'argument': 'The new movie is the best because everyone is saying it\'s the best.',
      'isFallacious': true,
      'fallacyType': 'Bandwagon (Ad Populum)',
      'explanation': 'This is a Bandwagon fallacy, arguing that something is true or good because many people believe it or do it.',
    },
  ];

  void _checkAnswer() {
    if (_isFallacious == null) {
      setState(() {
        _feedback = 'Please select whether the argument is fallacious or not.';
      });
      return;
    }

    if (_isFallacious == _arguments[_currentArgumentIndex]['isFallacious']) {
      setState(() {
        _feedback = 'Correct! Fallacy Type: ${_arguments[_currentArgumentIndex]['fallacyType']}. ${_arguments[_currentArgumentIndex]['explanation']}';
        _score++;
      });
    } else {
      setState(() {
        _feedback = 'Incorrect. This argument is ${_arguments[_currentArgumentIndex]['isFallacious']! ? 'fallacious' : 'not fallacious'}. Fallacy Type: ${_arguments[_currentArgumentIndex]['fallacyType']}. ${_arguments[_currentArgumentIndex]['explanation']}';
      });
    }
  }

  void _nextArgument() {
    setState(() {
      if (_currentArgumentIndex < _arguments.length - 1) {
        _currentArgumentIndex++;
        _isFallacious = null;
        _feedback = '';
      } else {
        _feedback = 'Challenge Completed! Your score: $_score / ${_arguments.length}';
      }
    });
  }

  void _resetChallenge() {
    setState(() {
      _currentArgumentIndex = 0;
      _isFallacious = null;
      _feedback = '';
      _score = 0;
    });
  }

  @override
  Widget build(BuildContext context) {
    final currentArgument = _arguments[_currentArgumentIndex];

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text(
            'Fallacious Argument Detector',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.primary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 20),
          Card(
            elevation: 4,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            color: Theme.of(context).colorScheme.surfaceVariant,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Argument to Analyze:',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                  const SizedBox(height: 10),
                  Text(
                    currentArgument['argument'],
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                  const SizedBox(height: 15),
                  Text(
                    'Is this argument fallacious?',
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                  RadioListTile<bool>(
                    title: Text(
                      'Yes, it is fallacious',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                    value: true,
                    groupValue: _isFallacious,
                    onChanged: (value) {
                      setState(() {
                        _isFallacious = value;
                        _feedback = '';
                      });
                    },
                    activeColor: Theme.of(context).colorScheme.secondary,
                  ),
                  RadioListTile<bool>(
                    title: Text(
                      'No, it is not fallacious',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                    value: false,
                    groupValue: _isFallacious,
                    onChanged: (value) {
                      setState(() {
                        _isFallacious = value;
                        _feedback = '';
                      });
                    },
                    activeColor: Theme.of(context).colorScheme.secondary,
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 20),
          AnimatedButton(
            onTap: _checkAnswer,
            label: 'Check Answer',
            color: Theme.of(context).colorScheme.secondary,
            labelColor: Theme.of(context).colorScheme.onSecondary,
          ),
          if (_feedback.isNotEmpty) ...[
            const SizedBox(height: 10),
            Text(
              _feedback,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: _feedback.startsWith('Correct') ? Colors.green : Colors.red,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
          ],
          const SizedBox(height: 20),
          AnimatedButton(
            onTap: _nextArgument,
            label: _currentArgumentIndex < _arguments.length - 1 ? 'Next Argument' : 'Finish Challenge',
            color: Theme.of(context).colorScheme.tertiary,
            labelColor: Theme.of(context).colorScheme.onTertiary,
          ),
          if (_currentArgumentIndex == _arguments.length - 1 && _feedback.contains('Completed'))
            Padding(
              padding: const EdgeInsets.only(top: 20.0),
              child: AnimatedButton(
                onTap: _resetChallenge,
                label: 'Restart Challenge',
                color: Theme.of(context).colorScheme.primary,
                labelColor: Theme.of(context).colorScheme.onPrimary,
              ),
            ),
        ],
      ),
    );
  }
}
