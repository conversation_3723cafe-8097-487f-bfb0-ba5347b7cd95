import 'package:flutter/material.dart';
import '../../services/service_provider.dart';
import '../../services/course_service.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({Key? key}) : super(key: key);

  @override
  _SettingsScreenState createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  @override
  Widget build(BuildContext context) {
    final courseService = ServiceProvider.of(context).courseService;
    final user = courseService.currentUser;
    final isGuest = user?.isGuest ?? true;

    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Account Section
            _buildSectionHeader('Account'),

            // User Profile Card
            Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  // User Avatar
                  CircleAvatar(
                    radius: 30,
                    backgroundColor: const Color.fromRGBO(124, 66, 210, 0.2),
                    child: Icon(
                      Icons.person,
                      size: 30,
                      color: const Color.fromRGBO(124, 66, 210, 1),
                    ),
                  ),

                  const SizedBox(width: 16),

                  // User Info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          isGuest ? 'Guest User' : (user?.name ?? 'User'),
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                            fontFamily: 'WorkSans',
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          isGuest
                              ? 'Sign in to sync your progress'
                              : (user?.email ?? 'No email'),
                          style: TextStyle(
                            color: Colors.grey[700],
                            fontSize: 14,
                            fontFamily: 'WorkSans',
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Edit Button
                  IconButton(
                    onPressed: () {
                      if (isGuest) {
                        _showSignInPrompt(context);
                      } else {
                        // TODO: Navigate to profile edit screen
                      }
                    },
                    icon: const Icon(
                      Icons.edit_outlined,
                      color: Color.fromRGBO(124, 66, 210, 1),
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 8),

            // Account Settings
            _buildSettingItem(
              icon: Icons.person_outline,
              title: isGuest ? 'Sign In / Create Account' : 'Account Settings',
              onTap: () {
                if (isGuest) {
                  _showSignInPrompt(context);
                } else {
                  // TODO: Navigate to account settings
                }
              },
            ),

            if (!isGuest)
              _buildSettingItem(
                icon: Icons.logout,
                title: 'Sign Out',
                onTap: () {
                  // TODO: Implement sign out
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text(
                        'Sign out functionality not implemented in demo',
                      ),
                      duration: Duration(seconds: 2),
                    ),
                  );
                },
              ),

            const SizedBox(height: 24),

            // Preferences Section
            _buildSectionHeader('Preferences'),

            _buildSettingItem(
              icon: Icons.notifications_outlined,
              title: 'Notifications',
              onTap: () {
                // TODO: Navigate to notifications settings
              },
            ),

            _buildSettingItem(
              icon: Icons.language_outlined,
              title: 'Language',
              subtitle: 'English',
              onTap: () {
                // TODO: Show language selection
              },
            ),

            _buildSettingItem(
              icon: Icons.dark_mode_outlined,
              title: 'Dark Mode',
              trailing: Switch(
                value: false,
                onChanged: (value) {
                  // TODO: Implement dark mode toggle
                },
                activeColor: const Color.fromRGBO(124, 66, 210, 1),
              ),
              onTap: () {},
            ),

            const SizedBox(height: 24),

            // Learning Section
            _buildSectionHeader('Learning'),

            _buildSettingItem(
              icon: Icons.timer_outlined,
              title: 'Daily Goal',
              subtitle: '${user?.dailyGoalMinutes ?? 15} minutes',
              onTap: () {
                // Show daily goal selection dialog
                _showDailyGoalSelection(context, courseService);
              },
            ),

            _buildSettingItem(
              icon: Icons.download_outlined,
              title: 'Downloads',
              onTap: () {
                // TODO: Navigate to downloads screen
              },
            ),

            _buildSettingItem(
              icon: Icons.delete_outline,
              title: 'Clear Learning Data',
              onTap: () {
                // Show confirmation dialog
                showDialog(
                  context: context,
                  builder:
                      (context) => AlertDialog(
                        title: const Text('Clear Learning Data'),
                        content: const Text(
                          'This will reset all your progress and learning data. This action cannot be undone.',
                          style: TextStyle(fontFamily: 'WorkSans'),
                        ),
                        actions: [
                          TextButton(
                            onPressed: () => Navigator.pop(context),
                            child: const Text('Cancel'),
                          ),
                          ElevatedButton(
                            onPressed: () {
                              Navigator.pop(context);
                              // TODO: Implement clear data
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text('Learning data cleared'),
                                  duration: Duration(seconds: 2),
                                ),
                              );
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.red,
                            ),
                            child: const Text('Clear Data'),
                          ),
                        ],
                      ),
                );
              },
            ),

            const SizedBox(height: 24),

            // Support Section
            _buildSectionHeader('Support'),

            _buildSettingItem(
              icon: Icons.help_outline,
              title: 'Help Center',
              onTap: () {
                // TODO: Navigate to help center
              },
            ),

            _buildSettingItem(
              icon: Icons.feedback_outlined,
              title: 'Send Feedback',
              onTap: () {
                // TODO: Show feedback form
              },
            ),

            _buildSettingItem(
              icon: Icons.privacy_tip_outlined,
              title: 'Privacy Policy',
              onTap: () {
                // TODO: Show privacy policy
              },
            ),

            _buildSettingItem(
              icon: Icons.description_outlined,
              title: 'Terms of Service',
              onTap: () {
                // TODO: Show terms of service
              },
            ),

            const SizedBox(height: 24),

            // App Info
            Center(
              child: Column(
                children: [
                  const Text(
                    'Resonance',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      fontFamily: 'WorkSans',
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Version 1.0.0',
                    style: TextStyle(
                      color: Colors.grey[700],
                      fontSize: 14,
                      fontFamily: 'WorkSans',
                    ),
                  ),
                  const SizedBox(height: 16),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Build section header
  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          fontFamily: 'WorkSans',
          color: Color.fromRGBO(124, 66, 210, 1),
        ),
      ),
    );
  }

  // Build setting item
  Widget _buildSettingItem({
    required IconData icon,
    required String title,
    String? subtitle,
    Widget? trailing,
    required VoidCallback onTap,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListTile(
        leading: Icon(icon, color: const Color.fromRGBO(124, 66, 210, 1)),
        title: Text(
          title,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            fontFamily: 'WorkSans',
          ),
        ),
        subtitle:
            subtitle != null
                ? Text(
                  subtitle,
                  style: TextStyle(
                    color: Colors.grey[700],
                    fontSize: 14,
                    fontFamily: 'WorkSans',
                  ),
                )
                : null,
        trailing: trailing ?? const Icon(Icons.arrow_forward_ios, size: 16),
        onTap: onTap,
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }

  // Show daily goal selection dialog
  void _showDailyGoalSelection(
    BuildContext context,
    CourseService courseService,
  ) {
    final user = courseService.currentUser;
    final currentGoal = user?.dailyGoalMinutes ?? 15;
    int selectedGoal = currentGoal;

    showDialog(
      context: context,
      builder:
          (context) => StatefulBuilder(
            builder: (context, setState) {
              return AlertDialog(
                title: const Text('Set Daily Goal'),
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Text(
                      'How many minutes would you like to study each day?',
                      style: TextStyle(fontFamily: 'WorkSans'),
                    ),
                    const SizedBox(height: 24),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        IconButton(
                          onPressed:
                              selectedGoal <= 5
                                  ? null
                                  : () {
                                    setState(() {
                                      selectedGoal = selectedGoal - 5;
                                    });
                                  },
                          icon: const Icon(Icons.remove_circle_outline),
                          color: const Color.fromRGBO(124, 66, 210, 1),
                        ),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 8,
                          ),
                          decoration: BoxDecoration(
                            color: const Color.fromRGBO(124, 66, 210, 0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            '$selectedGoal minutes',
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.w600,
                              fontFamily: 'WorkSans',
                            ),
                          ),
                        ),
                        IconButton(
                          onPressed: () {
                            setState(() {
                              selectedGoal = selectedGoal + 5;
                            });
                          },
                          icon: const Icon(Icons.add_circle_outline),
                          color: const Color.fromRGBO(124, 66, 210, 1),
                        ),
                      ],
                    ),
                  ],
                ),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: const Text('Cancel'),
                  ),
                  ElevatedButton(
                    onPressed: () {
                      // Update daily goal
                      courseService.updateDailyGoalMinutes(selectedGoal);
                      Navigator.pop(context);

                      // Refresh the settings screen
                      _SettingsScreenState state =
                          context
                              .findAncestorStateOfType<_SettingsScreenState>()!;
                      state.setState(() {});

                      // Show confirmation
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(
                            'Daily goal updated to $selectedGoal minutes',
                          ),
                          duration: const Duration(seconds: 2),
                        ),
                      );
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color.fromRGBO(124, 66, 210, 1),
                    ),
                    child: const Text('Save'),
                  ),
                ],
              );
            },
          ),
    );
  }

  // Show sign-in prompt for guest users
  void _showSignInPrompt(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Sign In'),
            content: const Text(
              'Sign in to sync your progress across devices and access premium features.',
              style: TextStyle(fontFamily: 'WorkSans'),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  // TODO: Navigate to sign-in screen
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color.fromRGBO(124, 66, 210, 1),
                ),
                child: const Text('Sign In'),
              ),
            ],
          ),
    );
  }
}
