import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:resonance_app/services/interactive_widget_service.dart';
import 'package:resonance_app/widgets/animated_button.dart';
import 'dart:math';

class InteractiveHeapOperationsSimulator extends StatefulWidget {
  final String widgetId;

  const InteractiveHeapOperationsSimulator({Key? key, required this.widgetId}) : super(key: key);

  @override
  _InteractiveHeapOperationsSimulatorState createState() => _InteractiveHeapOperationsSimulatorState();
}

class _InteractiveHeapOperationsSimulatorState extends State<InteractiveHeapOperationsSimulator> {
  List<int> _heap = [];
  TextEditingController _insertController = TextEditingController();
  String _message = '';
  bool _isMinHeap = true; // True for Min-Heap, False for Max-Heap

  @override
  void initState() {
    super.initState();
    _loadState();
    _buildInitialHeap();
  }

  void _loadState() async {
    final service = Provider.of<InteractiveWidgetService>(context, listen: false);
    final savedState = await service.getWidgetState(widget.widgetId);
    if (savedState != null) {
      setState(() {
        _heap = List<int>.from(savedState['heap'] ?? []);
        _message = savedState['message'] ?? '';
        _isMinHeap = savedState['isMinHeap'] ?? true;
      });
    }
  }

  void _saveState() {
    final service = Provider.of<InteractiveWidgetService>(context, listen: false);
    service.saveWidgetState(widget.widgetId, {
      'heap': _heap,
      'message': _message,
      'isMinHeap': _isMinHeap,
    });
  }

  void _buildInitialHeap() {
    setState(() {
      _heap = [];
      _insertHeap(10);
      _insertHeap(20);
      _insertHeap(15);
      _insertHeap(40);
      _insertHeap(50);
      _insertHeap(100);
      _insertHeap(25);
      _insertHeap(45);
      _message = 'Initial heap built.';
    });
    _saveState();
  }

  void _insertHeap(int value) {
    _heap.add(value);
    _heapifyUp(_heap.length - 1);
  }

  void _heapifyUp(int index) {
    int parentIndex = (index - 1) ~/ 2;
    while (index > 0 && _compare(_heap[index], _heap[parentIndex])) {
      _swap(index, parentIndex);
      index = parentIndex;
      parentIndex = (index - 1) ~/ 2;
    }
  }

  int _extractMinMax() {
    if (_heap.isEmpty) {
      setState(() {
        _message = 'Heap is empty.';
      });
      return -1;
    }
    int rootValue = _heap[0];
    _heap[0] = _heap.last;
    _heap.removeLast();
    if (_heap.isNotEmpty) {
      _heapifyDown(0);
    }
    setState(() {
      _message = 'Extracted ${rootValue}.';
    });
    _saveState();
    return rootValue;
  }

  void _heapifyDown(int index) {
    int leftChild = 2 * index + 1;
    int rightChild = 2 * index + 2;
    int minMaxIndex = index;

    if (leftChild < _heap.length && _compare(_heap[leftChild], _heap[minMaxIndex])) {
      minMaxIndex = leftChild;
    }
    if (rightChild < _heap.length && _compare(_heap[rightChild], _heap[minMaxIndex])) {
      minMaxIndex = rightChild;
    }

    if (minMaxIndex != index) {
      _swap(index, minMaxIndex);
      _heapifyDown(minMaxIndex);
    }
  }

  void _swap(int i, int j) {
    int temp = _heap[i];
    _heap[i] = _heap[j];
    _heap[j] = temp;
  }

  bool _compare(int a, int b) {
    return _isMinHeap ? a < b : a > b;
  }

  void _handleInsert() {
    final value = int.tryParse(_insertController.text);
    if (value != null) {
      setState(() {
        _insertHeap(value);
        _message = 'Inserted $value into the heap.';
      });
      _insertController.clear();
      _saveState();
    } else {
      setState(() {
        _message = 'Please enter a valid number to insert.';
      });
    }
  }

  void _handleExtract() {
    setState(() {
      _extractMinMax();
    });
  }

  void _toggleHeapType() {
    setState(() {
      _isMinHeap = !_isMinHeap;
      _message = _isMinHeap ? 'Switched to Min-Heap.' : 'Switched to Max-Heap.';
      // Rebuild heap to satisfy new property
      List<int> tempHeap = List.from(_heap);
      _heap = [];
      for (var val in tempHeap) {
        _insertHeap(val);
      }
    });
    _saveState();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Interactive Heap Operations Simulator (${_isMinHeap ? 'Min-Heap' : 'Max-Heap'})',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _insertController,
                  keyboardType: TextInputType.number,
                  decoration: const InputDecoration(
                    labelText: 'Insert Value',
                    border: OutlineInputBorder(),
                  ),
                ),
              ),
              const SizedBox(width: 10),
              AnimatedButton(
                onTap: _handleInsert,
                text: 'Insert',
                color: Colors.blue,
              ),
            ],
          ),
          const SizedBox(height: 10),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              AnimatedButton(
                onTap: _handleExtract,
                text: _isMinHeap ? 'Extract Min' : 'Extract Max',
                color: Colors.green,
              ),
              AnimatedButton(
                onTap: _toggleHeapType,
                text: _isMinHeap ? 'Switch to Max-Heap' : 'Switch to Min-Heap',
                color: Colors.orange,
              ),
              AnimatedButton(
                onTap: _buildInitialHeap,
                text: 'Reset Heap',
                color: Colors.red,
              ),
            ],
          ),
          const SizedBox(height: 20),
          Text(
            'Heap Array: ${_heap.join(', ')}',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 20),
          Text(
            'Message: $_message',
            style: Theme.of(context).textTheme.bodyLarge,
          ),
          const SizedBox(height: 20),
          Expanded(
            child: SingleChildScrollView(
              child: HeapView(heap: _heap),
            ),
          ),
        ],
      ),
    );
  }
}

class HeapView extends StatelessWidget {
  final List<int> heap;

  const HeapView({Key? key, required this.heap}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (heap.isEmpty) {
      return const Center(child: Text('Heap is empty.'));
    }
    return LayoutBuilder(
      builder: (context, constraints) {
        return CustomPaint(
          painter: HeapPainter(heap),
          size: Size(constraints.maxWidth, _calculateHeapHeight(heap.length) * 50.0 + 50),
        );
      },
    );
  }

  double _calculateHeapHeight(int size) {
    if (size == 0) return 0;
    return (log(size) / log(2)).ceilToDouble();
  }
}

class HeapPainter extends CustomPainter {
  final List<int> heap;
  final double nodeRadius = 15;
  final double verticalSpacing = 50;

  HeapPainter(this.heap);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.black
      ..strokeWidth = 2;

    if (heap.isEmpty) return;

    _drawNode(canvas, heap, 0, size.width / 2, nodeRadius + 10, size.width / 4);
  }

  void _drawNode(Canvas canvas, List<int> heap, int index, double x, double y, double horizontalOffset) {
    // Draw node circle
    canvas.drawCircle(Offset(x, y), nodeRadius, Paint()..color = Colors.blue);
    // Draw node text
    TextPainter textPainter = TextPainter(
      text: TextSpan(
        text: heap[index].toString(),
        style: const TextStyle(color: Colors.white, fontSize: 12, fontWeight: FontWeight.bold),
      ),
      textDirection: TextDirection.ltr,
    );
    textPainter.layout();
    textPainter.paint(canvas, Offset(x - textPainter.width / 2, y - textPainter.height / 2));

    int leftChildIndex = 2 * index + 1;
    int rightChildIndex = 2 * index + 2;

    // Draw lines to children
    if (leftChildIndex < heap.length) {
      canvas.drawLine(Offset(x, y + nodeRadius), Offset(x - horizontalOffset, y + verticalSpacing - nodeRadius), Paint()..color = Colors.black..strokeWidth = 2);
      _drawNode(canvas, heap, leftChildIndex, x - horizontalOffset, y + verticalSpacing, horizontalOffset / 2);
    }
    if (rightChildIndex < heap.length) {
      canvas.drawLine(Offset(x, y + nodeRadius), Offset(x + horizontalOffset, y + verticalSpacing - nodeRadius), Paint()..color = Colors.black..strokeWidth = 2);
      _drawNode(canvas, heap, rightChildIndex, x + horizontalOffset, y + verticalSpacing, horizontalOffset / 2);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}
