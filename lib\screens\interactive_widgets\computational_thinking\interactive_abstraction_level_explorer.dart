import 'package:flutter/material.dart';

class InteractiveAbstractionLevelExplorer extends StatefulWidget {
  const InteractiveAbstractionLevelExplorer({super.key});

  @override
  State<InteractiveAbstractionLevelExplorer> createState() => _InteractiveAbstractionLevelExplorerState();
}

class _InteractiveAbstractionLevelExplorerState extends State<InteractiveAbstractionLevelExplorer> {
  String _selectedConcept = 'Smartphone';
  String _selectedLevel = 'High-Level';
  String _description = '';

  final Map<String, Map<String, String>> _conceptDetails = {
    'Smartphone': {
      'High-Level': 'A device for communication and information access.',
      'Mid-Level': 'Composed of a screen, processor, camera, and battery.',
      'Low-Level': 'Made of silicon chips, circuits, pixels, and chemical energy storage.',
    },
    'Internet': {
      'High-Level': 'A global network connecting computers and information.',
      'Mid-Level': 'Consists of servers, routers, cables, and wireless signals.',
      'Low-Level': 'Transmits data packets using electrical signals or light pulses.',
    },
    'Human Body': {
      'High-Level': 'A complex biological organism capable of thought and movement.',
      'Mid-Level': 'Composed of organ systems (digestive, circulatory, nervous, etc.).',
      'Low-Level': 'Made of cells, proteins, DNA, and chemical reactions.',
    },
  };

  @override
  void initState() {
    super.initState();
    _updateDescription();
  }

  void _updateDescription() {
    setState(() {
      _description = _conceptDetails[_selectedConcept]?[_selectedLevel] ?? 'Select a concept and level.';
    });
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Interactive Abstraction Level Explorer',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Text('Concept:'),
                const SizedBox(width: 10),
                DropdownButton<String>(
                  value: _selectedConcept,
                  onChanged: (String? newValue) {
                    setState(() {
                      _selectedConcept = newValue!;
                      _updateDescription();
                    });
                  },
                  items: _conceptDetails.keys
                      .map<DropdownMenuItem<String>>((String value) {
                    return DropdownMenuItem<String>(
                      value: value,
                      child: Text(value),
                    );
                  }).toList(),
                ),
              ],
            ),
            const SizedBox(height: 10),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Text('Abstraction Level:'),
                const SizedBox(width: 10),
                DropdownButton<String>(
                  value: _selectedLevel,
                  onChanged: (String? newValue) {
                    setState(() {
                      _selectedLevel = newValue!;
                      _updateDescription();
                    });
                  },
                  items: _conceptDetails[_selectedConcept]?.keys
                          .map<DropdownMenuItem<String>>((String value) {
                        return DropdownMenuItem<String>(
                          value: value,
                          child: Text(value),
                        );
                      }).toList() ??
                      [],
                ),
              ],
            ),
            const SizedBox(height: 20),
            Text(
              'Description at $_selectedLevel Level:',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: Colors.blue),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 10),
            Text(
              _description,
              style: TextStyle(fontSize: 14),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            const Text(
              'Abstraction allows us to simplify complex systems by focusing on relevant details for a given purpose.',
              style: TextStyle(fontStyle: FontStyle.italic, color: Colors.grey),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
