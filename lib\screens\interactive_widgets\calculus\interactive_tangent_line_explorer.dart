import 'package:flutter/material.dart';
import 'dart:math';

class InteractiveTangentLineExplorer extends StatefulWidget {
  const InteractiveTangentLineExplorer({super.key});

  @override
  State<InteractiveTangentLineExplorer> createState() => _InteractiveTangentLineExplorerState();
}

class _InteractiveTangentLineExplorerState extends State<InteractiveTangentLineExplorer> {
  double _xValue = 0.0;
  final TextEditingController _functionController = TextEditingController(text: 'x*x'); // Default function x^2

  double _function(double x) {
    try {
      // Basic parser for simple functions. For a robust solution, a math expression parser library would be needed.
      // This example handles x*x, x+2, sin(x)
      if (_functionController.text.contains('x*x')) {
        return x * x;
      } else if (_functionController.text.contains('x+2')) {
        return x + 2;
      } else if (_functionController.text.contains('sin(x)')) {
        return sin(x);
      }
      return double.nan; // Indicate unsupported function
    } catch (e) {
      return double.nan;
    }
  }

  // Derivative of the example functions
  double _derivative(double x) {
    if (_functionController.text.contains('x*x')) {
      return 2 * x;
    } else if (_functionController.text.contains('x+2')) {
      return 1.0;
    } else if (_functionController.text.contains('sin(x)')) {
      return cos(x);
    }
    return double.nan;
  }

  @override
  Widget build(BuildContext context) {
    double yValue = _function(_xValue);
    double slope = _derivative(_xValue);

    return Card(
      margin: const EdgeInsets.all(16.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Interactive Tangent Line Explorer',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            TextField(
              controller: _functionController,
              decoration: InputDecoration(
                labelText: 'Enter Function f(x) (e.g., x*x, x+2, sin(x))',
                border: OutlineInputBorder(),
              ),
              onChanged: (value) {
                setState(() {}); // Rebuild on function change
              },
            ),
            const SizedBox(height: 20),
            Text(
              'Current x: ${_xValue.toStringAsFixed(2)}',
              style: TextStyle(fontSize: 16),
            ),
            Slider(
              value: _xValue,
              min: -5.0,
              max: 5.0,
              divisions: 100,
              onChanged: (newValue) {
                setState(() {
                  _xValue = newValue;
                });
              },
            ),
            const SizedBox(height: 10),
            Text(
              'f(${_xValue.toStringAsFixed(2)}): ${yValue.isNaN ? 'Undefined' : yValue.toStringAsFixed(2)}',
              style: TextStyle(fontSize: 16),
            ),
            Text(
              'Slope of Tangent (f\'(x)): ${slope.isNaN ? 'Undefined' : slope.toStringAsFixed(2)}',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: Colors.blue),
            ),
            const SizedBox(height: 20),
            CustomPaint(
              size: Size(300, 200),
              painter: _TangentLineGraphPainter(_function, _xValue, _derivative),
            ),
          ],
        ),
      ),
    );
  }
}

class _TangentLineGraphPainter extends CustomPainter {
  final Function(double) _function;
  final double _x;
  final Function(double) _derivative;

  _TangentLineGraphPainter(this._function, this._x, this._derivative);

  @override
  void paint(Canvas canvas, Size size) {
    final Paint paint = Paint()
      ..color = Colors.blue
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    final Paint pointPaint = Paint()
      ..color = Colors.red
      ..strokeWidth = 8
      ..style = PaintingStyle.fill;

    final Paint tangentLinePaint = Paint()
      ..color = Colors.green
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    final double minX = -5.0;
    final double maxX = 5.0;
    final double minY = -10.0; // Adjusted for x^2 function
    final double maxY = 25.0;

    final double rangeX = maxX - minX;
    final double rangeY = maxY - minY;

    final double scaleX = size.width / rangeX;
    final double scaleY = size.height / rangeY;

    Offset toCanvas(double x, double y) {
      final double canvasX = (x - minX) * scaleX;
      final double canvasY = size.height - (y - minY) * scaleY;
      return Offset(canvasX, canvasY);
    }

    // Draw axes
    canvas.drawLine(toCanvas(minX, 0), toCanvas(maxX, 0), paint..color = Colors.grey); // X-axis
    canvas.drawLine(toCanvas(0, minY), toCanvas(0, maxY), paint..color = Colors.grey); // Y-axis

    // Draw function graph
    final Path path = Path();
    bool firstPoint = true;
    for (double i = minX; i <= maxX; i += 0.1) {
      double xVal = i;
      double yVal = _function(xVal);
      if (!yVal.isNaN && yVal.isFinite) {
        if (firstPoint) {
          path.moveTo(toCanvas(xVal, yVal).dx, toCanvas(xVal, yVal).dy);
          firstPoint = false;
        } else {
          path.lineTo(toCanvas(xVal, yVal).dx, toCanvas(xVal, yVal).dy);
        }
      } else {
        firstPoint = true;
      }
    }
    canvas.drawPath(path, paint);

    // Draw point (x, f(x))
    double y = _function(_x);
    if (!y.isNaN && y.isFinite) {
      canvas.drawCircle(toCanvas(_x, y), 4, pointPaint);

      // Draw tangent line
      double slope = _derivative(_x);
      if (!slope.isNaN && slope.isFinite) {
        // Equation of tangent line: Y - y = m(X - x) => Y = m(X - x) + y
        // Choose two points for the tangent line
        double xTangent1 = minX;
        double yTangent1 = slope * (xTangent1 - _x) + y;
        double xTangent2 = maxX;
        double yTangent2 = slope * (xTangent2 - _x) + y;

        canvas.drawLine(toCanvas(xTangent1, yTangent1), toCanvas(xTangent2, yTangent2), tangentLinePaint);
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    _TangentLineGraphPainter oldPainter = oldDelegate as _TangentLineGraphPainter;
    return oldPainter._x != _x;
  }
}
