import 'package:flutter/material.dart';
import 'dart:math';

class InteractiveProjectileMotionSimulator extends StatefulWidget {
  const InteractiveProjectileMotionSimulator({super.key});

  @override
  State<InteractiveProjectileMotionSimulator> createState() => _InteractiveProjectileMotionSimulatorState();
}

class _InteractiveProjectileMotionSimulatorState extends State<InteractiveProjectileMotionSimulator> {
  final TextEditingController _initialVelocityController = TextEditingController(text: '50');
  final TextEditingController _launchAngleController = TextEditingController(text: '45');
  final TextEditingController _gravityController = TextEditingController(text: '9.8');

  double _initialVelocity = 50.0; // m/s
  double _launchAngle = 45.0; // degrees
  double _gravity = 9.8; // m/s^2

  double _maxHeight = 0.0;
  double _range = 0.0;
  double _timeOfFlight = 0.0;

  List<Offset> _trajectoryPoints = [];

  @override
  void dispose() {
    _initialVelocityController.dispose();
    _launchAngleController.dispose();
    _gravityController.dispose();
    super.dispose();
  }

  void _calculateTrajectory() {
    setState(() {
      _initialVelocity = double.tryParse(_initialVelocityController.text) ?? 50.0;
      _launchAngle = double.tryParse(_launchAngleController.text) ?? 45.0;
      _gravity = double.tryParse(_gravityController.text) ?? 9.8;

      final double angleRad = _launchAngle * pi / 180.0;
      final double vx = _initialVelocity * cos(angleRad);
      final double vy = _initialVelocity * sin(angleRad);

      _timeOfFlight = (2 * vy) / _gravity;
      _maxHeight = (vy * vy) / (2 * _gravity);
      _range = (vx * _timeOfFlight);

      _trajectoryPoints.clear();
      const int steps = 100;
      for (int i = 0; i <= steps; i++) {
        final double t = _timeOfFlight * i / steps;
        final double x = vx * t;
        final double y = vy * t - 0.5 * _gravity * t * t;
        _trajectoryPoints.add(Offset(x, y));
      }
    });
  }

  @override
  void initState() {
    super.initState();
    _calculateTrajectory(); // Calculate initial trajectory on load
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Interactive Projectile Motion Simulator'),
        backgroundColor: Colors.blueAccent,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            TextField(
              controller: _initialVelocityController,
              keyboardType: TextInputType.number,
              decoration: const InputDecoration(
                labelText: 'Initial Velocity (m/s)',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 10),
            TextField(
              controller: _launchAngleController,
              keyboardType: TextInputType.number,
              decoration: const InputDecoration(
                labelText: 'Launch Angle (degrees)',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 10),
            TextField(
              controller: _gravityController,
              keyboardType: TextInputType.number,
              decoration: const InputDecoration(
                labelText: 'Gravity (m/s²)',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: _calculateTrajectory,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blueAccent,
                padding: const EdgeInsets.symmetric(vertical: 15),
              ),
              child: const Text(
                'Simulate',
                style: TextStyle(fontSize: 18, color: Colors.white),
              ),
            ),
            const SizedBox(height: 20),
            Text(
              'Max Height: ${_maxHeight.toStringAsFixed(2)} m',
              style: const TextStyle(fontSize: 16),
            ),
            Text(
              'Range: ${_range.toStringAsFixed(2)} m',
              style: const TextStyle(fontSize: 16),
            ),
            Text(
              'Time of Flight: ${_timeOfFlight.toStringAsFixed(2)} s',
              style: const TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 20),
            Expanded(
              child: CustomPaint(
                painter: ProjectilePainter(_trajectoryPoints),
                child: Container(),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class ProjectilePainter extends CustomPainter {
  final List<Offset> trajectoryPoints;

  ProjectilePainter(this.trajectoryPoints);

  @override
  void paint(Canvas canvas, Size size) {
    final Paint paint = Paint()
      ..color = Colors.red
      ..strokeWidth = 3.0
      ..style = PaintingStyle.stroke;

    final Paint groundPaint = Paint()
      ..color = Colors.brown
      ..strokeWidth = 5.0
      ..style = PaintingStyle.stroke;

    // Draw ground
    canvas.drawLine(Offset(0, size.height), Offset(size.width, size.height), groundPaint);

    if (trajectoryPoints.isEmpty) return;

    // Scale points to fit the canvas
    final double maxX = trajectoryPoints.map((p) => p.dx).reduce(max);
    final double maxY = trajectoryPoints.map((p) => p.dy).reduce(max);

    final double scaleX = size.width / (maxX * 1.2); // Add some padding
    final double scaleY = size.height / (maxY * 1.2); // Add some padding

    final List<Offset> scaledPoints = trajectoryPoints.map((p) {
      // Flip y-axis for correct visual representation (Flutter's y increases downwards)
      return Offset(p.dx * scaleX, size.height - (p.dy * scaleY));
    }).toList();

    // Draw trajectory
    final Path path = Path();
    path.moveTo(scaledPoints.first.dx, scaledPoints.first.dy);
    for (int i = 1; i < scaledPoints.length; i++) {
      path.lineTo(scaledPoints[i].dx, scaledPoints[i].dy);
    }
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant ProjectilePainter oldDelegate) {
    return oldDelegate.trajectoryPoints != trajectoryPoints;
  }
}
