import 'package:flutter/material.dart';
import 'home/minimal_home_screen.dart';
import 'courses/courses_screen.dart'; // Changed to use our redesigned screen
import 'battle/battle_screen.dart';
import 'payment/payment_screen.dart';
import 'settings/settings_screen.dart';
import '../widgets/custom_nav_bar.dart';

class MainAppScaffold extends StatefulWidget {
  final int initialIndex;

  const MainAppScaffold({super.key, this.initialIndex = 0});

  @override
  State<MainAppScaffold> createState() => MainAppScaffoldState();
}

class MainAppScaffoldState extends State<MainAppScaffold> {
  late int _selectedIndex;

  @override
  void initState() {
    super.initState();
    _selectedIndex = widget.initialIndex;
  }

  // List of screens for the bottom navigation
  final List<Widget> _widgetOptions = [
    const MinimalHomeScreen(
      key: ValueKey('home'),
    ), // Home screen with recommended courses
    const CoursesScreen(
      key: ValueKey('courses'),
    ), // Courses screen with categories and courses
    const BattleScreen(key: Value<PERSON>ey('battle')), // Battle screen (coming soon)
    const PaymentScreen(key: ValueKey('payment')),
    const SettingsScreen(key: ValueKey('settings')),
  ];

  void _onItemTapped(int index) {
    if (index == _selectedIndex) return;

    setState(() {
      _selectedIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // Body with animated switcher for smooth transitions
      body: AnimatedSwitcher(
        duration: const Duration(milliseconds: 200),
        transitionBuilder: (Widget child, Animation<double> animation) {
          return FadeTransition(opacity: animation, child: child);
        },
        child: _widgetOptions[_selectedIndex],
      ),

      // Custom animated navigation bar
      bottomNavigationBar: CustomNavBar(
        currentIndex: _selectedIndex,
        onItemTapped: _onItemTapped,
      ),
    );
  }
}
