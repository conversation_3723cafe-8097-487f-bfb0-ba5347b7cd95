import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that allows users to simulate and visualize exponential decay.
class InteractiveExponentialDecaySimulatorWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveExponentialDecaySimulatorWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  /// Factory constructor to create the widget from JSON data
  factory InteractiveExponentialDecaySimulatorWidget.fromData(Map<String, dynamic> data) {
    return InteractiveExponentialDecaySimulatorWidget(
      data: data,
    );
  }

  @override
  State<InteractiveExponentialDecaySimulatorWidget> createState() => _InteractiveExponentialDecaySimulatorWidgetState();
}

class _InteractiveExponentialDecaySimulatorWidgetState extends State<InteractiveExponentialDecaySimulatorWidget> with SingleTickerProviderStateMixin {
  late double _initialAmount; // N0
  late double _decayConstant; // lambda
  late double _time; // current time for display

  late AnimationController _animationController;
  late Animation<double> _animation;

  // Colors
  late Color _primaryColor;
  late Color _secondaryColor;
  late Color _backgroundColor;
  late Color _textColor;

  @override
  void initState() {
    super.initState();
    _initialAmount = widget.data['initial_amount']?.toDouble() ?? 100.0;
    _decayConstant = widget.data['decay_constant']?.toDouble() ?? 0.1;
    _time = widget.data['initial_time']?.toDouble() ?? 0.0;

    _primaryColor = _parseColor(widget.data['primary_color']) ?? Colors.blue;
    _secondaryColor = _parseColor(widget.data['secondary_color']) ?? Colors.orange;
    _backgroundColor = _parseColor(widget.data['background_color']) ?? Colors.white;
    _textColor = _parseColor(widget.data['text_color']) ?? Colors.black87;

    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 5),
    );
    _animation = Tween<double>(begin: 0.0, end: 1.0).animate(_animationController)
      ..addListener(() {
        setState(() {
          _time = _animation.value * 20.0; // Animate time from 0 to 20
        });
      });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Color? _parseColor(String? hexString) {
    if (hexString == null) return null;
    hexString = hexString.replaceAll('#', '');
    if (hexString.length == 6) {
      hexString = 'FF$hexString';
    }
    try {
      return Color(int.parse(hexString, radix: 16));
    } catch (e) {
      return null;
    }
  }

  // Calculate amount at time t
  double _calculateAmount(double t) {
    return _initialAmount * math.exp(-_decayConstant * t);
  }

  // Calculate half-life
  double _calculateHalfLife() {
    if (_decayConstant <= 0) return double.infinity;
    return math.log(2) / _decayConstant;
  }

  void _startSimulation() {
    _animationController.reset();
    _animationController.forward();
  }

  @override
  Widget build(BuildContext context) {
    final currentAmount = _calculateAmount(_time);
    final halfLife = _calculateHalfLife();

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _backgroundColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.data['title'] ?? 'Exponential Decay Simulator',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),
          const SizedBox(height: 8),
          if (widget.data['description'] != null)
            Text(
              widget.data['description'],
              style: TextStyle(
                fontSize: 14,
                color: _textColor.withOpacity(0.8),
              ),
            ),
          const SizedBox(height: 16),

          Text(
            'Initial Amount (N₀): ${_initialAmount.toStringAsFixed(1)}',
            style: TextStyle(color: _textColor),
          ),
          Slider(
            value: _initialAmount,
            min: 10.0,
            max: 200.0,
            divisions: 190,
            label: _initialAmount.toStringAsFixed(1),
            activeColor: _primaryColor,
            onChanged: (value) {
              setState(() {
                _initialAmount = value;
              });
            },
          ),
          const SizedBox(height: 8),

          Text(
            'Decay Constant (λ): ${_decayConstant.toStringAsFixed(2)}',
            style: TextStyle(color: _textColor),
          ),
          Slider(
            value: _decayConstant,
            min: 0.01,
            max: 0.5,
            divisions: 49,
            label: _decayConstant.toStringAsFixed(2),
            activeColor: _secondaryColor,
            onChanged: (value) {
              setState(() {
                _decayConstant = value;
              });
            },
          ),
          const SizedBox(height: 16),

          Center(
            child: ElevatedButton.icon(
              onPressed: _startSimulation,
              icon: const Icon(Icons.play_arrow),
              label: const Text('Start Simulation'),
              style: ElevatedButton.styleFrom(
                backgroundColor: _primaryColor,
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              ),
            ),
          ),
          const SizedBox(height: 16),

          Text(
            'Current Time (t): ${_time.toStringAsFixed(1)}',
            style: TextStyle(color: _textColor),
          ),
          Text(
            'Amount Remaining: ${currentAmount.toStringAsFixed(2)}',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: _primaryColor,
            ),
          ),
          Text(
            'Half-Life: ${halfLife.isFinite ? halfLife.toStringAsFixed(2) : '∞'}',
            style: TextStyle(
              fontSize: 14,
              color: _textColor.withOpacity(0.8),
            ),
          ),
          const SizedBox(height: 16),

          Container(
            height: 200,
            child: CustomPaint(
              painter: ExponentialDecayPainter(
                initialAmount: _initialAmount,
                decayConstant: _decayConstant,
                currentTime: _time,
                primaryColor: _primaryColor,
                secondaryColor: _secondaryColor,
                textColor: _textColor,
              ),
              child: Container(),
            ),
          ),

          // Widget name tag
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'InteractiveExponentialDecaySimulator',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }
}

class ExponentialDecayPainter extends CustomPainter {
  final double initialAmount;
  final double decayConstant;
  final double currentTime;
  final Color primaryColor;
  final Color secondaryColor;
  final Color textColor;

  ExponentialDecayPainter({
    required this.initialAmount,
    required this.decayConstant,
    required this.currentTime,
    required this.primaryColor,
    required this.secondaryColor,
    required this.textColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint();
    final textPainter = TextPainter(textDirection: TextDirection.ltr);
    final textStyle = TextStyle(color: textColor, fontSize: 10);

    // Define plot area padding
    const double padding = 30.0;
    final double plotWidth = size.width - 2 * padding;
    final double plotHeight = size.height - 2 * padding;

    // Calculate max values for scaling
    final double maxTime = 20.0; // Fixed time range for graph
    final double maxAmount = initialAmount * 1.1; // A bit more than initial amount

    // Scale factors
    final double xScale = plotWidth / maxTime;
    final double yScale = plotHeight / maxAmount;

    // Draw axes
    paint.color = Colors.grey;
    paint.strokeWidth = 1.0;
    // X-axis
    canvas.drawLine(Offset(padding, size.height - padding), Offset(size.width - padding, size.height - padding), paint);
    // Y-axis
    canvas.drawLine(Offset(padding, padding), Offset(padding, size.height - padding), paint);

    // Draw axis labels
    textPainter.text = TextSpan(text: 'Time (t)', style: textStyle);
    textPainter.layout();
    textPainter.paint(canvas, Offset(size.width / 2 - textPainter.width / 2, size.height - padding + 5));

    textPainter.text = TextSpan(text: 'Amount', style: textStyle);
    textPainter.layout();
    canvas.save();
    canvas.translate(padding - 15, size.height / 2 + textPainter.width / 2);
    canvas.rotate(-math.pi / 2);
    textPainter.paint(canvas, Offset.zero);
    canvas.restore();

    // Draw curve
    paint.color = primaryColor;
    paint.style = PaintingStyle.stroke;
    paint.strokeWidth = 2.0;

    final path = Path();
    bool firstPoint = true;
    for (int i = 0; i <= 100; i++) {
      final t = i / 100 * maxTime;
      final amount = initialAmount * math.exp(-decayConstant * t);
      
      final x = padding + t * xScale;
      final y = size.height - padding - amount * yScale;

      if (firstPoint) {
        path.moveTo(x, y);
        firstPoint = false;
      } else {
        path.lineTo(x, y);
      }
    }
    canvas.drawPath(path, paint);

    // Draw current time indicator
    paint.color = secondaryColor;
    paint.style = PaintingStyle.fill;
    final currentX = padding + currentTime * xScale;
    final currentY = size.height - padding - initialAmount * math.exp(-decayConstant * currentTime) * yScale;
    canvas.drawCircle(Offset(currentX, currentY), 5, paint);

    // Draw half-life indicator
    if (decayConstant > 0) {
      final halfLife = math.log(2) / decayConstant;
      final halfLifeX = padding + halfLife * xScale;
      final halfAmountY = size.height - padding - (initialAmount / 2) * yScale;

      paint.color = Colors.grey.withOpacity(0.5);
      paint.style = PaintingStyle.stroke;
      paint.strokeWidth = 1.0;
      paint.strokeCap = StrokeCap.round;
      paint.shader = null;

      // Horizontal line at half amount
      canvas.drawLine(Offset(padding, halfAmountY), Offset(halfLifeX, halfAmountY), paint);
      // Vertical line at half-life time
      canvas.drawLine(Offset(halfLifeX, halfAmountY), Offset(halfLifeX, size.height - padding), paint);

      // Half-life text
      textPainter.text = TextSpan(text: 'T½: ${halfLife.toStringAsFixed(1)}', style: textStyle);
      textPainter.layout();
      textPainter.paint(canvas, Offset(halfLifeX + 5, size.height - padding - textPainter.height - 5));

      // Half amount text
      textPainter.text = TextSpan(text: '${(initialAmount / 2).toStringAsFixed(1)}', style: textStyle);
      textPainter.layout();
      textPainter.paint(canvas, Offset(padding + 5, halfAmountY - textPainter.height - 5));
    }
  }

  @override
  bool shouldRepaint(covariant ExponentialDecayPainter oldDelegate) {
    return oldDelegate.initialAmount != initialAmount ||
           oldDelegate.decayConstant != decayConstant ||
           oldDelegate.currentTime != currentTime;
  }
}
