import 'package:flutter/material.dart';
import 'package:resonance/widgets/interactive_widget.dart';

class InteractiveComputationalArgumentationTool extends InteractiveWidget {
  const InteractiveComputationalArgumentationTool({super.key});

  @override
  InteractiveWidgetMetaData get metaData => InteractiveWidgetMetaData(
        title: 'Interactive Computational Argumentation Tool',
        category: 'Reasoning',
        course: 'Reasoning: Frontiers',
        module: 'Advanced Topics in Reasoning',
        slug: 'interactive-computational-argumentation-tool',
        description:
            'A tool to explore computational models of argumentation and argument mining.',
        difficulty: InteractiveWidgetDifficulty.hard,
        tags: const ['logic', 'argumentation', 'computational', 'advanced'],
      );

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(metaData.title),
        backgroundColor: Colors.deepPurple,
      ),
      body: const Center(
        child: Padding(
          padding: EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'Welcome to the Interactive Computational Argumentation Tool!',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 20),
              Text(
                'This widget will help you explore computational aspects of argumentation.',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 16),
              ),
              SizedBox(height: 40),
              // Placeholder for interactive content
              CircularProgressIndicator(),
              SizedBox(height: 20),
              Text(
                'Content coming soon!',
                style: TextStyle(fontSize: 18, fontStyle: FontStyle.italic),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
