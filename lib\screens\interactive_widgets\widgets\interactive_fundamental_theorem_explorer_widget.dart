import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that allows users to explore the Fundamental Theorem of Calculus.
class InteractiveFundamentalTheoremExplorerWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveFundamentalTheoremExplorerWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  /// Factory constructor to create the widget from JSON data
  factory InteractiveFundamentalTheoremExplorerWidget.fromData(Map<String, dynamic> data) {
    return InteractiveFundamentalTheoremExplorerWidget(
      data: data,
    );
  }

  @override
  State<InteractiveFundamentalTheoremExplorerWidget> createState() => _InteractiveFundamentalTheoremExplorerWidgetState();
}

class _InteractiveFundamentalTheoremExplorerWidgetState extends State<InteractiveFundamentalTheoremExplorerWidget> {
  late double _currentX; // The upper limit of integration for A(x)

  // Original function f(t) = t
  double _function(double t) {
    return t;
  }

  // Accumulation function A(x) = integral from 0 to x of f(t) dt = x^2 / 2
  double _accumulationFunction(double x) {
    return (x * x) / 2.0;
  }

  // Colors
  late Color _primaryColor; // For f(x)
  late Color _secondaryColor; // For A(x)
  late Color _backgroundColor;
  late Color _textColor;

  @override
  void initState() {
    super.initState();
    _currentX = widget.data['initial_x']?.toDouble() ?? 2.0;

    _primaryColor = _parseColor(widget.data['primary_color']) ?? Colors.blue;
    _secondaryColor = _parseColor(widget.data['secondary_color']) ?? Colors.orange;
    _backgroundColor = _parseColor(widget.data['background_color']) ?? Colors.white;
    _textColor = _parseColor(widget.data['text_color']) ?? Colors.black87;
  }

  Color? _parseColor(String? hexString) {
    if (hexString == null) return null;
    hexString = hexString.replaceAll('#', '');
    if (hexString.length == 6) {
      hexString = 'FF$hexString';
    }
    try {
      return Color(int.parse(hexString, radix: 16));
    } catch (e) {
      return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    final fxValue = _function(_currentX);
    final AxValue = _accumulationFunction(_currentX);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _backgroundColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.data['title'] ?? 'Fundamental Theorem Explorer',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),
          const SizedBox(height: 8),
          if (widget.data['description'] != null)
            Text(
              widget.data['description'],
              style: TextStyle(
                fontSize: 14,
                color: _textColor.withOpacity(0.8),
              ),
            ),
          const SizedBox(height: 16),

          Text(
            'Function: f(t) = t',
            style: TextStyle(color: _textColor, fontSize: 16, fontStyle: FontStyle.italic),
          ),
          Text(
            'Accumulation Function: A(x) = ∫₀ˣ f(t) dt = x²/2',
            style: TextStyle(color: _textColor, fontSize: 16, fontStyle: FontStyle.italic),
          ),
          const SizedBox(height: 16),

          Text(
            'Current x (upper limit): ${_currentX.toStringAsFixed(1)}',
            style: TextStyle(color: _primaryColor, fontSize: 16, fontWeight: FontWeight.bold),
          ),
          Slider(
            value: _currentX,
            min: 0.0,
            max: 5.0,
            divisions: 50,
            label: _currentX.toStringAsFixed(1),
            activeColor: _primaryColor,
            onChanged: (value) {
              setState(() {
                _currentX = value;
              });
            },
          ),
          const SizedBox(height: 16),

          Center(
            child: Column(
              children: [
                Text(
                  'f(${_currentX.toStringAsFixed(1)}) = ${fxValue.toStringAsFixed(2)}',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: _primaryColor,
                  ),
                ),
                Text(
                  'A(${_currentX.toStringAsFixed(1)}) = ${AxValue.toStringAsFixed(2)}',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: _secondaryColor,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Observe: A\'(x) = f(x)',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: _textColor,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),

          Container(
            height: 250,
            child: CustomPaint(
              painter: FundamentalTheoremPainter(
                function: _function,
                accumulationFunction: _accumulationFunction,
                currentX: _currentX,
                primaryColor: _primaryColor,
                secondaryColor: _secondaryColor,
                textColor: _textColor,
              ),
              child: Container(),
            ),
          ),

          // Widget name tag
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'InteractiveFundamentalTheoremExplorer',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }
}

class FundamentalTheoremPainter extends CustomPainter {
  final Function(double) function;
  final Function(double) accumulationFunction;
  final double currentX;
  final Color primaryColor;
  final Color secondaryColor;
  final Color textColor;

  FundamentalTheoremPainter({
    required this.function,
    required this.accumulationFunction,
    required this.currentX,
    required this.primaryColor,
    required this.secondaryColor,
    required this.textColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint();
    final textPainter = TextPainter(textDirection: TextDirection.ltr);
    final textStyle = TextStyle(color: textColor, fontSize: 10);

    // Define plot area padding
    const double padding = 30.0;
    final double plotWidth = size.width - 2 * padding;
    final double plotHeight = size.height - 2 * padding;

    // Determine x and y ranges for plotting
    final double minPlotX = 0.0;
    final double maxPlotX = 5.0;
    final double minPlotY = 0.0;
    final double maxPlotY = 15.0; // For A(x) = x^2/2, max at x=5 is 12.5

    // Scale factors
    final double xScale = plotWidth / (maxPlotX - minPlotX);
    final double yScale = plotHeight / (maxPlotY - minPlotY);

    // Helper to convert plot coordinates to canvas coordinates
    Offset toCanvas(double x, double y) {
      final canvasX = padding + (x - minPlotX) * xScale;
      final canvasY = size.height - padding - (y - minPlotY) * yScale;
      return Offset(canvasX, canvasY);
    }

    // Draw axes
    paint.color = Colors.grey;
    paint.strokeWidth = 1.0;
    // X-axis
    canvas.drawLine(toCanvas(minPlotX, 0), toCanvas(maxPlotX, 0), paint);
    // Y-axis
    canvas.drawLine(toCanvas(0, minPlotY), toCanvas(0, maxPlotY), paint);

    // Draw axis labels
    textPainter.text = TextSpan(text: 'x', style: textStyle);
    textPainter.layout();
    textPainter.paint(canvas, Offset(size.width - padding + 5, size.height - padding - textPainter.height / 2));

    textPainter.text = TextSpan(text: 'y', style: textStyle);
    textPainter.layout();
    textPainter.paint(canvas, Offset(padding - textPainter.width - 5, padding - textPainter.height / 2));

    // Draw f(x) curve
    paint.color = primaryColor;
    paint.style = PaintingStyle.stroke;
    paint.strokeWidth = 2.0;

    final pathF = Path();
    bool firstPointF = true;
    for (double x = minPlotX; x <= maxPlotX; x += (maxPlotX - minPlotX) / 100) {
      final y = function(x);
      final point = toCanvas(x, y);
      if (firstPointF) {
        pathF.moveTo(point.dx, point.dy);
        firstPointF = false;
      } else {
        pathF.lineTo(point.dx, point.dy);
      }
    }
    canvas.drawPath(pathF, paint);

    // Draw A(x) curve
    paint.color = secondaryColor;
    paint.style = PaintingStyle.stroke;
    paint.strokeWidth = 2.0;

    final pathA = Path();
    bool firstPointA = true;
    for (double x = minPlotX; x <= maxPlotX; x += (maxPlotX - minPlotX) / 100) {
      final y = accumulationFunction(x);
      final point = toCanvas(x, y);
      if (firstPointA) {
        pathA.moveTo(point.dx, point.dy);
        firstPointA = false;
      } else {
        pathA.lineTo(point.dx, point.dy);
      }
    }
    canvas.drawPath(pathA, paint);

    // Draw current x line and points
    paint.color = Colors.grey.withOpacity(0.7);
    paint.strokeWidth = 1.0;
    canvas.drawLine(toCanvas(currentX, minPlotY), toCanvas(currentX, maxPlotY), paint);

    paint.color = primaryColor;
    paint.style = PaintingStyle.fill;
    canvas.drawCircle(toCanvas(currentX, function(currentX)), 5, paint);

    paint.color = secondaryColor;
    paint.style = PaintingStyle.fill;
    canvas.drawCircle(toCanvas(currentX, accumulationFunction(currentX)), 5, paint);

    // Draw labels for f(x) and A(x)
    textPainter.text = TextSpan(text: 'f(x)', style: textStyle.copyWith(color: primaryColor));
    textPainter.layout();
    textPainter.paint(canvas, Offset(toCanvas(maxPlotX - 0.5, function(maxPlotX - 0.5)).dx, toCanvas(maxPlotX - 0.5, function(maxPlotX - 0.5)).dy - textPainter.height - 5));

    textPainter.text = TextSpan(text: 'A(x)', style: textStyle.copyWith(color: secondaryColor));
    textPainter.layout();
    textPainter.paint(canvas, Offset(toCanvas(maxPlotX - 0.5, accumulationFunction(maxPlotX - 0.5)).dx, toCanvas(maxPlotX - 0.5, accumulationFunction(maxPlotX - 0.5)).dy - textPainter.height - 5));
  }

  @override
  bool shouldRepaint(covariant FundamentalTheoremPainter oldDelegate) {
    return oldDelegate.currentX != currentX ||
           oldDelegate.primaryColor != primaryColor ||
           oldDelegate.secondaryColor != secondaryColor ||
           oldDelegate.textColor != textColor;
  }
}
