import 'package:flutter/material.dart';
import 'package:resonance/utils/page_transitions.dart';
import 'package:resonance/widgets/animated_button.dart';

class InteractiveNodalAnalysisTool extends StatefulWidget {
  const InteractiveNodalAnalysisTool({super.key});

  @override
  State<InteractiveNodalAnalysisTool> createState() => _InteractiveNodalAnalysisToolState();
}

class _InteractiveNodalAnalysisToolState extends State<InteractiveNodalAnalysisTool> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Interactive Nodal Analysis Tool'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            Navigator.of(context).pop();
          },
        ),
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Text(
                'This is the Interactive Nodal Analysis Tool.',
                textAlign: TextAlign.center,
                style: Text<PERSON>tyle(fontSize: 18),
              ),
              const SizedBox(height: 30),
              AnimatedButton(
                onTap: () {
                  // Add functionality for the button here
                },
                text: 'Explore Nodal Analysis',
                color: Colors.blueAccent,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
