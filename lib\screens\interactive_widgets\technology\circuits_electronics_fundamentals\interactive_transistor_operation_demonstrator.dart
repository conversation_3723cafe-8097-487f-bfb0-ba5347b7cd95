import 'package:flutter/material.dart';
import 'package:resonance_app/widgets/interactive_widget_scaffold.dart';

class InteractiveTransistorOperationDemonstrator extends StatefulWidget {
  const InteractiveTransistorOperationDemonstrator({super.key});

  @override
  State<InteractiveTransistorOperationDemonstrator> createState() => _InteractiveTransistorOperationDemonstratorState();
}

class _InteractiveTransistorOperationDemonstratorState extends State<InteractiveTransistorOperationDemonstrator> {
  double baseVoltage = 0.0; // Vbe
  double collectorVoltage = 0.0; // Vce
  double baseCurrent = 0.0; // Ib
  double collectorCurrent = 0.0; // Ic
  double beta = 100.0; // Current gain (hFE)

  String operationMode = 'Cut-off';

  @override
  void initState() {
    super.initState();
    _calculateTransistorOperation();
  }

  void _calculateTransistorOperation() {
    setState(() {
      // Simplified BJT NPN model for demonstration
      if (baseVoltage < 0.7) { // Assuming Vbe(on) = 0.7V
        operationMode = 'Cut-off';
        baseCurrent = 0.0;
        collectorCurrent = 0.0;
      } else {
        // Active region (simplified)
        baseCurrent = (baseVoltage - 0.7) / 1000; // Assuming 1kOhm base resistor for simplicity
        collectorCurrent = beta * baseCurrent;

        // Saturation check (simplified)
        if (collectorCurrent * 10 < collectorVoltage) { // Assuming Vce(sat) is low, e.g., 0.2V, and R_collector is 10 Ohm
          operationMode = 'Active';
        } else {
          operationMode = 'Saturation';
          collectorCurrent = collectorVoltage / 10; // Max current limited by collector resistor
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return InteractiveWidgetScaffold(
      title: 'Interactive Transistor Operation Demonstrator',
      description: 'Demonstrate the operation modes of a BJT transistor (Cut-off, Active, Saturation).',
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text(
              'Base Voltage (Vbe): ${baseVoltage.toStringAsFixed(2)} V',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            Slider(
              value: baseVoltage,
              min: 0.0,
              max: 1.5,
              divisions: 150,
              label: baseVoltage.toStringAsFixed(2),
              onChanged: (value) {
                setState(() {
                  baseVoltage = value;
                  _calculateTransistorOperation();
                });
              },
            ),
            const SizedBox(height: 16.0),
            Text(
              'Collector Voltage (Vce supply): ${collectorVoltage.toStringAsFixed(2)} V',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            Slider(
              value: collectorVoltage,
              min: 0.0,
              max: 10.0,
              divisions: 100,
              label: collectorVoltage.toStringAsFixed(2),
              onChanged: (value) {
                setState(() {
                  collectorVoltage = value;
                  _calculateTransistorOperation();
                });
              },
            ),
            const SizedBox(height: 16.0),
            Text(
              'Beta (β/hFE): ${beta.toStringAsFixed(0)}',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            Slider(
              value: beta,
              min: 50.0,
              max: 200.0,
              divisions: 150,
              label: beta.toStringAsFixed(0),
              onChanged: (value) {
                setState(() {
                  beta = value;
                  _calculateTransistorOperation();
                });
              },
            ),
            const SizedBox(height: 24.0),
            Text(
              'Operation Mode: $operationMode',
              style: Theme.of(context).textTheme.headlineSmall,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8.0),
            Text(
              'Base Current (Ib): ${baseCurrent.toStringAsFixed(4)} A',
              style: Theme.of(context).textTheme.titleMedium,
              textAlign: TextAlign.center,
            ),
            Text(
              'Collector Current (Ic): ${collectorCurrent.toStringAsFixed(4)} A',
              style: Theme.of(context).textTheme.titleMedium,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
