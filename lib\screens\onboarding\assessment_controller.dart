import 'package:flutter/material.dart';
import '../../models/user_preferences.dart';
import '../../widgets/animated_progress_bar.dart';
import '../../widgets/animated_button.dart';
import '../../utils/page_transitions.dart';
import '../../services/service_provider.dart';
import '../main_app_scaffold.dart';
import 'assessment_screens.dart';
import 'assessment_screens_part2.dart';
import 'assessment_screens_part3.dart';
import 'assessment_screens_part4.dart';
import '../auth/signin_screen.dart';

class AssessmentController extends StatefulWidget {
  const AssessmentController({Key? key}) : super(key: key);

  @override
  _AssessmentControllerState createState() => _AssessmentControllerState();
}

class _AssessmentControllerState extends State<AssessmentController> {
  final PageController _pageController = PageController();
  final UserPreferences _userPreferences = UserPreferences();
  int _currentPage = 1;
  final int _totalPages = 10;

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _goToNextPage() {
    if (_currentPage < _totalPages) {
      // Check if the current page requires a selection and if it has been made
      if (_userPreferences.hasSelectionForPage(_currentPage)) {
        setState(() {
          _currentPage++;
        });
        _pageController.animateToPage(
          _currentPage - 1,
          duration: Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      } else {
        // Show a message that a selection is required
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Please make a selection to continue'),
            duration: Duration(seconds: 2),
          ),
        );
      }
    } else {
      // We've reached the end of the assessment, save preferences and navigate to sign-in
      _savePreferencesAndNavigate();
    }
  }

  void _goToPreviousPage() {
    if (_currentPage > 1) {
      setState(() {
        _currentPage--;
      });
      _pageController.animateToPage(
        _currentPage - 1,
        duration: Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _onPageChanged(int page) {
    // Calculate the new page number (1-based)
    int newPage = page + 1;

    // If trying to move forward, check if the current page has a selection
    if (newPage > _currentPage) {
      // Check if the current page requires a selection
      if (!_userPreferences.hasSelectionForPage(_currentPage)) {
        // If no selection, show a message and animate back to the current page
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Please make a selection to continue'),
            duration: Duration(seconds: 2),
          ),
        );

        // Animate back to the current page
        _pageController.animateToPage(
          _currentPage - 1,
          duration: Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
        return;
      }
    }

    setState(() {
      _currentPage = newPage;
    });
  }

  void _savePreferencesAndNavigate() {
    // Here you would typically save the preferences to local storage or a database
    print('User preferences: $_userPreferences');

    // Get course service
    final courseService = ServiceProvider.of(context).courseService;

    // Update user preferences if needed
    if (_userPreferences.dailyGoalMinutes != null) {
      // In a real app, you would save these preferences to the user account
      // For now, we'll just navigate to the main app
    }

    // Navigate to the main app as a guest user
    Navigator.of(context).pushReplacement(
      MaterialPageRoute(builder: (_) => const MainAppScaffold()),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Column(
          children: [
            // Progress bar
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: AnimatedProgressBar(
                currentPage: _currentPage,
                totalPages: _totalPages,
                title: 'Assessment',
              ),
            ),

            // Page content
            Expanded(
              child: PageView(
                controller: _pageController,
                physics:
                    _currentPage == 1
                        ? NeverScrollableScrollPhysics() // Disable swipe on first page
                        : PageScrollPhysics(), // Enable swipe on other pages
                onPageChanged: _onPageChanged,
                children: [
                  // Page 1: Introduction with animation
                  IntroductionScreen(onContinue: _goToNextPage),

                  // Page 2: Learning goal selection
                  LearningGoalScreen(
                    userPreferences: _userPreferences,
                    onContinue: _goToNextPage,
                  ),

                  // Page 3: Learning focus selection
                  LearningFocusScreen(
                    userPreferences: _userPreferences,
                    onContinue: _goToNextPage,
                  ),

                  // Page 4: Personalized message
                  PersonalizedMessageScreen(
                    userPreferences: _userPreferences,
                    onContinue: _goToNextPage,
                  ),

                  // Page 5: Topic selection
                  TopicSelectionScreen(
                    userPreferences: _userPreferences,
                    onContinue: _goToNextPage,
                  ),

                  // Page 6: Effectiveness message
                  EffectivenessScreen(
                    userPreferences: _userPreferences,
                    onContinue: _goToNextPage,
                  ),

                  // Page 7: Daily goal and time preference
                  DailyGoalScreen(
                    userPreferences: _userPreferences,
                    onContinue: _goToNextPage,
                  ),

                  // Page 8: Notifications permission
                  NotificationsScreen(
                    userPreferences: _userPreferences,
                    onContinue: _goToNextPage,
                  ),

                  // Page 9: Smart every day message
                  SmartEveryDayScreen(
                    userPreferences: _userPreferences,
                    onContinue: _goToNextPage,
                  ),

                  // Page 10: Final "Let's go" screen
                  FinalScreen(
                    userPreferences: _userPreferences,
                    onComplete: _savePreferencesAndNavigate,
                  ),
                ],
              ),
            ),

            // Navigation buttons (only show on certain pages)
            if (_currentPage > 1 && _currentPage < _totalPages)
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // Back button
                    AnimatedTextButton(
                      onPressed: _goToPreviousPage,
                      text: 'Back',
                      textColor: Color.fromRGBO(100, 45, 176, 1),
                    ),

                    // Continue button
                    AnimatedButton(
                      onPressed: _goToNextPage,
                      text: 'Continue',
                      icon: Icons.arrow_forward,
                      backgroundColor: Color.fromRGBO(16, 17, 20, 1),
                      textColor: Colors.white,
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }
}
