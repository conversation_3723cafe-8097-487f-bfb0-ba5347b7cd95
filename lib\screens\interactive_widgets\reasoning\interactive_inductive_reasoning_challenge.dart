import 'package:flutter/material.dart';
import 'package:resonance/widgets/animated_button.dart';

class InteractiveInductiveReasoningChallenge extends StatefulWidget {
  const InteractiveInductiveReasoningChallenge({super.key});

  @override
  State<InteractiveInductiveReasoningChallenge> createState() => _InteractiveInductiveReasoningChallengeState();
}

class _InteractiveInductiveReasoningChallengeState extends State<InteractiveInductiveReasoningChallenge> {
  int _currentQuestionIndex = 0;
  String? _selectedAnswer;
  String _feedback = '';
  int _score = 0;

  final List<Map<String, dynamic>> _questions = [
    {
      'question': 'Which of the following is an example of a strong inductive argument?',
      'options': [
        'All observed swans are white, therefore all swans are white.',
        'The sun has risen every day, therefore the sun will rise tomorrow.',
        'My neighbor\'s cat is black, therefore all cats are black.',
        'This coin has landed on heads 10 times in a row, therefore it will land on heads next.',
      ],
      'correctAnswer': 'The sun has risen every day, therefore the sun will rise tomorrow.',
      'explanation': 'This is a strong inductive argument based on consistent past observations, making the conclusion highly probable.',
    },
    {
      'question': 'What is the primary goal of inductive reasoning?',
      'options': [
        'To guarantee the truth of the conclusion.',
        'To prove a conclusion with certainty.',
        'To make a conclusion probable based on evidence.',
        'To deduce specific truths from general principles.',
      ],
      'correctAnswer': 'To make a conclusion probable based on evidence.',
      'explanation': 'Inductive reasoning aims to establish conclusions that are likely or probable, not certain.',
    },
    {
      'question': 'Which factor typically strengthens an inductive argument?',
      'options': [
        'A smaller sample size.',
        'A biased sample.',
        'More diverse evidence.',
        'Ignoring counter-examples.',
      ],
      'correctAnswer': 'More diverse evidence.',
      'explanation': 'Diverse evidence makes the premises more representative and thus strengthens the inductive argument.',
    },
  ];

  void _checkAnswer() {
    if (_selectedAnswer == null) {
      setState(() {
        _feedback = 'Please select an answer.';
      });
      return;
    }

    if (_selectedAnswer == _questions[_currentQuestionIndex]['correctAnswer']) {
      setState(() {
        _feedback = 'Correct! ${_questions[_currentQuestionIndex]['explanation']}';
        _score++;
      });
    } else {
      setState(() {
        _feedback = 'Incorrect. The correct answer was: "${_questions[_currentQuestionIndex]['correctAnswer']}". ${_questions[_currentQuestionIndex]['explanation']}';
      });
    }
  }

  void _nextQuestion() {
    setState(() {
      if (_currentQuestionIndex < _questions.length - 1) {
        _currentQuestionIndex++;
        _selectedAnswer = null;
        _feedback = '';
      } else {
        _feedback = 'Challenge Completed! Your score: $_score / ${_questions.length}';
      }
    });
  }

  void _resetChallenge() {
    setState(() {
      _currentQuestionIndex = 0;
      _selectedAnswer = null;
      _feedback = '';
      _score = 0;
    });
  }

  @override
  Widget build(BuildContext context) {
    final currentQuestion = _questions[_currentQuestionIndex];

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text(
            'Inductive Reasoning Challenge',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.primary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 20),
          Card(
            elevation: 4,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            color: Theme.of(context).colorScheme.surfaceVariant,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Question ${_currentQuestionIndex + 1} / ${_questions.length}',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                  const SizedBox(height: 10),
                  Text(
                    currentQuestion['question'],
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                  const SizedBox(height: 15),
                  ... (currentQuestion['options'] as List<String>).map((option) {
                    return RadioListTile<String>(
                      title: Text(
                        option,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                      value: option,
                      groupValue: _selectedAnswer,
                      onChanged: (value) {
                        setState(() {
                          _selectedAnswer = value;
                          _feedback = ''; // Clear feedback on new selection
                        });
                      },
                      activeColor: Theme.of(context).colorScheme.secondary,
                    );
                  }).toList(),
                ],
              ),
            ),
          ),
          const SizedBox(height: 20),
          AnimatedButton(
            onTap: _checkAnswer,
            label: 'Check Answer',
            color: Theme.of(context).colorScheme.secondary,
            labelColor: Theme.of(context).colorScheme.onSecondary,
          ),
          if (_feedback.isNotEmpty) ...[
            const SizedBox(height: 10),
            Text(
              _feedback,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: _feedback.startsWith('Correct') ? Colors.green : Colors.red,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
          ],
          const SizedBox(height: 20),
          AnimatedButton(
            onTap: _nextQuestion,
            label: _currentQuestionIndex < _questions.length - 1 ? 'Next Question' : 'Finish Challenge',
            color: Theme.of(context).colorScheme.tertiary,
            labelColor: Theme.of(context).colorScheme.onTertiary,
          ),
          if (_currentQuestionIndex == _questions.length - 1 && _feedback.contains('Completed'))
            Padding(
              padding: const EdgeInsets.only(top: 20.0),
              child: AnimatedButton(
                onTap: _resetChallenge,
                label: 'Restart Challenge',
                color: Theme.of(context).colorScheme.primary,
                labelColor: Theme.of(context).colorScheme.onPrimary,
              ),
            ),
        ],
      ),
    );
  }
}
