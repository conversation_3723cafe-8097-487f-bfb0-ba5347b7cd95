import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart'; // Import flutter_svg

// Define the callback type
typedef OnItemTapped = void Function(int index);

class CustomNavBar extends StatefulWidget {
  final int currentIndex;
  final OnItemTapped onItemTapped;

  const CustomNavBar({
    Key? key,
    required this.currentIndex,
    required this.onItemTapped,
  }) : super(key: key);

  @override
  State<CustomNavBar> createState() => _CustomNavBarState();
}

class _CustomNavBarState extends State<CustomNavBar>
    with TickerProviderStateMixin {
  // Animation controller for nav bar items
  late List<AnimationController> _animationControllers;
  late List<Animation<double>> _scaleAnimations;

  @override
  void initState() {
    super.initState();

    // Initialize animation controllers for each nav item
    _animationControllers = List.generate(
      5, // Number of tabs
      (index) => AnimationController(
        duration: const Duration(milliseconds: 200),
        vsync: this,
      ),
    );

    // Initialize scale animations
    _scaleAnimations =
        _animationControllers.map((controller) {
          return Tween<double>(begin: 1.0, end: 1.2).animate(
            CurvedAnimation(parent: controller, curve: Curves.easeInOut),
          );
        }).toList();

    // Start animation for the initial selected tab
    _animationControllers[widget.currentIndex].forward();
  }

  @override
  void didUpdateWidget(CustomNavBar oldWidget) {
    super.didUpdateWidget(oldWidget);

    // If the selected tab changed, animate the transition
    if (oldWidget.currentIndex != widget.currentIndex) {
      // Reset previous tab animation
      _animationControllers[oldWidget.currentIndex].reverse();

      // Start animation for the new tab
      _animationControllers[widget.currentIndex].forward();
    }
  }

  @override
  void dispose() {
    // Dispose all animation controllers
    for (var controller in _animationControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Define SVG asset paths for the navigation bar
    final List<String> icons = [
      'assets/icons/home.svg',
      'assets/icons/course.svg',
      'assets/icons/rank.svg',
      'assets/icons/premium.svg',
      'assets/icons/settings.svg',
    ];
    // Define selected SVG asset paths
    final List<String> selectedIcons = [
      'assets/icons/homesel.svg',
      'assets/icons/coursesel.svg',
      'assets/icons/ranksel.svg',
      'assets/icons/premiumsel.svg',
      'assets/icons/settingssel.svg',
    ];
    // Define labels for navigation items
    final List<String> labels = [
      'Home',
      'Courses',
      'Battle',
      'Premium',
      'Settings',
    ];

    // Define the background color for the selected item container
    const Color selectedItemBgColor = Color.fromRGBO(243, 243, 243, 1);
    // Define the background color for the nav bar itself
    const Color navBarBgColor = Colors.white; // White background as per design

    return Container(
      height: 85, // Increased height to accommodate labels
      decoration: BoxDecoration(
        color: navBarBgColor,
        // Add shadow and curved top border
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(26), // 0.1 opacity
            blurRadius: 10,
            spreadRadius: 0,
            offset: const Offset(0, -2), // Shadow position (top shadow)
          ),
        ],
        // Optional: Add curved top border if needed, simple rounding for now
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
      child: Row(
        mainAxisAlignment:
            MainAxisAlignment.spaceAround, // Distribute items evenly
        children: List.generate(icons.length, (index) {
          bool isSelected = widget.currentIndex == index;
          return GestureDetector(
            onTap: () => widget.onItemTapped(index),
            behavior:
                HitTestBehavior.opaque, // Ensure the whole area is tappable
            child: Container(
              // Outer container for spacing/hit area if needed (adjust padding here if necessary)
              padding: const EdgeInsets.symmetric(
                vertical: 8,
              ), // Keep vertical padding for spacing
              child: ScaleTransition(
                scale: _scaleAnimations[index],
                child: Container(
                  // Inner container for the rounded background effect on selection
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ), // Reduced padding to fit text
                  decoration: BoxDecoration(
                    color:
                        isSelected ? selectedItemBgColor : Colors.transparent,
                    borderRadius: BorderRadius.circular(
                      24,
                    ), // Rounded corners for selection bg
                  ),
                  child: Column(
                    // Use Column to stack icon and label
                    mainAxisSize: MainAxisSize.min, // Take minimum space
                    mainAxisAlignment:
                        MainAxisAlignment.center, // Center items vertically
                    children: [
                      SvgPicture.asset(
                        isSelected ? selectedIcons[index] : icons[index],
                        width: 24, // Specify width
                        height: 24, // Specify height
                        semanticsLabel:
                            isSelected
                                ? 'Selected ${icons[index]}'
                                : icons[index],
                      ),
                      const SizedBox(
                        height: 4,
                      ), // Spacing between icon and text
                      Text(
                        labels[index],
                        style: TextStyle(
                          color:
                              isSelected
                                  ? const Color.fromRGBO(124, 66, 210, 1)
                                  : Colors.grey.shade700,
                          fontSize: 12,
                          fontWeight:
                              isSelected ? FontWeight.w600 : FontWeight.w500,
                          fontFamily: 'WorkSans',
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        }),
      ),
    );
  }
}
