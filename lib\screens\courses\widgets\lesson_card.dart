import 'package:flutter/material.dart';
import '../../../models/course_models.dart';

class LessonCard extends StatelessWidget {
  final LessonDefinition lesson; // Changed to LessonDefinition
  final bool isLocked;
  final VoidCallback onTap;

  const LessonCard({
    Key? key,
    required this.lesson,
    required this.isLocked,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 5,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            // Lesson Number
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color:
                    isLocked
                        ? Colors.grey[200]
                        : const Color.fromRGBO(124, 66, 210, 0.1),
                shape: BoxShape.circle,
                border: Border.all(
                  color:
                      isLocked
                          ? Colors.grey[400]!
                          : const Color.fromRGBO(124, 66, 210, 0.5),
                  width: 1,
                ),
              ),
              child: Center(
                child: Text(
                  lesson.order.toString(),
                  style: TextStyle(
                    color:
                        isLocked
                            ? Colors.grey[600]
                            : const Color.fromRGBO(124, 66, 210, 1),
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    fontFamily: 'WorkSans',
                  ),
                ),
              ),
            ),

            const SizedBox(width: 16),

            // Lesson Info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Lesson Title
                  Text(
                    lesson.title,
                    style: TextStyle(
                      color: isLocked ? Colors.grey[600] : Colors.black,
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      fontFamily: 'WorkSans',
                    ),
                  ),

                  const SizedBox(height: 4),

                  // Lesson Description
                  Text(
                    lesson.description,
                    style: TextStyle(
                      color: isLocked ? Colors.grey : Colors.grey[700],
                      fontSize: 14,
                      fontFamily: 'WorkSans',
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),

                  const SizedBox(height: 8),

                  // Lesson Stats
                  Row(
                    children: [
                      // Content Blocks
                      Icon(
                        Icons.article_outlined,
                        size: 14,
                        color: isLocked ? Colors.grey : Colors.grey[600],
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '${lesson.contentBlocks.length} ${lesson.contentBlocks.length == 1 ? 'item' : 'items'}',
                        style: TextStyle(
                          color: isLocked ? Colors.grey : Colors.grey[600],
                          fontSize: 12,
                          fontFamily: 'WorkSans',
                        ),
                      ),

                      const SizedBox(width: 16),

                      // Estimated Time
                      Icon(
                        Icons.access_time,
                        size: 14,
                        color: isLocked ? Colors.grey : Colors.grey[600],
                      ),
                      const SizedBox(width: 4),
                      Text(
                        _formatDuration(
                          lesson.estimatedTimeMinutes,
                        ), // Changed to estimatedTimeMinutes
                        style: TextStyle(
                          color: isLocked ? Colors.grey : Colors.grey[600],
                          fontSize: 12,
                          fontFamily: 'WorkSans',
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // Lock Icon or Arrow
            Icon(
              isLocked ? Icons.lock_outline : Icons.arrow_forward_ios,
              size: isLocked ? 20 : 16,
              color: isLocked ? Colors.grey : Colors.grey[600],
            ),
          ],
        ),
      ),
    );
  }

  // Format duration to readable string
  String _formatDuration(int totalMinutes) {
    // Changed to accept int minutes
    if (totalMinutes < 60) {
      return '$totalMinutes min';
    } else {
      final hours = totalMinutes ~/ 60;
      final remainingMinutes = totalMinutes % 60;
      return hours > 0
          ? '$hours hr${hours > 1 ? 's' : ''}${remainingMinutes > 0 ? ' $remainingMinutes min' : ''}'
          : '$remainingMinutes min';
    }
  }
}
