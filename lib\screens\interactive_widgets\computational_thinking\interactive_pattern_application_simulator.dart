import 'package:flutter/material.dart';
import 'dart:math';

class InteractivePatternApplicationSimulator extends StatefulWidget {
  const InteractivePatternApplicationSimulator({super.key});

  @override
  State<InteractivePatternApplicationSimulator> createState() => _InteractivePatternApplicationSimulatorState();
}

class _InteractivePatternApplicationSimulatorState extends State<InteractivePatternApplicationSimulator> {
  String _selectedPattern = 'Growth';
  double _initialValue = 10.0;
  double _rate = 0.1; // 10% growth or decay
  int _steps = 5;
  List<double> _simulationResults = [];

  final Map<String, String> _patternDescriptions = {
    'Growth': 'Applies a constant growth rate over time (e.g., population growth).',
    'Decay': 'Applies a constant decay rate over time (e.g., radioactive decay).',
    'Oscillation': 'Simulates a repeating up-and-down pattern (e.g., pendulum swing).',
  };

  @override
  void initState() {
    super.initState();
    _runSimulation();
  }

  void _runSimulation() {
    setState(() {
      _simulationResults.clear();
      double currentValue = _initialValue;
      _simulationResults.add(currentValue);

      for (int i = 0; i < _steps; i++) {
        if (_selectedPattern == 'Growth') {
          currentValue *= (1 + _rate);
        } else if (_selectedPattern == 'Decay') {
          currentValue *= (1 - _rate);
        } else if (_selectedPattern == 'Oscillation') {
          // Simple sine wave oscillation
          currentValue = _initialValue + (_initialValue * 0.5 * sin(i * pi / 2));
        }
        _simulationResults.add(currentValue);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Interactive Pattern Application Simulator',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Text('Pattern Type:'),
                const SizedBox(width: 10),
                DropdownButton<String>(
                  value: _selectedPattern,
                  onChanged: (String? newValue) {
                    setState(() {
                      _selectedPattern = newValue!;
                      _runSimulation();
                    });
                  },
                  items: _patternDescriptions.keys
                      .map<DropdownMenuItem<String>>((String value) {
                    return DropdownMenuItem<String>(
                      value: value,
                      child: Text(value),
                    );
                  }).toList(),
                ),
              ],
            ),
            const SizedBox(height: 10),
            Text(
              _patternDescriptions[_selectedPattern]!,
              style: TextStyle(fontStyle: FontStyle.italic, color: Colors.grey),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            Text('Initial Value: ${_initialValue.toStringAsFixed(1)}'),
            Slider(
              value: _initialValue,
              min: 1.0,
              max: 100.0,
              divisions: 99,
              onChanged: (newValue) {
                setState(() {
                  _initialValue = newValue;
                  _runSimulation();
                });
              },
            ),
            Text('Rate: ${(_rate * 100).toStringAsFixed(0)}%'),
            Slider(
              value: _rate,
              min: 0.01,
              max: 0.5,
              divisions: 49,
              onChanged: (newValue) {
                setState(() {
                  _rate = newValue;
                  _runSimulation();
                });
              },
            ),
            Text('Steps: $_steps'),
            Slider(
              value: _steps.toDouble(),
              min: 1.0,
              max: 10.0,
              divisions: 9,
              onChanged: (newValue) {
                setState(() {
                  _steps = newValue.toInt();
                  _runSimulation();
                });
              },
            ),
            const SizedBox(height: 20),
            const Text(
              'Simulation Results:',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 10),
            Container(
              height: 100,
              child: ListView.builder(
                itemCount: _simulationResults.length,
                itemBuilder: (context, index) {
                  return Text(
                    'Step $index: ${_simulationResults[index].toStringAsFixed(2)}',
                    textAlign: TextAlign.center,
                  );
                },
              ),
            ),
            const SizedBox(height: 20),
            CustomPaint(
              size: Size(300, 150),
              painter: _PatternGraphPainter(_simulationResults),
            ),
          ],
        ),
      ),
    );
  }
}

class _PatternGraphPainter extends CustomPainter {
  final List<double> data;

  _PatternGraphPainter(this.data);

  @override
  void paint(Canvas canvas, Size size) {
    final Paint linePaint = Paint()
      ..color = Colors.blue
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    final Paint pointPaint = Paint()
      ..color = Colors.red
      ..strokeWidth = 6
      ..style = PaintingStyle.fill;

    if (data.isEmpty) return;

    double minY = data.reduce(min);
    double maxY = data.reduce(max);
    if (minY == maxY) { // Handle constant function
      minY -= 1;
      maxY += 1;
    }

    final double scaleX = size.width / (data.length - 1).toDouble();
    final double scaleY = size.height / (maxY - minY);

    Offset toCanvas(int index, double value) {
      final double x = index * scaleX;
      final double y = size.height - (value - minY) * scaleY;
      return Offset(x, y);
    }

    final Path path = Path();
    path.moveTo(toCanvas(0, data[0]).dx, toCanvas(0, data[0]).dy);
    for (int i = 1; i < data.length; i++) {
      path.lineTo(toCanvas(i, data[i]).dx, toCanvas(i, data[i]).dy);
    }
    canvas.drawPath(path, linePaint);

    for (int i = 0; i < data.length; i++) {
      canvas.drawCircle(toCanvas(i, data[i]), 3, pointPaint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    _PatternGraphPainter oldPainter = oldDelegate as _PatternGraphPainter;
    return oldPainter.data != data;
  }
}
