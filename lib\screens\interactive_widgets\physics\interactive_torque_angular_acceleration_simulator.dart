import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:resonance_app/widgets/custom_text_field.dart';

class InteractiveTorqueAngularAccelerationSimulator extends StatefulWidget {
  const InteractiveTorqueAngularAccelerationSimulator({super.key});

  @override
  State<InteractiveTorqueAngularAccelerationSimulator> createState() =>
      _InteractiveTorqueAngularAccelerationSimulatorState();
}

class _InteractiveTorqueAngularAccelerationSimulatorState
    extends State<InteractiveTorqueAngularAccelerationSimulator> {
  final TextEditingController _torqueController = TextEditingController();
  final TextEditingController _momentOfInertiaController =
      TextEditingController();

  String _result = '';

  @override
  void dispose() {
    _torqueController.dispose();
    _momentOfInertiaController.dispose();
    super.dispose();
  }

  void _calculateAngularAcceleration() {
    setState(() {
      _result = ''; // Clear previous result

      double? torque = double.tryParse(_torqueController.text);
      double? momentOfInertia =
          double.tryParse(_momentOfInertiaController.text);

      if (torque == null || momentOfInertia == null) {
        _result = 'Please enter valid numerical values for Torque and Moment of Inertia.';
        return;
      }

      if (momentOfInertia == 0) {
        _result = 'Moment of Inertia cannot be zero.';
        return;
      }

      try {
        double angularAcceleration = torque / momentOfInertia;
        _result = 'Angular Acceleration (α): ${angularAcceleration.toStringAsFixed(4)} rad/s²';
      } catch (e) {
        _result = 'Error: Invalid input or calculation issue.';
      }
    });
  }

  void _clearFields() {
    setState(() {
      _torqueController.clear();
      _momentOfInertiaController.clear();
      _result = '';
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Torque & Angular Acceleration Simulator'),
        backgroundColor: Colors.deepPurple,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            CustomTextField(
              controller: _torqueController,
              labelText: 'Torque (τ) in N·m',
              keyboardType:
                  const TextInputType.numberWithOptions(decimal: true),
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp(r'^-?\d*\.?\d*')),
              ],
            ),
            const SizedBox(height: 12.0),
            CustomTextField(
              controller: _momentOfInertiaController,
              labelText: 'Moment of Inertia (I) in kg·m²',
              keyboardType:
                  const TextInputType.numberWithOptions(decimal: true),
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp(r'^-?\d*\.?\d*')),
              ],
            ),
            const SizedBox(height: 20.0),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ElevatedButton(
                  onPressed: _calculateAngularAcceleration,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.deepPurple,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 30, vertical: 15),
                    textStyle: const TextStyle(fontSize: 16),
                  ),
                  child: const Text('Calculate'),
                ),
                ElevatedButton(
                  onPressed: _clearFields,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 30, vertical: 15),
                    textStyle: const TextStyle(fontSize: 16),
                  ),
                  child: const Text('Clear'),
                ),
              ],
            ),
            const SizedBox(height: 20.0),
            Text(
              _result,
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
