import 'package:flutter/material.dart';

class InteractiveLimitApproachVisualizer extends StatefulWidget {
  const InteractiveLimitApproachVisualizer({super.key});

  @override
  State<InteractiveLimitApproachVisualizer> createState() => _InteractiveLimitApproachVisualizerState();
}

class _InteractiveLimitApproachVisualizerState extends State<InteractiveLimitApproachVisualizer> {
  double _xValue = 0.0;
  final double _limitPoint = 2.0; // The point x approaches

  double _function(double x) {
    // Example function: (x^2 - 4) / (x - 2) which simplifies to x + 2 for x != 2
    if (x == _limitPoint) {
      return double.nan; // Undefined at the limit point for this example
    }
    return (x * x - 4) / (x - 2);
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Interactive Limit Approach Visualizer',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 20),
            Text(
              'Function: f(x) = (x² - 4) / (x - 2)',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 10),
            Text(
              'Approaching x = $_limitPoint',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 20),
            Slider(
              value: _xValue,
              min: _limitPoint - 3.0,
              max: _limitPoint + 3.0,
              divisions: 100,
              onChanged: (newValue) {
                setState(() {
                  _xValue = newValue;
                });
              },
            ),
            const SizedBox(height: 10),
            Text(
              'Current x: ${_xValue.toStringAsFixed(3)}',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 10),
            Text(
              'f(${_xValue.toStringAsFixed(3)}): ${_function(_xValue).toStringAsFixed(3)}',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 20),
            CustomPaint(
              size: Size(300, 200),
              painter: _LimitGraphPainter(_function, _limitPoint, _xValue),
            ),
          ],
        ),
      ),
    );
  }
}

class _LimitGraphPainter extends CustomPainter {
  final Function(double) _function;
  final double _limitPoint;
  final double _currentX;

  _LimitGraphPainter(this._function, this._limitPoint, this._currentX);

  @override
  void paint(Canvas canvas, Size size) {
    final Paint paint = Paint()
      ..color = Colors.blue
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    final Paint pointPaint = Paint()
      ..color = Colors.red
      ..strokeWidth = 8
      ..style = PaintingStyle.fill;

    final Paint limitLinePaint = Paint()
      ..color = Colors.green
      ..strokeWidth = 1
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    final double scaleX = size.width / 6.0; // x from -1 to 5 (range of 6)
    final double scaleY = size.height / 10.0; // y from -5 to 5 (range of 10)

    // Draw axes
    canvas.drawLine(Offset(0, size.height / 2), Offset(size.width, size.height / 2), paint..color = Colors.grey); // X-axis
    canvas.drawLine(Offset(size.width / 2, 0), Offset(size.width / 2, size.height), paint..color = Colors.grey); // Y-axis

    // Draw function graph
    final Path path = Path();
    bool firstPoint = true;
    for (double i = -1.0; i <= 5.0; i += 0.1) {
      double x = (i - (_limitPoint - 3.0)) * scaleX; // Adjust x to fit the slider range
      double y = size.height / 2 - _function(i) * scaleY; // Adjust y for canvas coordinates

      if (!y.isNaN && y.isFinite) {
        if (firstPoint) {
          path.moveTo(x, y);
          firstPoint = false;
        } else {
          path.lineTo(x, y);
        }
      } else {
        firstPoint = true; // Reset for discontinuities
      }
    }
    canvas.drawPath(path, paint);

    // Draw limit point on X-axis
    double limitXCanvas = (_limitPoint - (_limitPoint - 3.0)) * scaleX;
    canvas.drawLine(Offset(limitXCanvas, size.height / 2 - 5), Offset(limitXCanvas, size.height / 2 + 5), limitLinePaint);
    TextPainter(
      text: TextSpan(text: 'x=${_limitPoint.toStringAsFixed(1)}', style: TextStyle(color: Colors.green, fontSize: 10)),
      textDirection: TextDirection.ltr,
    )..layout()..paint(canvas, Offset(limitXCanvas - 20, size.height / 2 + 10));

    // Draw current x and f(x) point
    double currentXCanvas = (_currentX - (_limitPoint - 3.0)) * scaleX;
    double currentYCanvas = size.height / 2 - _function(_currentX) * scaleY;

    if (!currentYCanvas.isNaN && currentYCanvas.isFinite) {
      canvas.drawCircle(Offset(currentXCanvas, currentYCanvas), 4, pointPaint);
      // Draw lines to axes
      canvas.drawLine(Offset(currentXCanvas, currentYCanvas), Offset(currentXCanvas, size.height / 2), limitLinePaint);
      canvas.drawLine(Offset(currentXCanvas, currentYCanvas), Offset(size.width / 2, currentYCanvas), limitLinePaint);

      TextPainter(
        text: TextSpan(text: '(${_currentX.toStringAsFixed(2)}, ${_function(_currentX).toStringAsFixed(2)})', style: TextStyle(color: Colors.red, fontSize: 10)),
        textDirection: TextDirection.ltr,
      )..layout()..paint(canvas, Offset(currentXCanvas + 5, currentYCanvas - 15));
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    _LimitGraphPainter oldPainter = oldDelegate as _LimitGraphPainter;
    return oldPainter._currentX != _currentX;
  }
}
