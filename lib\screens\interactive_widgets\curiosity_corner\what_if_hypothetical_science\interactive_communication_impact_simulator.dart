import 'package:flutter/material.dart';
import 'package:resonance/widgets/interactive_widget_scaffold.dart';

class InteractiveCommunicationImpactSimulator extends StatefulWidget {
  const InteractiveCommunicationImpactSimulator({super.key});

  @override
  State<InteractiveCommunicationImpactSimulator> createState() => _InteractiveCommunicationImpactSimulatorState();
}

class _InteractiveCommunicationImpactSimulatorState extends State<InteractiveCommunicationImpactSimulator> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  double _speedFactor = 1.0;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 2),
    )..repeat(reverse: true);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return InteractiveWidgetScaffold(
      title: 'Communication Impact Simulator',
      description: 'Explore how communication is affected by different light speeds.',
      interactiveContent: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            AnimatedBuilder(
              animation: _controller,
              builder: (context, child) {
                return Transform.scale(
                  scale: 1.0 + (_controller.value * 0.2),
                  child: Icon(Icons.wifi, size: 100 * _speedFactor, color: Colors.blue),
                );
              },
            ),
            const SizedBox(height: 20),
            Text(
              'Communication Speed Factor: ${_speedFactor.toStringAsFixed(1)}x',
              style: const TextStyle(fontSize: 18),
            ),
            Slider(
              value: _speedFactor,
              min: 0.1,
              max: 2.0,
              divisions: 19,
              label: _speedFactor.toStringAsFixed(1),
              onChanged: (value) {
                setState(() {
                  _speedFactor = value;
                  _controller.duration = Duration(milliseconds: (2000 / _speedFactor).round());
                  _controller.repeat(reverse: true);
                });
              },
            ),
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: Text(
                'Adjust the slider to see how the speed factor affects communication signal visualization. A lower speed factor simulates slower light, impacting signal propagation.',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 16),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
