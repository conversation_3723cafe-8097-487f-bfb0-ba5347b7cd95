import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:resonance_app/widgets/custom_text_field.dart';

class InteractiveRotationalKinematicsCalculator extends StatefulWidget {
  const InteractiveRotationalKinematicsCalculator({super.key});

  @override
  State<InteractiveRotationalKinematicsCalculator> createState() =>
      _InteractiveRotationalKinematicsCalculatorState();
}

class _InteractiveRotationalKinematicsCalculatorState
    extends State<InteractiveRotationalKinematicsCalculator> {
  final TextEditingController _thetaController = TextEditingController();
  final TextEditingController _omega0Controller = TextEditingController();
  final TextEditingController _omegaController = TextEditingController();
  final TextEditingController _alphaController = TextEditingController();
  final TextEditingController _timeController = TextEditingController();

  String _variableToCalculate = 'theta'; // Default variable to calculate
  String _result = '';

  @override
  void dispose() {
    _thetaController.dispose();
    _omega0Controller.dispose();
    _omegaController.dispose();
    _alphaController.dispose();
    _timeController.dispose();
    super.dispose();
  }

  void _calculate() {
    setState(() {
      _result = ''; // Clear previous result

      double? theta = double.tryParse(_thetaController.text);
      double? omega0 = double.tryParse(_omega0Controller.text);
      double? omega = double.tryParse(_omegaController.text);
      double? alpha = double.tryParse(_alphaController.text);
      double? time = double.tryParse(_timeController.text);

      int knownVariablesCount = 0;
      if (theta != null) knownVariablesCount++;
      if (omega0 != null) knownVariablesCount++;
      if (omega != null) knownVariablesCount++;
      if (alpha != null) knownVariablesCount++;
      if (time != null) knownVariablesCount++;

      if (knownVariablesCount < 4) {
        _result = 'Please enter at least four known variables.';
        return;
      }

      try {
        switch (_variableToCalculate) {
          case 'theta':
            if (omega0 != null && alpha != null && time != null) {
              theta = omega0 * time + 0.5 * alpha * time * time;
              _result = 'Angular Displacement (θ): ${theta.toStringAsFixed(4)} rad';
            } else if (omega0 != null && omega != null && time != null) {
              theta = 0.5 * (omega0 + omega) * time;
              _result = 'Angular Displacement (θ): ${theta.toStringAsFixed(4)} rad';
            } else if (omega0 != null && omega != null && alpha != null) {
              theta = (omega * omega - omega0 * omega0) / (2 * alpha);
              _result = 'Angular Displacement (θ): ${theta.toStringAsFixed(4)} rad';
            } else {
              _result = 'Insufficient variables to calculate θ.';
            }
            break;
          case 'omega0':
            if (omega != null && alpha != null && time != null) {
              omega0 = omega - alpha * time;
              _result = 'Initial Angular Velocity (ω₀): ${omega0.toStringAsFixed(4)} rad/s';
            } else if (theta != null && alpha != null && time != null) {
              omega0 = (theta - 0.5 * alpha * time * time) / time;
              _result = 'Initial Angular Velocity (ω₀): ${omega0.toStringAsFixed(4)} rad/s';
            } else if (omega != null && alpha != null && theta != null) {
              omega0 = (omega * omega - 2 * alpha * theta) >= 0
                  ? -((omega * omega - 2 * alpha * theta) >= 0
                      ? (omega * omega - 2 * alpha * theta)
                      : 0.0)
                  : double.nan;
              if (omega0.isNaN) {
                _result = 'Cannot calculate ω₀ (negative value under square root).';
              } else {
                _result = 'Initial Angular Velocity (ω₀): ${omega0.toStringAsFixed(4)} rad/s';
              }
            } else if (theta != null && omega != null && time != null) {
              omega0 = (2 * theta / time) - omega;
              _result = 'Initial Angular Velocity (ω₀): ${omega0.toStringAsFixed(4)} rad/s';
            } else {
              _result = 'Insufficient variables to calculate ω₀.';
            }
            break;
          case 'omega':
            if (omega0 != null && alpha != null && time != null) {
              omega = omega0 + alpha * time;
              _result = 'Final Angular Velocity (ω): ${omega.toStringAsFixed(4)} rad/s';
            } else if (omega0 != null && alpha != null && theta != null) {
              omega = (omega0 * omega0 + 2 * alpha * theta) >= 0
                  ? ((omega0 * omega0 + 2 * alpha * theta) >= 0
                      ? (omega0 * omega0 + 2 * alpha * theta)
                      : 0.0)
                  : double.nan;
              if (omega.isNaN) {
                _result = 'Cannot calculate ω (negative value under square root).';
              } else {
                _result = 'Final Angular Velocity (ω): ${omega.toStringAsFixed(4)} rad/s';
              }
            } else if (theta != null && omega0 != null && time != null) {
              omega = (2 * theta / time) - omega0;
              _result = 'Final Angular Velocity (ω): ${omega.toStringAsFixed(4)} rad/s';
            } else {
              _result = 'Insufficient variables to calculate ω.';
            }
            break;
          case 'alpha':
            if (omega0 != null && omega != null && time != null) {
              alpha = (omega - omega0) / time;
              _result = 'Angular Acceleration (α): ${alpha.toStringAsFixed(4)} rad/s²';
            } else if (theta != null && omega0 != null && time != null) {
              alpha = 2 * (theta - omega0 * time) / (time * time);
              _result = 'Angular Acceleration (α): ${alpha.toStringAsFixed(4)} rad/s²';
            } else if (omega0 != null && omega != null && theta != null) {
              alpha = (omega * omega - omega0 * omega0) / (2 * theta);
              _result = 'Angular Acceleration (α): ${alpha.toStringAsFixed(4)} rad/s²';
            } else {
              _result = 'Insufficient variables to calculate α.';
            }
            break;
          case 'time':
            if (omega0 != null && omega != null && alpha != null) {
              time = (omega - omega0) / alpha;
              _result = 'Time (t): ${time.toStringAsFixed(4)} s';
            } else if (theta != null && omega0 != null && alpha != null) {
              // Quadratic equation: 0.5 * alpha * t^2 + omega0 * t - theta = 0
              double a = 0.5 * alpha!;
              double b = omega0!;
              double c = -theta!;
              double discriminant = b * b - 4 * a * c;

              if (discriminant < 0) {
                _result = 'Cannot calculate time (negative discriminant).';
              } else {
                double t1 = (-b + (discriminant)) / (2 * a);
                double t2 = (-b - (discriminant)) / (2 * a);
                if (t1 >= 0 && t2 >= 0) {
                  time = t1 < t2 ? t1 : t2; // Take the smaller positive time
                } else if (t1 >= 0) {
                  time = t1;
                } else if (t2 >= 0) {
                  time = t2;
                } else {
                  _result = 'Cannot calculate time (no positive solution).';
                }
                if (time != null) {
                  _result = 'Time (t): ${time.toStringAsFixed(4)} s';
                } else {
                  _result = 'Cannot calculate time (no positive solution).';
                }
              }
            } else if (theta != null && omega0 != null && omega != null) {
              time = 2 * theta / (omega0 + omega);
              _result = 'Time (t): ${time.toStringAsFixed(4)} s';
            } else {
              _result = 'Insufficient variables to calculate t.';
            }
            break;
        }
      } catch (e) {
        _result = 'Error: Invalid input or calculation issue.';
      }
    });
  }

  void _clearFields() {
    setState(() {
      _thetaController.clear();
      _omega0Controller.clear();
      _omegaController.clear();
      _alphaController.clear();
      _timeController.clear();
      _result = '';
      _variableToCalculate = 'theta';
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Rotational Kinematics Calculator'),
        backgroundColor: Colors.deepPurple,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            CustomTextField(
              controller: _thetaController,
              labelText: 'Angular Displacement (θ) in rad',
              keyboardType:
                  const TextInputType.numberWithOptions(decimal: true),
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp(r'^-?\d*\.?\d*')),
              ],
              enabled: _variableToCalculate != 'theta',
            ),
            const SizedBox(height: 12.0),
            CustomTextField(
              controller: _omega0Controller,
              labelText: 'Initial Angular Velocity (ω₀) in rad/s',
              keyboardType:
                  const TextInputType.numberWithOptions(decimal: true),
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp(r'^-?\d*\.?\d*')),
              ],
              enabled: _variableToCalculate != 'omega0',
            ),
            const SizedBox(height: 12.0),
            CustomTextField(
              controller: _omegaController,
              labelText: 'Final Angular Velocity (ω) in rad/s',
              keyboardType:
                  const TextInputType.numberWithOptions(decimal: true),
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp(r'^-?\d*\.?\d*')),
              ],
              enabled: _variableToCalculate != 'omega',
            ),
            const SizedBox(height: 12.0),
            CustomTextField(
              controller: _alphaController,
              labelText: 'Angular Acceleration (α) in rad/s²',
              keyboardType:
                  const TextInputType.numberWithOptions(decimal: true),
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp(r'^-?\d*\.?\d*')),
              ],
              enabled: _variableToCalculate != 'alpha',
            ),
            const SizedBox(height: 12.0),
            CustomTextField(
              controller: _timeController,
              labelText: 'Time (t) in s',
              keyboardType:
                  const TextInputType.numberWithOptions(decimal: true),
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp(r'^-?\d*\.?\d*')),
              ],
              enabled: _variableToCalculate != 'time',
            ),
            const SizedBox(height: 20.0),
            DropdownButtonFormField<String>(
              value: _variableToCalculate,
              decoration: const InputDecoration(
                labelText: 'Calculate',
                border: OutlineInputBorder(),
              ),
              items: const [
                DropdownMenuItem(value: 'theta', child: Text('Angular Displacement (θ)')),
                DropdownMenuItem(value: 'omega0', child: Text('Initial Angular Velocity (ω₀)')),
                DropdownMenuItem(value: 'omega', child: Text('Final Angular Velocity (ω)')),
                DropdownMenuItem(value: 'alpha', child: Text('Angular Acceleration (α)')),
                DropdownMenuItem(value: 'time', child: Text('Time (t)')),
              ],
              onChanged: (value) {
                setState(() {
                  _variableToCalculate = value!;
                  _clearFields(); // Clear fields when changing calculation target
                });
              },
            ),
            const SizedBox(height: 20.0),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ElevatedButton(
                  onPressed: _calculate,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.deepPurple,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 30, vertical: 15),
                    textStyle: const TextStyle(fontSize: 16),
                  ),
                  child: const Text('Calculate'),
                ),
                ElevatedButton(
                  onPressed: _clearFields,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 30, vertical: 15),
                    textStyle: const TextStyle(fontSize: 16),
                  ),
                  child: const Text('Clear'),
                ),
              ],
            ),
            const SizedBox(height: 20.0),
            Text(
              _result,
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
