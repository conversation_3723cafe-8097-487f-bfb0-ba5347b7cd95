import 'package:flutter/material.dart';
import 'package:resonance/widgets/interactive_widget_scaffold.dart';

class InteractiveEnergyProductionCalculator extends StatefulWidget {
  const InteractiveEnergyProductionCalculator({super.key});

  @override
  State<InteractiveEnergyProductionCalculator> createState() => _InteractiveEnergyProductionCalculatorState();
}

class _InteractiveEnergyProductionCalculatorState extends State<InteractiveEnergyProductionCalculator> {
  double _sunlightExposureHours = 8.0;
  double _photosynthesisEfficiency = 0.1; // as a fraction, e.g., 0.1 for 10%
  double _energyProducedKcal = 0.0;

  @override
  void initState() {
    super.initState();
    _calculateEnergyProduction();
  }

  void _calculateEnergyProduction() {
    // Simplified model: Energy = Sunlight * Efficiency * ConversionFactor
    // Assuming 1 hour of direct sunlight provides X kcal, adjust conversion factor as needed
    const double sunlightToKcalConversion = 500.0; // Arbitrary value for demonstration
    setState(() {
      _energyProducedKcal = _sunlightExposureHours * _photosynthesisEfficiency * sunlightToKcalConversion;
    });
  }

  @override
  Widget build(BuildContext context) {
    return InteractiveWidgetScaffold(
      title: 'Energy Production Calculator',
      description: 'Calculate energy production from human photosynthesis.',
      interactiveContent: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.wb_sunny, size: 100, color: Colors.yellow.shade700),
            const SizedBox(height: 20),
            Text(
              'Daily Sunlight Exposure: ${_sunlightExposureHours.toStringAsFixed(1)} hours',
              style: const TextStyle(fontSize: 18),
            ),
            Slider(
              value: _sunlightExposureHours,
              min: 0.0,
              max: 12.0,
              divisions: 12,
              label: _sunlightExposureHours.toStringAsFixed(1),
              onChanged: (value) {
                setState(() {
                  _sunlightExposureHours = value;
                  _calculateEnergyProduction();
                });
              },
            ),
            const SizedBox(height: 10),
            Text(
              'Photosynthesis Efficiency: ${(_photosynthesisEfficiency * 100).toStringAsFixed(0)}%',
              style: const TextStyle(fontSize: 18),
            ),
            Slider(
              value: _photosynthesisEfficiency,
              min: 0.01,
              max: 0.5,
              divisions: 49,
              label: (_photosynthesisEfficiency * 100).toStringAsFixed(0),
              onChanged: (value) {
                setState(() {
                  _photosynthesisEfficiency = value;
                  _calculateEnergyProduction();
                });
              },
            ),
            const SizedBox(height: 20),
            Text(
              'Estimated Energy Produced: ${_energyProducedKcal.toStringAsFixed(1)} kcal/day',
              style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: Text(
                'Adjust the daily sunlight exposure and the hypothetical photosynthesis efficiency to see how much energy a photosynthesizing human could produce. This is a simplified model for illustrative purposes.',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 16),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
