import 'package:flutter/material.dart';
import 'package:resonance/widgets/animated_button.dart'; // Assuming this is a common button widget

class InteractiveKinematicsChallenge extends StatefulWidget {
  const InteractiveKinematicsChallenge({super.key});

  @override
  State<InteractiveKinematicsChallenge> createState() => _InteractiveKinematicsChallengeState();
}

class _InteractiveKinematicsChallengeState extends State<InteractiveKinematicsChallenge> {
  int _score = 0;
  String _currentQuestion = "What is the formula for displacement?";
  List<String> _options = ["v = d/t", "a = Δv/Δt", "Δx = v₀t + ½at²", "F = ma"];
  String? _selectedOption;
  String _feedback = "";

  void _checkAnswer(String selectedAnswer) {
    setState(() {
      _selectedOption = selectedAnswer;
      if (selectedAnswer == "Δx = v₀t + ½at²") {
        _feedback = "Correct!";
        _score++;
      } else {
        _feedback = "Incorrect. Try again!";
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Kinematics Challenge'),
        backgroundColor: Colors.blueAccent,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text(
              _currentQuestion,
              style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            ..._options.map((option) => Padding(
              padding: const EdgeInsets.symmetric(vertical: 8.0),
              child: AnimatedButton(
                onTap: () => _checkAnswer(option),
                label: option,
                backgroundColor: _selectedOption == option
                    ? (_feedback == "Correct!" ? Colors.green : Colors.red)
                    : Colors.blueGrey,
                textColor: Colors.white,
              ),
            )).toList(),
            const SizedBox(height: 20),
            Text(
              _feedback,
              style: TextStyle(
                fontSize: 18,
                color: _feedback == "Correct!" ? Colors.green : Colors.red,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            Text(
              'Score: $_score',
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
