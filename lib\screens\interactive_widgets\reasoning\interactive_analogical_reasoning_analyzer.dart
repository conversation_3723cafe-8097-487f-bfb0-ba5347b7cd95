import 'package:flutter/material.dart';
import 'package:resonance/widgets/animated_button.dart';

class InteractiveAnalogicalReasoningAnalyzer extends StatefulWidget {
  const InteractiveAnalogicalReasoningAnalyzer({super.key});

  @override
  State<InteractiveAnalogicalReasoningAnalyzer> createState() => _InteractiveAnalogicalReasoningAnalyzerState();
}

class _InteractiveAnalogicalReasoningAnalyzerState extends State<InteractiveAnalogicalReasoningAnalyzer> {
  String _sourceDomain = '';
  String _targetDomain = '';
  String _sharedFeatures = '';
  String _conclusionFeature = '';
  String _analysisResult = '';

  void _analyzeAnalogy() {
    setState(() {
      if (_sourceDomain.isEmpty || _targetDomain.isEmpty || _sharedFeatures.isEmpty || _conclusionFeature.isEmpty) {
        _analysisResult = 'Please fill in all fields to analyze the analogy.';
        return;
      }

      _analysisResult = 'Analyzing Analogy:\n\n'
          'Source Domain: $_sourceDomain\n'
          'Target Domain: $_targetDomain\n'
          'Shared Features: $_sharedFeatures\n'
          'Conclusion Feature: $_conclusionFeature\n\n'
          'This tool helps in understanding analogical reasoning. '
          'In a real scenario, it would assess the relevance and number of shared features, '
          'the disanalogies, and the overall strength of the analogical argument '
          'in transferring the conclusion feature from the source to the target domain.';
    });
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text(
            'Analogical Reasoning Analyzer',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.primary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 20),
          TextField(
            onChanged: (value) {
              setState(() {
                _sourceDomain = value;
              });
            },
            decoration: InputDecoration(
              labelText: 'Source Domain (known)',
              hintText: 'e.g., "Human heart"',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              filled: true,
              fillColor: Theme.of(context).cardColor,
            ),
          ),
          const SizedBox(height: 10),
          TextField(
            onChanged: (value) {
              setState(() {
                _targetDomain = value;
              });
            },
            decoration: InputDecoration(
              labelText: 'Target Domain (unknown)',
              hintText: 'e.g., "Mechanical pump"',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              filled: true,
              fillColor: Theme.of(context).cardColor,
            ),
          ),
          const SizedBox(height: 10),
          TextField(
            onChanged: (value) {
              setState(() {
                _sharedFeatures = value;
              });
            },
            maxLines: 2,
            decoration: InputDecoration(
              labelText: 'Shared Features (comma-separated)',
              hintText: 'e.g., "Pumps blood, has valves"',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              filled: true,
              fillColor: Theme.of(context).cardColor,
            ),
          ),
          const SizedBox(height: 10),
          TextField(
            onChanged: (value) {
              setState(() {
                _conclusionFeature = value;
              });
            },
            decoration: InputDecoration(
              labelText: 'Conclusion Feature (inferred in target)',
              hintText: 'e.g., "Circulates fluid"',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              filled: true,
              fillColor: Theme.of(context).cardColor,
            ),
          ),
          const SizedBox(height: 20),
          AnimatedButton(
            onTap: _analyzeAnalogy,
            label: 'Analyze Analogy',
            color: Theme.of(context).colorScheme.secondary,
            labelColor: Theme.of(context).colorScheme.onSecondary,
          ),
          const SizedBox(height: 20),
          Expanded(
            child: SingleChildScrollView(
              child: Card(
                elevation: 4,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                color: Theme.of(context).colorScheme.surfaceVariant,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Analogy Analysis:',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                      const SizedBox(height: 10),
                      Text(
                        _analysisResult.isEmpty ? 'Fill in the fields and click "Analyze Analogy" to see the result.' : _analysisResult,
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
