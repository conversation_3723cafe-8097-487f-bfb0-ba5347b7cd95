import 'package:flutter/material.dart';
import '../../models/user_preferences.dart';
import '../../widgets/selection_option.dart';

// Screen 7: Daily goal and time preference
class DailyGoalScreen extends StatefulWidget {
  final UserPreferences userPreferences;
  final VoidCallback onContinue;

  const DailyGoalScreen({
    Key? key,
    required this.userPreferences,
    required this.onContinue,
  }) : super(key: key);

  @override
  _DailyGoalScreenState createState() => _DailyGoalScreenState();
}

class _DailyGoalScreenState extends State<DailyGoalScreen> {
  final List<int> _minuteOptions = [5, 10, 15, 20];
  final List<String> _timePreferences = [
    'Morning routine',
    'During breakfast',
    'Quick break during lunch',
    'Evening/night time',
    'Another time in my day',
  ];

  int? _selectedMinutes;
  String? _selectedTimePreference;

  @override
  void initState() {
    super.initState();
    _selectedMinutes = widget.userPreferences.dailyGoalMinutes;
    _selectedTimePreference = widget.userPreferences.learningTimePreference;
  }

  void _selectMinutes(int minutes) {
    setState(() {
      _selectedMinutes = minutes;
      widget.userPreferences.dailyGoalMinutes = minutes;
    });
  }

  void _selectTimePreference(String preference) {
    setState(() {
      _selectedTimePreference = preference;
      widget.userPreferences.learningTimePreference = preference;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'What\'s your daily brilliance goal?',
            style: TextStyle(
              color: Color.fromRGBO(16, 17, 20, 1),
              fontFamily: 'WorkSans',
              fontSize: 30,
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 24),

          // Minutes options
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children:
                _minuteOptions.map((minutes) {
                  return GestureDetector(
                    onTap: () => _selectMinutes(minutes),
                    child: Container(
                      width: 70,
                      padding: EdgeInsets.symmetric(vertical: 16),
                      decoration: BoxDecoration(
                        color:
                            _selectedMinutes == minutes
                                ? Color.fromRGBO(100, 45, 176, 1)
                                : Color.fromRGBO(243, 243, 243, 1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      alignment: Alignment.center,
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            '$minutes',
                            style: TextStyle(
                              color:
                                  _selectedMinutes == minutes
                                      ? Colors.white
                                      : Color.fromRGBO(16, 17, 20, 1),
                              fontFamily: 'WorkSans',
                              fontSize: 20,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          Text(
                            'min',
                            style: TextStyle(
                              color:
                                  _selectedMinutes == minutes
                                      ? Colors.white.withOpacity(0.8)
                                      : Color.fromRGBO(16, 17, 20, 0.6),
                              fontFamily: 'WorkSans',
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                }).toList(),
          ),

          SizedBox(height: 32),

          Text(
            'How will learning fit into your day?',
            style: TextStyle(
              color: Color.fromRGBO(16, 17, 20, 1),
              fontFamily: 'WorkSans',
              fontSize: 24,
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 16),

          // Time preference options
          Expanded(
            child: ListView.builder(
              itemCount: _timePreferences.length,
              itemBuilder: (context, index) {
                return SelectionOption(
                  text: _timePreferences[index],
                  isSelected:
                      _selectedTimePreference == _timePreferences[index],
                  onTap: () => _selectTimePreference(_timePreferences[index]),
                  icon: _getIconForTimePreference(_timePreferences[index]),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  IconData? _getIconForTimePreference(String preference) {
    switch (preference) {
      case 'Morning routine':
        return Icons.wb_sunny;
      case 'During breakfast':
        return Icons.free_breakfast;
      case 'Quick break during lunch':
        return Icons.lunch_dining;
      case 'Evening/night time':
        return Icons.nightlight_round;
      case 'Another time in my day':
        return Icons.access_time;
      default:
        return null;
    }
  }
}

// Screen 8: Notifications permission
class NotificationsScreen extends StatefulWidget {
  final UserPreferences userPreferences;
  final VoidCallback onContinue;

  const NotificationsScreen({
    Key? key,
    required this.userPreferences,
    required this.onContinue,
  }) : super(key: key);

  @override
  _NotificationsScreenState createState() => _NotificationsScreenState();
}

class _NotificationsScreenState extends State<NotificationsScreen> {
  bool? _allowNotifications;

  @override
  void initState() {
    super.initState();
    _allowNotifications = widget.userPreferences.allowNotifications;
  }

  void _setNotificationPreference(bool allow) {
    setState(() {
      _allowNotifications = allow;
      widget.userPreferences.allowNotifications = allow;
    });

    // Automatically continue after selection
    Future.delayed(Duration(milliseconds: 500), widget.onContinue);
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Notification icon
          Icon(
            Icons.notifications_active,
            size: 120,
            color: Color.fromRGBO(100, 45, 176, 1),
          ),
          SizedBox(height: 32),
          Text(
            'Stay on track with reminders',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Color.fromRGBO(16, 17, 20, 1),
              fontFamily: 'WorkSans',
              fontSize: 30,
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 16),
          Text(
            'We\'ll send you gentle reminders to help you meet your daily learning goals.',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Color.fromRGBO(57, 59, 66, 1),
              fontFamily: 'WorkSans',
              fontSize: 18,
              fontWeight: FontWeight.w400,
            ),
          ),
          SizedBox(height: 48),

          // Notification permission buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              ElevatedButton(
                onPressed: () => _setNotificationPreference(true),
                style: ElevatedButton.styleFrom(
                  foregroundColor: Colors.white,
                  backgroundColor: Color.fromRGBO(100, 45, 176, 1),
                  padding: EdgeInsets.symmetric(horizontal: 24, vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(19),
                  ),
                ),
                child: Text(
                  'Allow',
                  style: TextStyle(fontFamily: 'WorkSans', fontSize: 16),
                ),
              ),
              SizedBox(width: 16),
              TextButton(
                onPressed: () => _setNotificationPreference(false),
                style: TextButton.styleFrom(
                  foregroundColor: Color.fromRGBO(16, 17, 20, 1),
                  padding: EdgeInsets.symmetric(horizontal: 24, vertical: 16),
                ),
                child: Text(
                  'No thanks',
                  style: TextStyle(fontFamily: 'WorkSans', fontSize: 16),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
