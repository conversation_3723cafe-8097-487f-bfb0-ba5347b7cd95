import 'package:flutter/material.dart';
import '../../../models/course_models.dart';

class CourseListItem extends StatelessWidget {
  final String title;
  final Widget icon;
  final double progress;
  final bool isInProgress;
  final bool isNew;
  final VoidCallback onTap;

  const CourseListItem({
    Key? key,
    required this.title,
    required this.icon,
    this.progress = 0.0,
    this.isInProgress = false,
    this.isNew = false,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.only(bottom: 16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 5,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            children: [
              // Course info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Status badges
                    if (isInProgress || isNew)
                      Padding(
                        padding: const EdgeInsets.only(bottom: 8.0),
                        child: Row(
                          children: [
                            if (isInProgress)
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 4,
                                ),
                                decoration: BoxDecoration(
                                  color: Colors.green[100],
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Text(
                                  'IN PROGRESS',
                                  style: TextStyle(
                                    color: Colors.green[800],
                                    fontSize: 10,
                                    fontWeight: FontWeight.bold,
                                    fontFamily: 'WorkSans',
                                  ),
                                ),
                              ),
                            if (isNew)
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 4,
                                ),
                                decoration: BoxDecoration(
                                  color: Colors.green[100],
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Text(
                                  'NEW',
                                  style: TextStyle(
                                    color: Colors.green[800],
                                    fontSize: 10,
                                    fontWeight: FontWeight.bold,
                                    fontFamily: 'WorkSans',
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ),
                    
                    // Course title
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        fontFamily: 'WorkSans',
                      ),
                    ),
                    
                    // Progress bar
                    if (progress > 0) ...[
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Expanded(
                            child: Stack(
                              children: [
                                // Background
                                Container(
                                  height: 4,
                                  decoration: BoxDecoration(
                                    color: Colors.grey[200],
                                    borderRadius: BorderRadius.circular(2),
                                  ),
                                ),
                                // Progress
                                FractionallySizedBox(
                                  widthFactor: progress / 100,
                                  child: Container(
                                    height: 4,
                                    decoration: BoxDecoration(
                                      color: Colors.green,
                                      borderRadius: BorderRadius.circular(2),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ],
                ),
              ),
              
              // Course icon
              const SizedBox(width: 16),
              SizedBox(
                width: 60,
                height: 60,
                child: icon,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
