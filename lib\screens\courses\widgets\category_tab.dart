import 'package:flutter/material.dart';
import '../../../models/course_models.dart';

class CategoryTab extends StatelessWidget {
  final CourseCategory category;
  final bool isSelected;
  final VoidCallback onTap;

  const CategoryTab({
    Key? key,
    required this.category,
    required this.isSelected,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Parse the color from hex string
    final color = _parseColor(category.color);
    
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? color.withOpacity(0.1) : Colors.transparent,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected ? color : Colors.grey[300]!,
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Category Icon
            Image.asset(
              category.iconPath,
              width: 20,
              height: 20,
              errorBuilder: (context, error, stackTrace) {
                return Icon(
                  _getCategoryIcon(category.id),
                  size: 20,
                  color: isSelected ? color : Colors.grey[600],
                );
              },
            ),
            const SizedBox(width: 8),
            
            // Category Name
            Text(
              category.name,
              style: TextStyle(
                color: isSelected ? color : Colors.grey[600],
                fontSize: 14,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                fontFamily: 'WorkSans',
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  // Parse hex color string to Color
  Color _parseColor(String hexColor) {
    try {
      hexColor = hexColor.replaceAll('#', '');
      if (hexColor.length == 6) {
        hexColor = 'FF$hexColor';
      }
      return Color(int.parse(hexColor, radix: 16));
    } catch (e) {
      // Default to purple if parsing fails
      return const Color.fromRGBO(124, 66, 210, 1);
    }
  }
  
  // Get fallback icon for category
  IconData _getCategoryIcon(String categoryId) {
    switch (categoryId.toLowerCase()) {
      case 'math':
        return Icons.calculate;
      case 'science':
        return Icons.science;
      case 'computer_science':
        return Icons.computer;
      case 'language':
        return Icons.translate;
      case 'arts':
        return Icons.palette;
      default:
        return Icons.school;
    }
  }
}
