import 'package:flutter/material.dart';

class InteractivePropositionalLogicCalculator extends StatelessWidget {
  const InteractivePropositionalLogicCalculator({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Propositional Logic Calculator'),
        backgroundColor: Colors.blueGrey,
      ),
      body: const Center(
        child: Text(
          'Interactive Propositional Logic Calculator Widget',
          style: TextStyle(fontSize: 24),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }
}
