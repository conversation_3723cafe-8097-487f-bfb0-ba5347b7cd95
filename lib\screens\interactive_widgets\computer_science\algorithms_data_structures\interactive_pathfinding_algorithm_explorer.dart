import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:resonance_app/services/interactive_widget_service.dart';
import 'package:resonance_app/widgets/animated_button.dart';
import 'dart:collection';

class InteractivePathfindingAlgorithmExplorer extends StatefulWidget {
  final String widgetId;

  const InteractivePathfindingAlgorithmExplorer({Key? key, required this.widgetId}) : super(key: key);

  @override
  _InteractivePathfindingAlgorithmExplorerState createState() => _InteractivePathfindingAlgorithmExplorerState();
}

class _InteractivePathfindingAlgorithmExplorerState extends State<InteractivePathfindingAlgorithmExplorer> {
  Map<String, Map<String, int>> _graph = {
    'A': {'B': 1, 'C': 4},
    'B': {'A': 1, 'D': 2, 'E': 5},
    'C': {'A': 4, 'F': 3},
    'D': {'B': 2},
    'E': {'B': 5, 'F': 1},
    'F': {'C': 3, 'E': 1},
  };
  List<String> _nodes = ['A', 'B', 'C', 'D', 'E', 'F'];
  String _startNode = 'A';
  String _endNode = 'F';
  String _pathResult = '';
  String _message = '';
  bool _isSearching = false;
  double _animationSpeed = 500; // milliseconds

  @override
  void initState() {
    super.initState();
    _loadState();
  }

  void _loadState() async {
    final service = Provider.of<InteractiveWidgetService>(context, listen: false);
    final savedState = await service.getWidgetState(widget.widgetId);
    if (savedState != null) {
      setState(() {
        _graph = Map<String, Map<String, int>>.from(savedState['graph']?.map((key, value) => MapEntry(key, Map<String, int>.from(value))) ?? {});
        _nodes = List<String>.from(savedState['nodes'] ?? []);
        _startNode = savedState['startNode'] ?? 'A';
        _endNode = savedState['endNode'] ?? 'F';
        _pathResult = savedState['pathResult'] ?? '';
        _message = savedState['message'] ?? '';
        _isSearching = savedState['isSearching'] ?? false;
        _animationSpeed = savedState['animationSpeed'] ?? 500;
      });
    }
  }

  void _saveState() {
    final service = Provider.of<InteractiveWidgetService>(context, listen: false);
    service.saveWidgetState(widget.widgetId, {
      'graph': _graph,
      'nodes': _nodes,
      'startNode': _startNode,
      'endNode': _endNode,
      'pathResult': _pathResult,
      'message': _message,
      'isSearching': _isSearching,
      'animationSpeed': _animationSpeed,
    });
  }

  void _resetPathfinding() {
    setState(() {
      _pathResult = '';
      _message = 'Ready for new search.';
      _isSearching = false;
    });
    _saveState();
  }

  Future<void> _dijkstra() async {
    _resetPathfinding();
    if (!_nodes.contains(_startNode) || !_nodes.contains(_endNode)) {
      setState(() {
        _message = 'Start or End node not found.';
      });
      return;
    }

    setState(() {
      _isSearching = true;
      _message = 'Starting Dijkstra from $_startNode to $_endNode...';
    });
    _saveState();
    await Future.delayed(Duration(milliseconds: _animationSpeed.toInt()));

    Map<String, int> distances = {};
    Map<String, String?> previousNodes = {};
    PriorityQueue<NodeDistance> priorityQueue = PriorityQueue();

    for (var node in _nodes) {
      distances[node] = node == _startNode ? 0 : 999999; // Infinity
      previousNodes[node] = null;
      priorityQueue.add(NodeDistance(node, distances[node]!));
    }

    while (priorityQueue.isNotEmpty) {
      NodeDistance smallest = priorityQueue.removeFirst();
      String currentNode = smallest.node;

      if (currentNode == _endNode) break;

      if (distances[currentNode]! > smallest.distance) continue;

      setState(() {
        _message = 'Visiting node: $currentNode (Distance: ${distances[currentNode]})';
      });
      _saveState();
      await Future.delayed(Duration(milliseconds: _animationSpeed.toInt()));

      for (var neighborEntry in _graph[currentNode]!.entries) {
        String neighbor = neighborEntry.key;
        int weight = neighborEntry.value;
        int alt = distances[currentNode]! + weight;

        if (alt < distances[neighbor]!) {
          distances[neighbor] = alt;
          previousNodes[neighbor] = currentNode;
          priorityQueue.add(NodeDistance(neighbor, alt));
          setState(() {
            _message = 'Updating distance to $neighbor via $currentNode. New distance: $alt';
          });
          _saveState();
          await Future.delayed(Duration(milliseconds: _animationSpeed.toInt()));
        }
      }
    }

    List<String> path = [];
    String? current = _endNode;
    while (current != null) {
      path.insert(0, current);
      current = previousNodes[current];
    }

    setState(() {
      _isSearching = false;
      if (distances[_endNode] == 999999) {
        _pathResult = 'No path found from $_startNode to $_endNode.';
        _message = 'Pathfinding completed: No path.';
      } else {
        _pathResult = 'Shortest path: ${path.join(' -> ')} (Total distance: ${distances[_endNode]})';
        _message = 'Pathfinding completed!';
      }
    });
    _saveState();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Interactive Pathfinding Algorithm Explorer (Dijkstra)',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Text('Start Node: '),
              DropdownButton<String>(
                value: _startNode,
                onChanged: (String? newValue) {
                  setState(() {
                    _startNode = newValue!;
                  });
                },
                items: _nodes.map<DropdownMenuItem<String>>((String value) {
                  return DropdownMenuItem<String>(
                    value: value,
                    child: Text(value),
                  );
                }).toList(),
              ),
              const SizedBox(width: 20),
              Text('End Node: '),
              DropdownButton<String>(
                value: _endNode,
                onChanged: (String? newValue) {
                  setState(() {
                    _endNode = newValue!;
                  });
                },
                items: _nodes.map<DropdownMenuItem<String>>((String value) {
                  return DropdownMenuItem<String>(
                    value: value,
                    child: Text(value),
                  );
                }).toList(),
              ),
            ],
          ),
          const SizedBox(height: 10),
          Row(
            children: [
              Expanded(
                child: Slider(
                  value: _animationSpeed,
                  min: 50,
                  max: 1000,
                  divisions: 19,
                  label: _animationSpeed.round().toString(),
                  onChanged: (double value) {
                    setState(() {
                      _animationSpeed = value;
                    });
                  },
                ),
              ),
              Text('Speed: ${_animationSpeed.round()}ms'),
            ],
          ),
          const SizedBox(height: 10),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              AnimatedButton(
                onTap: _isSearching ? null : _dijkstra,
                text: _isSearching ? 'Searching...' : 'Start Dijkstra',
                color: _isSearching ? Colors.grey : Colors.green,
              ),
              AnimatedButton(
                onTap: _resetPathfinding,
                text: 'Reset',
                color: Colors.red,
              ),
            ],
          ),
          const SizedBox(height: 20),
          Text(
            'Graph (Adjacency List with Weights):',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          ..._graph.entries.map((entry) => Text('${entry.key}: ${entry.value.entries.map((e) => '${e.key}(${e.value})').join(', ')}')).toList(),
          const SizedBox(height: 20),
          Text(
            'Path Result: $_pathResult',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 20),
          Text(
            'Message: $_message',
            style: Theme.of(context).textTheme.bodyLarge,
          ),
        ],
      ),
    );
  }
}

class NodeDistance {
  final String node;
  final int distance;

  NodeDistance(this.node, this.distance);
}

class PriorityQueue {
  List<NodeDistance> _heap = [];

  void add(NodeDistance item) {
    _heap.add(item);
    _heap.sort((a, b) => a.distance.compareTo(b.distance)); // Simple sort for priority
  }

  NodeDistance removeFirst() {
    return _heap.removeAt(0);
  }

  bool get isNotEmpty => _heap.isNotEmpty;
}
