import 'package:flutter/material.dart';
import 'package:resonance/widgets/interactive_widget_scaffold.dart';

class InteractiveCausalityAnalyzer extends StatefulWidget {
  const InteractiveCausalityAnalyzer({super.key});

  @override
  State<InteractiveCausalityAnalyzer> createState() => _InteractiveCausalityAnalyzerState();
}

class _InteractiveCausalityAnalyzerState extends State<InteractiveCausalityAnalyzer> {
  String _cause = 'Event A';
  String _effect = 'Event B';
  bool _isCausal = true;
  bool _isParadoxical = false;

  final TextEditingController _causeController = TextEditingController(text: 'Event A');
  final TextEditingController _effectController = TextEditingController(text: 'Event B');

  @override
  void initState() {
    super.initState();
    _causeController.addListener(_updateCause);
    _effectController.addListener(_updateEffect);
  }

  @override
  void dispose() {
    _causeController.removeListener(_updateCause);
    _effectController.removeListener(_updateEffect);
    _causeController.dispose();
    _effectController.dispose();
    super.dispose();
  }

  void _updateCause() {
    setState(() {
      _cause = _causeController.text;
      _analyzeCausality();
    });
  }

  void _updateEffect() {
    setState(() {
      _effect = _effectController.text;
      _analyzeCausality();
    });
  }

  void _analyzeCausality() {
    // Simplified logic for demonstration
    if (_cause.toLowerCase().contains('time travel') && _effect.toLowerCase().contains('past')) {
      _isCausal = false;
      _isParadoxical = true;
    } else if (_cause.toLowerCase().contains('future') && _effect.toLowerCase().contains('past')) {
      _isCausal = false;
      _isParadoxical = true;
    } else if (_cause.toLowerCase().contains('grandparent') && _effect.toLowerCase().contains('existence')) {
      _isCausal = false;
      _isParadoxical = true;
    }
    else {
      _isCausal = true;
      _isParadoxical = false;
    }
  }

  @override
  Widget build(BuildContext context) {
    return InteractiveWidgetScaffold(
      title: 'Causality Analyzer',
      description: 'Analyze causality and its implications in time travel.',
      interactiveContent: Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              TextField(
                controller: _causeController,
                decoration: const InputDecoration(
                  labelText: 'Cause Event',
                  border: OutlineInputBorder(),
                ),
                onChanged: (text) {
                  _cause = text;
                  _analyzeCausality();
                },
              ),
              const SizedBox(height: 20),
              const Icon(Icons.arrow_downward, size: 40, color: Colors.grey),
              const SizedBox(height: 20),
              TextField(
                controller: _effectController,
                decoration: const InputDecoration(
                  labelText: 'Effect Event',
                  border: OutlineInputBorder(),
                  hintText: 'e.g., "Grandparent not born"',
                ),
                onChanged: (text) {
                  _effect = text;
                  _analyzeCausality();
                },
              ),
              const SizedBox(height: 30),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'Is Causal: ${_isCausal ? "Yes" : "No"}',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: _isCausal ? Colors.green : Colors.red,
                    ),
                  ),
                  const SizedBox(width: 30),
                  Text(
                    'Is Paradoxical: ${_isParadoxical ? "Yes" : "No"}',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: _isParadoxical ? Colors.red : Colors.green,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),
              const Padding(
                padding: EdgeInsets.all(16.0),
                child: Text(
                  'Enter a "Cause Event" and an "Effect Event" to analyze their causal relationship, especially in the context of time travel. The analyzer will attempt to identify potential paradoxes based on simplified rules.',
                  textAlign: TextAlign.center,
                  style: TextStyle(fontSize: 16),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
