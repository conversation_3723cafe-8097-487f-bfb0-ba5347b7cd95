import 'package:flutter/material.dart';
import 'course_service.dart';
import 'interactive_widget_service.dart';

/// Provider for accessing services throughout the app
class ServiceProvider extends InheritedWidget {
  final CourseService courseService;
  final InteractiveWidgetService interactiveWidgetService;

  const ServiceProvider({
    super.key,
    required this.courseService,
    required this.interactiveWidgetService,
    required Widget child,
  }) : super(child: child);

  static ServiceProvider of(BuildContext context) {
    final ServiceProvider? result =
        context.dependOnInheritedWidgetOfExactType<ServiceProvider>();
    assert(result != null, 'No ServiceProvider found in context');
    return result!;
  }

  @override
  bool updateShouldNotify(ServiceProvider oldWidget) {
    return courseService != oldWidget.courseService ||
        interactiveWidgetService != oldWidget.interactiveWidgetService;
  }
}
