import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:math';

class InteractiveRotationalMotionChallenge extends StatefulWidget {
  const InteractiveRotationalMotionChallenge({super.key});

  @override
  State<InteractiveRotationalMotionChallenge> createState() => _InteractiveRotationalMotionChallengeState();
}

class _InteractiveRotationalMotionChallengeState extends State<InteractiveRotationalMotionChallenge> {
  int _currentQuestionIndex = 0;
  String? _selectedAnswer;
  String _feedback = '';
  bool _isAnswered = false;
  int _score = 0;

  final List<Map<String, dynamic>> _questions = [
    {
      'question': 'What is the unit of angular displacement?',
      'options': ['meters', 'radians', 'seconds', 'kg'],
      'correctAnswer': 'radians',
      'explanation': 'Angular displacement is measured in radians, which is a unit of angle.',
    },
    {
      'question': 'Which of the following is the rotational equivalent of force?',
      'options': ['Momentum', 'Energy', 'Torque', 'Power'],
      'correctAnswer': 'Torque',
      'explanation': 'Torque is the rotational equivalent of linear force, causing angular acceleration.',
    },
    {
      'question': 'What is conserved in a closed system when there are no external torques?',
      'options': ['Angular velocity', 'Moment of inertia', 'Angular momentum', 'Rotational kinetic energy'],
      'correctAnswer': 'Angular momentum',
      'explanation': 'The principle of conservation of angular momentum states that if no external torque acts on a system, then the angular momentum of the system remains constant.',
    },
    {
      'question': 'If a spinning ice skater pulls her arms in, what happens to her angular velocity?',
      'options': ['Decreases', 'Increases', 'Stays the same', 'Becomes zero'],
      'correctAnswer': 'Increases',
      'explanation': 'When the ice skater pulls her arms in, her moment of inertia decreases. Due to conservation of angular momentum (L = Iω), her angular velocity (ω) must increase to keep L constant.',
    },
    {
      'question': 'What is the formula for rotational kinetic energy?',
      'options': ['0.5 * m * v^2', 'I * α', '0.5 * I * ω^2', 'm * g * h'],
      'correctAnswer': '0.5 * I * ω^2',
      'explanation': 'Rotational kinetic energy is given by the formula KE_rot = 0.5 * I * ω², where I is the moment of inertia and ω is the angular velocity.',
    },
  ];

  void _checkAnswer() {
    if (_selectedAnswer == null) {
      setState(() {
        _feedback = 'Please select an answer.';
      });
      return;
    }

    setState(() {
      _isAnswered = true;
      if (_selectedAnswer == _questions[_currentQuestionIndex]['correctAnswer']) {
        _feedback = 'Correct! ${_questions[_currentQuestionIndex]['explanation']}';
        _score++;
      } else {
        _feedback = 'Incorrect. The correct answer is "${_questions[_currentQuestionIndex]['correctAnswer']}". ${_questions[_currentQuestionIndex]['explanation']}';
      }
    });
  }

  void _nextQuestion() {
    setState(() {
      if (_currentQuestionIndex < _questions.length - 1) {
        _currentQuestionIndex++;
        _selectedAnswer = null;
        _feedback = '';
        _isAnswered = false;
      } else {
        _showCompletionDialog();
      }
    });
  }

  void _showCompletionDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Challenge Completed!'),
          content: Text('You scored $_score out of ${_questions.length}!'),
          actions: <Widget>[
            TextButton(
              child: const Text('Restart'),
              onPressed: () {
                Navigator.of(context).pop();
                setState(() {
                  _currentQuestionIndex = 0;
                  _selectedAnswer = null;
                  _feedback = '';
                  _isAnswered = false;
                  _score = 0;
                });
              },
            ),
            TextButton(
              child: const Text('Close'),
              onPressed: () {
                Navigator.of(context).pop();
                // Optionally navigate back or to another screen
              },
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final currentQuestion = _questions[_currentQuestionIndex];

    return Scaffold(
      appBar: AppBar(
        title: const Text('Rotational Motion Challenge'),
        backgroundColor: Colors.deepPurple,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text(
              'Question ${_currentQuestionIndex + 1} of ${_questions.length}',
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 10),
            LinearProgressIndicator(
              value: (_currentQuestionIndex + 1) / _questions.length,
              backgroundColor: Colors.grey[300],
              valueColor: const AlwaysStoppedAnimation<Color>(Colors.deepPurple),
            ),
            const SizedBox(height: 20),
            Card(
              elevation: 4,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Text(
                  currentQuestion['question'],
                  style: const TextStyle(fontSize: 20, fontWeight: FontWeight.w500),
                ),
              ),
            ),
            const SizedBox(height: 20),
            ...currentQuestion['options'].map<Widget>((option) {
              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 8.0),
                child: RadioListTile<String>(
                  title: Text(option, style: const TextStyle(fontSize: 16)),
                  value: option,
                  groupValue: _selectedAnswer,
                  onChanged: _isAnswered
                      ? null
                      : (value) {
                          setState(() {
                            _selectedAnswer = value;
                          });
                        },
                  activeColor: Colors.deepPurple,
                ),
              );
            }).toList(),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: _isAnswered ? null : _checkAnswer,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.deepPurple,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 15),
                textStyle: const TextStyle(fontSize: 18),
              ),
              child: const Text('Check Answer'),
            ),
            const SizedBox(height: 10),
            if (_feedback.isNotEmpty)
              Card(
                elevation: 2,
                color: _selectedAnswer == currentQuestion['correctAnswer'] ? Colors.green.shade100 : Colors.red.shade100,
                margin: const EdgeInsets.symmetric(vertical: 10),
                child: Padding(
                  padding: const EdgeInsets.all(12.0),
                  child: Text(
                    _feedback,
                    style: TextStyle(
                      color: _selectedAnswer == currentQuestion['correctAnswer'] ? Colors.green.shade800 : Colors.red.shade800,
                      fontSize: 16,
                    ),
                  ),
                ),
              ),
            const SizedBox(height: 10),
            if (_isAnswered)
              ElevatedButton(
                onPressed: _nextQuestion,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blueAccent,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 15),
                  textStyle: const TextStyle(fontSize: 18),
                ),
                child: Text(_currentQuestionIndex < _questions.length - 1 ? 'Next Question' : 'Finish Challenge'),
              ),
          ],
        ),
      ),
    );
  }
}
