import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that calculates and visualizes the average rate of change of a function.
class InteractiveAverageRateOfChangeCalculatorWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveAverageRateOfChangeCalculatorWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  /// Factory constructor to create the widget from JSON data
  factory InteractiveAverageRateOfChangeCalculatorWidget.fromData(Map<String, dynamic> data) {
    return InteractiveAverageRateOfChangeCalculatorWidget(
      data: data,
    );
  }

  @override
  State<InteractiveAverageRateOfChangeCalculatorWidget> createState() => _InteractiveAverageRateOfChangeCalculatorWidgetState();
}

class _InteractiveAverageRateOfChangeCalculatorWidgetState extends State<InteractiveAverageRateOfChangeCalculatorWidget> {
  late double _x1;
  late double _x2;

  // Function to visualize (e.g., f(x) = x^2)
  double _function(double x) {
    // Can be made configurable via widget.data
    return x * x;
  }

  // Colors
  late Color _primaryColor;
  late Color _secondaryColor;
  late Color _backgroundColor;
  late Color _textColor;

  @override
  void initState() {
    super.initState();
    _x1 = widget.data['initial_x1']?.toDouble() ?? 1.0;
    _x2 = widget.data['initial_x2']?.toDouble() ?? 3.0;

    _primaryColor = _parseColor(widget.data['primary_color']) ?? Colors.blue;
    _secondaryColor = _parseColor(widget.data['secondary_color']) ?? Colors.orange;
    _backgroundColor = _parseColor(widget.data['background_color']) ?? Colors.white;
    _textColor = _parseColor(widget.data['text_color']) ?? Colors.black87;
  }

  Color? _parseColor(String? hexString) {
    if (hexString == null) return null;
    hexString = hexString.replaceAll('#', '');
    if (hexString.length == 6) {
      hexString = 'FF$hexString';
    }
    try {
      return Color(int.parse(hexString, radix: 16));
    } catch (e) {
      return null;
    }
  }

  double _calculateAverageRateOfChange() {
    if (_x1 == _x2) return double.nan; // Avoid division by zero
    return (_function(_x2) - _function(_x1)) / (_x2 - _x1);
  }

  @override
  Widget build(BuildContext context) {
    final y1 = _function(_x1);
    final y2 = _function(_x2);
    final averageRateOfChange = _calculateAverageRateOfChange();

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _backgroundColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.data['title'] ?? 'Average Rate of Change Calculator',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),
          const SizedBox(height: 8),
          if (widget.data['description'] != null)
            Text(
              widget.data['description'],
              style: TextStyle(
                fontSize: 14,
                color: _textColor.withOpacity(0.8),
              ),
            ),
          const SizedBox(height: 16),

          Text(
            'Function: f(x) = x²',
            style: TextStyle(color: _textColor, fontSize: 16, fontStyle: FontStyle.italic),
          ),
          const SizedBox(height: 16),

          Text(
            'Point 1 (x₁): ${_x1.toStringAsFixed(1)}',
            style: TextStyle(color: _primaryColor, fontSize: 16, fontWeight: FontWeight.bold),
          ),
          Slider(
            value: _x1,
            min: -5.0,
            max: 5.0,
            divisions: 100,
            label: _x1.toStringAsFixed(1),
            activeColor: _primaryColor,
            onChanged: (value) {
              setState(() {
                _x1 = value;
              });
            },
          ),
          const SizedBox(height: 8),

          Text(
            'Point 2 (x₂): ${_x2.toStringAsFixed(1)}',
            style: TextStyle(color: _secondaryColor, fontSize: 16, fontWeight: FontWeight.bold),
          ),
          Slider(
            value: _x2,
            min: -5.0,
            max: 5.0,
            divisions: 100,
            label: _x2.toStringAsFixed(1),
            activeColor: _secondaryColor,
            onChanged: (value) {
              setState(() {
                _x2 = value;
              });
            },
          ),
          const SizedBox(height: 16),

          Center(
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: _primaryColor),
              ),
              child: Text(
                'Average Rate of Change: ${averageRateOfChange.isNaN ? 'Undefined' : averageRateOfChange.toStringAsFixed(2)}',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: _primaryColor,
                ),
              ),
            ),
          ),
          const SizedBox(height: 16),

          Container(
            height: 250,
            child: CustomPaint(
              painter: AverageRateOfChangePainter(
                function: _function,
                x1: _x1,
                x2: _x2,
                primaryColor: _primaryColor,
                secondaryColor: _secondaryColor,
                textColor: _textColor,
              ),
              child: Container(),
            ),
          ),

          // Widget name tag
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'InteractiveAverageRateOfChangeCalculator',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }
}

class AverageRateOfChangePainter extends CustomPainter {
  final Function(double) function;
  final double x1;
  final double x2;
  final Color primaryColor;
  final Color secondaryColor;
  final Color textColor;

  AverageRateOfChangePainter({
    required this.function,
    required this.x1,
    required this.x2,
    required this.primaryColor,
    required this.secondaryColor,
    required this.textColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint();
    final textPainter = TextPainter(textDirection: TextDirection.ltr);
    final textStyle = TextStyle(color: textColor, fontSize: 10);

    // Define plot area padding
    const double padding = 30.0;
    final double plotWidth = size.width - 2 * padding;
    final double plotHeight = size.height - 2 * padding;

    // Determine x and y ranges for plotting
    final double minPlotX = -5.0;
    final double maxPlotX = 5.0;
    final double minPlotY = -5.0;
    final double maxPlotY = 25.0; // For x^2, max at x=5 is 25

    // Scale factors
    final double xScale = plotWidth / (maxPlotX - minPlotX);
    final double yScale = plotHeight / (maxPlotY - minPlotY);

    // Helper to convert plot coordinates to canvas coordinates
    Offset toCanvas(double x, double y) {
      final canvasX = padding + (x - minPlotX) * xScale;
      final canvasY = size.height - padding - (y - minPlotY) * yScale;
      return Offset(canvasX, canvasY);
    }

    // Draw axes
    paint.color = Colors.grey;
    paint.strokeWidth = 1.0;
    // X-axis
    canvas.drawLine(toCanvas(minPlotX, 0), toCanvas(maxPlotX, 0), paint);
    // Y-axis
    canvas.drawLine(toCanvas(0, minPlotY), toCanvas(0, maxPlotY), paint);

    // Draw function curve
    paint.color = primaryColor;
    paint.style = PaintingStyle.stroke;
    paint.strokeWidth = 2.0;

    final path = Path();
    bool firstPoint = true;
    for (double x = minPlotX; x <= maxPlotX; x += (maxPlotX - minPlotX) / 100) {
      final y = function(x);
      final point = toCanvas(x, y);
      if (firstPoint) {
        path.moveTo(point.dx, point.dy);
        firstPoint = false;
      } else {
        path.lineTo(point.dx, point.dy);
      }
    }
    canvas.drawPath(path, paint);

    // Draw points (x1, y1) and (x2, y2)
    final point1 = toCanvas(x1, function(x1));
    final point2 = toCanvas(x2, function(x2));
    paint.color = primaryColor;
    paint.style = PaintingStyle.fill;
    canvas.drawCircle(point1, 5, paint);
    canvas.drawCircle(point2, 5, paint);

    // Draw secant line
    if (x1 != x2) {
      paint.color = secondaryColor;
      paint.strokeWidth = 2.0;
      canvas.drawLine(point1, point2, paint);
    }

    // Draw labels for points
    textPainter.text = TextSpan(text: '(${x1.toStringAsFixed(1)}, ${function(x1).toStringAsFixed(1)})', style: textStyle.copyWith(color: primaryColor));
    textPainter.layout();
    textPainter.paint(canvas, Offset(point1.dx + 5, point1.dy - textPainter.height / 2));

    textPainter.text = TextSpan(text: '(${x2.toStringAsFixed(1)}, ${function(x2).toStringAsFixed(1)})', style: textStyle.copyWith(color: secondaryColor));
    textPainter.layout();
    textPainter.paint(canvas, Offset(point2.dx + 5, point2.dy - textPainter.height / 2));
  }

  @override
  bool shouldRepaint(covariant AverageRateOfChangePainter oldDelegate) {
    return oldDelegate.x1 != x1 ||
           oldDelegate.x2 != x2 ||
           oldDelegate.primaryColor != primaryColor ||
           oldDelegate.secondaryColor != secondaryColor ||
           oldDelegate.textColor != textColor;
  }
}
