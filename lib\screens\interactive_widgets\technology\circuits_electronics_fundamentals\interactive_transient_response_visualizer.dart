import 'package:flutter/material.dart';
import 'package:resonance/utils/page_transitions.dart';
import 'package:resonance/widgets/animated_button.dart';

class InteractiveTransientResponseVisualizer extends StatefulWidget {
  const InteractiveTransientResponseVisualizer({super.key});

  @override
  State<InteractiveTransientResponseVisualizer> createState() => _InteractiveTransientResponseVisualizerState();
}

class _InteractiveTransientResponseVisualizerState extends State<InteractiveTransientResponseVisualizer> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Interactive Transient Response Visualizer'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            Navigator.of(context).pop();
          },
        ),
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Text(
                'This is the Interactive Transient Response Visualizer.',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 18),
              ),
              const SizedBox(height: 30),
              AnimatedButton(
                onTap: () {
                  // Add functionality for the button here
                },
                text: 'Visualize Transient Response',
                color: Colors.blueAccent,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
