import 'package:flutter/material.dart';
import 'dart:math';
import 'package:resonance/widgets/interactive_widget_scaffold.dart';

class InteractiveStructuralStabilityAnalyzer extends StatefulWidget {
  const InteractiveStructuralStabilityAnalyzer({super.key});

  @override
  State<InteractiveStructuralStabilityAnalyzer> createState() =>
      _InteractiveStructuralStabilityAnalyzerState();
}

class _InteractiveStructuralStabilityAnalyzerState
    extends State<InteractiveStructuralStabilityAnalyzer> {
  final TextEditingController _beamLengthController = TextEditingController(text: '200.0');
  final TextEditingController _loadPositionController = TextEditingController(text: '100.0');
  final TextEditingController _loadMagnitudeController = TextEditingController(text: '100.0');
  final TextEditingController _youngsModulusController = TextEditingController(text: '200.0');
  final TextEditingController _momentOfInertiaController = TextEditingController(text: '100.0');

  double _beamLength = 200.0; // pixels
  double _loadPosition = 100.0; // pixels from left support
  double _loadMagnitude = 100.0; // N
  double _youngsModulus = 200.0; // GPa (simplified for visualization)
  double _momentOfInertia = 100.0; // m^4 (simplified for visualization)

  List<Offset> _deflectionPoints = [];

  @override
  void initState() {
    super.initState();
    _calculateDeflection();
  }

  void _calculateDeflection() {
    _deflectionPoints.clear();
    // Simplified deflection calculation for a simply supported beam with a point load
    // Max deflection at load for simply supported beam: (P * a^2 * b^2) / (3 * E * I * L)
    // Where P = load, a = distance from left support to load, b = distance from load to right support,
    // E = Young's Modulus, I = Moment of Inertia, L = beam length.

    // For visualization, we'll just create a parabolic-like curve
    // This is not physically accurate, but demonstrates the concept.
    double maxDeflection = -(_loadMagnitude * _loadPosition * (_beamLength - _loadPosition) * (_beamLength + _loadPosition - _loadPosition)) / (3 * _youngsModulus * _momentOfInertia * _beamLength);
    // Scale maxDeflection for visual representation
    maxDeflection = -_loadMagnitude / 50; // Arbitrary scaling for visual effect

    for (int i = 0; i <= _beamLength.toInt(); i++) {
      double x = i.toDouble();
      double y = 0.0;

      if (x <= _loadPosition) {
        y = maxDeflection * (x / _loadPosition) * (1 - x / _beamLength);
      } else {
        y = maxDeflection * ((_beamLength - x) / (_beamLength - _loadPosition)) * (x / _beamLength);
      }
      _deflectionPoints.add(Offset(x, y));
    }
  }

  void _analyzeStructure() {
    setState(() {
      _beamLength = double.tryParse(_beamLengthController.text) ?? _beamLength;
      _loadPosition = double.tryParse(_loadPositionController.text) ?? _loadPosition;
      _loadMagnitude = double.tryParse(_loadMagnitudeController.text) ?? _loadMagnitude;
      _youngsModulus = double.tryParse(_youngsModulusController.text) ?? _youngsModulus;
      _momentOfInertia = double.tryParse(_momentOfInertiaController.text) ?? _momentOfInertia;
      _calculateDeflection();
    });
  }

  @override
  void dispose() {
    _beamLengthController.dispose();
    _loadPositionController.dispose();
    _loadMagnitudeController.dispose();
    _youngsModulusController.dispose();
    _momentOfInertiaController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return InteractiveWidgetScaffold(
      title: 'Structural Stability Analyzer',
      interactiveWidget: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _beamLengthController,
                    keyboardType: TextInputType.number,
                    decoration: const InputDecoration(
                      labelText: 'Beam Length (pixels)',
                      border: OutlineInputBorder(),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextField(
                    controller: _loadPositionController,
                    keyboardType: TextInputType.number,
                    decoration: const InputDecoration(
                      labelText: 'Load Position (pixels)',
                      border: OutlineInputBorder(),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _loadMagnitudeController,
                    keyboardType: TextInputType.number,
                    decoration: const InputDecoration(
                      labelText: 'Load Magnitude (N)',
                      border: OutlineInputBorder(),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextField(
                    controller: _youngsModulusController,
                    keyboardType: TextInputType.number,
                    decoration: const InputDecoration(
                      labelText: 'Young\'s Modulus (GPa)',
                      border: OutlineInputBorder(),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _momentOfInertiaController,
                    keyboardType: TextInputType.number,
                    decoration: const InputDecoration(
                      labelText: 'Moment of Inertia (m^4)',
                      border: OutlineInputBorder(),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(child: Container()), // Empty space
              ],
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _analyzeStructure,
              child: const Text('Analyze Structure'),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: LayoutBuilder(
                builder: (context, constraints) {
                  return CustomPaint(
                    painter: BeamPainter(
                      deflectionPoints: _deflectionPoints,
                      beamLength: _beamLength,
                      loadPosition: _loadPosition,
                      maxHeight: constraints.maxHeight,
                      maxWidth: constraints.maxWidth,
                    ),
                    child: Container(),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class BeamPainter extends CustomPainter {
  final List<Offset> deflectionPoints;
  final double beamLength;
  final double loadPosition;
  final double maxHeight;
  final double maxWidth;

  BeamPainter({
    required this.deflectionPoints,
    required this.beamLength,
    required this.loadPosition,
    required this.maxHeight,
    required this.maxWidth,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final beamPaint = Paint()
      ..color = Colors.brown
      ..strokeWidth = 5.0
      ..style = PaintingStyle.stroke;

    final deflectionPaint = Paint()
      ..color = Colors.red
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;

    final loadPaint = Paint()
      ..color = Colors.black
      ..strokeWidth = 2.0
      ..style = PaintingStyle.fill;

    final supportPaint = Paint()
      ..color = Colors.grey
      ..strokeWidth = 3.0
      ..style = PaintingStyle.fill;

    // Scale the beam to fit the available width
    double scaleX = size.width / beamLength;
    double scaleY = size.height / (maxHeight * 0.5); // Adjust Y scale for better visualization of deflection

    // Offset to center the beam vertically and horizontally
    final double offsetX = (size.width - beamLength * scaleX) / 2;
    final double offsetY = size.height / 2;

    // Draw beam
    canvas.drawLine(
      Offset(offsetX, offsetY),
      Offset(offsetX + beamLength * scaleX, offsetY),
      beamPaint,
    );

    // Draw supports
    canvas.drawRect(
      Rect.fromLTWH(offsetX - 10, offsetY + 5, 20, 10),
      supportPaint,
    );
    canvas.drawRect(
      Rect.fromLTWH(offsetX + beamLength * scaleX - 10, offsetY + 5, 20, 10),
      supportPaint,
    );

    // Draw load
    canvas.drawPath(
      Path()
        ..moveTo(offsetX + loadPosition * scaleX, offsetY - 20)
        ..lineTo(offsetX + loadPosition * scaleX - 5, offsetY)
        ..lineTo(offsetX + loadPosition * scaleX + 5, offsetY)
        ..close(),
      loadPaint,
    );

    // Draw deflection curve
    final deflectionPath = Path();
    if (deflectionPoints.isNotEmpty) {
      deflectionPath.moveTo(
        offsetX + deflectionPoints[0].dx * scaleX,
        offsetY + deflectionPoints[0].dy * scaleY,
      );
      for (int i = 1; i < deflectionPoints.length; i++) {
        deflectionPath.lineTo(
          offsetX + deflectionPoints[i].dx * scaleX,
          offsetY + deflectionPoints[i].dy * scaleY,
        );
      }
    }
    canvas.drawPath(deflectionPath, deflectionPaint);
  }

  @override
  bool shouldRepaint(covariant BeamPainter oldDelegate) {
    return oldDelegate.deflectionPoints != deflectionPoints ||
        oldDelegate.beamLength != beamLength ||
        oldDelegate.loadPosition != loadPosition;
  }
}
