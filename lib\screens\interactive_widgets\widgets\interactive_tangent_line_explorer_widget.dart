import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that allows users to explore tangent lines and their slopes.
class InteractiveTangentLineExplorerWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveTangentLineExplorerWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  /// Factory constructor to create the widget from JSON data
  factory InteractiveTangentLineExplorerWidget.fromData(Map<String, dynamic> data) {
    return InteractiveTangentLineExplorerWidget(
      data: data,
    );
  }

  @override
  State<InteractiveTangentLineExplorerWidget> createState() => _InteractiveTangentLineExplorerWidgetState();
}

class _InteractiveTangentLineExplorerWidgetState extends State<InteractiveTangentLineExplorerWidget> {
  late double _pointX; // The x-value where the tangent line is drawn

  // Function to visualize (e.g., f(x) = x^2)
  double _function(double x) {
    return x * x;
  }

  // Derivative of the function (f'(x) = 2x for f(x) = x^2)
  double _derivative(double x) {
    return 2 * x;
  }

  // Colors
  late Color _primaryColor;
  late Color _secondaryColor;
  late Color _backgroundColor;
  late Color _textColor;

  @override
  void initState() {
    super.initState();
    _pointX = widget.data['initial_point_x']?.toDouble() ?? 1.0;

    _primaryColor = _parseColor(widget.data['primary_color']) ?? Colors.blue;
    _secondaryColor = _parseColor(widget.data['secondary_color']) ?? Colors.orange;
    _backgroundColor = _parseColor(widget.data['background_color']) ?? Colors.white;
    _textColor = _parseColor(widget.data['text_color']) ?? Colors.black87;
  }

  Color? _parseColor(String? hexString) {
    if (hexString == null) return null;
    hexString = hexString.replaceAll('#', '');
    if (hexString.length == 6) {
      hexString = 'FF$hexString';
    }
    try {
      return Color(int.parse(hexString, radix: 16));
    } catch (e) {
      return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    final pointY = _function(_pointX);
    final slope = _derivative(_pointX);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _backgroundColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.data['title'] ?? 'Tangent Line Explorer',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),
          const SizedBox(height: 8),
          if (widget.data['description'] != null)
            Text(
              widget.data['description'],
              style: TextStyle(
                fontSize: 14,
                color: _textColor.withOpacity(0.8),
              ),
            ),
          const SizedBox(height: 16),

          Text(
            'Function: f(x) = x²',
            style: TextStyle(color: _textColor, fontSize: 16, fontStyle: FontStyle.italic),
          ),
          Text(
            'Derivative: f\'(x) = 2x',
            style: TextStyle(color: _textColor, fontSize: 16, fontStyle: FontStyle.italic),
          ),
          const SizedBox(height: 16),

          Text(
            'Point of Tangency (x): ${_pointX.toStringAsFixed(1)}',
            style: TextStyle(color: _primaryColor, fontSize: 16, fontWeight: FontWeight.bold),
          ),
          Slider(
            value: _pointX,
            min: -5.0,
            max: 5.0,
            divisions: 100,
            label: _pointX.toStringAsFixed(1),
            activeColor: _primaryColor,
            onChanged: (value) {
              setState(() {
                _pointX = value;
              });
            },
          ),
          const SizedBox(height: 16),

          Center(
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: _primaryColor),
              ),
              child: Text(
                'Slope of Tangent Line (f\'(${_pointX.toStringAsFixed(1)})): ${slope.toStringAsFixed(2)}',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: _primaryColor,
                ),
              ),
            ),
          ),
          const SizedBox(height: 16),

          Container(
            height: 250,
            child: CustomPaint(
              painter: TangentLinePainter(
                function: _function,
                derivative: _derivative,
                pointX: _pointX,
                primaryColor: _primaryColor,
                secondaryColor: _secondaryColor,
                textColor: _textColor,
              ),
              child: Container(),
            ),
          ),

          // Widget name tag
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'InteractiveTangentLineExplorer',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }
}

class TangentLinePainter extends CustomPainter {
  final Function(double) function;
  final Function(double) derivative;
  final double pointX;
  final Color primaryColor;
  final Color secondaryColor;
  final Color textColor;

  TangentLinePainter({
    required this.function,
    required this.derivative,
    required this.pointX,
    required this.primaryColor,
    required this.secondaryColor,
    required this.textColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint();
    final textPainter = TextPainter(textDirection: TextDirection.ltr);
    final textStyle = TextStyle(color: textColor, fontSize: 10);

    // Define plot area padding
    const double padding = 30.0;
    final double plotWidth = size.width - 2 * padding;
    final double plotHeight = size.height - 2 * padding;

    // Determine x and y ranges for plotting
    final double minPlotX = -5.0;
    final double maxPlotX = 5.0;
    final double minPlotY = -5.0;
    final double maxPlotY = 25.0; // For x^2, max at x=5 is 25

    // Scale factors
    final double xScale = plotWidth / (maxPlotX - minPlotX);
    final double yScale = plotHeight / (maxPlotY - minPlotY);

    // Helper to convert plot coordinates to canvas coordinates
    Offset toCanvas(double x, double y) {
      final canvasX = padding + (x - minPlotX) * xScale;
      final canvasY = size.height - padding - (y - minPlotY) * yScale;
      return Offset(canvasX, canvasY);
    }

    // Draw axes
    paint.color = Colors.grey;
    paint.strokeWidth = 1.0;
    // X-axis
    canvas.drawLine(toCanvas(minPlotX, 0), toCanvas(maxPlotX, 0), paint);
    // Y-axis
    canvas.drawLine(toCanvas(0, minPlotY), toCanvas(0, maxPlotY), paint);

    // Draw function curve
    paint.color = primaryColor;
    paint.style = PaintingStyle.stroke;
    paint.strokeWidth = 2.0;

    final path = Path();
    bool firstPoint = true;
    for (double x = minPlotX; x <= maxPlotX; x += (maxPlotX - minPlotX) / 100) {
      final y = function(x);
      final point = toCanvas(x, y);
      if (firstPoint) {
        path.moveTo(point.dx, point.dy);
        firstPoint = false;
      } else {
        path.lineTo(point.dx, point.dy);
      }
    }
    canvas.drawPath(path, paint);

    // Draw point of tangency
    final pointY = function(pointX);
    final tangentPoint = toCanvas(pointX, pointY);
    paint.color = primaryColor;
    paint.style = PaintingStyle.fill;
    canvas.drawCircle(tangentPoint, 5, paint);

    // Draw tangent line
    final slope = derivative(pointX);
    // Equation of tangent line: y - y1 = m(x - x1)
    // y = m(x - x1) + y1
    // To draw the line across the canvas, calculate y at minPlotX and maxPlotX
    final tangentY1 = slope * (minPlotX - pointX) + pointY;
    final tangentY2 = slope * (maxPlotX - pointX) + pointY;

    paint.color = secondaryColor;
    paint.strokeWidth = 2.0;
    canvas.drawLine(toCanvas(minPlotX, tangentY1), toCanvas(maxPlotX, tangentY2), paint);

    // Draw labels for point and slope
    textPainter.text = TextSpan(text: '(${pointX.toStringAsFixed(1)}, ${pointY.toStringAsFixed(1)})', style: textStyle.copyWith(color: primaryColor));
    textPainter.layout();
    textPainter.paint(canvas, Offset(tangentPoint.dx + 5, tangentPoint.dy - textPainter.height / 2));

    textPainter.text = TextSpan(text: 'Slope: ${slope.toStringAsFixed(2)}', style: textStyle.copyWith(color: secondaryColor));
    textPainter.layout();
    // Position slope label near the tangent line, avoiding overlap
    final midX = (minPlotX + maxPlotX) / 2;
    final midY = slope * (midX - pointX) + pointY;
    final midCanvasPoint = toCanvas(midX, midY);
    textPainter.paint(canvas, Offset(midCanvasPoint.dx - textPainter.width / 2, midCanvasPoint.dy - textPainter.height - 5));
  }

  @override
  bool shouldRepaint(covariant TangentLinePainter oldDelegate) {
    return oldDelegate.pointX != pointX ||
           oldDelegate.primaryColor != primaryColor ||
           oldDelegate.secondaryColor != secondaryColor ||
           oldDelegate.textColor != textColor;
  }
}
