import 'package:flutter/material.dart';
import '../../models/course_models.dart';
import '../../services/service_provider.dart';
import '../../utils/page_transitions.dart';
import 'continuous_lesson_screen.dart';

class CourseDetailScreen extends StatefulWidget {
  final String courseId;

  const CourseDetailScreen({Key? key, required this.courseId})
    : super(key: key);

  @override
  _CourseDetailScreenState createState() => _CourseDetailScreenState();
}

class _CourseDetailScreenState extends State<CourseDetailScreen>
    with SingleTickerProviderStateMixin {
  double _progress = 0.0;
  late List<Module> _modulesList; // Changed from _lessons to _modulesList
  late Color _categoryColor;
  int _selectedModuleIndex = 0; // Changed from _selectedLessonIndex
  bool _isLoading = true;

  // Animation controller for node selection
  late AnimationController _animationController;

  // Scroll controller for auto-scrolling
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _loadCourseData();
  }

  Future<void> _loadCourseData() async {
    setState(() {
      _isLoading = true;
    });

    final courseService = ServiceProvider.of(context).courseService;
    debugPrint('Loading course data for ID: ${widget.courseId}');

    // Try to get the full course with all modules loaded
    final course = await courseService.getCourseById(widget.courseId);
    final user = courseService.currentUser;

    if (course == null || !mounted) {
      debugPrint('Course not found or widget not mounted');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
      return;
    }

    debugPrint('Course loaded: ${course.title}');
    debugPrint('Course has ${course.modules.length} modules');

    for (var module in course.modules) {
      debugPrint('Module ${module.id} has ${module.lessons.length} lessons');
    }

    // Use modules directly instead of flattening lessons
    final allModules = course.modules;

    debugPrint('Total modules: ${allModules.length}');

    // Get category color
    final category = courseService.getCategoryById(course.categoryId);
    final categoryColor =
        category != null
            ? _parseColor(category.color)
            : const Color.fromRGBO(124, 66, 210, 1);

    // Get user progress
    double progress = 0.0;
    // Progress calculation might need adjustment if it's based on lessons
    // For now, keep existing logic, but it might be inaccurate for module view
    if (user != null && user.courseProgress.containsKey(widget.courseId)) {
      progress =
          user.courseProgress[widget.courseId]?.progressPercentage ?? 0.0;

      // Find the module containing the most recently accessed lesson
      final courseProgress = user.courseProgress[widget.courseId];
      if (courseProgress != null && courseProgress.lessonProgress.isNotEmpty) {
        final lastAccessedLessonId =
            courseProgress.lessonProgress.entries
                .reduce(
                  (a, b) =>
                      a.value.lastAccessed.isAfter(b.value.lastAccessed)
                          ? a
                          : b,
                )
                .key;

        // Find the module index that contains this lesson
        int foundModuleIndex = -1;
        for (int i = 0; i < allModules.length; i++) {
          if (allModules[i].lessons.any(
            (lesson) => lesson.id == lastAccessedLessonId,
          )) {
            foundModuleIndex = i;
            break;
          }
        }
        if (foundModuleIndex >= 0) {
          _selectedModuleIndex = foundModuleIndex;
        } else if (allModules.isNotEmpty) {
          // If last accessed lesson's module not found (e.g. lessons removed), or no lessons, default to first module if any
          _selectedModuleIndex = 0;
        }
      } else if (allModules.isNotEmpty) {
        _selectedModuleIndex =
            0; // Default to first module if no progress or no lessons
      }
    } else if (allModules.isNotEmpty) {
      _selectedModuleIndex =
          0; // Default to first module if no user or course progress
    }

    if (mounted) {
      setState(() {
        _modulesList = allModules; // Use the modules list
        _categoryColor = categoryColor;
        _progress = progress;
        _isLoading = false;
      });
    }

    // Scroll to the selected node after the UI is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollToSelectedNode();
    });
  }

  // Scroll to the selected node
  void _scrollToSelectedNode() {
    if (_scrollController.hasClients) {
      // Calculate approximate position of the node
      final nodeHeight = 100.0; // Approximate height of node + spacing
      final targetPosition =
          100.0 +
          (_selectedModuleIndex * nodeHeight); // Use _selectedModuleIndex

      // Scroll to position with animation
      _scrollController.animateTo(
        targetPosition,
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOut,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(body: Center(child: CircularProgressIndicator()));
    }

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.black),
        title: const Text(
          'PUZZLES',
          style: TextStyle(
            color: Colors.blue,
            fontSize: 16,
            fontWeight: FontWeight.bold,
            fontFamily: 'WorkSans',
          ),
        ),
        actions: [
          // Keys icon (from reference image)
          Container(
            margin: const EdgeInsets.only(right: 16),
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(20),
            ),
            child: Row(
              children: [
                Icon(Icons.vpn_key, size: 18, color: Colors.amber[700]),
                Icon(Icons.vpn_key, size: 18, color: Colors.amber[700]),
              ],
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          // Course title and progress
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 8, 16, 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Course title
                Text(
                  "100 Days of Puzzles",
                  style: const TextStyle(
                    color: Colors.black,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    fontFamily: 'WorkSans',
                  ),
                ),

                // Info icon
                Align(
                  alignment: Alignment.centerRight,
                  child: IconButton(
                    icon: const Icon(Icons.info_outline, color: Colors.grey),
                    onPressed: () {
                      // Show course info
                      showDialog(
                        context: context,
                        builder:
                            (context) => AlertDialog(
                              title: const Text('Course Information'),
                              content: const Text(
                                'This course contains 100 days of puzzles to improve your problem-solving skills. Each puzzle is designed to challenge you in different ways.',
                              ),
                              actions: [
                                TextButton(
                                  onPressed: () => Navigator.pop(context),
                                  child: const Text('Close'),
                                ),
                              ],
                            ),
                      );
                    },
                  ),
                ),

                // Progress indicator
                const SizedBox(height: 4),
                Row(
                  children: [
                    // Green dot
                    Container(
                      width: 8,
                      height: 8,
                      decoration: const BoxDecoration(
                        color: Colors.green,
                        shape: BoxShape.circle,
                      ),
                    ),
                    const SizedBox(width: 8),
                    // Progress bar
                    Expanded(
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(4),
                        child: LinearProgressIndicator(
                          value: _progress / 100,
                          backgroundColor: Colors.grey[200],
                          valueColor: const AlwaysStoppedAnimation<Color>(
                            Colors.green,
                          ),
                          minHeight: 4,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // Roadmap content
          Expanded(child: _buildRoadmap()),
        ],
      ),
    );
  }

  Widget _buildRoadmap() {
    if (_modulesList.isEmpty) {
      // Handle case where there are no modules
      return const Center(child: Text("No modules available for this course."));
    }
    return Stack(
      children: [
        // Scrollable roadmap
        SingleChildScrollView(
          controller: _scrollController,
          physics: const BouncingScrollPhysics(),
          child: Padding(
            padding: const EdgeInsets.only(
              bottom: 120,
            ), // Space for the module card
            child: Stack(
              children: [
                // Diagonal lines
                CustomPaint(
                  size: Size(
                    MediaQuery.of(context).size.width,
                    (_modulesList.length * 100) +
                        200, // Use _modulesList.length
                  ),
                  painter: RoadmapPainter(
                    _categoryColor.withOpacity(0.5),
                  ), // Use category color for path
                ),

                Column(
                  children: [
                    // Level indicators
                    ..._buildLevelIndicators(),

                    // Bottom padding
                    const SizedBox(height: 80),
                  ],
                ),
              ],
            ),
          ),
        ),

        // Selected module card
        if (_selectedModuleIndex < _modulesList.length)
          _buildSelectedModuleCard(), // Use _selectedModuleIndex and _modulesList
      ],
    );
  }

  List<Widget> _buildLevelIndicators() {
    final List<Widget> widgets = [];
    // Level indicators might be less relevant if there are few modules.
    // For now, keep the logic, it will show "LEVEL 1" if < 10 modules.
    final int totalLevels =
        (_modulesList.length / 10).ceil(); // Use _modulesList.length

    for (int level = 0; level < totalLevels; level++) {
      // Add level indicator
      widgets.add(
        Padding(
          padding: const EdgeInsets.only(top: 20),
          child: _buildLevelIndicator(level + 1),
        ),
      );

      // Add modules for this level
      final int startIndex = level * 10;
      final int endIndex = (level + 1) * 10;
      final int moduleCount = _modulesList.length; // Use _modulesList.length

      for (int i = startIndex; i < endIndex && i < moduleCount; i++) {
        // Determine node position (alternating left and right)
        final NodePosition position =
            i % 2 == 0
                ? (i == 0 ? NodePosition.center : NodePosition.left)
                : NodePosition.right;

        // Determine node type (alternating hexagon and square)
        final NodeType type = i % 2 == 0 ? NodeType.hexagon : NodeType.square;

        // Determine if node is completed
        // This logic needs to be based on module completion.
        // Placeholder: a module is completed if _selectedModuleIndex > i.
        // Proper module completion status would require checking if all its lessons are complete,
        // or if the module itself has a 'completed' status.
        // For now, using _selectedModuleIndex as a proxy for "current or past"
        final bool isCompleted = i < _selectedModuleIndex;

        widgets.add(
          Column(
            children: [
              // Node
              _buildRoadmapNode(
                i,
                type,
                isActive: true,
                isCompleted: isCompleted,
                position: position,
              ),

              // Space between nodes
              const SizedBox(height: 60),
            ],
          ),
        );
      }
    }

    return widgets;
  }

  Widget _buildLevelIndicator(int level) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: _categoryColor.withOpacity(0.1), // Use category color
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            'LEVEL',
            style: TextStyle(
              color: _categoryColor, // Use category color
              fontSize: 14,
              fontWeight: FontWeight.w600,
              fontFamily: 'WorkSans',
            ),
          ),
          const SizedBox(width: 8),
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: _categoryColor, // Use category color
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Text(
                level.toString(),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  fontFamily: 'WorkSans',
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRoadmapNode(
    int index,
    NodeType type, {
    required bool isActive,
    required bool isCompleted,
    required NodePosition position,
  }) {
    if (index >= _modulesList.length) {
      // Use _modulesList
      return const SizedBox.shrink();
    }

    final module = _modulesList[index]; // Use module from _modulesList
    final isSelected =
        index == _selectedModuleIndex; // Use _selectedModuleIndex

    // Determine node color based on state
    Color nodeColor = Colors.grey[400]!;
    if (isActive) {
      // Use category color for active nodes, green for completed
      nodeColor = isCompleted ? Colors.greenAccent.shade700 : _categoryColor;
    }
    Color selectedColor = _categoryColor.withOpacity(
      0.7,
    ); // Lighter shade for selected shadow

    return Padding(
      padding: EdgeInsets.only(
        left:
            position == NodePosition.left
                ? 40
                : position == NodePosition.right
                ? 0
                : 0,
        right:
            position == NodePosition.right
                ? 40
                : position == NodePosition.left
                ? 0
                : 0,
      ),
      child: Row(
        mainAxisAlignment:
            position == NodePosition.left
                ? MainAxisAlignment.start
                : position == NodePosition.right
                ? MainAxisAlignment.end
                : MainAxisAlignment.center,
        children: [
          Column(
            children: [
              // Node with animation
              TweenAnimationBuilder<double>(
                tween: Tween<double>(begin: 1.0, end: isSelected ? 1.2 : 1.0),
                duration: const Duration(milliseconds: 300),
                builder: (context, scale, child) {
                  return Transform.scale(
                    scale: scale,
                    child: GestureDetector(
                      onTap: () {
                        setState(() {
                          _selectedModuleIndex =
                              index; // Use _selectedModuleIndex
                        });

                        // Scroll to make the selected node visible
                        _scrollToSelectedNode();
                      },
                      child: _buildNodeShape(
                        type,
                        nodeColor,
                        isSelected,
                        isCompleted,
                        index,
                        selectedColor, // Pass selectedColor
                      ),
                    ),
                  );
                },
              ),

              const SizedBox(height: 8),

              // Module title
              SizedBox(
                width: 120,
                child: Text(
                  module.title, // Use module.title
                  style: TextStyle(
                    color: isSelected ? Colors.black : Colors.grey[700],
                    fontSize: 14,
                    fontWeight: isSelected ? FontWeight.bold : FontWeight.w500,
                    fontFamily: 'WorkSans',
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildNodeShape(
    NodeType type,
    Color color,
    bool isSelected,
    bool isCompleted,
    int index,
    Color selectedColor, // Add selectedColor parameter
  ) {
    IconData iconData;
    switch (type) {
      case NodeType.hexagon:
        iconData = Icons.hexagon_outlined; // Or any other icon for hexagon
        return _buildHexagonNode(
          color,
          isSelected,
          isCompleted,
          iconData, // Pass IconData
          selectedColor,
        );
      case NodeType.square:
        iconData = Icons.square_outlined; // Or any other icon for square
        return _buildSquareNode(
          color,
          isSelected,
          isCompleted,
          iconData, // Pass IconData
          selectedColor,
        );
    }
  }

  Widget _buildHexagonNode(
    Color color,
    bool isSelected,
    bool isCompleted,
    IconData iconData, // Changed from index, now IconData
    Color selectedColor,
  ) {
    return Container(
      width: 60,
      height: 60,
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(12), // Keep hexagon shape for now
        boxShadow:
            isSelected
                ? [
                  BoxShadow(
                    color: selectedColor, // Use modified selected color
                    blurRadius: 12,
                    spreadRadius: 3,
                  ),
                ]
                : [
                  // Add a subtle shadow for non-selected nodes too
                  BoxShadow(
                    color: Colors.black.withAlpha(26), // 0.1 opacity = 26 alpha
                    blurRadius: 5,
                    spreadRadius: 1,
                  ),
                ],
      ),
      child: Center(
        child:
            isCompleted
                ? const Icon(Icons.check, color: Colors.white, size: 30)
                : Icon(
                  iconData, // Use passed IconData
                  color: Colors.white.withAlpha(204), // 0.8 opacity = 204 alpha
                  size: 30,
                ),
      ),
    );
  }

  Widget _buildSquareNode(
    Color color,
    bool isSelected,
    bool isCompleted,
    IconData iconData, // Changed from index, now IconData
    Color selectedColor,
  ) {
    return Container(
      width: 50,
      height: 50,
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(8), // Keep square shape for now
        boxShadow:
            isSelected
                ? [
                  BoxShadow(
                    color: selectedColor, // Use modified selected color
                    blurRadius: 10,
                    spreadRadius: 2,
                  ),
                ]
                : [
                  // Add a subtle shadow for non-selected nodes too
                  BoxShadow(
                    color: Colors.black.withAlpha(26), // 0.1 opacity = 26 alpha
                    blurRadius: 4,
                    spreadRadius: 1,
                  ),
                ],
      ),
      child: Center(
        child:
            isCompleted
                ? const Icon(Icons.check, color: Colors.white, size: 28)
                : Icon(
                  iconData, // Use passed IconData
                  color: Colors.white.withOpacity(0.8),
                  size: 28,
                ),
      ),
    );
  }

  Widget _buildSelectedModuleCard() {
    // Renamed from _buildSelectedLessonCard
    if (_selectedModuleIndex >= _modulesList.length) {
      return const SizedBox.shrink(); // Guard
    }
    final module =
        _modulesList[_selectedModuleIndex]; // Use module and _selectedModuleIndex

    return Positioned(
      bottom: 20,
      left: 16, // Adjusted padding
      right: 16, // Adjusted padding
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20), // Slightly more rounded
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.08), // Softer shadow
              blurRadius: 15,
              spreadRadius: 2,
              offset: const Offset(0, 4),
            ),
          ],
          border: Border.all(
            color: _categoryColor.withOpacity(0.3),
            width: 1,
          ), // Subtle border
        ),
        child: ClipRRect(
          // Clip content to rounded border
          borderRadius: BorderRadius.circular(19), // Match outer -1 for border
          child: Container(
            // Inner container for padding and top accent
            padding: const EdgeInsets.fromLTRB(
              20,
              0,
              20,
              20,
            ), // No top padding here
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Category color accent bar
                Container(
                  height: 8,
                  margin: const EdgeInsets.only(bottom: 16),
                  decoration: BoxDecoration(
                    color: _categoryColor,
                    // Optionally, round only top corners if desired
                    // borderRadius: BorderRadius.vertical(top: Radius.circular(19))
                  ),
                ),
                // Module title with animation
                AnimatedSwitcher(
                  duration: const Duration(milliseconds: 300),
                  child: Text(
                    module.title, // Use module.title
                    key: ValueKey<String>(module.id), // Use module.id
                    style: const TextStyle(
                      color: Colors.black,
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      fontFamily: 'WorkSans',
                    ),
                  ),
                ),

                const SizedBox(height: 8),

                // Lesson description with animation
                AnimatedSwitcher(
                  duration: const Duration(milliseconds: 300),
                  child: Text(
                    module.description, // Use module.description
                    key: ValueKey<String>(module.id), // Use module.id
                    style: TextStyle(
                      color: Colors.grey[700],
                      fontSize: 16,
                      fontFamily: 'WorkSans',
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),

                const SizedBox(height: 16),

                // Start module/lesson button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () async {
                      // Navigate to the first lesson of this module, if any.
                      // Otherwise, this button might be disabled or show "Coming Soon".
                      if (module.lessons.isNotEmpty) {
                        // Use ContinuousLessonScreen for a continuous scrolling experience
                        AppNavigator.push(
                          context,
                          ContinuousLessonScreen(
                            courseId: widget.courseId,
                            lessonId:
                                module.lessons.first.id, // Pass first lesson ID
                          ),
                        );
                      } else {
                        // Optionally, show a snackbar or do nothing if no lessons
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text(
                              'No lessons available in this module yet.',
                            ),
                          ),
                        );
                        debugPrint(
                          "No lessons in module ${module.id} to start.",
                        );
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.black,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(30),
                      ),
                    ),
                    child: Text(
                      module.lessons.isNotEmpty
                          ? 'Start First Lesson'
                          : 'View Module (No Lessons)',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        fontFamily: 'WorkSans',
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Parse hex color string to Color
  Color _parseColor(String hexColor) {
    try {
      hexColor = hexColor.replaceAll('#', '');
      if (hexColor.length == 6) {
        hexColor = 'FF$hexColor';
      }
      return Color(int.parse(hexColor, radix: 16));
    } catch (e) {
      // Default to purple if parsing fails
      return const Color.fromRGBO(124, 66, 210, 1);
    }
  }
}

enum NodeType { hexagon, square }

enum NodePosition { left, center, right }

class RoadmapPainter extends CustomPainter {
  final Color lineColor;

  RoadmapPainter(this.lineColor);

  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..color = lineColor
          ..strokeWidth =
              4 // Increased strokeWidth for a thicker path
          ..style = PaintingStyle.stroke
          ..strokeCap = StrokeCap.round; // Rounder line caps

    final width = size.width;
    final height = size.height;

    // Draw diagonal lines
    final path = Path();

    // Starting point (center top)
    path.moveTo(width / 2, 60);

    // Draw zigzag pattern down the screen
    bool isRight = true;
    double y = 120;
    final double spacing = 100; // Vertical spacing between nodes

    while (y < height - 100) {
      if (isRight) {
        // Draw line to right
        path.lineTo(width - 80, y);
      } else {
        // Draw line to left
        path.lineTo(80, y);
      }

      isRight = !isRight;
      y += spacing;
    }

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
