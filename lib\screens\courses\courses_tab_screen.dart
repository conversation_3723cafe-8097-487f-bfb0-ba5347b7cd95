import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../../models/course_models.dart';
import '../../services/service_provider.dart';
import 'course_detail_screen.dart';
import '../../utils/page_transitions.dart';

class CoursesTabScreen extends StatefulWidget {
  const CoursesTabScreen({super.key});

  @override
  State<CoursesTabScreen> createState() => _CoursesTabScreenState();
}

class _CoursesTabScreenState extends State<CoursesTabScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final ScrollController _scrollController = ScrollController();
  String _selectedCategoryId = 'math'; // Default to math category
  final Map<String, GlobalKey> _categoryKeys = {};

  @override
  void initState() {
    super.initState();
    _tabController = TabController(
      length: 4,
      vsync: this,
    ); // 4 tabs: Math, CS, Data, Science

    // Listen to scroll events to update the selected tab
    _scrollController.addListener(_updateSelectedTabOnScroll);

    // Listen to tab changes to scroll to the corresponding section
    _tabController.addListener(() {
      if (!_tabController.indexIsChanging) {
        _scrollToSelectedCategory();
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  // Initialize category keys
  void _initCategoryKeys(List<CourseCategory> categories) {
    if (_categoryKeys.isEmpty) {
      for (var category in categories) {
        _categoryKeys[category.id] = GlobalKey();
      }
    }
  }

  // Update selected tab based on scroll position
  void _updateSelectedTabOnScroll() {
    if (_scrollController.positions.isEmpty) return;

    // Find the category section that is most visible in the viewport
    double viewportHeight = _scrollController.position.viewportDimension;
    double scrollOffset = _scrollController.offset;

    String? mostVisibleCategory;
    double maxVisibleArea = 0;

    _categoryKeys.forEach((categoryId, key) {
      if (key.currentContext != null) {
        final RenderBox box =
            key.currentContext!.findRenderObject() as RenderBox;
        final position = box.localToGlobal(Offset.zero);
        final size = box.size;

        // Calculate how much of this category is visible in the viewport
        double top = position.dy;
        double bottom = top + size.height;
        double visibleTop = math.max(top, scrollOffset);
        double visibleBottom = math.min(bottom, scrollOffset + viewportHeight);

        if (visibleBottom > visibleTop) {
          double visibleArea = visibleBottom - visibleTop;
          if (visibleArea > maxVisibleArea) {
            maxVisibleArea = visibleArea;
            mostVisibleCategory = categoryId;
          }
        }
      }
    });

    if (mostVisibleCategory != null &&
        mostVisibleCategory != _selectedCategoryId) {
      setState(() {
        _selectedCategoryId = mostVisibleCategory!;
        // Update tab controller without triggering its listener
        _tabController.removeListener(() {});
        switch (_selectedCategoryId) {
          case 'math':
            _tabController.animateTo(0);
            break;
          case 'computer_science':
            _tabController.animateTo(1);
            break;
          case 'data':
            _tabController.animateTo(2);
            break;
          case 'science':
            _tabController.animateTo(3);
            break;
        }
        _tabController.addListener(() {
          if (!_tabController.indexIsChanging) {
            _scrollToSelectedCategory();
          }
        });
      });
    }
  }

  // Scroll to the selected category
  void _scrollToSelectedCategory() {
    String categoryId;
    switch (_tabController.index) {
      case 0:
        categoryId = 'math';
        break;
      case 1:
        categoryId = 'computer_science';
        break;
      case 2:
        categoryId = 'data';
        break;
      case 3:
        categoryId = 'science';
        break;
      default:
        categoryId = 'math';
    }

    setState(() {
      _selectedCategoryId = categoryId;
    });

    if (_categoryKeys.containsKey(categoryId) &&
        _categoryKeys[categoryId]!.currentContext != null) {
      final RenderBox box =
          _categoryKeys[categoryId]!.currentContext!.findRenderObject()
              as RenderBox;
      final position = box.localToGlobal(Offset.zero);

      _scrollController.animateTo(
        _scrollController.offset +
            position.dy -
            100, // Offset to account for app bar
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final courseService = ServiceProvider.of(context).courseService;
    final categories = courseService.categories;
    final user = courseService.currentUser;

    // Initialize category keys
    _initCategoryKeys(categories);

    // Get courses for the selected category
    final selectedCategory = categories.firstWhere(
      (category) => category.id == _selectedCategoryId,
      orElse: () => categories.first,
    );
    final courses = courseService.getCoursesByCategory(selectedCategory.id);

    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Top navigation icons
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 8, 16, 16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildNavIcon(Icons.assignment_outlined, 'Lessons', 0),
                  _buildNavIcon(
                    Icons.laptop_outlined,
                    'Courses',
                    1,
                    isSelected: true,
                  ),
                  _buildNavIcon(Icons.bar_chart_outlined, 'Stats', 2),
                  _buildNavIcon(Icons.person_outline, 'Profile', 3),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // Category title
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Text(
                selectedCategory.id == 'math'
                    ? 'Foundational Math'
                    : selectedCategory.name,
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  fontFamily: 'WorkSans',
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Course list
            Expanded(
              child: ListView.builder(
                controller: _scrollController,
                padding: const EdgeInsets.fromLTRB(16, 0, 16, 24),
                physics: const BouncingScrollPhysics(),
                itemCount: courses.length,
                itemBuilder: (context, index) {
                  final course = courses[index];
                  final isEnrolled =
                      user?.enrolledCourseIds.contains(course.id) ?? false;
                  final progress =
                      isEnrolled
                          ? (user
                                  ?.courseProgress[course.id]
                                  ?.progressPercentage ??
                              0)
                          : 0;

                  // Determine if course is new (for demo purposes, let's say the second course is new)
                  final isNew = index == 1;

                  return _buildCourseItem(
                    context,
                    course.title,
                    progress.toDouble(),
                    isEnrolled && progress > 0,
                    isNew,
                    () {
                      AppNavigator.push(
                        context,
                        CourseDetailScreen(courseId: course.id),
                      );
                    },
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Build a navigation icon for the top bar
  Widget _buildNavIcon(
    IconData icon,
    String label,
    int index, {
    bool isSelected = false,
  }) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            color: isSelected ? Colors.blue[50] : Colors.grey[100],
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(
            icon,
            color: isSelected ? Colors.blue[700] : Colors.grey[700],
            size: 24,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            color: isSelected ? Colors.blue[700] : Colors.grey[700],
            fontSize: 12,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
            fontFamily: 'WorkSans',
          ),
        ),
      ],
    );
  }

  // Build a course item card
  Widget _buildCourseItem(
    BuildContext context,
    String title,
    double progress,
    bool isInProgress,
    bool isNew,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.only(bottom: 16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey[200]!),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            children: [
              // Course info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Status badges
                    if (isInProgress || isNew)
                      Padding(
                        padding: const EdgeInsets.only(bottom: 8.0),
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.green[100],
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            isInProgress ? 'IN PROGRESS' : 'NEW',
                            style: TextStyle(
                              color: Colors.green[800],
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                              fontFamily: 'WorkSans',
                            ),
                          ),
                        ),
                      ),

                    // Course title
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        fontFamily: 'WorkSans',
                      ),
                    ),

                    // Progress bar
                    if (progress > 0) ...[
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Expanded(
                            child: Stack(
                              children: [
                                // Background
                                Container(
                                  height: 4,
                                  decoration: BoxDecoration(
                                    color: Colors.grey[200],
                                    borderRadius: BorderRadius.circular(2),
                                  ),
                                ),
                                // Progress
                                FractionallySizedBox(
                                  widthFactor: progress / 100,
                                  child: Container(
                                    height: 4,
                                    decoration: BoxDecoration(
                                      color: Colors.green,
                                      borderRadius: BorderRadius.circular(2),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ],
                ),
              ),

              // Course icon
              const SizedBox(width: 16),
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: Colors.blue[50],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  _getCourseIconData(title),
                  color: Colors.blue[700],
                  size: 24,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Helper method to get course icon data
  IconData _getCourseIconData(String title) {
    if (title.contains('Math')) {
      return Icons.calculate_outlined;
    } else if (title.contains('Algebra')) {
      return Icons.functions_outlined;
    } else if (title.contains('Equation')) {
      return Icons.balance_outlined;
    } else if (title.contains('Geometry')) {
      return Icons.change_history_outlined;
    } else {
      return Icons.school_outlined;
    }
  }
}
