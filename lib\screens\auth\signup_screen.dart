import 'package:flutter/material.dart';
import 'dart:math' as math;
import 'signin_screen.dart';

class SignupWidget extends StatefulWidget {
  @override
  _SignupWidgetState createState() => _SignupWidgetState();
}

class _SignupWidgetState extends State<SignupWidget> {
  // Controllers for text fields
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  
  bool _isPasswordVisible = false;
  bool _isConfirmPasswordVisible = false;
  bool _passwordsMatch = true;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  // Check if passwords match
  void _validatePasswords() {
    setState(() {
      _passwordsMatch = _passwordController.text == _confirmPasswordController.text;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SingleChildScrollView(
        child: Column(
          children: <Widget>[
            // --- Header Section with Background Image/Gradient ---
            Container(
              height: 256,
              width: double.infinity,
              child: Stack(
                children: <Widget>[
                  // Background Logo Image (Rotated)
                  Positioned(
                    top: -382.56,
                    left: -290,
                    child: Transform.rotate(
                      angle: 0.6319 * (math.pi / 180),
                      child: Container(
                        width: 946.34,
                        height: 946.34,
                        decoration: BoxDecoration(
                          image: DecorationImage(
                            image: AssetImage('assets/images/logo.png'),
                            fit: BoxFit.fitWidth,
                            colorFilter: ColorFilter.mode(
                              Colors.white.withOpacity(0.15),
                              BlendMode.dstATop,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                  // White Fade Gradient Overlay
                  Positioned.fill(
                    child: Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            Colors.white.withOpacity(0),
                            Colors.white,
                          ],
                          stops: [0.0, 0.9],
                        ),
                      ),
                    ),
                  ),
                  // Header Text
                  Positioned(
                    bottom: 20,
                    left: 14,
                    right: 14,
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: <Widget>[
                        Text(
                          'Sign Up For Free',
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            color: Color.fromRGBO(16, 17, 20, 1),
                            fontFamily: 'WorkSans',
                            fontSize: 30,
                            fontWeight: FontWeight.w600,
                            height: 1.27,
                          ),
                        ),
                        SizedBox(height: 8),
                        Text(
                          'Quickly make your account in 1 minute',
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            color: Color.fromRGBO(57, 59, 66, 1),
                            fontFamily: 'WorkSans',
                            fontSize: 16,
                            fontWeight: FontWeight.w400,
                            height: 1.5,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            // --- Form Section ---
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  SizedBox(height: 24),

                  // --- Email Field ---
                  Text(
                    'Email Address',
                    style: TextStyle(
                      color: Color.fromRGBO(16, 17, 20, 1),
                      fontFamily: 'WorkSans',
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  SizedBox(height: 8),
                  TextFormField(
                    controller: _emailController,
                    keyboardType: TextInputType.emailAddress,
                    decoration: InputDecoration(
                      hintText: '<EMAIL>',
                      hintStyle: TextStyle(
                        color: Colors.grey[400],
                        fontFamily: 'WorkSans',
                      ),
                      filled: true,
                      fillColor: Color.fromRGBO(243, 243, 243, 1),
                      prefixIcon: Icon(Icons.email_outlined, color: Colors.grey[600]),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(19),
                        borderSide: BorderSide.none,
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(19),
                        borderSide: BorderSide(color: Colors.grey.shade300, width: 1),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(19),
                        borderSide: BorderSide(color: Color.fromRGBO(100, 45, 176, 1), width: 1.5),
                      ),
                      contentPadding: EdgeInsets.symmetric(vertical: 16, horizontal: 16),
                    ),
                    style: TextStyle(
                      color: Color.fromRGBO(57, 59, 66, 1),
                      fontFamily: 'WorkSans',
                      fontSize: 16,
                    ),
                  ),

                  SizedBox(height: 24),

                  // --- Password Field ---
                  Text(
                    'Password',
                    style: TextStyle(
                      color: Color.fromRGBO(16, 17, 20, 1),
                      fontFamily: 'WorkSans',
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  SizedBox(height: 8),
                  TextFormField(
                    controller: _passwordController,
                    obscureText: !_isPasswordVisible,
                    onChanged: (_) => _validatePasswords(),
                    decoration: InputDecoration(
                      hintText: 'Enter your password',
                      hintStyle: TextStyle(
                        color: Colors.grey[400],
                        fontFamily: 'WorkSans',
                      ),
                      filled: true,
                      fillColor: Color.fromRGBO(243, 243, 243, 1),
                      prefixIcon: Icon(Icons.lock_outline, color: Colors.grey[600]),
                      suffixIcon: IconButton(
                        icon: Icon(
                          _isPasswordVisible ? Icons.visibility_off_outlined : Icons.visibility_outlined,
                          color: Colors.grey[600],
                        ),
                        onPressed: () {
                          setState(() {
                            _isPasswordVisible = !_isPasswordVisible;
                          });
                        },
                      ),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(19),
                        borderSide: BorderSide.none,
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(19),
                        borderSide: BorderSide(color: Colors.grey.shade300, width: 1),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(19),
                        borderSide: BorderSide(color: Color.fromRGBO(100, 45, 176, 1), width: 1.5),
                      ),
                      contentPadding: EdgeInsets.symmetric(vertical: 16, horizontal: 16),
                    ),
                    style: TextStyle(
                      color: Color.fromRGBO(57, 59, 66, 1),
                      fontFamily: 'WorkSans',
                      fontSize: 16,
                    ),
                  ),

                  SizedBox(height: 24),

                  // --- Confirm Password Field ---
                  Text(
                    'Confirm Password',
                    style: TextStyle(
                      color: Color.fromRGBO(16, 17, 20, 1),
                      fontFamily: 'WorkSans',
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  SizedBox(height: 8),
                  TextFormField(
                    controller: _confirmPasswordController,
                    obscureText: !_isConfirmPasswordVisible,
                    onChanged: (_) => _validatePasswords(),
                    decoration: InputDecoration(
                      hintText: 'Confirm your password',
                      hintStyle: TextStyle(
                        color: Colors.grey[400],
                        fontFamily: 'WorkSans',
                      ),
                      filled: true,
                      fillColor: Color.fromRGBO(243, 243, 243, 1),
                      prefixIcon: Icon(Icons.lock_outline, color: Colors.grey[600]),
                      suffixIcon: IconButton(
                        icon: Icon(
                          _isConfirmPasswordVisible ? Icons.visibility_off_outlined : Icons.visibility_outlined,
                          color: Colors.grey[600],
                        ),
                        onPressed: () {
                          setState(() {
                            _isConfirmPasswordVisible = !_isConfirmPasswordVisible;
                          });
                        },
                      ),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(19),
                        borderSide: BorderSide.none,
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(19),
                        borderSide: BorderSide(
                          color: !_passwordsMatch && _confirmPasswordController.text.isNotEmpty
                              ? Color.fromRGBO(248, 62, 89, 1)
                              : Colors.grey.shade300,
                          width: 1,
                        ),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(19),
                        borderSide: BorderSide(
                          color: !_passwordsMatch && _confirmPasswordController.text.isNotEmpty
                              ? Color.fromRGBO(248, 62, 89, 1)
                              : Color.fromRGBO(100, 45, 176, 1),
                          width: 1.5,
                        ),
                      ),
                      contentPadding: EdgeInsets.symmetric(vertical: 16, horizontal: 16),
                    ),
                    style: TextStyle(
                      color: Color.fromRGBO(57, 59, 66, 1),
                      fontFamily: 'WorkSans',
                      fontSize: 16,
                    ),
                  ),

                  // Error message for password mismatch
                  if (!_passwordsMatch && _confirmPasswordController.text.isNotEmpty)
                    Padding(
                      padding: const EdgeInsets.only(top: 16.0),
                      child: Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(12),
                          color: Color.fromRGBO(255, 237, 240, 1),
                          border: Border.all(
                            color: Color.fromRGBO(248, 62, 89, 1),
                            width: 1,
                          ),
                        ),
                        padding: EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                        child: Row(
                          children: <Widget>[
                            Icon(
                              Icons.error_outline,
                              color: Color.fromRGBO(248, 62, 89, 1),
                              size: 18,
                            ),
                            SizedBox(width: 4),
                            Text(
                              "ERROR: Passwords Don't Match!",
                              style: TextStyle(
                                color: Color.fromRGBO(248, 62, 89, 1),
                                fontFamily: 'WorkSans',
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                  SizedBox(height: 32),

                  // --- Sign Up Button ---
                  ElevatedButton.icon(
                    onPressed: () {
                      // Validate form before proceeding
                      if (_emailController.text.isEmpty ||
                          _passwordController.text.isEmpty ||
                          _confirmPasswordController.text.isEmpty) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(content: Text('Please fill in all fields')),
                        );
                        return;
                      }
                      
                      if (!_passwordsMatch) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(content: Text('Passwords do not match')),
                        );
                        return;
                      }
                      
                      // TODO: Implement Sign Up Logic
                      print('Email: ${_emailController.text}');
                      print('Password: ${_passwordController.text}');
                    },
                    icon: Icon(Icons.arrow_forward, size: 18),
                    label: Text('Sign Up'),
                    style: ElevatedButton.styleFrom(
                      foregroundColor: Colors.white,
                      backgroundColor: Color.fromRGBO(16, 17, 20, 1),
                      padding: EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(19),
                      ),
                      textStyle: TextStyle(
                        fontFamily: 'WorkSans',
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),

                  SizedBox(height: 32),

                  // --- Already have an account link ---
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        'Already have an account? ',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontFamily: 'WorkSans',
                          fontSize: 14,
                        ),
                      ),
                      InkWell(
                        onTap: () {
                          // Navigate to Sign In Screen
                          Navigator.of(context).pushReplacement(
                            MaterialPageRoute(builder: (_) => SigninWidget()),
                          );
                        },
                        child: Text(
                          'Sign In.',
                          style: TextStyle(
                            color: Color.fromRGBO(100, 45, 176, 1),
                            fontFamily: 'WorkSans',
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 20),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
