# Course Content Structure

This directory contains JSON files for course content organized in a hierarchical structure.

## Directory Structure

```
lib/data/courses/
  categories/
    maths/
      mathematical_thinking/
        course.json (basic course info and module list)
        modules/
          art-of-logical-deduction.json
          problem-solving-strategies.json
          ...
    science/
      physics_101/
        course.json
        modules/
          ...
```

## Benefits of This Structure

1. **Organization**: Content is organized by category and course
2. **Modularity**: Each module is in its own file, making it easier to manage
3. **Scalability**: Easy to add new courses and modules
4. **Security**: Content can be encrypted/decrypted when fetched from a server
5. **Flexibility**: Content can be dynamically loaded, updated, or swapped without app updates

## JSON Structure

### Course JSON (course.json)

```json
{
  "id": "course-id",
  "title": "Course Title",
  "description": "Course description",
  "categoryId": "category-id",
  "thumbnailPath": "path/to/thumbnail.png",
  "difficulty": "Beginner|Intermediate|Advanced",
  "modules": [
    {
      "id": "module-id",
      "title": "Module Title",
      "description": "Module description",
      "order": 1,
      "lessons": [
        {
          "id": "lesson-id",
          "title": "Lesson Title",
          "description": "Lesson description",
          "order": 1,
          "estimatedTime": 15, // in minutes
          "contentBlocks": [
            // Content blocks (see below)
          ]
        }
      ],
      "endOfModuleAssessment": {
        "id": "assessment-id",
        "title": "Assessment Title",
        "description": "Assessment description",
        "questions": [
          // Quiz blocks (see below)
        ],
        "passingScorePercentage": 70
      }
    }
  ]
}
```

## Content Block Types

### Text Block

```json
{
  "id": "text-block-id",
  "type": "text",
  "order": 1,
  "content": "Text content with **markdown** support",
  "isMarkdown": true
}
```

### Image Block

```json
{
  "id": "image-block-id",
  "type": "image",
  "order": 2,
  "imagePath": "image_filename.png",
  "remoteUrl": "https://example.com/images/image.png",
  "caption": "Optional image caption",
  "width": 300, // Optional
  "height": 200 // Optional
}
```

### Video Block

```json
{
  "id": "video-block-id",
  "type": "video",
  "order": 3,
  "videoPath": "video_filename.mp4",
  "remoteUrl": "https://example.com/videos/video.mp4",
  "thumbnailPath": "thumbnail.png",
  "remoteThumbnailUrl": "https://example.com/thumbnails/video.png",
  "duration": 120 // in seconds
}
```

### Animation Block

```json
{
  "id": "animation-block-id",
  "type": "animation",
  "order": 4,
  "animationPath": "animation_filename.gif", // or .json for Lottie
  "remoteUrl": "https://example.com/animations/animation.gif",
  "caption": "Optional animation caption",
  "autoPlay": true,
  "duration": 5 // in seconds, optional
}
```

### Quiz Block

```json
{
  "id": "quiz-block-id",
  "type": "quiz",
  "order": 5,
  "question": "What is 2 + 2?",
  "options": ["3", "4", "5", "6"],
  "correctOptionIndex": 1,
  "explanation": "2 + 2 = 4"
}
```

### Interactive Block

```json
{
  "id": "interactive-block-id",
  "type": "interactive",
  "order": 6,
  "interactiveType": "sequenceChallenge",
  "data": {
    // Widget-specific properties
    "title": "Find the Pattern",
    "instruction": "What comes next in this sequence?",
    "sequenceType": "numeric",
    "displayItems": ["1", "2", "3", "?"],
    "answerOptions": ["4", "5", "6", "7"],
    "correctAnswerIndex": 0,
    "explanation": "The pattern is adding 1 each time"
  }
}
```

## Available Interactive Widget Types

1. `fraction-visualizer`: Visualize fractions and operations
2. `equation-solver`: Interactive equation solving
3. `graph-plotter`: Visualize mathematical functions
4. `geometry-explorer`: Interactive geometry demonstrations
5. `number-line`: Explore number concepts visually
6. `probability-simulator`: Visualize probability concepts
7. `problemSorter`: Categorize problems by type
8. `sequenceChallenge`: Pattern recognition exercises
9. `conditionalMatcher`: Match conditions with outcomes
10. `deductionPuzzle`: Logic puzzles requiring deductive reasoning
11. `problemDecomposer`: Break down complex problems
12. `estimationChallenge`: Make reasonable estimates
13. `fermiProblemSolver`: Order-of-magnitude estimation problems
14. `assumptionSpotter`: Identify hidden assumptions in statements

## Media Assets

Media assets (images, animations, videos) can be loaded in two ways:

1. **Bundled Assets**: Include in the app bundle (use for essential assets)
2. **Remote Assets**: Download from a server (use for large or frequently updated assets)

For remote assets, include both the local path and the remote URL:

```json
"imagePath": "image_filename.png",
"remoteUrl": "https://example.com/images/image.png"
```

The app will:
1. Check if the file exists locally
2. If not, download it from the remote URL
3. Cache it for future use
4. Fall back to bundled assets if download fails

## Using Giphy and Unsplash APIs

For finding relevant images and GIFs, you can use the Giphy and Unsplash APIs:

### Giphy API
```dart
// Search for a GIF
final giphyApiKey = 'YOUR_GIPHY_API_KEY';
final response = await http.get(
  Uri.parse('https://api.giphy.com/v1/gifs/search?api_key=$giphyApiKey&q=pattern&limit=1'),
);
final data = jsonDecode(response.body);
final gifUrl = data['data'][0]['images']['original']['url'];
```

### Unsplash API
```dart
// Search for an image
final unsplashApiKey = 'YOUR_UNSPLASH_API_KEY';
final response = await http.get(
  Uri.parse('https://api.unsplash.com/search/photos?query=mathematics&per_page=1'),
  headers: {'Authorization': 'Client-ID $unsplashApiKey'},
);
final data = jsonDecode(response.body);
final imageUrl = data['results'][0]['urls']['regular'];
```

## How to Add a New Course

1. Create a new directory structure:
   ```
   lib/data/courses/categories/[category_id]/[course_id]/
   ```

2. Create a course.json file with basic course info and module list

3. Create a modules directory:
   ```
   lib/data/courses/categories/[category_id]/[course_id]/modules/
   ```

4. Create JSON files for each module

## How to Update Existing Content

Simply edit the JSON files for the course or modules you want to update. The app will load the updated content the next time it starts.

## Future Enhancements

- Encryption/decryption of course content
- Version control for course content
- Differential updates to minimize data usage
- User-generated content and notes
