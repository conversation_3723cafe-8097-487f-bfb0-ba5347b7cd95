import 'package:flutter/material.dart';
import 'dart:math';

class InteractiveDataPatternAnalyzer extends StatefulWidget {
  const InteractiveDataPatternAnalyzer({super.key});

  @override
  State<InteractiveDataPatternAnalyzer> createState() => _InteractiveDataPatternAnalyzerState();
}

class _InteractiveDataPatternAnalyzerState extends State<InteractiveDataPatternAnalyzer> {
  final TextEditingController _dataController = TextEditingController(text: '1, 2, 3, 4, 5');
  String _analysisResult = '';

  void _analyzeData() {
    setState(() {
      List<double> data = _dataController.text
          .split(',')
          .map((s) => double.tryParse(s.trim()))
          .whereType<double>()
          .toList();

      if (data.isEmpty) {
        _analysisResult = 'Please enter some numbers.';
        return;
      }

      String pattern = _identifyPattern(data);
      _analysisResult = 'Identified Pattern: $pattern';
    });
  }

  String _identifyPattern(List<double> data) {
    if (data.length < 2) {
      return 'Not enough data points to identify a pattern.';
    }

    // Check for arithmetic progression
    if (data.length >= 2) {
      double diff = data[1] - data[0];
      bool isArithmetic = true;
      for (int i = 2; i < data.length; i++) {
        if ((data[i] - data[i - 1] - diff).abs() > 0.001) { // Use tolerance for double comparison
          isArithmetic = false;
          break;
        }
      }
      if (isArithmetic) {
        return 'Arithmetic Progression (Common Difference: ${diff.toStringAsFixed(2)})';
      }
    }

    // Check for geometric progression
    if (data.length >= 2) {
      if (data[0] == 0) { // Avoid division by zero
        bool allZero = true;
        for (var val in data) {
          if (val != 0) {
            allZero = false;
            break;
          }
        }
        if (allZero) return 'All zeros';
        return 'Cannot determine geometric ratio with zero first term.';
      }
      double ratio = data[1] / data[0];
      bool isGeometric = true;
      for (int i = 2; i < data.length; i++) {
        if (data[i - 1] == 0) { // Handle zero in sequence after first term
          isGeometric = false;
          break;
        }
        if ((data[i] / data[i - 1] - ratio).abs() > 0.001) {
          isGeometric = false;
          break;
        }
      }
      if (isGeometric) {
        return 'Geometric Progression (Common Ratio: ${ratio.toStringAsFixed(2)})';
      }
    }

    // Check for quadratic pattern (second differences are constant)
    if (data.length >= 3) {
      List<double> firstDifferences = [];
      for (int i = 0; i < data.length - 1; i++) {
        firstDifferences.add(data[i + 1] - data[i]);
      }

      if (firstDifferences.length >= 2) {
        double secondDiff = firstDifferences[1] - firstDifferences[0];
        bool isQuadratic = true;
        for (int i = 2; i < firstDifferences.length; i++) {
          if ((firstDifferences[i] - firstDifferences[i - 1] - secondDiff).abs() > 0.001) {
            isQuadratic = false;
            break;
          }
        }
        if (isQuadratic) {
          return 'Quadratic Pattern (Constant Second Difference: ${secondDiff.toStringAsFixed(2)})';
        }
      }
    }

    // Simple increasing/decreasing
    bool increasing = true;
    bool decreasing = true;
    for (int i = 0; i < data.length - 1; i++) {
      if (data[i] >= data[i + 1]) increasing = false;
      if (data[i] <= data[i + 1]) decreasing = false;
    }
    if (increasing) return 'Generally Increasing';
    if (decreasing) return 'Generally Decreasing';

    return 'No obvious simple pattern identified.';
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Interactive Data Pattern Analyzer',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            TextField(
              controller: _dataController,
              decoration: InputDecoration(
                labelText: 'Enter comma-separated numbers (e.g., 1, 2, 3, 4, 5)',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.text,
            ),
            const SizedBox(height: 10),
            ElevatedButton(
              onPressed: _analyzeData,
              child: const Text('Analyze Data'),
            ),
            const SizedBox(height: 20),
            Text(
              _analysisResult,
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: Colors.blue),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            const Text(
              'This tool helps identify common mathematical patterns in numerical sequences.',
              style: TextStyle(fontStyle: FontStyle.italic, color: Colors.grey),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
