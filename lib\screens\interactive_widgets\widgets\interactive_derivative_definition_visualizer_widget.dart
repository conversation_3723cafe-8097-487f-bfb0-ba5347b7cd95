import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that visualizes the limit definition of the derivative.
class InteractiveDerivativeDefinitionVisualizerWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveDerivativeDefinitionVisualizerWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  /// Factory constructor to create the widget from JSON data
  factory InteractiveDerivativeDefinitionVisualizerWidget.fromData(Map<String, dynamic> data) {
    return InteractiveDerivativeDefinitionVisualizerWidget(
      data: data,
    );
  }

  @override
  State<InteractiveDerivativeDefinitionVisualizerWidget> createState() => _InteractiveDerivativeDefinitionVisualizerWidgetState();
}

class _InteractiveDerivativeDefinitionVisualizerWidgetState extends State<InteractiveDerivativeDefinitionVisualizerWidget> {
  late double _pointX; // The fixed x-value
  late double _h; // The value of h, approaching 0

  // Function to visualize (e.g., f(x) = x^2)
  double _function(double x) {
    return x * x;
  }

  // Derivative of the function (f'(x) = 2x for f(x) = x^2)
  double _derivative(double x) {
    return 2 * x;
  }

  // Colors
  late Color _primaryColor;
  late Color _secondaryColor;
  late Color _backgroundColor;
  late Color _textColor;

  @override
  void initState() {
    super.initState();
    _pointX = widget.data['initial_point_x']?.toDouble() ?? 1.0;
    _h = widget.data['initial_h']?.toDouble() ?? 2.0; // Start with a larger h

    _primaryColor = _parseColor(widget.data['primary_color']) ?? Colors.blue;
    _secondaryColor = _parseColor(widget.data['secondary_color']) ?? Colors.orange;
    _backgroundColor = _parseColor(widget.data['background_color']) ?? Colors.white;
    _textColor = _parseColor(widget.data['text_color']) ?? Colors.black87;
  }

  Color? _parseColor(String? hexString) {
    if (hexString == null) return null;
    hexString = hexString.replaceAll('#', '');
    if (hexString.length == 6) {
      hexString = 'FF$hexString';
    }
    try {
      return Color(int.parse(hexString, radix: 16));
    } catch (e) {
      return null;
    }
  }

  double _calculateSecantSlope() {
    if (_h == 0.0) return _derivative(_pointX); // At h=0, it's the tangent slope
    return (_function(_pointX + _h) - _function(_pointX)) / _h;
  }

  @override
  Widget build(BuildContext context) {
    final secantSlope = _calculateSecantSlope();
    final tangentSlope = _derivative(_pointX);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _backgroundColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.data['title'] ?? 'Derivative Definition Visualizer',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),
          const SizedBox(height: 8),
          if (widget.data['description'] != null)
            Text(
              widget.data['description'],
              style: TextStyle(
                fontSize: 14,
                color: _textColor.withOpacity(0.8),
              ),
            ),
          const SizedBox(height: 16),

          Text(
            'Function: f(x) = x²',
            style: TextStyle(color: _textColor, fontSize: 16, fontStyle: FontStyle.italic),
          ),
          Text(
            'Derivative: f\'(x) = lim (h→0) [f(x+h) - f(x)] / h',
            style: TextStyle(color: _textColor, fontSize: 16, fontStyle: FontStyle.italic),
          ),
          Text(
            'At x = ${_pointX.toStringAsFixed(1)}',
            style: TextStyle(color: _textColor, fontSize: 16, fontStyle: FontStyle.italic),
          ),
          const SizedBox(height: 16),

          Text(
            'Value of h: ${_h.toStringAsFixed(2)}',
            style: TextStyle(color: _primaryColor, fontSize: 16, fontWeight: FontWeight.bold),
          ),
          Slider(
            value: _h,
            min: 0.01, // Avoid h=0 for division
            max: 3.0,
            divisions: 299,
            label: _h.toStringAsFixed(2),
            activeColor: _primaryColor,
            onChanged: (value) {
              setState(() {
                _h = value;
              });
            },
          ),
          const SizedBox(height: 16),

          Center(
            child: Column(
              children: [
                Text(
                  'Slope of Secant Line: ${secantSlope.toStringAsFixed(2)}',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: _primaryColor,
                  ),
                ),
                Text(
                  'Slope of Tangent Line (f\'(${_pointX.toStringAsFixed(1)})): ${tangentSlope.toStringAsFixed(2)}',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: _secondaryColor,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),

          Container(
            height: 250,
            child: CustomPaint(
              painter: DerivativeDefinitionPainter(
                function: _function,
                pointX: _pointX,
                h: _h,
                primaryColor: _primaryColor,
                secondaryColor: _secondaryColor,
                textColor: _textColor,
              ),
              child: Container(),
            ),
          ),

          // Widget name tag
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'InteractiveDerivativeDefinitionVisualizer',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }
}

class DerivativeDefinitionPainter extends CustomPainter {
  final Function(double) function;
  final double pointX;
  final double h;
  final Color primaryColor;
  final Color secondaryColor;
  final Color textColor;

  DerivativeDefinitionPainter({
    required this.function,
    required this.pointX,
    required this.h,
    required this.primaryColor,
    required this.secondaryColor,
    required this.textColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint();
    final textPainter = TextPainter(textDirection: TextDirection.ltr);
    final textStyle = TextStyle(color: textColor, fontSize: 10);

    // Define plot area padding
    const double padding = 30.0;
    final double plotWidth = size.width - 2 * padding;
    final double plotHeight = size.height - 2 * padding;

    // Determine x and y ranges for plotting
    final double minPlotX = pointX - 3.0;
    final double maxPlotX = pointX + 3.0;
    final double minPlotY = -5.0;
    final double maxPlotY = 25.0; // For x^2, max at x=5 is 25

    // Scale factors
    final double xScale = plotWidth / (maxPlotX - minPlotX);
    final double yScale = plotHeight / (maxPlotY - minPlotY);

    // Helper to convert plot coordinates to canvas coordinates
    Offset toCanvas(double x, double y) {
      final canvasX = padding + (x - minPlotX) * xScale;
      final canvasY = size.height - padding - (y - minPlotY) * yScale;
      return Offset(canvasX, canvasY);
    }

    // Draw axes
    paint.color = Colors.grey;
    paint.strokeWidth = 1.0;
    // X-axis
    canvas.drawLine(toCanvas(minPlotX, 0), toCanvas(maxPlotX, 0), paint);
    // Y-axis
    canvas.drawLine(toCanvas(0, minPlotY), toCanvas(0, maxPlotY), paint);

    // Draw function curve
    paint.color = primaryColor;
    paint.style = PaintingStyle.stroke;
    paint.strokeWidth = 2.0;

    final path = Path();
    bool firstPoint = true;
    for (double x = minPlotX; x <= maxPlotX; x += (maxPlotX - minPlotX) / 100) {
      final y = function(x);
      final point = toCanvas(x, y);
      if (firstPoint) {
        path.moveTo(point.dx, point.dy);
        firstPoint = false;
      } else {
        path.lineTo(point.dx, point.dy);
      }
    }
    canvas.drawPath(path, paint);

    // Draw points (x, f(x)) and (x+h, f(x+h))
    final point1 = toCanvas(pointX, function(pointX));
    final point2 = toCanvas(pointX + h, function(pointX + h));
    paint.color = primaryColor;
    paint.style = PaintingStyle.fill;
    canvas.drawCircle(point1, 5, paint);
    canvas.drawCircle(point2, 5, paint);

    // Draw secant line
    paint.color = secondaryColor;
    paint.strokeWidth = 2.0;
    canvas.drawLine(point1, point2, paint);

    // Draw labels for points
    textPainter.text = TextSpan(text: '(x, f(x))', style: textStyle.copyWith(color: primaryColor));
    textPainter.layout();
    textPainter.paint(canvas, Offset(point1.dx + 5, point1.dy - textPainter.height / 2));

    textPainter.text = TextSpan(text: '(x+h, f(x+h))', style: textStyle.copyWith(color: primaryColor));
    textPainter.layout();
    textPainter.paint(canvas, Offset(point2.dx + 5, point2.dy - textPainter.height / 2));

    // Draw h on x-axis
    paint.color = secondaryColor;
    paint.strokeWidth = 1.0;
    canvas.drawLine(toCanvas(pointX, 0), toCanvas(pointX + h, 0), paint);
    textPainter.text = TextSpan(text: 'h', style: textStyle.copyWith(color: secondaryColor));
    textPainter.layout();
    canvas.paint(Offset(toCanvas(pointX + h / 2, 0).dx - textPainter.width / 2, toCanvas(pointX + h / 2, 0).dy + 5));
  }

  @override
  bool shouldRepaint(covariant DerivativeDefinitionPainter oldDelegate) {
    return oldDelegate.pointX != pointX ||
           oldDelegate.h != h ||
           oldDelegate.primaryColor != primaryColor ||
           oldDelegate.secondaryColor != secondaryColor ||
           oldDelegate.textColor != textColor;
  }
}
