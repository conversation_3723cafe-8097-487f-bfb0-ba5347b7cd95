import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that analyzes and visualizes the concavity of a function.
class InteractiveConcavityExplorerWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveConcavityExplorerWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  /// Factory constructor to create the widget from JSON data
  factory InteractiveConcavityExplorerWidget.fromData(Map<String, dynamic> data) {
    return InteractiveConcavityExplorerWidget(
      data: data,
    );
  }

  @override
  State<InteractiveConcavityExplorerWidget> createState() => _InteractiveConcavityExplorerWidgetState();
}

class _InteractiveConcavityExplorerWidgetState extends State<InteractiveConcavityExplorerWidget> {
  // Function to visualize (e.g., f(x) = x^3)
  double _function(double x) {
    return x * x * x;
  }

  // Second derivative of the function (f''(x) = 6x for f(x) = x^3)
  double _secondDerivative(double x) {
    return 6 * x;
  }

  // Colors
  late Color _primaryColor;
  late Color _secondaryColor;
  late Color _backgroundColor;
  late Color _textColor;
  late Color _concaveUpColor;
  late Color _concaveDownColor;

  @override
  void initState() {
    super.initState();
    _primaryColor = _parseColor(widget.data['primary_color']) ?? Colors.blue;
    _secondaryColor = _parseColor(widget.data['secondary_color']) ?? Colors.orange;
    _backgroundColor = _parseColor(widget.data['background_color']) ?? Colors.white;
    _textColor = _parseColor(widget.data['text_color']) ?? Colors.black87;
    _concaveUpColor = _parseColor(widget.data['concave_up_color']) ?? Colors.green;
    _concaveDownColor = _parseColor(widget.data['concave_down_color']) ?? Colors.red;
  }

  Color? _parseColor(String? hexString) {
    if (hexString == null) return null;
    hexString = hexString.replaceAll('#', '');
    if (hexString.length == 6) {
      hexString = 'FF$hexString';
    }
    try {
      return Color(int.parse(hexString, radix: 16));
    } catch (e) {
      return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    // Inflection point where f''(x) = 0: x = 0
    final inflectionPointX = 0.0;
    final inflectionPointY = _function(inflectionPointX);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _backgroundColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.data['title'] ?? 'Concavity Explorer',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),
          const SizedBox(height: 8),
          if (widget.data['description'] != null)
            Text(
              widget.data['description'],
              style: TextStyle(
                fontSize: 14,
                color: _textColor.withOpacity(0.8),
              ),
            ),
          const SizedBox(height: 16),

          Text(
            'Function: f(x) = x³',
            style: TextStyle(color: _textColor, fontSize: 16, fontStyle: FontStyle.italic),
          ),
          Text(
            'Second Derivative: f\'\'(x) = 6x',
            style: TextStyle(color: _textColor, fontSize: 16, fontStyle: FontStyle.italic),
          ),
          const SizedBox(height: 16),

          Container(
            height: 250,
            child: CustomPaint(
              painter: ConcavityPainter(
                function: _function,
                secondDerivative: _secondDerivative,
                inflectionPointX: inflectionPointX,
                inflectionPointY: inflectionPointY,
                primaryColor: _primaryColor,
                secondaryColor: _secondaryColor,
                concaveUpColor: _concaveUpColor,
                concaveDownColor: _concaveDownColor,
                textColor: _textColor,
              ),
              child: Container(),
            ),
          ),
          const SizedBox(height: 16),

          Center(
            child: Column(
              children: [
                Text(
                  'Analysis:',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: _textColor,
                  ),
                ),
                Text(
                  'f(x) is concave up when f\'\'(x) > 0',
                  style: TextStyle(color: _concaveUpColor),
                ),
                Text(
                  'f(x) is concave down when f\'\'(x) < 0',
                  style: TextStyle(color: _concaveDownColor),
                ),
                Text(
                  'Inflection point where f\'\'(x) = 0: x = 0',
                  style: TextStyle(color: _textColor.withOpacity(0.8)),
                ),
                Text(
                  'Concave Up: (0, ∞)',
                  style: TextStyle(color: _concaveUpColor, fontWeight: FontWeight.bold),
                ),
                Text(
                  'Concave Down: (-∞, 0)',
                  style: TextStyle(color: _concaveDownColor, fontWeight: FontWeight.bold),
                ),
              ],
            ),
          ),

          // Widget name tag
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'InteractiveConcavityExplorer',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }
}

class ConcavityPainter extends CustomPainter {
  final Function(double) function;
  final Function(double) secondDerivative;
  final double inflectionPointX;
  final double inflectionPointY;
  final Color primaryColor;
  final Color secondaryColor;
  final Color concaveUpColor;
  final Color concaveDownColor;
  final Color textColor;

  ConcavityPainter({
    required this.function,
    required this.secondDerivative,
    required this.inflectionPointX,
    required this.inflectionPointY,
    required this.primaryColor,
    required this.secondaryColor,
    required this.concaveUpColor,
    required this.concaveDownColor,
    required this.textColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint();
    final textPainter = TextPainter(textDirection: TextDirection.ltr);
    final textStyle = TextStyle(color: textColor, fontSize: 10);

    // Define plot area padding
    const double padding = 30.0;
    final double plotWidth = size.width - 2 * padding;
    final double plotHeight = size.height - 2 * padding;

    // Determine x and y ranges for plotting
    final double minPlotX = -3.0;
    final double maxPlotX = 3.0;
    final double minPlotY = -27.0; // For x^3, min at x=-3 is -27
    final double maxPlotY = 27.0; // For x^3, max at x=3 is 27

    // Scale factors
    final double xScale = plotWidth / (maxPlotX - minPlotX);
    final double yScale = plotHeight / (maxPlotY - minPlotY);

    // Helper to convert plot coordinates to canvas coordinates
    Offset toCanvas(double x, double y) {
      final canvasX = padding + (x - minPlotX) * xScale;
      final canvasY = size.height - padding - (y - minPlotY) * yScale;
      return Offset(canvasX, canvasY);
    }

    // Draw axes
    paint.color = Colors.grey;
    paint.strokeWidth = 1.0;
    // X-axis
    canvas.drawLine(toCanvas(minPlotX, 0), toCanvas(maxPlotX, 0), paint);
    // Y-axis
    canvas.drawLine(toCanvas(0, minPlotY), toCanvas(0, maxPlotY), paint);

    // Draw function curve
    paint.color = primaryColor;
    paint.style = PaintingStyle.stroke;
    paint.strokeWidth = 2.0;

    final path = Path();
    bool firstPoint = true;
    for (double x = minPlotX; x <= maxPlotX; x += (maxPlotX - minPlotX) / 200) {
      final y = function(x);
      final point = toCanvas(x, y);
      if (firstPoint) {
        path.moveTo(point.dx, point.dy);
        firstPoint = false;
      } else {
        path.lineTo(point.dx, point.dy);
      }
    }
    canvas.drawPath(path, paint);

    // Draw second derivative curve
    paint.color = secondaryColor;
    paint.style = PaintingStyle.stroke;
    paint.strokeWidth = 1.5;
    final secondDerivativePath = Path();
    firstPoint = true;
    for (double x = minPlotX; x <= maxPlotX; x += (maxPlotX - minPlotX) / 200) {
      final y = secondDerivative(x);
      final point = toCanvas(x, y);
      if (firstPoint) {
        secondDerivativePath.moveTo(point.dx, point.dy);
        firstPoint = false;
      } else {
        secondDerivativePath.lineTo(point.dx, point.dy);
      }
    }
    canvas.drawPath(secondDerivativePath, paint);

    // Highlight concavity intervals
    // Inflection point for f(x) = x^3 is x = 0
    final inflectionCanvasPoint = toCanvas(inflectionPointX, inflectionPointY);

    // Draw vertical line at inflection point
    paint.color = Colors.grey.withOpacity(0.5);
    paint.strokeWidth = 1.0;
    canvas.drawLine(toCanvas(inflectionPointX, minPlotY), toCanvas(inflectionPointX, maxPlotY), paint);

    // Draw point at inflection point
    paint.color = primaryColor;
    paint.style = PaintingStyle.fill;
    canvas.drawCircle(inflectionCanvasPoint, 4, paint);

    // Draw labels for inflection point
    textPainter.text = TextSpan(text: 'x=0 (Inflection)', style: textStyle);
    textPainter.layout();
    textPainter.paint(canvas, Offset(toCanvas(inflectionPointX, 0).dx - textPainter.width / 2, size.height - padding + 5));
  }

  @override
  bool shouldRepaint(covariant ConcavityPainter oldDelegate) {
    return oldDelegate.inflectionPointX != inflectionPointX ||
           oldDelegate.inflectionPointY != inflectionPointY ||
           oldDelegate.primaryColor != primaryColor ||
           oldDelegate.secondaryColor != secondaryColor ||
           oldDelegate.concaveUpColor != concaveUpColor ||
           oldDelegate.concaveDownColor != concaveDownColor ||
           oldDelegate.textColor != textColor;
  }
}
