import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that allows users to calculate probabilities for linear (uniform) distributions.
class InteractiveLinearProbabilityCalculatorWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveLinearProbabilityCalculatorWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  /// Factory constructor to create the widget from JSON data
  factory InteractiveLinearProbabilityCalculatorWidget.fromData(Map<String, dynamic> data) {
    return InteractiveLinearProbabilityCalculatorWidget(
      data: data,
    );
  }

  @override
  State<InteractiveLinearProbabilityCalculatorWidget> createState() => _InteractiveLinearProbabilityCalculatorWidgetState();
}

class _InteractiveLinearProbabilityCalculatorWidgetState extends State<InteractiveLinearProbabilityCalculatorWidget> {
  late double _minRange;
  late double _maxRange;
  late double _lowerBound;
  late double _upperBound;
  
  double _calculatedProbability = 0.0;

  // Colors
  late Color _primaryColor;
  late Color _secondaryColor;
  late Color _backgroundColor;
  late Color _textColor;

  @override
  void initState() {
    super.initState();
    _minRange = widget.data['min_range']?.toDouble() ?? 0.0;
    _maxRange = widget.data['max_range']?.toDouble() ?? 10.0;
    _lowerBound = widget.data['lower_bound']?.toDouble() ?? 2.0;
    _upperBound = widget.data['upper_bound']?.toDouble() ?? 8.0;

    // Ensure bounds are within range and lower <= upper
    if (_lowerBound < _minRange) _lowerBound = _minRange;
    if (_upperBound > _maxRange) _upperBound = _maxRange;
    if (_lowerBound > _upperBound) _lowerBound = _upperBound;

    _primaryColor = _parseColor(widget.data['primary_color']) ?? Colors.blue;
    _secondaryColor = _parseColor(widget.data['secondary_color']) ?? Colors.orange;
    _backgroundColor = _parseColor(widget.data['background_color']) ?? Colors.white;
    _textColor = _parseColor(widget.data['text_color']) ?? Colors.black87;

    _calculateProbability();
  }

  Color? _parseColor(String? hexString) {
    if (hexString == null) return null;
    hexString = hexString.replaceAll('#', '');
    if (hexString.length == 6) {
      hexString = 'FF$hexString';
    }
    try {
      return Color(int.parse(hexString, radix: 16));
    } catch (e) {
      return null;
    }
  }

  void _calculateProbability() {
    setState(() {
      if (_maxRange <= _minRange) {
        _calculatedProbability = 0.0;
        return;
      }
      
      double effectiveLower = math.max(_lowerBound, _minRange);
      double effectiveUpper = math.min(_upperBound, _maxRange);

      if (effectiveUpper <= effectiveLower) {
        _calculatedProbability = 0.0;
      } else {
        _calculatedProbability = (effectiveUpper - effectiveLower) / (_maxRange - _minRange);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _backgroundColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.data['title'] ?? 'Linear Probability Calculator',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),
          const SizedBox(height: 8),
          if (widget.data['description'] != null)
            Text(
              widget.data['description'],
              style: TextStyle(
                fontSize: 14,
                color: _textColor.withOpacity(0.8),
              ),
            ),
          const SizedBox(height: 16),

          Text(
            'Distribution Range: [${_minRange.toStringAsFixed(1)}, ${_maxRange.toStringAsFixed(1)}]',
            style: TextStyle(color: _textColor),
          ),
          Row(
            children: [
              Expanded(
                child: Slider(
                  value: _minRange,
                  min: -10.0,
                  max: _maxRange - 1, // Ensure min is always less than max
                  divisions: 100,
                  label: _minRange.toStringAsFixed(1),
                  activeColor: _primaryColor,
                  onChanged: (value) {
                    setState(() {
                      _minRange = value;
                      if (_lowerBound < _minRange) _lowerBound = _minRange;
                      _calculateProbability();
                    });
                  },
                ),
              ),
              Expanded(
                child: Slider(
                  value: _maxRange,
                  min: _minRange + 1, // Ensure max is always greater than min
                  max: 20.0,
                  divisions: 100,
                  label: _maxRange.toStringAsFixed(1),
                  activeColor: _primaryColor,
                  onChanged: (value) {
                    setState(() {
                      _maxRange = value;
                      if (_upperBound > _maxRange) _upperBound = _maxRange;
                      _calculateProbability();
                    });
                  },
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          Text(
            'Interval: [${_lowerBound.toStringAsFixed(1)}, ${_upperBound.toStringAsFixed(1)}]',
            style: TextStyle(color: _textColor),
          ),
          Row(
            children: [
              Expanded(
                child: Slider(
                  value: _lowerBound,
                  min: _minRange,
                  max: _upperBound, // Ensure lower is always less than or equal to upper
                  divisions: 100,
                  label: _lowerBound.toStringAsFixed(1),
                  activeColor: _secondaryColor,
                  onChanged: (value) {
                    setState(() {
                      _lowerBound = value;
                      _calculateProbability();
                    });
                  },
                ),
              ),
              Expanded(
                child: Slider(
                  value: _upperBound,
                  min: _lowerBound, // Ensure upper is always greater than or equal to lower
                  max: _maxRange,
                  divisions: 100,
                  label: _upperBound.toStringAsFixed(1),
                  activeColor: _secondaryColor,
                  onChanged: (value) {
                    setState(() {
                      _upperBound = value;
                      _calculateProbability();
                    });
                  },
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          Center(
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: _primaryColor),
              ),
              child: Text(
                'P(${_lowerBound.toStringAsFixed(2)} ≤ X ≤ ${_upperBound.toStringAsFixed(2)}) = ${_calculatedProbability.toStringAsFixed(4)}',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: _primaryColor,
                ),
              ),
            ),
          ),
          const SizedBox(height: 16),

          Container(
            height: 100,
            child: CustomPaint(
              painter: LinearProbabilityPainter(
                minRange: _minRange,
                maxRange: _maxRange,
                lowerBound: _lowerBound,
                upperBound: _upperBound,
                distributionColor: _primaryColor,
                intervalColor: _secondaryColor,
                textColor: _textColor,
              ),
              child: Container(),
            ),
          ),

          // Widget name tag
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'InteractiveLinearProbabilityCalculator',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }
}

class LinearProbabilityPainter extends CustomPainter {
  final double minRange;
  final double maxRange;
  final double lowerBound;
  final double upperBound;
  final Color distributionColor;
  final Color intervalColor;
  final Color textColor;

  LinearProbabilityPainter({
    required this.minRange,
    required this.maxRange,
    required this.lowerBound,
    required this.upperBound,
    required this.distributionColor,
    required this.intervalColor,
    required this.textColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint();
    final textPainter = TextPainter(textDirection: TextDirection.ltr);
    final textStyle = TextStyle(color: textColor, fontSize: 12);

    // Draw the full distribution range (rectangle)
    paint.color = distributionColor.withOpacity(0.2);
    canvas.drawRect(Rect.fromLTWH(0, 0, size.width, size.height), paint);

    // Draw the interval rectangle
    if (maxRange > minRange) {
      final startX = (lowerBound - minRange) / (maxRange - minRange) * size.width;
      final endX = (upperBound - minRange) / (maxRange - minRange) * size.width;
      
      paint.color = intervalColor.withOpacity(0.5);
      canvas.drawRect(Rect.fromLTWH(startX, 0, endX - startX, size.height), paint);

      // Draw borders
      paint.color = distributionColor;
      paint.style = PaintingStyle.stroke;
      paint.strokeWidth = 1.0;
      canvas.drawRect(Rect.fromLTWH(0, 0, size.width, size.height), paint);

      paint.color = intervalColor;
      canvas.drawRect(Rect.fromLTWH(startX, 0, endX - startX, size.height), paint);
    }

    // Draw labels for minRange, maxRange, lowerBound, upperBound
    // minRange
    textPainter.text = TextSpan(text: minRange.toStringAsFixed(1), style: textStyle);
    textPainter.layout();
    textPainter.paint(canvas, Offset(0, size.height - textPainter.height));

    // maxRange
    textPainter.text = TextSpan(text: maxRange.toStringAsFixed(1), style: textStyle);
    textPainter.layout();
    textPainter.paint(canvas, Offset(size.width - textPainter.width, size.height - textPainter.height));

    // lowerBound
    if (maxRange > minRange) {
      final lowerX = (lowerBound - minRange) / (maxRange - minRange) * size.width;
      textPainter.text = TextSpan(text: lowerBound.toStringAsFixed(1), style: textStyle);
      textPainter.layout();
      textPainter.paint(canvas, Offset(lowerX - textPainter.width / 2, -textPainter.height));
    }

    // upperBound
    if (maxRange > minRange) {
      final upperX = (upperBound - minRange) / (maxRange - minRange) * size.width;
      textPainter.text = TextSpan(text: upperBound.toStringAsFixed(1), style: textStyle);
      textPainter.layout();
      textPainter.paint(canvas, Offset(upperX - textPainter.width / 2, -textPainter.height));
    }
  }

  @override
  bool shouldRepaint(covariant LinearProbabilityPainter oldDelegate) {
    return oldDelegate.minRange != minRange ||
           oldDelegate.maxRange != maxRange ||
           oldDelegate.lowerBound != lowerBound ||
           oldDelegate.upperBound != upperBound;
  }
}
