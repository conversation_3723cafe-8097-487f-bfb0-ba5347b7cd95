import 'package:flutter/material.dart';

class InteractiveEvolutionTimelineRefrigerator extends StatefulWidget {
  const InteractiveEvolutionTimelineRefrigerator({super.key});

  @override
  InteractiveEvolutionTimelineRefrigeratorState createState() => InteractiveEvolutionTimelineRefrigeratorState();
}

class InteractiveEvolutionTimelineRefrigeratorState extends State<InteractiveEvolutionTimelineRefrigerator> {
  final List<Map<String, String>> _timelineEvents = [
    {
      'year': '1748',
      'event': '<PERSON> demonstrates artificial refrigeration by evaporating ether in a vacuum.',
    },
    {
      'year': '1805',
      'event': '<PERSON> designs the first refrigeration machine using vapor compression, though it was never built.',
    },
    {
      'year': '1834',
      'event': '<PERSON> builds the first working vapor-compression refrigeration system in London.',
    },
    {
      'year': '1856',
      'event': '<PERSON> builds the first practical ice-making machine and a commercial vapor-compression refrigeration system in Australia.',
    },
    {
      'year': '1876',
      'event': '<PERSON> invents a new method for liquefying gas, leading to more efficient refrigeration.',
    },
    {
      'year': '1913',
      'event': 'The first home refrigerators for domestic use are invented by <PERSON> in Fort Wayne, Indiana, with the DOMELRE (DOMestic ELectric REfrigerator).',
    },
    {
      'year': '1922',
      'event': 'Baltzar von Platen and Carl Munters invent the absorption refrigerator for Electrolux, which could be powered by gas or electricity.',
    },
    {
      'year': '1920s-1930s',
      'event': 'Freon (CFCs) refrigerants are developed, making refrigerators safer and more common, but later found to deplete the ozone layer.',
    },
    {
      'year': '1970s-1990s',
      'event': 'Increased focus on energy efficiency and environmental impact leads to the phasing out of CFCs and the development of new refrigerants (HFCs, then HFOs).',
    },
    {
      'year': 'Present',
      'event': 'Modern refrigerators feature advanced energy efficiency, smart technology, and improved designs.',
    },
  ];

  final ScrollController _scrollController = ScrollController();

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Refrigerator Evolution Timeline',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            SizedBox(
              height: 300,
              child: ListView.builder(
                controller: _scrollController,
                itemCount: _timelineEvents.length,
                itemBuilder: (context, index) {
                  final event = _timelineEvents[index];
                  return TimelineTile(
                    year: event['year']!,
                    event: event['event']!,
                    isFirst: index == 0,
                    isLast: index == _timelineEvents.length - 1,
                  );
                },
              ),
            ),
            const SizedBox(height: 10),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                IconButton(
                  icon: const Icon(Icons.arrow_upward),
                  onPressed: () {
                    _scrollController.animateTo(
                      _scrollController.offset - 150,
                      duration: const Duration(milliseconds: 300),
                      curve: Curves.easeOut,
                    );
                  },
                ),
                IconButton(
                  icon: const Icon(Icons.arrow_downward),
                  onPressed: () {
                    _scrollController.animateTo(
                      _scrollController.offset + 150,
                      duration: const Duration(milliseconds: 300),
                      curve: Curves.easeOut,
                    );
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class TimelineTile extends StatelessWidget {
  final String year;
  final String event;
  final bool isFirst;
  final bool isLast;

  const TimelineTile({
    super.key,
    required this.year,
    required this.event,
    this.isFirst = false,
    this.isLast = false,
  });

  @override
  Widget build(BuildContext context) {
    return IntrinsicHeight(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Column(
            children: [
              Container(
                width: 2,
                height: isFirst ? 0 : 40, // No line above first item
                color: Colors.grey,
              ),
              Container(
                width: 12,
                height: 12,
                decoration: BoxDecoration(
                  color: Colors.blue,
                  shape: BoxShape.circle,
                  border: Border.all(color: Colors.blueAccent, width: 2),
                ),
              ),
              Expanded(
                child: Container(
                  width: 2,
                  color: isLast ? Colors.transparent : Colors.grey, // No line below last item
                ),
              ),
            ],
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.only(bottom: 20.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    year,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                      color: Colors.blue,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    event,
                    style: const TextStyle(fontSize: 14),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
