import 'package:flutter/material.dart';
import 'package:resonance/utils/page_transitions.dart';
import 'package:resonance/widgets/animated_button.dart';

class InteractiveCircuitApplicationsChallenge extends StatefulWidget {
  const InteractiveCircuitApplicationsChallenge({super.key});

  @override
  State<InteractiveCircuitApplicationsChallenge> createState() => _InteractiveCircuitApplicationsChallengeState();
}

class _InteractiveCircuitApplicationsChallengeState extends State<InteractiveCircuitApplicationsChallenge> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Interactive Circuit Applications Challenge'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            Navigator.of(context).pop();
          },
        ),
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Text(
                'This is the Interactive Circuit Applications Challenge.',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 18),
              ),
              const SizedBox(height: 30),
              AnimatedButton(
                onTap: () {
                  // Add functionality for the button here
                },
                text: 'Start Challenge',
                color: Colors.blueAccent,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
