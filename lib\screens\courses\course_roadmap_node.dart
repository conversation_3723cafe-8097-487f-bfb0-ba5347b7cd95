import 'package:flutter/material.dart';

/// A custom widget for displaying roadmap nodes with different shapes
class RoadmapNode extends StatelessWidget {
  final NodeShape shape;
  final Color color;
  final bool isSelected;
  final bool isCompleted;
  final bool isActive;
  final Widget? child;
  final VoidCallback? onTap;

  const RoadmapNode({
    Key? key,
    required this.shape,
    required this.color,
    this.isSelected = false,
    this.isCompleted = false,
    this.isActive = true,
    this.child,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Determine the actual color based on state
    Color nodeColor = Colors.grey[400]!;
    if (isActive) {
      nodeColor = isCompleted ? Colors.green : color;
    }

    return GestureDetector(
      onTap: isActive ? onTap : null,
      child: _buildNodeShape(shape, nodeColor),
    );
  }

  Widget _buildNodeShape(NodeShape shape, Color color) {
    switch (shape) {
      case NodeShape.hexagon:
        return _buildHexagonNode(color);
      case NodeShape.diamond:
        return _buildDiamondNode(color);
      case NodeShape.square:
        return _buildSquareNode(color);
      case NodeShape.circle:
        return _buildCircleNode(color);
    }
  }

  Widget _buildHexagonNode(Color color) {
    return Container(
      width: 60,
      height: 60,
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(12),
        boxShadow: isSelected ? [
          BoxShadow(
            color: color.withAlpha(128),
            blurRadius: 10,
            spreadRadius: 2,
          ),
        ] : null,
      ),
      child: child,
    );
  }

  Widget _buildDiamondNode(Color color) {
    return Transform.rotate(
      angle: 0.785398, // 45 degrees in radians
      child: Container(
        width: 50,
        height: 50,
        decoration: BoxDecoration(
          color: color,
          borderRadius: BorderRadius.circular(8),
          boxShadow: isSelected ? [
            BoxShadow(
              color: color.withAlpha(128),
              blurRadius: 10,
              spreadRadius: 2,
            ),
          ] : null,
        ),
        child: child != null 
            ? Transform.rotate(angle: -0.785398, child: child)
            : null,
      ),
    );
  }

  Widget _buildSquareNode(Color color) {
    return Container(
      width: 50,
      height: 50,
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(8),
        boxShadow: isSelected ? [
          BoxShadow(
            color: color.withAlpha(128),
            blurRadius: 10,
            spreadRadius: 2,
          ),
        ] : null,
      ),
      child: child,
    );
  }

  Widget _buildCircleNode(Color color) {
    return Container(
      width: 50,
      height: 50,
      decoration: BoxDecoration(
        color: color,
        shape: BoxShape.circle,
        boxShadow: isSelected ? [
          BoxShadow(
            color: color.withAlpha(128),
            blurRadius: 10,
            spreadRadius: 2,
          ),
        ] : null,
      ),
      child: child,
    );
  }
}

/// A custom widget for connecting roadmap nodes with lines
class RoadmapConnector extends StatelessWidget {
  final Color startColor;
  final Color endColor;
  final double length;
  final double thickness;
  final bool isDiagonal;
  final bool isActive;

  const RoadmapConnector({
    Key? key,
    required this.startColor,
    required this.endColor,
    this.length = 40,
    this.thickness = 3,
    this.isDiagonal = false,
    this.isActive = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final actualStartColor = isActive ? startColor : Colors.grey[300]!;
    final actualEndColor = isActive ? endColor : Colors.grey[300]!;

    if (isDiagonal) {
      return Transform.rotate(
        angle: 0.785398, // 45 degrees in radians
        child: Container(
          width: thickness,
          height: length * 1.414, // sqrt(2) for diagonal length
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [actualStartColor, actualEndColor],
            ),
          ),
        ),
      );
    } else {
      return Container(
        width: thickness,
        height: length,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [actualStartColor, actualEndColor],
          ),
        ),
      );
    }
  }
}

enum NodeShape {
  hexagon,
  diamond,
  square,
  circle,
}
