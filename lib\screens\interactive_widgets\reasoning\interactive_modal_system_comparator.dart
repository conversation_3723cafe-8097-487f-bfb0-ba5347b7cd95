import 'package:flutter/material.dart';
import 'package:resonance/widgets/interactive_widget.dart';

class InteractiveModalSystemComparator extends InteractiveWidget {
  const InteractiveModalSystemComparator({super.key});

  @override
  InteractiveWidgetMetaData get metaData => InteractiveWidgetMetaData(
        title: 'Interactive Modal System Comparator',
        category: 'Reasoning',
        course: 'Reasoning: Frontiers',
        module: 'Modal Logic: Reasoning About Possibility and Necessity',
        slug: 'interactive-modal-system-comparator',
        description:
            'An interactive tool to compare different modal logic systems (e.g., K, T, S4, S5) and their properties.',
        difficulty: InteractiveWidgetDifficulty.hard,
        tags: const ['logic', 'modal logic', 'systems', 'comparison'],
      );

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(metaData.title),
        backgroundColor: Colors.deepPurple,
      ),
      body: const Center(
        child: Padding(
          padding: EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'Welcome to the Interactive Modal System Comparator!',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 20),
              Text(
                'This widget will help you compare different modal logic systems and their properties.',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 16),
              ),
              SizedBox(height: 40),
              // Placeholder for interactive content
              CircularProgressIndicator(),
              SizedBox(height: 20),
              Text(
                'Content coming soon!',
                style: TextStyle(fontSize: 18, fontStyle: FontStyle.italic),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
