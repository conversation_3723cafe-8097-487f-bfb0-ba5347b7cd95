import 'package:flutter/material.dart';
import 'package:resonance/widgets/interactive_widget_scaffold.dart';

class InteractiveHeatTransferCalculator extends StatelessWidget {
  const InteractiveHeatTransferCalculator({super.key});

  @override
  Widget build(BuildContext context) {
    return InteractiveWidgetScaffold(
      title: 'Heat Transfer Calculator',
      interactiveWidget: Center(
        child: Text('Interactive Heat Transfer Calculator Placeholder'),
      ),
    );
  }
}
