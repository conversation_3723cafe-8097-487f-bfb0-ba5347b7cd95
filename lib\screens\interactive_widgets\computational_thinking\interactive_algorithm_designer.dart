import 'package:flutter/material.dart';

class InteractiveAlgorithmDesigner extends StatefulWidget {
  const InteractiveAlgorithmDesigner({super.key});

  @override
  State<InteractiveAlgorithmDesigner> createState() => _InteractiveAlgorithmDesignerState();
}

class _InteractiveAlgorithmDesignerState extends State<InteractiveAlgorithmDesigner> {
  List<String> _algorithmSteps = [];
  final TextEditingController _stepController = TextEditingController();

  void _addStep() {
    setState(() {
      String step = _stepController.text.trim();
      if (step.isNotEmpty) {
        _algorithmSteps.add(step);
        _stepController.clear();
      }
    });
  }

  void _removeStep(int index) {
    setState(() {
      _algorithmSteps.removeAt(index);
    });
  }

  void _clearSteps() {
    setState(() {
      _algorithmSteps.clear();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Interactive Algorithm Designer',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            TextField(
              controller: _stepController,
              decoration: InputDecoration(
                labelText: 'Enter an algorithm step (e.g., "Read input", "If condition, then do X")',
                border: OutlineInputBorder(),
              ),
              onSubmitted: (_) => _addStep(),
            ),
            const SizedBox(height: 10),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ElevatedButton(
                  onPressed: _addStep,
                  child: const Text('Add Step'),
                ),
                ElevatedButton(
                  onPressed: _clearSteps,
                  child: const Text('Clear All'),
                  style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
                ),
              ],
            ),
            const SizedBox(height: 20),
            if (_algorithmSteps.isNotEmpty)
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Algorithm Steps:',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 10),
                  ListView.builder(
                    shrinkWrap: true,
                    itemCount: _algorithmSteps.length,
                    itemBuilder: (context, index) {
                      return Card(
                        margin: const EdgeInsets.symmetric(vertical: 4.0),
                        child: ListTile(
                          leading: CircleAvatar(child: Text('${index + 1}')),
                          title: Text(_algorithmSteps[index]),
                          trailing: IconButton(
                            icon: const Icon(Icons.delete, color: Colors.red),
                            onPressed: () => _removeStep(index),
                          ),
                        ),
                      );
                    },
                  ),
                ],
              )
            else
              const Text(
                'Start adding steps to design your algorithm.',
                style: TextStyle(fontStyle: FontStyle.italic, color: Colors.grey),
              ),
          ],
        ),
      ),
    );
  }
}
