import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that helps users interpret the meaning of the integral in context.
class InteractiveIntegralInterpreterWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveIntegralInterpreterWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  /// Factory constructor to create the widget from JSON data
  factory InteractiveIntegralInterpreterWidget.fromData(Map<String, dynamic> data) {
    return InteractiveIntegralInterpreterWidget(
      data: data,
    );
  }

  @override
  State<InteractiveIntegralInterpreterWidget> createState() => _InteractiveIntegralInterpreterWidgetState();
}

class _InteractiveIntegralInterpreterWidgetState extends State<InteractiveIntegralInterpreterWidget> {
  late double _minTime; // Lower bound of integration
  late double _maxTime; // Upper bound of integration

  // Velocity function (e.g., v(t) = t)
  double _velocityFunction(double t) {
    return t;
  }

  // Displacement function (integral of velocity, s(t) = t^2 / 2)
  double _displacementFunction(double t) {
    return (t * t) / 2.0;
  }

  // Colors
  late Color _primaryColor; // For v(t)
  late Color _secondaryColor; // For shaded area (displacement)
  late Color _backgroundColor;
  late Color _textColor;

  @override
  void initState() {
    super.initState();
    _minTime = widget.data['initial_min_time']?.toDouble() ?? 0.0;
    _maxTime = widget.data['initial_max_time']?.toDouble() ?? 3.0;

    _primaryColor = _parseColor(widget.data['primary_color']) ?? Colors.blue;
    _secondaryColor = _parseColor(widget.data['secondary_color']) ?? Colors.orange;
    _backgroundColor = _parseColor(widget.data['background_color']) ?? Colors.white;
    _textColor = _parseColor(widget.data['text_color']) ?? Colors.black87;
  }

  Color? _parseColor(String? hexString) {
    if (hexString == null) return null;
    hexString = hexString.replaceAll('#', '');
    if (hexString.length == 6) {
      hexString = 'FF$hexString';
    }
    try {
      return Color(int.parse(hexString, radix: 16));
    } catch (e) {
      return null;
    }
  }

  double _calculateDisplacement() {
    // Displacement is the definite integral of velocity
    return _displacementFunction(_maxTime) - _displacementFunction(_minTime);
  }

  @override
  Widget build(BuildContext context) {
    final displacement = _calculateDisplacement();

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _backgroundColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.data['title'] ?? 'Integral Interpreter (Velocity-Displacement)',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),
          const SizedBox(height: 8),
          if (widget.data['description'] != null)
            Text(
              widget.data['description'],
              style: TextStyle(
                fontSize: 14,
                color: _textColor.withOpacity(0.8),
              ),
            ),
          const SizedBox(height: 16),

          Text(
            'Velocity Function: v(t) = t',
            style: TextStyle(color: _textColor, fontSize: 16, fontStyle: FontStyle.italic),
          ),
          Text(
            'Displacement = ∫ v(t) dt',
            style: TextStyle(color: _textColor, fontSize: 16, fontStyle: FontStyle.italic),
          ),
          const SizedBox(height: 16),

          Text(
            'Time Interval: [${_minTime.toStringAsFixed(1)}, ${_maxTime.toStringAsFixed(1)}]',
            style: TextStyle(color: _primaryColor, fontSize: 16, fontWeight: FontWeight.bold),
          ),
          Row(
            children: [
              Expanded(
                child: Slider(
                  value: _minTime,
                  min: 0.0,
                  max: _maxTime,
                  divisions: 50,
                  label: _minTime.toStringAsFixed(1),
                  activeColor: _primaryColor,
                  onChanged: (value) {
                    setState(() {
                      _minTime = value;
                      _calculateDisplacement();
                    });
                  },
                ),
              ),
              Expanded(
                child: Slider(
                  value: _maxTime,
                  min: _minTime,
                  max: 5.0,
                  divisions: 50,
                  label: _maxTime.toStringAsFixed(1),
                  activeColor: _primaryColor,
                  onChanged: (value) {
                    setState(() {
                      _maxTime = value;
                      _calculateDisplacement();
                    });
                  },
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          Center(
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: _primaryColor),
              ),
              child: Text(
                'Displacement: ${displacement.toStringAsFixed(2)} units',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: _primaryColor,
                ),
              ),
            ),
          ),
          const SizedBox(height: 16),

          Container(
            height: 250,
            child: CustomPaint(
              painter: IntegralInterpreterPainter(
                velocityFunction: _velocityFunction,
                minTime: _minTime,
                maxTime: _maxTime,
                primaryColor: _primaryColor,
                secondaryColor: _secondaryColor,
                textColor: _textColor,
              ),
              child: Container(),
            ),
          ),

          // Widget name tag
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'InteractiveIntegralInterpreter',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }
}

class IntegralInterpreterPainter extends CustomPainter {
  final Function(double) velocityFunction;
  final double minTime;
  final double maxTime;
  final Color primaryColor;
  final Color secondaryColor;
  final Color textColor;

  IntegralInterpreterPainter({
    required this.velocityFunction,
    required this.minTime,
    required this.maxTime,
    required this.primaryColor,
    required this.secondaryColor,
    required this.textColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint();
    final textPainter = TextPainter(textDirection: TextDirection.ltr);
    final textStyle = TextStyle(color: textColor, fontSize: 10);

    // Define plot area padding
    const double padding = 30.0;
    final double plotWidth = size.width - 2 * padding;
    final double plotHeight = size.height - 2 * padding;

    // Determine x (time) and y (velocity) ranges for plotting
    final double minPlotT = 0.0;
    final double maxPlotT = 5.0;
    final double minPlotV = 0.0;
    final double maxPlotV = 5.0; // For v(t) = t, max at t=5 is 5

    // Scale factors
    final double xScale = plotWidth / (maxPlotT - minPlotT);
    final double yScale = plotHeight / (maxPlotV - minPlotV);

    // Helper to convert plot coordinates to canvas coordinates
    Offset toCanvas(double t, double v) {
      final canvasX = padding + (t - minPlotT) * xScale;
      final canvasY = size.height - padding - (v - minPlotV) * yScale;
      return Offset(canvasX, canvasY);
    }

    // Draw axes
    paint.color = Colors.grey;
    paint.strokeWidth = 1.0;
    // T-axis (X-axis)
    canvas.drawLine(toCanvas(minPlotT, 0), toCanvas(maxPlotT, 0), paint);
    // V-axis (Y-axis)
    canvas.drawLine(toCanvas(0, minPlotV), toCanvas(0, maxPlotV), paint);

    // Draw axis labels
    textPainter.text = TextSpan(text: 'Time (t)', style: textStyle);
    textPainter.layout();
    textPainter.paint(canvas, Offset(size.width / 2 - textPainter.width / 2, size.height - padding + 5));

    textPainter.text = TextSpan(text: 'Velocity (v)', style: textStyle);
    textPainter.layout();
    canvas.save();
    canvas.translate(padding - 15, size.height / 2 + textPainter.width / 2);
    canvas.rotate(-math.pi / 2);
    textPainter.paint(canvas, Offset.zero);
    canvas.restore();

    // Draw velocity function curve
    paint.color = primaryColor;
    paint.style = PaintingStyle.stroke;
    paint.strokeWidth = 2.0;

    final path = Path();
    bool firstPoint = true;
    for (double t = minPlotT; t <= maxPlotT; t += (maxPlotT - minPlotT) / 100) {
      final v = velocityFunction(t);
      final point = toCanvas(t, v);
      if (firstPoint) {
        path.moveTo(point.dx, point.dy);
        firstPoint = false;
      } else {
        path.lineTo(point.dx, point.dy);
      }
    }
    canvas.drawPath(path, paint);

    // Draw shaded area (integral)
    paint.color = secondaryColor.withOpacity(0.4);
    paint.style = PaintingStyle.fill;
    final areaPath = Path()
      ..moveTo(toCanvas(minTime, 0).dx, toCanvas(minTime, 0).dy);
    for (double t = minTime; t <= maxTime; t += (maxTime - minTime) / 100) {
      areaPath.lineTo(toCanvas(t, velocityFunction(t)).dx, toCanvas(t, velocityFunction(t)).dy);
    }
    areaPath.lineTo(toCanvas(maxTime, 0).dx, toCanvas(maxTime, 0).dy);
    areaPath.close();
    canvas.drawPath(areaPath, paint);

    paint.color = secondaryColor;
    paint.style = PaintingStyle.stroke;
    paint.strokeWidth = 1.0;
    canvas.drawPath(areaPath, paint);

    // Draw labels for minTime and maxTime
    textPainter.text = TextSpan(text: minTime.toStringAsFixed(1), style: textStyle);
    textPainter.layout();
    textPainter.paint(canvas, Offset(toCanvas(minTime, 0).dx - textPainter.width / 2, size.height - padding + 5));

    textPainter.text = TextSpan(text: maxTime.toStringAsFixed(1), style: textStyle);
    textPainter.layout();
    textPainter.paint(canvas, Offset(toCanvas(maxTime, 0).dx - textPainter.width / 2, size.height - padding + 5));
  }

  @override
  bool shouldRepaint(covariant IntegralInterpreterPainter oldDelegate) {
    return oldDelegate.minTime != minTime ||
           oldDelegate.maxTime != maxTime ||
           oldDelegate.primaryColor != primaryColor ||
           oldDelegate.secondaryColor != secondaryColor ||
           oldDelegate.textColor != textColor;
  }
}
