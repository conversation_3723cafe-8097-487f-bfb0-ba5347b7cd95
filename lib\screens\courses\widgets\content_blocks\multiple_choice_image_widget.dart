import 'package:flutter/material.dart';
import 'package:flutter/services.dart'; // For HapticFeedback
import '../../../../models/course_models.dart';
import 'local_asset_visual_widget.dart'; // To display image options

class MultipleChoiceImageWidget extends StatefulWidget {
  final MultipleChoiceImageElement mcqImageElement;
  final PageController? pageController;
  final VoidCallback? onNextAction;
  final bool isLastSlideInLesson;

  const MultipleChoiceImageWidget({
    super.key,
    required this.mcqImageElement,
    this.pageController,
    this.onNextAction,
    this.isLastSlideInLesson = false,
  });

  @override
  State<MultipleChoiceImageWidget> createState() =>
      _MultipleChoiceImageWidgetState();
}

class _MultipleChoiceImageWidgetState extends State<MultipleChoiceImageWidget> {
  String? _selectedOptionId;
  bool _isAnswered = false;
  bool _isCorrect = false;

  void _handleOptionSelected(MultipleChoiceImageOption option) {
    if (_isAnswered) return;

    HapticFeedback.lightImpact();
    setState(() {
      _selectedOptionId = option.id;
      _isAnswered = true;
      _isCorrect = option.is_correct;
    });

    String feedbackMessage =
        (_isCorrect ? option.feedback_correct : option.feedback_incorrect) ??
        (_isCorrect ? "Correct!" : "Not quite, try again.");

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(feedbackMessage),
        backgroundColor: _isCorrect ? Colors.green : Colors.redAccent,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  Color _getOptionBackgroundColor(MultipleChoiceImageOption option) {
    if (!_isAnswered || _selectedOptionId != option.id) {
      return Colors.white;
    }
    return option.is_correct ? Colors.green.shade50 : Colors.red.shade50;
  }

  Color _getOptionBorderColor(MultipleChoiceImageOption option) {
    if (!_isAnswered || _selectedOptionId != option.id) {
      return Colors.grey.shade300;
    }
    return option.is_correct ? Colors.green.shade400 : Colors.red.shade400;
  }

  BoxDecoration _getOptionDecoration(
    MultipleChoiceImageOption option,
    BuildContext context,
  ) {
    Color borderColor = Colors.grey.shade300;
    double borderWidth = 1.0;
    Color bgColor = Colors.white;

    if (_isAnswered && _selectedOptionId == option.id) {
      borderColor = _getOptionBorderColor(option); // Use helper
      borderWidth = 2.0;
      bgColor = _getOptionBackgroundColor(option); // Use helper
    }

    return BoxDecoration(
      color: bgColor,
      borderRadius: BorderRadius.circular(8.0),
      border: Border.all(color: borderColor, width: borderWidth),
      boxShadow:
          _isAnswered && _selectedOptionId == option.id
              ? [
                BoxShadow(
                  color: borderColor.withOpacity(0.5),
                  spreadRadius: 1,
                  blurRadius: 3,
                ),
              ]
              : [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.2),
                  spreadRadius: 1,
                  blurRadius: 2,
                ),
              ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final options = widget.mcqImageElement.options;

    // Always use 2 columns for better layout unless we have only 1 option
    int crossAxisCount = options.length == 1 ? 1 : 2;

    // Check if any image is marked as wide
    bool hasWideImage = options.any((opt) {
      if (opt.image_visual is LocalAssetVisual) {
        return (opt.image_visual as LocalAssetVisual).value
            .toLowerCase()
            .contains('wide');
      }
      return false;
    });

    // Use single column for wide images
    if (hasWideImage) {
      crossAxisCount = 1;
    }

    // Adjust aspect ratio based on layout
    double childAspectRatio = crossAxisCount == 1 ? 2.0 : 1.2;

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: 8.0,
        vertical: 12.0,
      ), // Reduced vertical padding
      margin: const EdgeInsets.symmetric(
        vertical: 10.0,
        horizontal: 4.0,
      ), // Reduced vertical margin
      decoration: BoxDecoration(
        color: Colors.indigo[50],
        borderRadius: BorderRadius.circular(16.0),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          if (widget.mcqImageElement.question_text.isNotEmpty)
            Padding(
              padding: const EdgeInsets.only(
                bottom: 12.0,
              ), // Reduced bottom padding
              child: Text(
                widget.mcqImageElement.question_text,
                style: textTheme.titleMedium?.copyWith(
                  // Changed from titleLarge to titleMedium
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          LayoutBuilder(
            builder: (context, constraints) {
              double itemWidth =
                  (constraints.maxWidth - (crossAxisCount - 1) * 10.0) /
                  crossAxisCount;
              double itemHeight = itemWidth / childAspectRatio;

              return GridView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: options.length,
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: crossAxisCount,
                  crossAxisSpacing: 8.0, // Reduced spacing
                  mainAxisSpacing: 8.0, // Reduced spacing
                  childAspectRatio: childAspectRatio,
                ),
                itemBuilder: (context, index) {
                  final option = options[index];
                  Widget imageDisplayWidget;

                  if (option.image_visual is LocalAssetVisual) {
                    // Calculate a more appropriate height based on the grid layout
                    // Use a smaller multiplier (0.6 instead of 0.8) to make images smaller
                    double imageHeight = itemHeight * 0.6;
                    // Set a maximum height to prevent overly large images
                    double maxHeight =
                        MediaQuery.of(context).size.height * 0.15;
                    imageHeight =
                        imageHeight > maxHeight ? maxHeight : imageHeight;

                    imageDisplayWidget = LocalAssetVisualWidget(
                      assetPath:
                          (option.image_visual as LocalAssetVisual).value,
                      height: imageHeight,
                      // Also set a width constraint to keep images proportional
                      width: crossAxisCount == 1 ? null : itemWidth * 0.7,
                    );
                  } else {
                    imageDisplayWidget = Center(
                      child: Text(
                        'Unsupported: ${option.image_visual.type}',
                        style: const TextStyle(
                          fontSize: 10,
                          color: Colors.orangeAccent,
                        ),
                      ),
                    );
                  }

                  return Material(
                    elevation:
                        _isAnswered && _selectedOptionId == option.id
                            ? 4.0
                            : 1.5,
                    shadowColor:
                        _isAnswered && _selectedOptionId == option.id
                            ? _getOptionBorderColor(option)
                            : Colors.black38,
                    borderRadius: BorderRadius.circular(12.0),
                    child: InkWell(
                      onTap: () => _handleOptionSelected(option),
                      borderRadius: BorderRadius.circular(12.0),
                      child: AnimatedContainer(
                        duration: const Duration(milliseconds: 250),
                        decoration: _getOptionDecoration(
                          option,
                          context, // Pass context here
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(4.0), // Reduced padding
                          child: Stack(
                            alignment: Alignment.center,
                            children: [
                              imageDisplayWidget,
                              if (_isAnswered && _selectedOptionId == option.id)
                                Positioned(
                                  top: 6,
                                  right: 6,
                                  child: Container(
                                    padding: const EdgeInsets.all(2.0),
                                    decoration: BoxDecoration(
                                      color: _getOptionBackgroundColor(
                                        option,
                                      ).withAlpha((255 * 0.8).round()),
                                      shape: BoxShape.circle,
                                    ),
                                    child: Icon(
                                      option.is_correct
                                          ? Icons.check_circle_outline_rounded
                                          : Icons.highlight_off_rounded,
                                      color:
                                          option.is_correct
                                              ? Colors.green.shade800
                                              : Colors.red.shade800,
                                      size: 22,
                                    ),
                                  ),
                                ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  );
                },
              );
            },
          ),
          if (_isAnswered &&
              widget.mcqImageElement.action_button_text.isNotEmpty)
            Padding(
              padding: const EdgeInsets.only(top: 24.0),
              child: SizedBox(
                width: double.infinity,
                height: 56,
                child: ElevatedButton.icon(
                  icon: Icon(
                    widget.isLastSlideInLesson && _isCorrect
                        ? Icons.skip_next_rounded
                        : Icons.arrow_forward_ios_rounded,
                    size: 22,
                  ),
                  label: Text(widget.mcqImageElement.action_button_text),
                  onPressed:
                      (_isCorrect ||
                                  widget.mcqImageElement.options
                                          .firstWhere(
                                            (opt) =>
                                                opt.id == _selectedOptionId,
                                            orElse:
                                                () => MultipleChoiceImageOption(
                                                  id: '',
                                                  image_visual:
                                                      PlaceholderVisual(
                                                        data: {},
                                                      ),
                                                  is_correct: false,
                                                ),
                                          )
                                          .feedback_incorrect ==
                                      null) &&
                              _isAnswered
                          ? widget.onNextAction
                          : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor:
                        _isCorrect
                            ? Theme.of(context).primaryColor
                            : Colors.grey.shade500,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 14.0),
                    textStyle: const TextStyle(
                      fontSize: 17.0,
                      fontWeight: FontWeight.bold,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12.0),
                    ),
                    elevation: _isCorrect && _isAnswered ? 3 : 1,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}
