import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that visualizes the definite integral as the area under a curve.
class InteractiveDefiniteIntegralVisualizerWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> dynamic;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveDefiniteIntegralVisualizerWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  /// Factory constructor to create the widget from JSON data
  factory InteractiveDefiniteIntegralVisualizerWidget.fromData(Map<String, dynamic> data) {
    return InteractiveDefiniteIntegralVisualizerWidget(
      data: data,
    );
  }

  @override
  State<InteractiveDefiniteIntegralVisualizerWidget> createState() => _InteractiveDefiniteIntegralVisualizerWidgetState();
}

class _InteractiveDefiniteIntegralVisualizerWidgetState extends State<InteractiveDefiniteIntegralVisualizerWidget> {
  late double _minX;
  late double _maxX;

  // Function to integrate (e.g., f(x) = x^2)
  double _function(double x) {
    return x * x;
  }

  // Antiderivative of the function (F(x) = x^3 / 3 for f(x) = x^2)
  double _antiderivative(double x) {
    return (x * x * x) / 3.0;
  }

  // Colors
  late Color _primaryColor;
  late Color _secondaryColor;
  late Color _backgroundColor;
  late Color _textColor;

  @override
  void initState() {
    super.initState();
    _minX = widget.data['min_x']?.toDouble() ?? 0.0;
    _maxX = widget.data['max_x']?.toDouble() ?? 3.0;

    _primaryColor = _parseColor(widget.data['primary_color']) ?? Colors.blue;
    _secondaryColor = _parseColor(widget.data['secondary_color']) ?? Colors.orange;
    _backgroundColor = _parseColor(widget.data['background_color']) ?? Colors.white;
    _textColor = _parseColor(widget.data['text_color']) ?? Colors.black87;
  }

  Color? _parseColor(String? hexString) {
    if (hexString == null) return null;
    hexString = hexString.replaceAll('#', '');
    if (hexString.length == 6) {
      hexString = 'FF$hexString';
    }
    try {
      return Color(int.parse(hexString, radix: 16));
    } catch (e) {
      return null;
    }
  }

  double _calculateDefiniteIntegral() {
    // Using Fundamental Theorem of Calculus: F(b) - F(a)
    return _antiderivative(_maxX) - _antiderivative(_minX);
  }

  @override
  Widget build(BuildContext context) {
    final definiteIntegral = _calculateDefiniteIntegral();

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _backgroundColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.data['title'] ?? 'Definite Integral Visualizer',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),
          const SizedBox(height: 8),
          if (widget.data['description'] != null)
            Text(
              widget.data['description'],
              style: TextStyle(
                fontSize: 14,
                color: _textColor.withOpacity(0.8),
              ),
            ),
          const SizedBox(height: 16),

          Text(
            'Function: f(x) = x²',
            style: TextStyle(color: _textColor, fontSize: 16, fontStyle: FontStyle.italic),
          ),
          Text(
            'Interval: [${_minX.toStringAsFixed(0)}, ${_maxX.toStringAsFixed(0)}]',
            style: TextStyle(color: _textColor, fontSize: 16, fontStyle: FontStyle.italic),
          ),
          const SizedBox(height: 16),

          Center(
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: _primaryColor),
              ),
              child: Text(
                'Definite Integral: ${definiteIntegral.toStringAsFixed(2)}',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: _primaryColor,
                ),
              ),
            ),
          ),
          const SizedBox(height: 16),

          Container(
            height: 250,
            child: CustomPaint(
              painter: DefiniteIntegralPainter(
                function: _function,
                minX: _minX,
                maxX: _maxX,
                primaryColor: _primaryColor,
                secondaryColor: _secondaryColor,
                textColor: _textColor,
              ),
              child: Container(),
            ),
          ),

          // Widget name tag
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'InteractiveDefiniteIntegralVisualizer',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }
}

class DefiniteIntegralPainter extends CustomPainter {
  final Function(double) function;
  final double minX;
  final double maxX;
  final Color primaryColor;
  final Color secondaryColor;
  final Color textColor;

  DefiniteIntegralPainter({
    required this.function,
    required this.minX,
    required this.maxX,
    required this.primaryColor,
    required this.secondaryColor,
    required this.textColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint();
    final textPainter = TextPainter(textDirection: TextDirection.ltr);
    final textStyle = TextStyle(color: textColor, fontSize: 10);

    // Define plot area padding
    const double padding = 30.0;
    final double plotWidth = size.width - 2 * padding;
    final double plotHeight = size.height - 2 * padding;

    // Determine y range for plotting
    final double minY = 0.0;
    final double maxY = function(maxX) * 1.2; // Max value of x^2 on [0,3] is 9, so ~11

    // Scale factors
    final double xScale = plotWidth / (maxX - minX);
    final double yScale = plotHeight / (maxY - minY);

    // Helper to convert plot coordinates to canvas coordinates
    Offset toCanvas(double x, double y) {
      final canvasX = padding + (x - minX) * xScale;
      final canvasY = size.height - padding - (y - minY) * yScale;
      return Offset(canvasX, canvasY);
    }

    // Draw axes
    paint.color = Colors.grey;
    paint.strokeWidth = 1.0;
    // X-axis
    canvas.drawLine(toCanvas(minX, 0), toCanvas(maxX, 0), paint);
    // Y-axis
    canvas.drawLine(toCanvas(0, minY), toCanvas(0, maxY), paint);

    // Draw function curve
    paint.color = primaryColor;
    paint.style = PaintingStyle.stroke;
    paint.strokeWidth = 2.0;

    final path = Path();
    bool firstPoint = true;
    for (double x = minX; x <= maxX; x += (maxX - minX) / 100) {
      final y = function(x);
      final point = toCanvas(x, y);
      if (firstPoint) {
        path.moveTo(point.dx, point.dy);
        firstPoint = false;
      } else {
        path.lineTo(point.dx, point.dy);
      }
    }
    canvas.drawPath(path, paint);

    // Draw shaded area
    paint.color = secondaryColor.withOpacity(0.4);
    paint.style = PaintingStyle.fill;
    final areaPath = Path()
      ..moveTo(toCanvas(minX, 0).dx, toCanvas(minX, 0).dy);
    for (double x = minX; x <= maxX; x += (maxX - minX) / 100) {
      areaPath.lineTo(toCanvas(x, function(x)).dx, toCanvas(x, function(x)).dy);
    }
    areaPath.lineTo(toCanvas(maxX, 0).dx, toCanvas(maxX, 0).dy);
    areaPath.close();
    canvas.drawPath(areaPath, paint);

    paint.color = secondaryColor;
    paint.style = PaintingStyle.stroke;
    paint.strokeWidth = 1.0;
    canvas.drawPath(areaPath, paint);

    // Draw labels for minX and maxX
    textPainter.text = TextSpan(text: minX.toStringAsFixed(0), style: textStyle);
    textPainter.layout();
    textPainter.paint(canvas, Offset(toCanvas(minX, 0).dx - textPainter.width / 2, size.height - padding + 5));

    textPainter.text = TextSpan(text: maxX.toStringAsFixed(0), style: textStyle);
    textPainter.layout();
    textPainter.paint(canvas, Offset(toCanvas(maxX, 0).dx - textPainter.width / 2, size.height - padding + 5));
  }

  @override
  bool shouldRepaint(covariant DefiniteIntegralPainter oldDelegate) {
    return oldDelegate.minX != minX ||
           oldDelegate.maxX != maxX ||
           oldDelegate.primaryColor != primaryColor ||
           oldDelegate.secondaryColor != secondaryColor ||
           oldDelegate.textColor != textColor;
  }
}
