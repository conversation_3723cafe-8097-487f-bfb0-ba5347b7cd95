import 'package:flutter/material.dart';
import 'package:flutter/services.dart'; // For HapticFeedback
import '../../../../models/course_models.dart';

class ButtonElementWidget extends StatelessWidget {
  final ButtonElement buttonElement;
  final PageController? pageController;
  final VoidCallback? onNextLesson;
  final VoidCallback? onModuleComplete;
  final bool
  isLastSlideInLesson; // To help decide action if pageController is null

  const ButtonElementWidget({
    super.key,
    required this.buttonElement,
    this.pageController,
    this.onNextLesson,
    this.onModuleComplete,
    this.isLastSlideInLesson = false,
  });

  @override
  Widget build(BuildContext context) {
    VoidCallback? onPressedAction;
    String buttonText = buttonElement.text;
    Color buttonColor = Theme.of(context).primaryColor;
    IconData buttonIcon = Icons.arrow_forward;

    switch (buttonElement.action) {
      case 'next_screen':
        onPressedAction = () {
          if (pageController != null && pageController!.hasClients) {
            // Check if there is a next page
            if ((pageController!.page?.round() ?? 0) <
                (pageController!.positions.first.maxScrollExtent /
                        MediaQuery.of(context).size.width)
                    .floor()) {
              pageController!.nextPage(
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeInOut,
              );
            } else {
              // It's the last slide, try to trigger next lesson if available
              onNextLesson?.call();
            }
          } else if (isLastSlideInLesson) {
            // If no pageController but it's the last slide conceptually, try next lesson
            onNextLesson?.call();
          }
        };
        break;
      case 'next_lesson':
        onPressedAction = onNextLesson;
        buttonIcon = Icons.skip_next;
        break;
      case 'module_complete':
        onPressedAction = onModuleComplete;
        buttonColor = Colors.green;
        buttonIcon = Icons.check_circle_outline;
        break;
      default:
        // For custom actions, or if no specific action matches
        onPressedAction = () {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                "Button: '${buttonElement.text}' pressed. Action: ${buttonElement.action} (Custom)",
              ),
            ),
          );
        };
        buttonIcon = Icons.smart_button; // A generic icon
    }

    return Padding(
      // Reduced horizontal padding to make the button itself feel wider if it's constrained by parent
      padding: const EdgeInsets.symmetric(vertical: 20.0, horizontal: 8.0),
      child: SizedBox(
        width: double.infinity,
        height: 60, // Slightly increased height
        child: ElevatedButton.icon(
          icon: Icon(buttonIcon, size: 24), // Slightly larger icon
          label: Text(
            buttonText,
            style: const TextStyle(
              fontSize: 18, // Font size is good
              fontWeight: FontWeight.w600,
              fontFamily: 'WorkSans',
            ),
            textAlign: TextAlign.center,
            overflow: TextOverflow.ellipsis, // Handle long text
          ),
          onPressed:
              onPressedAction != null
                  ? () {
                    HapticFeedback.mediumImpact();
                    onPressedAction!();
                  }
                  : null,
          style: ElevatedButton.styleFrom(
            backgroundColor: buttonColor,
            foregroundColor: Colors.white,
            elevation: 4, // Slightly more elevation
            shadowColor: buttonColor.withAlpha(120), // Slightly darker shadow
            padding: const EdgeInsets.symmetric(
              horizontal: 20,
              vertical: 16,
            ), // Increased internal padding
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16.0), // Softer radius
            ),
          ),
        ),
      ),
    );
  }
}
