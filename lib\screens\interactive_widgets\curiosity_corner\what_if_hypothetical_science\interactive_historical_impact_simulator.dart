import 'package:flutter/material.dart';
import 'package:resonance/widgets/interactive_widget_scaffold.dart';

class InteractiveHistoricalImpactSimulator extends StatefulWidget {
  const InteractiveHistoricalImpactSimulator({super.key});

  @override
  State<InteractiveHistoricalImpactSimulator> createState() => _InteractiveHistoricalImpactSimulatorState();
}

class _InteractiveHistoricalImpactSimulatorState extends State<InteractiveHistoricalImpactSimulator> {
  String _historicalEvent = 'World War II';
  String _timeTravelIntervention = 'Prevented assassination of <PERSON><PERSON><PERSON>';
  String _simulatedOutcome = 'No change (paradox-free)';

  final TextEditingController _eventController = TextEditingController(text: 'World War II');
  final TextEditingController _interventionController = TextEditingController(text: 'Prevented assassination of <PERSON><PERSON><PERSON>');

  @override
  void initState() {
    super.initState();
    _eventController.addListener(_updateEvent);
    _interventionController.addListener(_updateIntervention);
  }

  @override
  void dispose() {
    _eventController.removeListener(_updateEvent);
    _interventionController.removeListener(_updateIntervention);
    _eventController.dispose();
    _interventionController.dispose();
    super.dispose();
  }

  void _updateEvent() {
    setState(() {
      _historicalEvent = _eventController.text;
      _simulateImpact();
    });
  }

  void _updateIntervention() {
    setState(() {
      _timeTravelIntervention = _interventionController.text;
      _simulateImpact();
    });
  }

  void _simulateImpact() {
    // Simplified logic for paradox-free time travel
    // In a paradox-free model, interventions either cause a new timeline branch
    // or are inherently impossible/self-correcting.
    setState(() {
      if (_timeTravelIntervention.toLowerCase().contains('prevented assassination')) {
        _simulatedOutcome = 'Timeline branches: World War I never happens in this branch, but a similar conflict may arise later.';
      } else if (_timeTravelIntervention.toLowerCase().contains('warn hitler')) {
        _simulatedOutcome = 'Timeline branches: Hitler avoids early pitfalls, leading to a different, potentially more devastating, conflict.';
      } else if (_timeTravelIntervention.toLowerCase().contains('give future tech')) {
        _simulatedOutcome = 'Timeline branches: Rapid technological advancement, but unforeseen societal consequences and new global powers emerge.';
      }
      else {
        _simulatedOutcome = 'No direct change to original timeline; a new, parallel timeline is created where this intervention occurs.';
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return InteractiveWidgetScaffold(
      title: 'Historical Impact Simulator',
      description: 'Simulate the impact of time travel on historical events.',
      interactiveContent: Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              TextField(
                controller: _eventController,
                decoration: const InputDecoration(
                  labelText: 'Historical Event',
                  border: OutlineInputBorder(),
                ),
                onChanged: (text) {
                  _historicalEvent = text;
                  _simulateImpact();
                },
              ),
              const SizedBox(height: 20),
              TextField(
                controller: _interventionController,
                decoration: const InputDecoration(
                  labelText: 'Time Travel Intervention',
                  border: OutlineInputBorder(),
                  hintText: 'e.g., "Prevented assassination of Archduke Ferdinand"',
                ),
                onChanged: (text) {
                  _timeTravelIntervention = text;
                  _simulateImpact();
                },
              ),
              const SizedBox(height: 30),
              Text(
                'Simulated Outcome:',
                style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 10),
              Text(
                _simulatedOutcome,
                textAlign: TextAlign.center,
                style: const TextStyle(fontSize: 16, fontStyle: FontStyle.italic, color: Colors.blue),
              ),
              const Padding(
                padding: EdgeInsets.all(16.0),
                child: Text(
                  'Enter a historical event and a hypothetical time travel intervention. This simulator explores potential outcomes in a paradox-free time travel model, where interventions often lead to new, branching timelines rather than altering the original past.',
                  textAlign: TextAlign.center,
                  style: TextStyle(fontSize: 16),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
