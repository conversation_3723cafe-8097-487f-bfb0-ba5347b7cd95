import 'package:flutter/material.dart';

class InteractiveProblemSolvingFlowchart extends StatefulWidget {
  const InteractiveProblemSolvingFlowchart({super.key});

  @override
  State<InteractiveProblemSolvingFlowchart> createState() => _InteractiveProblemSolvingFlowchartState();
}

class _InteractiveProblemSolvingFlowchartState extends State<InteractiveProblemSolvingFlowchart> {
  List<FlowchartNode> _nodes = [];
  Offset _offset = Offset.zero;
  double _scale = 1.0;

  @override
  void initState() {
    super.initState();
    _nodes = [
      FlowchartNode(id: 'start', type: NodeType.start, text: 'Start', position: Offset(150, 50)),
      FlowchartNode(id: 'problem', type: NodeType.process, text: 'Define Problem', position: Offset(150, 150)),
      FlowchartNode(id: 'analyze', type: NodeType.process, text: 'Analyze Problem', position: Offset(150, 250)),
      FlowchartNode(id: 'solution', type: NodeType.process, text: 'Develop Solution', position: Offset(150, 350)),
      FlowchartNode(id: 'test', type: NodeType.process, text: 'Test Solution', position: Offset(150, 450)),
      FlowchartNode(id: 'end', type: NodeType.end, text: 'End', position: Offset(150, 550)),
    ];
  }

  void _onPanUpdate(DragUpdateDetails details) {
    setState(() {
      _offset += details.delta;
    });
  }

  void _onScaleUpdate(ScaleUpdateDetails details) {
    setState(() {
      _scale = (_scale * details.scale).clamp(0.5, 2.0);
      _offset += details.focalPoint - (details.focalPoint * (details.scale / details.previousScale));
    });
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Interactive Problem-Solving Flowchart',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            Expanded(
              child: GestureDetector(
                onPanUpdate: _onPanUpdate,
                onScaleUpdate: _onScaleUpdate,
                child: CustomPaint(
                  size: Size.infinite,
                  painter: _FlowchartPainter(_nodes, _offset, _scale),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

enum NodeType { start, end, process, decision }

class FlowchartNode {
  final String id;
  final NodeType type;
  String text;
  Offset position;

  FlowchartNode({
    required this.id,
    required this.type,
    required this.text,
    required this.position,
  });
}

class _FlowchartPainter extends CustomPainter {
  final List<FlowchartNode> nodes;
  final Offset offset;
  final double scale;

  _FlowchartPainter(this.nodes, this.offset, this.scale);

  @override
  void paint(Canvas canvas, Size size) {
    canvas.save();
    canvas.translate(offset.dx, offset.dy);
    canvas.scale(scale);

    final Paint nodePaint = Paint()..style = PaintingStyle.fill;
    final Paint borderPaint = Paint()
      ..color = Colors.black
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;
    final Paint arrowPaint = Paint()
      ..color = Colors.black
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    // Draw nodes
    for (var node in nodes) {
      Rect rect;
      RRect rRect;
      Path path;

      switch (node.type) {
        case NodeType.start:
        case NodeType.end:
          nodePaint.color = node.type == NodeType.start ? Colors.green.shade200 : Colors.red.shade200;
          rRect = RRect.fromRectAndRadius(
            Rect.fromCenter(center: node.position, width: 100, height: 50),
            Radius.circular(25),
          );
          canvas.drawRRect(rRect, nodePaint);
          canvas.drawRRect(rRect, borderPaint);
          break;
        case NodeType.process:
          nodePaint.color = Colors.blue.shade200;
          rect = Rect.fromCenter(center: node.position, width: 120, height: 60);
          canvas.drawRect(rect, nodePaint);
          canvas.drawRect(rect, borderPaint);
          break;
        case NodeType.decision:
          nodePaint.color = Colors.orange.shade200;
          path = Path();
          path.moveTo(node.position.dx, node.position.dy - 30);
          path.lineTo(node.position.dx + 60, node.position.dy);
          path.lineTo(node.position.dx, node.position.dy + 30);
          path.lineTo(node.position.dx - 60, node.position.dy);
          path.close();
          canvas.drawPath(path, nodePaint);
          canvas.drawPath(path, borderPaint);
          break;
      }

      // Draw text
      TextPainter(
        text: TextSpan(
          text: node.text,
          style: TextStyle(color: Colors.black, fontSize: 12 / scale),
        ),
        textAlign: TextAlign.center,
        textDirection: TextDirection.ltr,
      )..layout(maxWidth: 100)..paint(canvas, node.position - Offset(50, 10));
    }

    // Draw arrows (simplified for sequential flow)
    for (int i = 0; i < nodes.length - 1; i++) {
      final p1 = nodes[i].position;
      final p2 = nodes[i + 1].position;

      // Adjust start/end points to be on the edge of the node shapes
      Offset startPoint;
      Offset endPoint;

      // For vertical flow, connect bottom of current node to top of next node
      if (nodes[i].type == NodeType.start || nodes[i].type == NodeType.end) {
        startPoint = Offset(p1.dx, p1.dy + 25);
      } else if (nodes[i].type == NodeType.process) {
        startPoint = Offset(p1.dx, p1.dy + 30);
      } else { // NodeType.decision
        startPoint = Offset(p1.dx, p1.dy + 30);
      }

      if (nodes[i+1].type == NodeType.start || nodes[i+1].type == NodeType.end) {
        endPoint = Offset(p2.dx, p2.dy - 25);
      } else if (nodes[i+1].type == NodeType.process) {
        endPoint = Offset(p2.dx, p2.dy - 30);
      } else { // NodeType.decision
        endPoint = Offset(p2.dx, p2.dy - 30);
      }

      canvas.drawLine(startPoint, endPoint, arrowPaint);

      // Draw arrowhead
      final double arrowSize = 8;
      final double angle = atan2(endPoint.dy - startPoint.dy, endPoint.dx - startPoint.dx);
      Path arrowPath = Path();
      arrowPath.moveTo(endPoint.dx - arrowSize * cos(angle - pi / 6), endPoint.dy - arrowSize * sin(angle - pi / 6));
      arrowPath.lineTo(endPoint.dx, endPoint.dy);
      arrowPath.lineTo(endPoint.dx - arrowSize * cos(angle + pi / 6), endPoint.dy - arrowSize * sin(angle + pi / 6));
      arrowPath.close();
      canvas.drawPath(arrowPath, arrowPaint);
    }

    canvas.restore();
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    _FlowchartPainter oldPainter = oldDelegate as _FlowchartPainter;
    return oldPainter.offset != offset || oldPainter.scale != scale || oldPainter.nodes.length != nodes.length;
  }
}
