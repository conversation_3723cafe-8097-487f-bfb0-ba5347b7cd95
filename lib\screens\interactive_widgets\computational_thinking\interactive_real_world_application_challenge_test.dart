import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_markdown/flutter_markdown.dart';

class InteractiveRealWorldApplicationChallengeTest extends ConsumerStatefulWidget {
  const InteractiveRealWorldApplicationChallengeTest({super.key});

  @override
  ConsumerState<InteractiveRealWorldApplicationChallengeTest> createState() => _InteractiveRealWorldApplicationChallengeTestState();
}

class _InteractiveRealWorldApplicationChallengeTestState extends ConsumerState<InteractiveRealWorldApplicationChallengeTest> {
  final List<Map<String, dynamic>> _questions = [
    {
      'question': 'A city wants to reduce traffic congestion. Which computational thinking concept is most relevant when breaking down this large problem into smaller, manageable parts?',
      'options': ['Abstraction', 'Decomposition', 'Pattern Recognition', 'Algorithm Design'],
      'correctAnswer': 'Decomposition',
      'userAnswer': '',
      'isCorrect': false,
    },
    {
      'question': 'When building a computational model for weather prediction, simplifying the complex atmospheric interactions into mathematical equations is an example of:',
      'options': ['Decomposition', 'Pattern Recognition', 'Abstraction', 'Algorithm Design'],
      'correctAnswer': 'Abstraction',
      'userAnswer': '',
      'isCorrect': false,
    },
    {
      'question': 'A company uses customer purchase history to recommend new products. This process primarily relies on:',
      'options': ['Computational Ethics', 'Systems Thinking', 'Data-Driven Decision Making', 'Real-World Problem Solving'],
      'correctAnswer': 'Data-Driven Decision Making',
      'userAnswer': '',
      'isCorrect': false,
    },
    {
      'question': 'Analyzing how changes in one part of a global supply chain affect other parts (e.g., a delay in manufacturing impacting delivery times) is an application of:',
      'options': ['Computational Ethics', 'Systems Thinking', 'Data-Driven Decision Making', 'Algorithm Design'],
      'correctAnswer': 'Systems Thinking',
      'userAnswer': '',
      'isCorrect': false,
    },
    {
      'question': 'Designing an AI system for medical diagnosis requires careful consideration of fairness, bias, and accountability. This falls under the domain of:',
      'options': ['Data-Driven Decision Making', 'Systems Thinking', 'Computational Ethics', 'Problem Decomposition'],
      'correctAnswer': 'Computational Ethics',
      'userAnswer': '',
      'isCorrect': false,
    },
  ];

  int _currentQuestionIndex = 0;
  bool _showResults = false;

  void _checkAnswer(String selectedOption) {
    setState(() {
      _questions[_currentQuestionIndex]['userAnswer'] = selectedOption;
      _questions[_currentQuestionIndex]['isCorrect'] =
          (_questions[_currentQuestionIndex]['correctAnswer'] == selectedOption);
    });
  }

  void _nextQuestion() {
    setState(() {
      if (_currentQuestionIndex < _questions.length - 1) {
        _currentQuestionIndex++;
      } else {
        _showResults = true;
      }
    });
  }

  void _resetQuiz() {
    setState(() {
      for (var q in _questions) {
        q['userAnswer'] = '';
        q['isCorrect'] = false;
      }
      _currentQuestionIndex = 0;
      _showResults = false;
    });
  }

  int _getScore() {
    return _questions.where((q) => q['isCorrect'] == true).length;
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Interactive Real-World Application Challenge',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 20),
          const MarkdownBody(
            data: '''
            Test your understanding of how computational thinking concepts apply to real-world scenarios.
            Answer the questions to see how well you can identify and apply these principles.
            ''',
          ),
          const SizedBox(height: 20),
          if (!_showResults) _buildQuestionCard(),
          if (_showResults) _buildResultsCard(),
          const SizedBox(height: 20),
          if (!_showResults)
            Align(
              alignment: Alignment.centerRight,
              child: ElevatedButton(
                onPressed: _nextQuestion,
                child: Text(_currentQuestionIndex == _questions.length - 1 ? 'Submit Quiz' : 'Next Question'),
              ),
            ),
          if (_showResults)
            Align(
              alignment: Alignment.center,
              child: ElevatedButton(
                onPressed: _resetQuiz,
                child: const Text('Retake Quiz'),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildQuestionCard() {
    final questionData = _questions[_currentQuestionIndex];
    return Card(
      elevation: 4,
      margin: const EdgeInsets.symmetric(vertical: 10),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Question ${_currentQuestionIndex + 1}/${_questions.length}',
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600, color: Colors.blueGrey),
            ),
            const SizedBox(height: 10),
            Text(
              questionData['question'],
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 20),
            ..._buildOptions(questionData),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildOptions(Map<String, dynamic> questionData) {
    List<Widget> optionWidgets = [];
    for (String option in questionData['options']) {
      optionWidgets.add(
        RadioListTile<String>(
          title: Text(option),
          value: option,
          groupValue: questionData['userAnswer'],
          onChanged: (value) {
            if (value != null) {
              _checkAnswer(value);
            }
          },
        ),
      );
    }
    return optionWidgets;
  }

  Widget _buildResultsCard() {
    return Card(
      elevation: 4,
      margin: const EdgeInsets.symmetric(vertical: 10),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Quiz Results',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold, color: Colors.teal),
            ),
            const Divider(),
            Text(
              'You scored: ${_getScore()} out of ${_questions.length}',
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 20),
            ..._questions.map((q) => _buildResultItem(q)).toList(),
          ],
        ),
      ),
    );
  }

  Widget _buildResultItem(Map<String, dynamic> questionData) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Q: ${questionData['question']}',
            style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
          Text(
            'Your Answer: ${questionData['userAnswer'].isNotEmpty ? questionData['userAnswer'] : 'No answer'}',
            style: TextStyle(
              fontSize: 15,
              color: questionData['isCorrect'] ? Colors.green : Colors.red,
            ),
          ),
          if (!questionData['isCorrect'])
            Text(
              'Correct Answer: ${questionData['correctAnswer']}',
              style: const TextStyle(fontSize: 15, color: Colors.green),
            ),
          const SizedBox(height: 5),
        ],
      ),
    );
  }
}
