import 'package:flutter/material.dart';

class InteractiveComponentExplorerRefrigerator extends StatefulWidget {
  const InteractiveComponentExplorerRefrigerator({super.key});

  @override
  InteractiveComponentExplorerRefrigeratorState createState() => InteractiveComponentExplorerRefrigeratorState();
}

class InteractiveComponentExplorerRefrigeratorState extends State<InteractiveComponentExplorerRefrigerator> {
  String _selectedComponent = 'Compressor';
  final Map<String, String> _componentDescriptions = {
    'Compressor': 'The compressor is the pump that circulates the refrigerant through the system. It increases the pressure and temperature of the refrigerant vapor.',
    'Condenser Coils': 'These coils are usually located on the back or bottom of the refrigerator. Hot, high-pressure refrigerant vapor flows through them, releasing heat to the surrounding air and condensing into a liquid.',
    'Evaporator Coils': 'Located inside the freezer compartment, these coils absorb heat from the refrigerator\'s interior. The liquid refrigerant expands and evaporates here, becoming a cold gas and cooling the air inside.',
    'Expansion Valve': 'This valve regulates the flow of liquid refrigerant into the evaporator coils. It causes a pressure drop, allowing the refrigerant to expand and cool rapidly.',
    'Thermostat': 'The thermostat monitors the temperature inside the refrigerator and signals the compressor to turn on or off to maintain the desired temperature.',
  };

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Refrigerator Component Explorer',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 20),
            DropdownButton<String>(
              value: _selectedComponent,
              onChanged: (String? newValue) {
                setState(() {
                  _selectedComponent = newValue!;
                });
              },
              items: _componentDescriptions.keys.map<DropdownMenuItem<String>>((String value) {
                return DropdownMenuItem<String>(
                  value: value,
                  child: Text(value),
                );
              }).toList(),
            ),
            const SizedBox(height: 20),
            Container(
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                _componentDescriptions[_selectedComponent]!,
                textAlign: TextAlign.center,
                style: const TextStyle(fontSize: 16),
              ),
            ),
            const SizedBox(height: 20),
            // Placeholder for a visual representation of the refrigerator components
            Container(
              width: 250,
              height: 250,
              color: Colors.blueGrey[50],
              child: Center(
                child: Text(
                  'Visual of $_selectedComponent Here',
                  textAlign: TextAlign.center,
                  style: TextStyle(color: Colors.blueGrey[400]),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
