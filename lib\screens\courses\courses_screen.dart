import 'package:flutter/material.dart';
import '../../models/course_models.dart';
import '../../services/service_provider.dart';
import './roadmap/index.dart'; // Corrected import path for RoadmapScreen

class CoursesScreen extends StatefulWidget {
  const CoursesScreen({super.key});

  @override
  State<CoursesScreen> createState() => _CoursesScreenState();
}

class _CoursesScreenState extends State<CoursesScreen> {
  String _selectedCategoryId = '';
  final ScrollController _scrollController = ScrollController();
  bool _isScrolled = false;

  // Track which category section is currently visible
  String _visibleCategoryId = '';

  // Map to store section positions for scrolling
  final Map<String, GlobalKey> _sectionKeys = {};

  // Helper method to get category icon
  IconData _getCategoryIcon(String categoryId) {
    switch (categoryId.toLowerCase()) {
      case 'maths':
        return Icons.calculate;
      case 'science':
        return Icons.science;
      case 'computer_science':
        return Icons.computer;
      case 'reasoning':
        return Icons.psychology;
      case 'technology':
        return Icons.devices;
      case 'puzzles':
        return Icons.extension;
      case 'curiosity_corner':
        return Icons.lightbulb;
      case 'coming_soon':
        return Icons.hourglass_empty;
      default:
        return Icons.school;
    }
  }

  @override
  void initState() {
    super.initState();

    // Add scroll listener to update header
    _scrollController.addListener(_onScroll);

    // Initialize with first category selected
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        final courseService = ServiceProvider.of(context).courseService;
        final categories = courseService.categories;
        if (categories.isNotEmpty) {
          setState(() {
            _selectedCategoryId = categories.first.id;
            _visibleCategoryId = categories.first.id;
          });
        }
      }
    });
  }

  void _onScroll() {
    // Update header visibility based on scroll position
    setState(() {
      _isScrolled = _scrollController.offset > 100;
    });

    // Update visible category based on scroll position
    _updateVisibleCategory();
  }

  void _updateVisibleCategory() {
    // Find which section is most visible
    for (final entry in _sectionKeys.entries) {
      final key = entry.value;
      if (key.currentContext != null) {
        final RenderBox box =
            key.currentContext!.findRenderObject() as RenderBox;
        final position = box.localToGlobal(Offset.zero);

        // If the section is visible on screen
        if (position.dy < MediaQuery.of(context).size.height / 2 &&
            position.dy > 0) {
          if (_visibleCategoryId != entry.key) {
            setState(() {
              _visibleCategoryId = entry.key;
              _selectedCategoryId = entry.key;
            });
          }
          break;
        }
      }
    }
  }

  void _scrollToCategory(String categoryId) {
    // Update selected category immediately for visual feedback
    setState(() {
      _selectedCategoryId = categoryId;
    });

    // Use a small delay to ensure the UI has updated before scrolling
    Future.delayed(const Duration(milliseconds: 50), () {
      if (!mounted) return;

      final key = _sectionKeys[categoryId];
      if (key != null && key.currentContext != null) {
        // Get the RenderBox to calculate proper alignment
        final RenderBox box =
            key.currentContext!.findRenderObject() as RenderBox;
        final position = box.localToGlobal(Offset.zero);

        // Calculate the offset to account for the header
        final headerHeight =
            120.0; // Approximate height of the category selector

        // Scroll to the position with alignment
        _scrollController.animateTo(
          _scrollController.offset + position.dy - headerHeight,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      } else {
        // If the context is not available, try to find the category index and scroll approximately
        if (!mounted) return;

        final courseService = ServiceProvider.of(context).courseService;
        final categories = courseService.categories;
        final index = categories.indexWhere((c) => c.id == categoryId);

        if (index >= 0) {
          // Approximate scroll position based on category index
          final approximatePosition =
              index * 300.0; // Rough estimate of category section height
          _scrollController.animateTo(
            approximatePosition,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
          );
        }
      }
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final courseService = ServiceProvider.of(context).courseService;
    final categories = courseService.categories;
    final user = courseService.currentUser;

    // Initialize section keys for each category
    for (final category in categories) {
      if (!_sectionKeys.containsKey(category.id)) {
        _sectionKeys[category.id] = GlobalKey();
      }
    }

    // Group courses by category
    final Map<String, List<Course>> coursesByCategory = {};
    for (final category in categories) {
      coursesByCategory[category.id] = courseService.getCoursesByCategory(
        category.id,
      );

      // Sort courses by difficulty
      coursesByCategory[category.id]!.sort((a, b) {
        final difficultyOrder = {
          'Beginner': 0,
          'Intermediate': 1,
          'Advanced': 2,
          'Expert': 3,
        };

        final aOrder = difficultyOrder[a.difficulty] ?? 0;
        final bOrder = difficultyOrder[b.difficulty] ?? 0;

        return aOrder.compareTo(bOrder);
      });
    }

    return Scaffold(
      backgroundColor: Colors.grey[100],
      body: SafeArea(
        child: Column(
          children: [
            // Sticky header with category tabs
            Container(
              color: Colors.white,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Title only shows when scrolled
                  // Removed the duplicated title that was showing when scrolled

                  // Category selector
                  Container(
                    padding: const EdgeInsets.fromLTRB(16, 20, 16, 20),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      boxShadow:
                          _isScrolled
                              ? [
                                BoxShadow(
                                  color: Colors.black.withAlpha(15),
                                  blurRadius: 6,
                                  offset: const Offset(0, 3),
                                ),
                              ]
                              : null,
                    ),
                    child: SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      physics: const BouncingScrollPhysics(),
                      padding: const EdgeInsets.symmetric(horizontal: 4),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children:
                            categories.map((category) {
                              final isSelected =
                                  _selectedCategoryId == category.id;
                              return _buildCategoryCard(category, isSelected);
                            }).toList(),
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Course list
            Expanded(
              child: Container(
                color: Colors.grey[100],
                child: ListView(
                  controller: _scrollController,
                  padding: EdgeInsets.zero,
                  physics: const BouncingScrollPhysics(),
                  children: [
                    // Build sections for each category
                    ...categories.map((category) {
                      final categoryCourses =
                          coursesByCategory[category.id] ?? [];
                      if (categoryCourses.isEmpty) {
                        // Even for empty categories, create a minimal section to scroll to
                        return Container(
                          key: _sectionKeys[category.id],
                          height: 100,
                          width: double.infinity,
                          padding: const EdgeInsets.all(24),
                          margin: const EdgeInsets.only(bottom: 16),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withAlpha(10),
                                blurRadius: 4,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Text(
                            "${category.name} - No courses available yet",
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.w500,
                              fontFamily: 'WorkSans',
                            ),
                          ),
                        );
                      }

                      return _buildCategorySection(
                        category,
                        categoryCourses,
                        user,
                        _sectionKeys[category.id]!,
                      );
                    }),

                    // Bottom padding
                    const SizedBox(height: 80),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryCard(CourseCategory category, bool isSelected) {
    final categoryColor = _getCategoryColor(category.id);
    final shortName = _getCategoryShortName(category.id);

    return GestureDetector(
      onTap: () => _scrollToCategory(category.id),
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 8),
        width: 90,
        child: Column(
          children: [
            // Category icon with background
            AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: isSelected ? categoryColor.withAlpha(26) : Colors.white,
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color:
                      isSelected
                          ? categoryColor.withAlpha(77)
                          : Colors.grey[300]!,
                  width: 1.5,
                ),
                boxShadow:
                    isSelected
                        ? [
                          BoxShadow(
                            color: categoryColor.withAlpha(40),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ]
                        : null,
              ),
              child: Center(
                child: Icon(
                  _getCategoryIcon(category.id),
                  size: 40,
                  color: categoryColor,
                ),
              ),
            ),

            const SizedBox(height: 10),

            // Category name
            Text(
              shortName,
              style: TextStyle(
                color: isSelected ? categoryColor : Colors.grey[700],
                fontSize: 15,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                fontFamily: 'WorkSans',
              ),
              textAlign: TextAlign.center,
            ),

            // Selected indicator
            if (isSelected)
              Container(
                margin: const EdgeInsets.only(top: 6),
                width: 30,
                height: 4,
                decoration: BoxDecoration(
                  color: categoryColor,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategorySection(
    CourseCategory category,
    List<Course> courses,
    User? user,
    GlobalKey sectionKey,
  ) {
    final categoryColor = _getCategoryColor(category.id);

    // Find if any course in this category is in progress
    bool hasCourseInProgress = false;
    for (final course in courses) {
      final isEnrolled = user?.enrolledCourseIds.contains(course.id) ?? false;
      final progress =
          isEnrolled
              ? user?.courseProgress[course.id]?.progressPercentage ?? 0
              : 0.0;
      if (isEnrolled && progress > 0 && progress < 100) {
        hasCourseInProgress = true;
        break;
      }
    }

    return Column(
      key: sectionKey,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Category header with background
        Container(
          width: double.infinity,
          padding: const EdgeInsets.fromLTRB(24, 30, 24, 30),
          decoration: BoxDecoration(
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(10),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // Left side: Title and badge
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Category title
                    Text(
                      category.name,
                      style: const TextStyle(
                        color: Colors.black,
                        fontSize: 32,
                        fontWeight: FontWeight.bold,
                        fontFamily: 'WorkSans',
                      ),
                    ),

                    // In Progress badge (if applicable)
                    if (hasCourseInProgress) ...[
                      const SizedBox(height: 16),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.green[50],
                          borderRadius: BorderRadius.circular(20),
                          border: Border.all(
                            color: Colors.green[100]!,
                            width: 1,
                          ),
                        ),
                        child: Text(
                          'IN PROGRESS',
                          style: TextStyle(
                            color: Colors.green[700],
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            fontFamily: 'WorkSans',
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ),

              // Right side: Category icon
              Container(
                width: 100,
                height: 100,
                decoration: BoxDecoration(
                  color: categoryColor.withAlpha(26),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: categoryColor.withAlpha(50),
                    width: 1.5,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: categoryColor.withAlpha(30),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Center(
                  child: Icon(
                    _getCategoryIcon(category.id),
                    size: 50,
                    color: categoryColor,
                  ),
                ),
              ),
            ],
          ),
        ),

        // Course list for this category
        ...courses.asMap().entries.map((entry) {
          final courseIndex = entry.key;
          final course = entry.value;

          final isEnrolled =
              user?.enrolledCourseIds.contains(course.id) ?? false;
          final progress =
              isEnrolled
                  ? user?.courseProgress[course.id]?.progressPercentage ?? 0
                  : 0.0;

          // Check if this course has prerequisites
          final hasPrerequisites = course.prerequisites?.isNotEmpty ?? false;

          // Check if all prerequisites are completed
          bool prerequisitesMet = true;
          if (hasPrerequisites) {
            for (final prereqId in course.prerequisites!) {
              // Added null assertion as hasPrerequisites is true
              final prereqCompleted =
                  user?.courseProgress[prereqId]?.isCompleted ?? false;
              if (!prereqCompleted) {
                prerequisitesMet = false;
                break;
              }
            }
          }

          // Show connection line to previous course
          final showConnector =
              courseIndex > 0 &&
              (course.prerequisites?.contains(courses[courseIndex - 1].id) ??
                  false);

          return Column(
            children: [
              // Connection line to previous course
              if (showConnector)
                Container(
                  width: 3,
                  height: 40,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [Colors.grey[300]!, categoryColor.withAlpha(100)],
                    ),
                  ),
                  margin: const EdgeInsets.only(bottom: 4),
                ),

              // Course card
              Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 10,
                ),
                child: _buildCourseCard(
                  course,
                  isEnrolled,
                  progress,
                  hasPrerequisites && !prerequisitesMet,
                ),
              ),

              // Connection line to next course (if not the last course)
              if (courseIndex < courses.length - 1 &&
                  (courses[courseIndex + 1].prerequisites?.contains(
                        course.id,
                      ) ??
                      false))
                Container(
                  width: 3,
                  height: 40,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [categoryColor.withAlpha(100), Colors.grey[300]!],
                    ),
                  ),
                  margin: const EdgeInsets.only(top: 4),
                ),
            ],
          );
        }),

        // Bottom padding for this section
        const SizedBox(height: 24),
      ],
    );
  }

  Widget _buildCourseCard(
    Course course,
    bool isEnrolled,
    double progress,
    bool isLocked,
  ) {
    final categoryColor = _getCategoryColor(course.categoryId);

    // Determine if course is new (for demo purposes, consider courses with ID containing "new")
    final isNew = course.id.contains('new');

    // Determine if course is in progress (used in the category section)

    return GestureDetector(
      onTap: () {
        if (isLocked) {
          // Show message that prerequisites are not met
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Complete the prerequisite courses first'),
              duration: Duration(seconds: 2),
            ),
          );
        } else {
          // Navigate to course detail
          Navigator.push(
            context,
            MaterialPageRoute(
              builder:
                  (context) => RoadmapScreen(
                    courseId: course.id,
                  ), // Navigate to RoadmapScreen
            ),
          );
        }
      },
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(16),
              blurRadius: 10,
              offset: const Offset(0, 3),
            ),
          ],
          border: Border.all(color: Colors.grey[200]!, width: 1),
        ),
        child: Stack(
          children: [
            // Main content
            Padding(
              padding: const EdgeInsets.all(20),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // Course icon
                  Container(
                    width: 70,
                    height: 70,
                    decoration: BoxDecoration(
                      color: categoryColor.withAlpha(26),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: categoryColor.withAlpha(40),
                        width: 1,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: categoryColor.withAlpha(20),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Center(
                      child: Icon(
                        _getCategoryIcon(course.categoryId),
                        size: 36,
                        color: categoryColor,
                      ),
                    ),
                  ),

                  const SizedBox(width: 20),

                  // Course info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Title
                        Text(
                          course.title,
                          style: const TextStyle(
                            color: Colors.black,
                            fontSize: 20,
                            fontWeight: FontWeight.w600,
                            fontFamily: 'WorkSans',
                          ),
                        ),

                        const SizedBox(height: 6),

                        // Lesson count
                        Text(
                          '${course.modules.fold(0, (sum, module) => sum + module.lessons.length)} ${course.modules.fold(0, (sum, module) => sum + module.lessons.length) == 1 ? 'Lesson' : 'Lessons'}',
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 14,
                            fontFamily: 'WorkSans',
                          ),
                        ),

                        // Progress bar
                        if (isEnrolled && progress > 0) ...[
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              Expanded(
                                child: LinearProgressIndicator(
                                  value: progress / 100,
                                  backgroundColor: Colors.grey[200],
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    Colors.green,
                                  ),
                                  minHeight: 8,
                                  borderRadius: BorderRadius.circular(4),
                                ),
                              ),
                              const SizedBox(width: 12),
                              Text(
                                '${progress.toInt()}%',
                                style: TextStyle(
                                  color: Colors.green[700],
                                  fontSize: 14,
                                  fontWeight: FontWeight.w600,
                                  fontFamily: 'WorkSans',
                                ),
                              ),
                            ],
                          ),
                        ],
                      ],
                    ),
                  ),
                ],
              ),
            ),

            // NEW badge
            if (isNew)
              Positioned(
                top: 16,
                right: 16,
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.green,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.green.withAlpha(70),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: const Text(
                    'NEW',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      fontFamily: 'WorkSans',
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}

// Get color for category
Color _getCategoryColor(String categoryId) {
  switch (categoryId.toLowerCase()) {
    case 'maths':
      return Color(0xFF4285F4); // Blue
    case 'science':
      return Color(0xFFFFB300); // Amber
    case 'computer_science':
      return Color(0xFF9C27B0); // Purple
    case 'reasoning':
      return Color(0xFFFF5722); // Deep Orange
    case 'technology':
      return Color(0xFF00BCD4); // Cyan
    case 'puzzles':
      return Color(0xFF8BC34A); // Light Green
    case 'curiosity_corner':
      return Color(0xFFFF9800); // Orange
    case 'coming_soon':
      return Color(0xFF9E9E9E); // Grey
    // Legacy categories
    case 'math':
      return Color(0xFF4285F4); // Blue
    case 'cs':
      return Color(0xFF9C27B0); // Purple
    case 'data':
      return Color(0xFFFF5722); // Orange
    default:
      return Color(0xFF4285F4); // Default blue
  }
}

// Get short name for category
String _getCategoryShortName(String categoryId) {
  switch (categoryId.toLowerCase()) {
    case 'maths':
      return 'Maths';
    case 'science':
      return 'Science';
    case 'computer_science':
      return 'CS';
    case 'reasoning':
      return 'Reasoning';
    case 'technology':
      return 'Tech';
    case 'puzzles':
      return 'Puzzles';
    case 'curiosity_corner':
      return 'Curiosity';
    case 'coming_soon':
      return 'Coming Soon';
    // Legacy categories
    case 'math':
      return 'Math';
    case 'data':
      return 'Data';
    default:
      return 'Category';
  }
}
