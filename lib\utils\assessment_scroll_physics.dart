import 'package:flutter/material.dart';
import '../models/user_preferences.dart';

class AssessmentScrollPhysics extends ScrollPhysics {
  final UserPreferences userPreferences;
  final int currentPage;
  final Function(String) showMessage;

  const AssessmentScrollPhysics({
    ScrollPhysics? parent,
    required this.userPreferences,
    required this.currentPage,
    required this.showMessage,
  }) : super(parent: parent);

  @override
  AssessmentScrollPhysics applyTo(ScrollPhysics? ancestor) {
    return AssessmentScrollPhysics(
      parent: buildParent(ancestor),
      userPreferences: userPreferences,
      currentPage: currentPage,
      showMessage: showMessage,
    );
  }

  // We don't need to override allowImplicitScrolling

  @override
  double applyPhysicsToUserOffset(ScrollMetrics position, double offset) {
    // Allow scrolling backward (right to left) always
    if (offset < 0) {
      return offset;
    }

    // For forward scrolling (left to right), check if the current page has a selection
    if (offset > 0) {
      // First page is introduction, always allow moving forward
      if (currentPage == 1) {
        return offset;
      }

      // Check if the current page has a selection
      if (!userPreferences.hasSelectionForPage(currentPage)) {
        // If no selection, show a message and prevent scrolling
        showMessage('Please make a selection to continue');
        return 0.0;
      }
    }

    return offset;
  }
}
