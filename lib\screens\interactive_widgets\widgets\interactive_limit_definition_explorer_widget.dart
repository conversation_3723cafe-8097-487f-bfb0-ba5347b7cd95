import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that allows users to explore the epsilon-delta definition of a limit.
class InteractiveLimitDefinitionExplorerWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveLimitDefinitionExplorerWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  /// Factory constructor to create the widget from JSON data
  factory InteractiveLimitDefinitionExplorerWidget.fromData(Map<String, dynamic> data) {
    return InteractiveLimitDefinitionExplorerWidget(
      data: data,
    );
  }

  @override
  State<InteractiveLimitDefinitionExplorerWidget> createState() => _InteractiveLimitDefinitionExplorerWidgetState();
}

class _InteractiveLimitDefinitionExplorerWidgetState extends State<InteractiveLimitDefinitionExplorerWidget> {
  late double _limitPointA; // The x-value where the limit is taken
  late double _limitL; // The limit value L = f(a)
  late double _epsilon; // Epsilon (ε) value
  late double _delta; // Delta (δ) value

  // Function to visualize (e.g., f(x) = 2x + 1)
  double _function(double x) {
    // Can be made configurable via widget.data
    return 2 * x + 1;
  }

  // Colors
  late Color _primaryColor;
  late Color _secondaryColor;
  late Color _backgroundColor;
  late Color _textColor;
  late Color _epsilonColor;
  late Color _deltaColor;

  @override
  void initState() {
    super.initState();
    _limitPointA = widget.data['limit_point_a']?.toDouble() ?? 2.0;
    _limitL = _function(_limitPointA); // For f(x) = 2x + 1, L = 2*2 + 1 = 5
    _epsilon = widget.data['initial_epsilon']?.toDouble() ?? 0.5;
    _delta = widget.data['initial_delta']?.toDouble() ?? 0.2; // A guess for delta

    _primaryColor = _parseColor(widget.data['primary_color']) ?? Colors.blue;
    _secondaryColor = _parseColor(widget.data['secondary_color']) ?? Colors.orange;
    _backgroundColor = _parseColor(widget.data['background_color']) ?? Colors.white;
    _textColor = _parseColor(widget.data['text_color']) ?? Colors.black87;
    _epsilonColor = _parseColor(widget.data['epsilon_color']) ?? Colors.green;
    _deltaColor = _parseColor(widget.data['delta_color']) ?? Colors.red;
  }

  Color? _parseColor(String? hexString) {
    if (hexString == null) return null;
    hexString = hexString.replaceAll('#', '');
    if (hexString.length == 6) {
      hexString = 'FF$hexString';
    }
    try {
      return Color(int.parse(hexString, radix: 16));
    } catch (e) {
      return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _backgroundColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.data['title'] ?? 'Limit Definition Explorer (ε-δ)',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),
          const SizedBox(height: 8),
          if (widget.data['description'] != null)
            Text(
              widget.data['description'],
              style: TextStyle(
                fontSize: 14,
                color: _textColor.withOpacity(0.8),
              ),
            ),
          const SizedBox(height: 16),

          Text(
            'Function: f(x) = 2x + 1',
            style: TextStyle(color: _textColor, fontSize: 16, fontStyle: FontStyle.italic),
          ),
          Text(
            'Limit Point (a): ${_limitPointA.toStringAsFixed(1)}',
            style: TextStyle(color: _textColor, fontSize: 16, fontStyle: FontStyle.italic),
          ),
          Text(
            'Limit (L): ${_limitL.toStringAsFixed(1)}',
            style: TextStyle(color: _textColor, fontSize: 16, fontStyle: FontStyle.italic),
          ),
          const SizedBox(height: 16),

          Text(
            'Epsilon (ε): ${_epsilon.toStringAsFixed(2)}',
            style: TextStyle(color: _epsilonColor, fontSize: 16, fontWeight: FontWeight.bold),
          ),
          Slider(
            value: _epsilon,
            min: 0.01,
            max: 2.0,
            divisions: 199,
            label: _epsilon.toStringAsFixed(2),
            activeColor: _epsilonColor,
            onChanged: (value) {
              setState(() {
                _epsilon = value;
              });
            },
          ),
          const SizedBox(height: 8),

          Text(
            'Delta (δ): ${_delta.toStringAsFixed(2)}',
            style: TextStyle(color: _deltaColor, fontSize: 16, fontWeight: FontWeight.bold),
          ),
          Slider(
            value: _delta,
            min: 0.01,
            max: 2.0,
            divisions: 199,
            label: _delta.toStringAsFixed(2),
            activeColor: _deltaColor,
            onChanged: (value) {
              setState(() {
                _delta = value;
              });
            },
          ),
          const SizedBox(height: 16),

          Container(
            height: 250,
            child: CustomPaint(
              painter: EpsilonDeltaPainter(
                function: _function,
                limitPointA: _limitPointA,
                limitL: _limitL,
                epsilon: _epsilon,
                delta: _delta,
                primaryColor: _primaryColor,
                epsilonColor: _epsilonColor,
                deltaColor: _deltaColor,
                textColor: _textColor,
              ),
              child: Container(),
            ),
          ),

          // Widget name tag
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'InteractiveLimitDefinitionExplorer',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }
}

class EpsilonDeltaPainter extends CustomPainter {
  final Function(double) function;
  final double limitPointA;
  final double limitL;
  final double epsilon;
  final double delta;
  final Color primaryColor;
  final Color epsilonColor;
  final Color deltaColor;
  final Color textColor;

  EpsilonDeltaPainter({
    required this.function,
    required this.limitPointA,
    required this.limitL,
    required this.epsilon,
    required this.delta,
    required this.primaryColor,
    required this.epsilonColor,
    required this.deltaColor,
    required this.textColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint();
    final textPainter = TextPainter(textDirection: TextDirection.ltr);
    final textStyle = TextStyle(color: textColor, fontSize: 10);

    // Define plot area padding
    const double padding = 30.0;
    final double plotWidth = size.width - 2 * padding;
    final double plotHeight = size.height - 2 * padding;

    // Determine x and y ranges for plotting
    final double minPlotX = limitPointA - 5;
    final double maxPlotX = limitPointA + 5;
    final double minPlotY = limitL - 5;
    final double maxPlotY = limitL + 5;

    // Scale factors
    final double xScale = plotWidth / (maxPlotX - minPlotX);
    final double yScale = plotHeight / (maxPlotY - minPlotY);

    // Helper to convert plot coordinates to canvas coordinates
    Offset toCanvas(double x, double y) {
      final canvasX = padding + (x - minPlotX) * xScale;
      final canvasY = size.height - padding - (y - minPlotY) * yScale;
      return Offset(canvasX, canvasY);
    }

    // Draw axes
    paint.color = Colors.grey;
    paint.strokeWidth = 1.0;
    // X-axis
    canvas.drawLine(toCanvas(minPlotX, 0), toCanvas(maxPlotX, 0), paint);
    // Y-axis
    canvas.drawLine(toCanvas(0, minPlotY), toCanvas(0, maxPlotY), paint);

    // Draw function curve
    paint.color = primaryColor;
    paint.style = PaintingStyle.stroke;
    paint.strokeWidth = 2.0;

    final path = Path();
    bool firstPoint = true;
    for (double x = minPlotX; x <= maxPlotX; x += (maxPlotX - minPlotX) / 100) {
      final y = function(x);
      final point = toCanvas(x, y);
      if (firstPoint) {
        path.moveTo(point.dx, point.dy);
        firstPoint = false;
      } else {
        path.lineTo(point.dx, point.dy);
      }
    }
    canvas.drawPath(path, paint);

    // Draw limit point (a, L)
    final limitCanvasPoint = toCanvas(limitPointA, limitL);
    paint.color = primaryColor;
    paint.style = PaintingStyle.fill;
    canvas.drawCircle(limitCanvasPoint, 5, paint);

    // Draw epsilon interval on Y-axis
    paint.color = epsilonColor.withOpacity(0.5);
    paint.style = PaintingStyle.fill;
    canvas.drawRect(
      Rect.fromPoints(
        toCanvas(minPlotX, limitL - epsilon),
        toCanvas(maxPlotX, limitL + epsilon),
      ),
      paint,
    );
    paint.color = epsilonColor;
    paint.style = PaintingStyle.stroke;
    paint.strokeWidth = 1.5;
    canvas.drawLine(toCanvas(minPlotX, limitL + epsilon), toCanvas(maxPlotX, limitL + epsilon), paint);
    canvas.drawLine(toCanvas(minPlotX, limitL - epsilon), toCanvas(maxPlotX, limitL - epsilon), paint);

    // Draw delta interval on X-axis
    paint.color = deltaColor.withOpacity(0.5);
    paint.style = PaintingStyle.fill;
    canvas.drawRect(
      Rect.fromPoints(
        toCanvas(limitPointA - delta, minPlotY),
        toCanvas(limitPointA + delta, maxPlotY),
      ),
      paint,
    );
    paint.color = deltaColor;
    paint.style = PaintingStyle.stroke;
    paint.strokeWidth = 1.5;
    canvas.drawLine(toCanvas(limitPointA - delta, minPlotY), toCanvas(limitPointA - delta, maxPlotY), paint);
    canvas.drawLine(toCanvas(limitPointA + delta, minPlotY), toCanvas(limitPointA + delta, maxPlotY), paint);

    // Draw labels
    textPainter.text = TextSpan(text: 'L = ${limitL.toStringAsFixed(1)}', style: textStyle.copyWith(color: primaryColor));
    textPainter.layout();
    textPainter.paint(canvas, Offset(padding + 5, toCanvas(0, limitL).dy - textPainter.height / 2));

    textPainter.text = TextSpan(text: 'a = ${limitPointA.toStringAsFixed(1)}', style: textStyle.copyWith(color: primaryColor));
    textPainter.layout();
    textPainter.paint(canvas, Offset(toCanvas(limitPointA, 0).dx - textPainter.width / 2, size.height - padding + 5));

    textPainter.text = TextSpan(text: 'L+ε', style: textStyle.copyWith(color: epsilonColor));
    textPainter.layout();
    textPainter.paint(canvas, Offset(padding + 5, toCanvas(0, limitL + epsilon).dy - textPainter.height / 2));

    textPainter.text = TextSpan(text: 'L-ε', style: textStyle.copyWith(color: epsilonColor));
    textPainter.layout();
    textPainter.paint(canvas, Offset(padding + 5, toCanvas(0, limitL - epsilon).dy - textPainter.height / 2));

    textPainter.text = TextSpan(text: 'a-δ', style: textStyle.copyWith(color: deltaColor));
    textPainter.layout();
    textPainter.paint(canvas, Offset(toCanvas(limitPointA - delta, 0).dx - textPainter.width / 2, size.height - padding + 5));

    textPainter.text = TextSpan(text: 'a+δ', style: textStyle.copyWith(color: deltaColor));
    textPainter.layout();
    textPainter.paint(canvas, Offset(toCanvas(limitPointA + delta, 0).dx - textPainter.width / 2, size.height - padding + 5));
  }

  @override
  bool shouldRepaint(covariant EpsilonDeltaPainter oldDelegate) {
    return oldDelegate.limitPointA != limitPointA ||
           oldDelegate.limitL != limitL ||
           oldDelegate.epsilon != epsilon ||
           oldDelegate.delta != delta ||
           oldDelegate.primaryColor != primaryColor ||
           oldDelegate.epsilonColor != epsilonColor ||
           oldDelegate.deltaColor != deltaColor ||
           oldDelegate.textColor != textColor;
  }
}
