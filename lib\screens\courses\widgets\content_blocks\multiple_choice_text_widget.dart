import 'package:flutter/material.dart';
import 'package:flutter/services.dart'; // For HapticFeedback
import '../../../../models/course_models.dart';

class MultipleChoiceTextWidget extends StatefulWidget {
  final MultipleChoiceTextElement mcqElement;
  final PageController? pageController;
  final VoidCallback? onNextAction; // For advancing slide or lesson
  final bool isLastSlideInLesson;

  const MultipleChoiceTextWidget({
    super.key,
    required this.mcqElement,
    this.pageController,
    this.onNextAction,
    this.isLastSlideInLesson = false,
  });

  @override
  State<MultipleChoiceTextWidget> createState() =>
      _MultipleChoiceTextWidgetState();
}

class _MultipleChoiceTextWidgetState extends State<MultipleChoiceTextWidget> {
  String? _selectedOptionId;
  bool _isAnswered = false;
  bool _isCorrect = false;

  void _handleOptionSelected(MultipleChoiceTextOption option) {
    if (_isAnswered) return; // Prevent changing answer after submission

    HapticFeedback.lightImpact();
    setState(() {
      _selectedOptionId = option.id;
      _isAnswered = true;
      _isCorrect = option.is_correct;
    });

    String feedbackMessage =
        (_isCorrect ? option.feedback_correct : option.feedback_incorrect) ??
        (_isCorrect ? "Correct!" : "Not quite, try again.");

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(feedbackMessage),
        backgroundColor: _isCorrect ? Colors.green : Colors.redAccent,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  Color _getOptionBackgroundColor(MultipleChoiceTextOption option) {
    if (!_isAnswered || _selectedOptionId != option.id) {
      return Colors.white;
    }
    return option.is_correct
        ? Colors.green.shade50
        : Colors.red.shade50; // Adjusted shades
  }

  Color _getOptionBorderColor(MultipleChoiceTextOption option) {
    if (!_isAnswered || _selectedOptionId != option.id) {
      return Colors.grey.shade300;
    }
    return option.is_correct ? Colors.green.shade400 : Colors.red.shade400;
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Container(
      padding: const EdgeInsets.all(
        16.0,
      ), // Reverted to all(16.0) for simplicity, can be fine-tuned later
      margin: const EdgeInsets.symmetric(vertical: 12.0),
      decoration: BoxDecoration(
        color: Colors.grey[100], // Standardized background
        borderRadius: BorderRadius.circular(12.0),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          if (widget.mcqElement.question_text != null &&
              widget.mcqElement.question_text!.isNotEmpty)
            Padding(
              padding: const EdgeInsets.only(bottom: 16.0),
              child: Text(
                widget.mcqElement.question_text!,
                style: textTheme.titleLarge?.copyWith(
                  // Using titleLarge for question
                  fontWeight: FontWeight.w600, // Bolder question
                  color: Colors.black87,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: widget.mcqElement.options.length,
            separatorBuilder: (context, index) => const SizedBox(height: 10.0),
            itemBuilder: (context, index) {
              final option = widget.mcqElement.options[index];
              return Material(
                color: _getOptionBackgroundColor(option),
                borderRadius: BorderRadius.circular(10.0), // Consistent radius
                elevation:
                    _isAnswered && _selectedOptionId == option.id ? 3.0 : 1.0,
                shadowColor:
                    _isAnswered && _selectedOptionId == option.id
                        ? _getOptionBorderColor(option)
                        : Colors.black26,
                child: InkWell(
                  onTap: () => _handleOptionSelected(option),
                  borderRadius: BorderRadius.circular(10.0),
                  child: Container(
                    width: double.infinity,
                    padding: const EdgeInsets.symmetric(
                      vertical: 16.0,
                      horizontal: 16.0,
                    ),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10.0),
                      border: Border.all(
                        color: _getOptionBorderColor(option),
                        width:
                            _isAnswered && _selectedOptionId == option.id
                                ? 2.0
                                : 1.5,
                      ),
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          child: Text(
                            option.text,
                            textAlign:
                                TextAlign
                                    .left, // Align text to left for readability
                            style: textTheme.bodyLarge?.copyWith(
                              fontSize: 16, // Standardized font size
                              fontWeight:
                                  _isAnswered && _selectedOptionId == option.id
                                      ? FontWeight.bold
                                      : FontWeight.normal,
                              color:
                                  _isAnswered && _selectedOptionId == option.id
                                      ? (option.is_correct
                                          ? Colors.green.shade800
                                          : Colors.red.shade800)
                                      : Colors.black87,
                            ),
                          ),
                        ),
                        if (_isAnswered && _selectedOptionId == option.id)
                          Padding(
                            padding: const EdgeInsets.only(left: 10.0),
                            child: Icon(
                              option.is_correct
                                  ? Icons.check_circle_outline
                                  : Icons.highlight_off, // Different icons
                              color:
                                  option.is_correct
                                      ? Colors.green.shade600
                                      : Colors.red.shade600,
                              size: 22,
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
              );
            }, // Correctly closing itemBuilder
          ), // Correctly closing ListView.separated
          if (_isAnswered &&
              widget.mcqElement.action_button_text != null &&
              widget.mcqElement.action_button_text!.isNotEmpty)
            Padding(
              padding: const EdgeInsets.only(top: 20.0),
              child: SizedBox(
                width: double.infinity,
                height: 52, // Slightly adjusted height
                child: ElevatedButton.icon(
                  icon: Icon(
                    widget.isLastSlideInLesson && _isCorrect
                        ? Icons.skip_next
                        : Icons.arrow_forward,
                    size: 22,
                  ),
                  label: Text(widget.mcqElement.action_button_text!),
                  onPressed:
                      (_isCorrect ||
                                  widget.mcqElement.options
                                          .firstWhere(
                                            (opt) =>
                                                opt.id == _selectedOptionId,
                                            orElse:
                                                () => MultipleChoiceTextOption(
                                                  id: '',
                                                  text: '',
                                                  is_correct: false,
                                                ),
                                          )
                                          .feedback_incorrect ==
                                      null) &&
                              _isAnswered
                          ? widget.onNextAction
                          : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor:
                        _isCorrect
                            ? Theme.of(context).primaryColor
                            : Colors.grey.shade500,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12.0),
                    textStyle: const TextStyle(
                      fontSize: 16.0,
                      fontWeight: FontWeight.bold,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10.0),
                    ),
                    elevation: _isCorrect && _isAnswered ? 2 : 0,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}
