import 'package:flutter/material.dart';

class InteractiveArgumentStrengthEvaluator extends StatefulWidget {
  const InteractiveArgumentStrengthEvaluator({super.key});

  @override
  State<InteractiveArgumentStrengthEvaluator> createState() => _InteractiveArgumentStrengthEvaluatorState();
}

class _InteractiveArgumentStrengthEvaluatorState extends State<InteractiveArgumentStrengthEvaluator> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Argument Strength Evaluator'),
        backgroundColor: Colors.orange,
      ),
      body: const Center(
        child: Text(
          'Interactive Argument Strength Evaluator Widget',
          style: TextStyle(fontSize: 24),
        ),
      ),
    );
  }
}
