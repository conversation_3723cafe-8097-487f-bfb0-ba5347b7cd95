import 'package:flutter/material.dart';
import 'package:resonance/widgets/animated_button.dart';

class InteractiveCausalInferenceBuilder extends StatefulWidget {
  const InteractiveCausalInferenceBuilder({super.key});

  @override
  State<InteractiveCausalInferenceBuilder> createState() => _InteractiveCausalInferenceBuilderState();
}

class _InteractiveCausalInferenceBuilderState extends State<InteractiveCausalInferenceBuilder> {
  String _cause = '';
  String _effect = '';
  String _inferenceResult = '';

  void _buildCausalInference() {
    setState(() {
      if (_cause.isEmpty || _effect.isEmpty) {
        _inferenceResult = 'Please enter both a cause and an effect.';
      } else {
        // Placeholder for actual causal inference building logic
        _inferenceResult = 'Building causal inference: If "$_cause", then "$_effect".\n\n'
            'This tool helps in constructing and understanding causal relationships. '
            'In a real application, this would involve analyzing potential confounding '
            'variables, temporal precedence, and correlation strength to establish '
            'a plausible causal link.';
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text(
            'Causal Inference Builder',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.primary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 20),
          TextField(
            onChanged: (value) {
              setState(() {
                _cause = value;
              });
            },
            decoration: InputDecoration(
              labelText: 'Enter a potential cause',
              hintText: 'e.g., "Increased exercise"',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              filled: true,
              fillColor: Theme.of(context).cardColor,
            ),
          ),
          const SizedBox(height: 10),
          TextField(
            onChanged: (value) {
              setState(() {
                _effect = value;
              });
            },
            decoration: InputDecoration(
              labelText: 'Enter a potential effect',
              hintText: 'e.g., "Improved health"',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              filled: true,
              fillColor: Theme.of(context).cardColor,
            ),
          ),
          const SizedBox(height: 20),
          AnimatedButton(
            onTap: _buildCausalInference,
            label: 'Build Inference',
            color: Theme.of(context).colorScheme.secondary,
            labelColor: Theme.of(context).colorScheme.onSecondary,
          ),
          const SizedBox(height: 20),
          Expanded(
            child: SingleChildScrollView(
              child: Card(
                elevation: 4,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                color: Theme.of(context).colorScheme.surfaceVariant,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Causal Inference:',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                      const SizedBox(height: 10),
                      Text(
                        _inferenceResult.isEmpty ? 'Enter a cause and effect and click "Build Inference" to see the result.' : _inferenceResult,
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
