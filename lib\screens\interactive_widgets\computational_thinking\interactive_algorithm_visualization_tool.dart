import 'package:flutter/material.dart';
import 'dart:async';
import 'dart:math';

class InteractiveAlgorithmVisualizationTool extends StatefulWidget {
  const InteractiveAlgorithmVisualizationTool({super.key});

  @override
  State<InteractiveAlgorithmVisualizationTool> createState() => _InteractiveAlgorithmVisualizationToolState();
}

class _InteractiveAlgorithmVisualizationToolState extends State<InteractiveAlgorithmVisualizationTool> {
  List<int> _numbers = [];
  String _selectedAlgorithm = 'Bubble Sort';
  bool _isSorting = false;
  int _currentIndex = -1;
  int _compareIndex = -1;
  Timer? _timer;

  final Map<String, Function> _algorithms = {
    'Bubble Sort': _bubbleSort,
    'Selection Sort': _selectionSort,
    'Insertion Sort': _insertionSort,
  };

  @override
  void initState() {
    super.initState();
    _generateNumbers();
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  void _generateNumbers() {
    _timer?.cancel();
    setState(() {
      _numbers = List.generate(20, (index) => Random().nextInt(100) + 1);
      _isSorting = false;
      _currentIndex = -1;
      _compareIndex = -1;
    });
  }

  void _startSorting() {
    _timer?.cancel();
    setState(() {
      _isSorting = true;
      _currentIndex = -1;
      _compareIndex = -1;
    });
    _runAlgorithm();
  }

  void _runAlgorithm() {
    List<int> tempNumbers = List.from(_numbers);
    Iterator<List<int>>? steps;

    if (_selectedAlgorithm == 'Bubble Sort') {
      steps = _bubbleSort(tempNumbers).iterator;
    } else if (_selectedAlgorithm == 'Selection Sort') {
      steps = _selectionSort(tempNumbers).iterator;
    } else if (_selectedAlgorithm == 'Insertion Sort') {
      steps = _insertionSort(tempNumbers).iterator;
    }

    _timer = Timer.periodic(const Duration(milliseconds: 100), (timer) {
      if (steps != null && steps.moveNext()) {
        setState(() {
          _numbers = List.from(steps!.current);
          // Extract current and compare indices if the generator yields them
          if (steps.current.length > _numbers.length) { // Assuming last two elements are indices
            _compareIndex = steps.current.last;
            _currentIndex = steps.current[steps.current.length - 2];
            _numbers = steps.current.sublist(0, _numbers.length); // Revert to original size
          } else {
            _currentIndex = -1;
            _compareIndex = -1;
          }
        });
      } else {
        timer.cancel();
        setState(() {
          _isSorting = false;
          _currentIndex = -1;
          _compareIndex = -1;
        });
      }
    });
  }

  static Iterable<List<int>> _bubbleSort(List<int> arr) sync* {
    int n = arr.length;
    for (int i = 0; i < n - 1; i++) {
      for (int j = 0; j < n - i - 1; j++) {
        yield [...arr, j, j + 1]; // Yield current comparison
        if (arr[j] > arr[j + 1]) {
          int temp = arr[j];
          arr[j] = arr[j + 1];
          arr[j + 1] = temp;
          yield [...arr, j, j + 1]; // Yield after swap
        }
      }
    }
  }

  static Iterable<List<int>> _selectionSort(List<int> arr) sync* {
    int n = arr.length;
    for (int i = 0; i < n - 1; i++) {
      int minIdx = i;
      for (int j = i + 1; j < n; j++) {
        yield [...arr, i, j]; // Yield current comparison
        if (arr[j] < arr[minIdx]) {
          minIdx = j;
        }
      }
      int temp = arr[minIdx];
      arr[minIdx] = arr[i];
      arr[i] = temp;
      yield [...arr, i, minIdx]; // Yield after swap
    }
  }

  static Iterable<List<int>> _insertionSort(List<int> arr) sync* {
    int n = arr.length;
    for (int i = 1; i < n; i++) {
      int key = arr[i];
      int j = i - 1;
      yield [...arr, i, j]; // Yield initial comparison
      while (j >= 0 && arr[j] > key) {
        arr[j + 1] = arr[j];
        j = j - 1;
        yield [...arr, i, j + 1]; // Yield shift
      }
      arr[j + 1] = key;
      yield [...arr, i, j + 1]; // Yield placement
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Interactive Algorithm Visualization Tool',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Text('Algorithm:'),
                const SizedBox(width: 10),
                DropdownButton<String>(
                  value: _selectedAlgorithm,
                  onChanged: _isSorting ? null : (String? newValue) {
                    setState(() {
                      _selectedAlgorithm = newValue!;
                      _generateNumbers(); // Reset numbers for new algorithm
                    });
                  },
                  items: _algorithms.keys
                      .map<DropdownMenuItem<String>>((String value) {
                    return DropdownMenuItem<String>(
                      value: value,
                      child: Text(value),
                    );
                  }).toList(),
                ),
              ],
            ),
            const SizedBox(height: 20),
            Container(
              height: 150,
              padding: const EdgeInsets.symmetric(horizontal: 8.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                crossAxisAlignment: CrossAxisAlignment.end,
                children: _numbers.asMap().entries.map((entry) {
                  int index = entry.key;
                  int value = entry.value;
                  return Container(
                    width: 10,
                    height: value.toDouble(),
                    color: index == _currentIndex
                        ? Colors.red
                        : (index == _compareIndex ? Colors.green : Colors.blue),
                    margin: const EdgeInsets.symmetric(horizontal: 1),
                  );
                }).toList(),
              ),
            ),
            const SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ElevatedButton(
                  onPressed: _isSorting ? null : _generateNumbers,
                  child: const Text('Generate New Array'),
                ),
                ElevatedButton(
                  onPressed: _isSorting ? null : _startSorting,
                  child: const Text('Start Sorting'),
                ),
              ],
            ),
            const SizedBox(height: 20),
            const Text(
              'Visualize how different sorting algorithms arrange data elements.',
              style: TextStyle(fontStyle: FontStyle.italic, color: Colors.grey),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
