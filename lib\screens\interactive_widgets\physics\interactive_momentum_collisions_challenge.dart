import 'package:flutter/material.dart';

class InteractiveMomentumCollisionsChallenge extends StatefulWidget {
  const InteractiveMomentumCollisionsChallenge({super.key});

  @override
  State<InteractiveMomentumCollisionsChallenge> createState() => _InteractiveMomentumCollisionsChallengeState();
}

class _InteractiveMomentumCollisionsChallengeState extends State<InteractiveMomentumCollisionsChallenge> {
  int _currentQuestionIndex = 0;
  int _score = 0;
  bool _quizCompleted = false;

  final List<Map<String, dynamic>> _questions = [
    {
      'question': 'What is the unit of momentum?',
      'options': ['N', 'J', 'kg·m/s', 'W'],
      'correctAnswerIndex': 2,
    },
    {
      'question': 'In a perfectly elastic collision, which quantities are conserved?',
      'options': ['Momentum only', 'Kinetic energy only', 'Both momentum and kinetic energy', 'Neither momentum nor kinetic energy'],
      'correctAnswerIndex': 2,
    },
    {
      'question': 'If a 2 kg object moving at 5 m/s collides with a stationary 3 kg object and they stick together, what is their final common velocity?',
      'options': ['1 m/s', '2 m/s', '2.5 m/s', '5 m/s'],
      'correctAnswerIndex': 1, // (2*5 + 3*0) / (2+3) = 10/5 = 2 m/s
    },
    {
      'question': 'What is impulse equal to?',
      'options': ['Change in momentum', 'Force times distance', 'Kinetic energy', 'Work done'],
      'correctAnswerIndex': 0,
    },
  ];

  void _answerQuestion(int selectedOptionIndex) {
    if (_quizCompleted) return;

    setState(() {
      if (_questions[_currentQuestionIndex]['correctAnswerIndex'] == selectedOptionIndex) {
        _score++;
      }

      if (_currentQuestionIndex < _questions.length - 1) {
        _currentQuestionIndex++;
      } else {
        _quizCompleted = true;
      }
    });
  }

  void _resetQuiz() {
    setState(() {
      _currentQuestionIndex = 0;
      _score = 0;
      _quizCompleted = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Momentum & Collisions Challenge'),
        backgroundColor: Colors.indigo,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: _quizCompleted
            ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      'Quiz Completed!',
                      style: TextStyle(fontSize: 28, fontWeight: FontWeight.bold, color: Colors.indigo),
                    ),
                    const SizedBox(height: 20),
                    Text(
                      'Your Score: $_score / ${_questions.length}',
                      style: TextStyle(fontSize: 24, color: Colors.black87),
                    ),
                    const SizedBox(height: 30),
                    ElevatedButton(
                      onPressed: _resetQuiz,
                      child: const Text('Retake Quiz'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.indigo,
                        foregroundColor: Colors.white,
                        padding: EdgeInsets.symmetric(horizontal: 30, vertical: 15),
                        textStyle: TextStyle(fontSize: 18),
                      ),
                    ),
                  ],
                ),
              )
            : Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Text(
                    'Question ${_currentQuestionIndex + 1} of ${_questions.length}',
                    style: const TextStyle(fontSize: 18, color: Colors.grey),
                  ),
                  const SizedBox(height: 20),
                  Text(
                    _questions[_currentQuestionIndex]['question'],
                    style: const TextStyle(fontSize: 22, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 30),
                  ...(_questions[_currentQuestionIndex]['options'] as List<String>).asMap().entries.map((entry) {
                    int optionIndex = entry.key;
                    String optionText = entry.value;
                    return Padding(
                      padding: const EdgeInsets.symmetric(vertical: 8.0),
                      child: ElevatedButton(
                        onPressed: () => _answerQuestion(optionIndex),
                        child: Text(optionText),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blueGrey,
                          foregroundColor: Colors.white,
                          padding: EdgeInsets.symmetric(horizontal: 20, vertical: 15),
                          textStyle: TextStyle(fontSize: 18),
                        ),
                      ),
                    );
                  }).toList(),
                ],
              ),
      ),
    );
  }
}
