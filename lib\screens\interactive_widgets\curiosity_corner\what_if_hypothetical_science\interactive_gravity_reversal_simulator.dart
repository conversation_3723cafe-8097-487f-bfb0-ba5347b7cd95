import 'package:flutter/material.dart';
import 'package:resonance/widgets/interactive_widget_scaffold.dart';

class InteractiveGravityReversalSimulator extends StatefulWidget {
  const InteractiveGravityReversalSimulator({super.key});

  @override
  State<InteractiveGravityReversalSimulator> createState() => _InteractiveGravityReversalSimulatorState();
}

class _InteractiveGravityReversalSimulatorState extends State<InteractiveGravityReversalSimulator> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;
  bool _isGravityReversed = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 2),
    )..addListener(() {
        setState(() {});
      });

    _animation = Tween<double>(begin: 0.0, end: 1.0).animate(_controller);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _toggleGravity() {
    setState(() {
      _isGravityReversed = !_isGravityReversed;
      if (_isGravityReversed) {
        _controller.forward(from: _animation.value);
      } else {
        _controller.reverse(from: _animation.value);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return InteractiveWidgetScaffold(
      title: 'Gravity Reversal Simulator',
      interactiveWidget: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          GestureDetector(
            onTap: _toggleGravity,
            child: Container(
              width: 100,
              height: 100,
              color: Colors.blue,
              alignment: _isGravityReversed ? Alignment.topCenter : Alignment.bottomCenter,
              child: Transform.translate(
                offset: Offset(0.0, _animation.value * (_isGravityReversed ? -100 : 100)),
                child: Container(
                  width: 50,
                  height: 50,
                  color: Colors.red,
                ),
              ),
            ),
          ),
          const SizedBox(height: 20),
          ElevatedButton(
            onPressed: _toggleGravity,
            child: Text(_isGravityReversed ? 'Reverse Gravity' : 'Normal Gravity'),
          ),
        ],
      ),
    );
  }
}
