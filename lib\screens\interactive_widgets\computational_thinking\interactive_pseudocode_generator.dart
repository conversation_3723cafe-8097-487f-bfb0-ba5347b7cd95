import 'package:flutter/material.dart';

class InteractivePseudocodeGenerator extends StatefulWidget {
  const InteractivePseudocodeGenerator({super.key});

  @override
  State<InteractivePseudocodeGenerator> createState() => _InteractivePseudocodeGeneratorState();
}

class _InteractivePseudocodeGeneratorState extends State<InteractivePseudocodeGenerator> {
  String _selectedAlgorithm = 'Calculate Sum';
  String _pseudocode = '';

  final Map<String, String> _algorithms = {
    'Calculate Sum': '''
FUNCTION CalculateSum(numbers):
  sum = 0
  FOR EACH number IN numbers:
    sum = sum + number
  RETURN sum
END FUNCTION
''',
    'Find Max Element': '''
FUNCTION FindMax(list):
  IF list is empty:
    RETURN "List is empty"
  max_element = list[0]
  FOR i FROM 1 TO length(list) - 1:
    IF list[i] > max_element:
      max_element = list[i]
  RETURN max_element
END FUNCTION
''',
    'Check if Even': '''
FUNCTION IsEven(number):
  IF number MOD 2 == 0:
    RETURN TRUE
  ELSE:
    RETURN FALSE
END FUNCTION
''',
  };

  @override
  void initState() {
    super.initState();
    _generatePseudocode();
  }

  void _generatePseudocode() {
    setState(() {
      _pseudocode = _algorithms[_selectedAlgorithm] ?? 'Select an algorithm.';
    });
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Interactive Pseudocode Generator',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Text('Select Algorithm:'),
                const SizedBox(width: 10),
                DropdownButton<String>(
                  value: _selectedAlgorithm,
                  onChanged: (String? newValue) {
                    setState(() {
                      _selectedAlgorithm = newValue!;
                      _generatePseudocode();
                    });
                  },
                  items: _algorithms.keys
                      .map<DropdownMenuItem<String>>((String value) {
                    return DropdownMenuItem<String>(
                      value: value,
                      child: Text(value),
                    );
                  }).toList(),
                ),
              ],
            ),
            const SizedBox(height: 20),
            Container(
              padding: const EdgeInsets.all(12.0),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(8.0),
              ),
              width: double.infinity,
              child: SelectableText(
                _pseudocode,
                style: TextStyle(fontFamily: 'monospace', fontSize: 14, color: Colors.black87),
              ),
            ),
            const SizedBox(height: 20),
            const Text(
              'Pseudocode is a high-level description of an algorithm, independent of any specific programming language.',
              style: TextStyle(fontStyle: FontStyle.italic, color: Colors.grey),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
